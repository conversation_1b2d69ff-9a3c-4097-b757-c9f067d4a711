name: v_card
description: "infyVCard project."

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  google_fonts: ^6.2.1

  get: ^4.6.6
  flutter_screenutil: ^5.9.3
  cached_network_image: ^3.3.1
  flutter_native_splash: ^2.4.2
  dio: ^5.7.0
  dio_smart_retry: ^6.0.0
  dio_cache_interceptor: ^3.5.0
  # http_cache_hive_store: ^5.0.1
  dio_cache_interceptor_hive_store: ^4.0.0
  pretty_dio_logger: ^1.4.0
  get_it: ^8.0.1
  injectable: ^2.5.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0
  shared_preferences: ^2.3.3
  path_provider: ^2.1.4
  crypto: ^3.0.6
  collection: ^1.18.0
  gap: ^3.0.1
  flutter_svg: ^2.0.16
  flutter_easyloading: ^3.0.5
  pinput: ^5.0.0
  animated_text_kit: ^4.2.2
  flutter_ripple:
  siri_wave:
  flutter_sinusoidals_v2: ^0.2.3
  image_picker: ^1.1.2
  flutter_gen_runner: ^5.10.0
  toastification: ^2.3.0
  fl_chart: ^0.66.2
  dotted_border: ^2.1.0
  mobile_scanner: ^6.0.7
  flutter_downloader: ^1.12.0
  permission_handler: ^11.4.0
  http: ^1.3.0
  flutter_quill: ^11.2.0
  flutter_html: ^3.0.0-alpha.6
  vsc_quill_delta_to_html: ^1.0.5
  html: ^0.15.5
  country_picker: ^2.0.27
  percent_indicator: ^4.2.4
  nfc_manager: ^3.5.0
  animated_splash_screen: ^1.3.0
  html_unescape: ^2.0.0
  flutter_popup: ^3.3.4 
  url_launcher: ^6.3.1
  share_plus: ^10.1.4
  shimmer: ^3.0.0
  persistent_bottom_nav_bar: ^6.0.1
  file_picker: ^10.1.2
  qr_flutter: ^4.1.0
  flutter_colorpicker: ^1.1.0
  flutter_image_compress: ^2.4.0
  qr_image: ^1.0.0
  ai_barcode_scanner: ^6.0.1
  connectivity_plus: ^6.1.4
  internet_connection_checker: ^1.0.0+1
  device_info_plus: ^9.1.2
  carousel_slider: ^5.0.0
  media_scanner: ^2.2.0
  google_sign_in: ^6.0.0


# Code Generation
flutter_gen:
  line_length: 160
  integrations:
    flutter_svg: true
    lottie: true
  assets:
    enabled: true
  output: lib/app/utils/helpers/generated 
 

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0
  retrofit_generator: ^9.1.4
  build_runner: ^2.4.13
  intl_utils: ^2.8.7
  json_serializable: ^6.8.0
  injectable_generator: ^2.6.2

flutter_intl:
  enabled: true
  class_name: AppLocalizations
  main_locale: en
  arb_dir: lib/l10n
  output_dir: lib/app/utils/helpers/generated 
 
flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/icon/
    - assets/images/icon/vCard/
    - assets/images/icon/flag/
    - assets/images/png/other/
    - assets/images/png/icons/
    - assets/images/svg/other/
    - assets/images/svg/onboarding/
    - assets/images/svg/auth/

  fonts:
  - family: Inter
    fonts:
      - asset: assets/fonts/Inter-Regular.ttf
        weight: 400
      - asset: assets/fonts/Inter-Medium.ttf
        weight: 500
      - asset: assets/fonts/Inter-SemiBold.ttf
        weight: 600
      - asset: assets/fonts/Inter-Bold.ttf
        weight: 700
        
  - family: Good Timing
    fonts:
      - asset: assets/fonts/good_timing_bd.ttf



flutter_native_splash:
  color: "#01070A"
  android_12:
    image: assets\images\png\other\pngFullSplash.png
    icon_background_color: "#01070A"

