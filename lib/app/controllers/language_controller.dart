import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/controllers/app_controller.dart';
import 'package:v_card/app/data/apis/repository/language_repo.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class LanguageController extends GetxController {
  final LanguageRepository languageRepository;

  LanguageController({required this.languageRepository}) {
    onInit();
  }

  final Rx<int> selectedLanguageIndex = (-1).obs;
  final RxBool isLoading = false.obs;
  final Rx<ApiState> updateLanguageState = ApiState.initial().obs;


    final Rx<String?> selectedLanguageCode = Rx<String?>(null);


  Future<void> updateLanguage(String language) async {
    try {
      isLoading.value = true;
      var response = await languageRepository.updatelanguageRepository({
        "language": language,
      });
      if (response.success) {
        updateLanguageState.value = SuccessState(response);
        getIt<SharedPreferences>().setAppLocal = language.toLowerCase();
        getIt<AppController>().updateLocale(language.toLowerCase());
        // Get.find<AppController>().updateLocale(language.toLowerCase());
      } else if (!response.success) {
        updateLanguageState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text("Failed to update Language"),
        autoCloseDuration: const Duration(seconds: 3),
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
