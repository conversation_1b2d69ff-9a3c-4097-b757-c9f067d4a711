import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/font_repo.dart';
import 'package:v_card/app/data/model/font/font_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class FontController extends GetxController {
  final FontRepository fontRepository;

  FontController({required this.fontRepository}) {
    onInit();
  }

  final Rx<FontListModel?> fontList = Rx<FontListModel?>(null);
  final Rx<FontModel?> currentFont = Rx<FontModel?>(null);
  final Rx<ApiState> fontState = ApiState.initial().obs;
  final RxBool isLoadingGetFont = false.obs;
  final RxBool isLoadingGetFontList = false.obs;
  final RxBool isLoadingSaveFont = false.obs;
  final TextEditingController selectedFontFamily = TextEditingController();
  final TextEditingController selectedFontSize = TextEditingController();

  Future<void> getFontList() async {
    try {
      isLoadingGetFontList.value = true;
      var response = await fontRepository.getFontList();
      if (response.success) {
        fontList.value = response;
        fontState.value = SuccessState(response);
      } else if (!response.success) {
        fontState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingGetFontList.value = false;
    }
  }

  Future<void> getFontById(String vcardId) async {
    try {
      isLoadingGetFont.value = true;
      var response = await fontRepository.getFontById(vcardId);
      if (response.success) {
        currentFont.value = response;
        selectedFontFamily.text = response.fontFamily;
        selectedFontSize.text = response.fontSize;
        fontState.value = SuccessState(response);
      } else if (!response.success) {
        fontState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingGetFont.value = false;
    }
  }

  Future<void> updateFont(String vcardId) async {
    try {
      isLoadingSaveFont.value = true;
      var response = await fontRepository.updateFont(
        fontFamily: selectedFontFamily.value.text,
        fontSize: selectedFontSize.value.text,
        vcardId: vcardId,
      );

      if (response.success) {
        fontState.value = SuccessState(null);

        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.updateSeoPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        fontState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            'Font Update Failed',
            'Failed to update font',
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingSaveFont.value = false;
    }
  }
}
