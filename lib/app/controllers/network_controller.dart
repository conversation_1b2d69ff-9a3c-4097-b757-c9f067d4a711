import 'dart:async';
import 'package:get/get.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:v_card/app/utils/helpers/logger.dart';

class NetworkController extends GetxController {
  final InternetConnectionChecker _connectionChecker = InternetConnectionChecker();

  // Observable values for reactive UI updates
  final RxBool isConnected = true.obs;
  final RxBool isReconnected = false.obs;
  final RxBool hasInitialized = false.obs;
  final RxBool isCheckingConnection = false.obs;

  StreamSubscription<InternetConnectionStatus>? _connectionSubscription;

  @override
  void onInit() {
    super.onInit();
    _setupConnectionListener();
    initialCheck();
  }

  void _setupConnectionListener() {
    // Cancel any existing subscription first
    _connectionSubscription?.cancel();
    
    // Set up new subscription
    _connectionSubscription = _connectionChecker.onStatusChange.listen(
      (status) async {
        final bool currentStatus = status != InternetConnectionStatus.disconnected;
        final bool wasDisconnected = !isConnected.value;
        final bool nowReconnected = wasDisconnected && currentStatus;

        // Update state
        isConnected.value = currentStatus;
        isReconnected.value = nowReconnected;

        Logger.log("CONNECTIVITY STATUS CHANGED: $currentStatus");
        
        // Optional: If reconnected, you could refresh data here
        if (nowReconnected) {
          // Perform any reconnection logic if needed
        }
      },
      onError: (error) {
        Logger.log("CONNECTIVITY LISTENER ERROR: $error");
        // Make sure we reset to a reasonable state
        isConnected.value = true;
        isReconnected.value = false;
        isCheckingConnection.value = false;
      },
    );
  }

  Future<void> initialCheck() async {
    if (isCheckingConnection.value) return;
    
    try {
      isCheckingConnection.value = true;
      
      // Check connection status
      final bool currentStatus = await _connectionChecker.hasConnection;
      
      // Update state
      isConnected.value = currentStatus;
      isReconnected.value = false;
      
      // Set initialized flag if not already set
      if (!hasInitialized.value) {
        hasInitialized.value = true;
      }
      
      Logger.log("CONNECTION CHECK: $currentStatus");
    } catch (e) {
      Logger.log("CONNECTION CHECK ERROR: $e");
      // Default to assuming connection is available in case of error
      isConnected.value = true;
    } finally {
      isCheckingConnection.value = false;
    }
  }

  @override
  void onClose() {
    _connectionSubscription?.cancel();
    super.onClose();
  }
}