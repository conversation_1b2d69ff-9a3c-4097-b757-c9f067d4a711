import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/enquiries_repo.dart';
import 'package:v_card/app/data/model/enquiries/enquiries_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class EnquiriesController extends GetxController {
  final EnquiriesRepository enquiriesRepository;

  EnquiriesController({required this.enquiriesRepository}) {
    onInit();
  }

  // Main enquiries list variables (for enquiries_page.dart)
  final Rx<EnquiriesModel?> enquiriesList = Rx<EnquiriesModel?>(null);
  final RxList<EnquiriesData> fullEnquiriesList = <EnquiriesData>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool hasInitialData = false.obs;

  // Filtered enquiries list variables (for fep.dart)
  final Rx<EnquiriesModel?> filteredEnquiriesList = Rx<EnquiriesModel?>(null);
  final RxList<EnquiriesData> fullFilteredEnquiriesList = <EnquiriesData>[].obs;
  final RxBool isFilteredLoading = false.obs;
  final RxBool hasFilteredInitialData = false.obs;

  // Common variables
  final Rx<ApiState> enquiriesListState = ApiState.initial().obs;
  final Rx<EnquiriesModel?> enquiriesIdData = Rx<EnquiriesModel?>(null);
  final Rx<DeleteEnquiriesModel?> deleteByIdData = Rx<DeleteEnquiriesModel?>(
    null,
  );
  final Rx<ApiState> enquiriesByIdState = ApiState.initial().obs;
  final RxBool isLoadingEnquiriesById = false.obs;
  final RxBool isLoadingEnquiriesDelete = false.obs;
  final RxBool isConnected = true.obs;
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  final RxBool isSearchActive = false.obs;
  final RxString searchText = ''.obs;

  void closeSearch() {
    isSearchActive.value = false;
    searchText.value = '';
    searchController.clear();
    searchFocusNode.unfocus();
  }

  void toggleSearchMode() {
    isSearchActive.value = !isSearchActive.value;
    if (isSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        searchFocusNode.requestFocus();
      });
    } else {
      searchController.clear();
      searchText.value = '';
    }
  }

  final TextEditingController fepSearchController = TextEditingController();
  final FocusNode fepSearchFocusNode = FocusNode();
  final RxBool isFepSearchActive = false.obs;
  final RxString fepSearchText = ''.obs;

  void closeFepSearch() {
    isFepSearchActive.value = false;
    fepSearchText.value = '';
    fepSearchController.clear();
    fepSearchFocusNode.unfocus();
  }

  void toggleFepSearchMode() {
    isFepSearchActive.value = !isFepSearchActive.value;
    if (isFepSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        fepSearchFocusNode.requestFocus();
      });
    } else {
      fepSearchController.clear();
      fepSearchText.value = '';
    }
  }

  // Methods for enquiries_page.dart
  Future<void> checkNetworkAndLoad() async {
    try {
      final networkHelper = NetworkHelper();
      isConnected.value = await networkHelper.hasNetworkConnection();

      Connectivity().onConnectivityChanged.listen((_) async {
        isConnected.value = await networkHelper.hasNetworkConnection();
        if (isConnected.value) await getEnquiriesList();
      });

      if (isConnected.value) await getEnquiriesList();
    } finally {
      hasInitialData.value = true;
    }
  }

  Future<void> getEnquiriesList() async {
    try {
      isLoading.value = true;
      var response = await enquiriesRepository.getEnquiriesListRepository();
      if (response.success) {
        enquiriesList.value = response;
        enquiriesListState.value = SuccessState(response);
        fullEnquiriesList.value = response.data;
      } else {
        enquiriesListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      enquiriesListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Methods for fep.dart
  Future<void> checkFilteredNetworkAndLoad() async {
    try {
      final networkHelper = NetworkHelper();
      isConnected.value = await networkHelper.hasNetworkConnection();

      Connectivity().onConnectivityChanged.listen((_) async {
        isConnected.value = await networkHelper.hasNetworkConnection();
        if (isConnected.value) await getFilteredEnquiriesList();
      });

      if (isConnected.value) await getFilteredEnquiriesList();
    } finally {
      hasFilteredInitialData.value = true;
    }
  }

  Future<void> getFilteredEnquiriesList() async {
    try {
      isFilteredLoading.value = true;
      var response = await enquiriesRepository.getEnquiriesListRepository();
      if (response.success) {
        filteredEnquiriesList.value = response;
        enquiriesListState.value = SuccessState(response);
        fullFilteredEnquiriesList.value = response.data;
      } else {
        enquiriesListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      enquiriesListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isFilteredLoading.value = false;
    }
  }

  // Common methods
  Future<void> getEnquiriesById({required int id}) async {
    try {
      isLoadingEnquiriesById.value = true;

      String? authToken = getIt<SharedPreferences>().getToken;
      var response = await enquiriesRepository.getEnquiriesByIdRepository(
        id: id.toString(),
        authToken: authToken!,
      );
      if (response.success) {
        enquiriesIdData.value = response;
        enquiriesByIdState.value = SuccessState(response);
      } else {
        enquiriesByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      enquiriesByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingEnquiriesById.value = false;
    }
  }

  Future<void> deleteEnquiriesById({required int id}) async {
    try {
      isLoadingEnquiriesDelete.value = true;

      // Delete from both lists optimistically
      _deleteEnquiryFromList(id, enquiriesList, fullEnquiriesList);
      _deleteEnquiryFromList(
        id,
        filteredEnquiriesList,
        fullFilteredEnquiriesList,
      );

      String? authToken = getIt<SharedPreferences>().getToken;
      var response = await enquiriesRepository.deleteEnquiriesByIdRepository(
        id: id,
        authToken: authToken!,
      );

      if (response.success) {
        NavigationService.navigateBack();
      } else {
        // Revert both lists if failed
        _revertDeletedEnquiry(id, enquiriesList, fullEnquiriesList);
        _revertDeletedEnquiry(
          id,
          filteredEnquiriesList,
          fullFilteredEnquiriesList,
        );
        throw Exception('Delete failed');
      }
    } catch (e) {
      enquiriesByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingEnquiriesDelete.value = false;
    }
  }

  void _deleteEnquiryFromList(
    int id,
    Rx<EnquiriesModel?> list,
    RxList<EnquiriesData> fullList,
  ) {
    final originalData = list.value?.data ?? [];
    final updatedData = List<EnquiriesData>.from(originalData)
      ..removeWhere((e) => e.id == id);
    list.value = list.value?.copyWith(data: updatedData);
    fullList.removeWhere((e) => e.id == id);
  }

  void _revertDeletedEnquiry(
    int id,
    Rx<EnquiriesModel?> list,
    RxList<EnquiriesData> fullList,
  ) {
    final originalData = list.value?.data ?? [];
    final itemToRestore = fullList.firstWhereOrNull((e) => e.id == id);
    if (itemToRestore != null) {
      final revertedData = List<EnquiriesData>.from(originalData)
        ..add(itemToRestore);
      list.value = list.value?.copyWith(data: revertedData);
      if (!fullList.contains(itemToRestore)) {
        fullList.add(itemToRestore);
      }
    }
  }

  void reset() {
    // Reset main enquiries state
    enquiriesList.value = null;
    fullEnquiriesList.clear();
    isLoading.value = false;
    hasInitialData.value = false;

    // Reset filtered enquiries state
    filteredEnquiriesList.value = null;
    fullFilteredEnquiriesList.clear();
    isFilteredLoading.value = false;
    hasFilteredInitialData.value = false;

    // Reset common states
    enquiriesListState.value = ApiState.initial();
    enquiriesIdData.value = null;
    deleteByIdData.value = null;
    enquiriesByIdState.value = ApiState.initial();
    isLoadingEnquiriesById.value = false;
    isLoadingEnquiriesDelete.value = false;
    isConnected.value = true;

    // Clear search inputs and focus
    searchController.clear();
    fepSearchController.clear();
    searchFocusNode.unfocus();
    fepSearchFocusNode.unfocus();
    isSearchActive.value = false;
    isFepSearchActive.value = false;
    searchText.value = '';
    fepSearchText.value = '';
  }

  @override
  @i.disposeMethod
  void dispose() {
    // Dispose controllers and focus nodes
    searchController.dispose();
    fepSearchController.dispose();
    searchFocusNode.dispose();
    fepSearchFocusNode.dispose();
    super.dispose();
  }
}
