import 'dart:io';

import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/profile_repo.dart';
import 'package:v_card/app/data/model/profile/profile_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class ProfileController extends GetxController {
  final ProfileRepository profileRepository;

  ProfileController({required this.profileRepository}) {
    onInit();
  }
  final isShowingToast = false.obs;

  final Rx<ProfileModel?> profileData = Rx<ProfileModel?>(null);
  final Rx<ApiState> profileDataState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingUpdateProfile = false.obs;
  final Rx<ApiState> updateProfileDataState = ApiState.initial().obs;
  final registerCountryCode = '91'.obs;
  final registerCountryflag = '🇮🇳'.obs;

  final TextEditingController firstNameController = TextEditingController();
  final TextEditingController lastNameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController contactController = TextEditingController();

  final Rx<File?> profileImageFile = Rx<File?>(null);

  late RxBool isProcessingPWAIcon;

  Future<void> getProfileData() async {
    try {
      isLoading.value = true;
      var response = await profileRepository.getProfileDataRepository();

      if (response.success) {
        profileData.value = response;
        profileDataState.value = SuccessState(response);
        final user = response.data.first;

        firstNameController.text = user.firstName ?? "";
        lastNameController.text = user.lastName ?? "";
        emailController.text = user.email ?? "";
        contactController.text = user.contact ?? "";

        if ((user.regionCode ?? '').isNotEmpty) {
          registerCountryCode.value = user.regionCode!;
        }

        if ((user.regionCode ?? '').isNotEmpty) {
          registerCountryCode.value = user.regionCode!;
        }
      } else if (!response.success) {
        profileDataState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      profileDataState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateProfile(
    String profileImg,
    String firstName,
    String lastName,
    String email,
    String contact,
    String regionCode,
  ) async {
    isLoadingUpdateProfile.value = true;
    try {
      var response = await profileRepository.updateProfileDataRepository({
        "profile": [profileImg],
        "first_name": firstName,
        "last_name": lastName,
        "email": email,
        "contact": contact,
        "region_code": regionCode,
      });

      if (response.success) {
        profileData.value = response;
        updateProfileDataState.value = SuccessState(response);
        NavigationService.navigateBack();
      } else if (!response.success) {
        updateProfileDataState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      updateProfileDataState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingUpdateProfile.value = false;
    }
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
    contactController.clear();
  }
}
