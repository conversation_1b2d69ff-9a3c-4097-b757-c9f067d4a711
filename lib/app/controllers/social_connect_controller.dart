import 'dart:io';

import 'package:get/get.dart';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/repository/social_connect_repo.dart';
import 'package:v_card/app/data/model/social_connect/social_connect_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';


@lazySingleton
@injectable
class SocialConnectController extends GetxController {
  final SocialConnectRepository repository;

  SocialConnectController({required this.repository});
  @override
  void onInit() {
    super.onInit();
    final String vcardId = Get.arguments['vcardId'] ?? '0';
    getSocialConnectData(vcardId);
  }

  void updateTextControllers() {
    if (socialConnectData.value != null) {
      enableIcon.value =
          socialConnectData.value!.data.socialIcon?.isNotEmpty ?? false;
      twitterController.text = socialConnectData.value?.data.twitter ?? '';
      facebookController.text = socialConnectData.value?.data.facebook ?? '';
      whatsappController.text = socialConnectData.value?.data.whatsapp ?? '';
      instagramController.text = socialConnectData.value?.data.instagram ?? '';
      youtubeController.text = socialConnectData.value?.data.youtube ?? '';
      websiteController.text = socialConnectData.value?.data.website ?? '';
      tumblrController.text = socialConnectData.value?.data.tumblr ?? '';
      redditController.text = socialConnectData.value?.data.reddit ?? '';
      linkedinController.text = socialConnectData.value?.data.linkedin ?? '';
      pinterestController.text = socialConnectData.value?.data.pinterest ?? '';
      tiktokController.text = socialConnectData.value?.data.tiktok ?? '';
      snapchatController.text = socialConnectData.value?.data.snapchat ?? '';
    }
  }

  final RxBool enableIcon = false.obs;
  late RxBool isProcessingIcon;
  final Rx<File?> profileImageFile = Rx<File?>(null);
  final isShowingToast = false.obs;


  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;
  final Rx<ApiState> productListState = ApiState.initial().obs;
  final Rx<SocialConnectModel?> socialConnectData = Rx<SocialConnectModel?>(
    null,
  );

  final twitterController = TextEditingController();
  final facebookController = TextEditingController();
  final instagramController = TextEditingController();
  final youtubeController = TextEditingController();
  final websiteController = TextEditingController();
  final tumblrController = TextEditingController();
  final redditController = TextEditingController();
  final linkedinController = TextEditingController();
  final whatsappController = TextEditingController();
  final pinterestController = TextEditingController();
  final tiktokController = TextEditingController();
  final snapchatController = TextEditingController();

  final Rx<ApiState> saveProductListState = ApiState.initial().obs;

  Future<void> getSocialConnectData(String vcardId) async {
    try {
      isLoading.value = true;
      var response = await repository.getsosialConnectData(vcardId);

      if (response.success) {
        socialConnectData.value = response;
        productListState.value = SuccessState(response);
        updateTextControllers();
      } else if (!response.success) {
        productListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      productListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> saveSocialConnectData(
    Map<String, dynamic> data,
    String vcardId,
  ) async {
    try {
      isSaving.value = true;

      var response = await repository.saveSocialConnectData(data);

      if (response.success) {
        saveProductListState.value = SuccessState(response);

        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createCustomLinkPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else if (!response.success) {
        saveProductListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      saveProductListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isSaving.value = false;
    }
  }
}
