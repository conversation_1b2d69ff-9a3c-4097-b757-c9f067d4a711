import 'dart:io';

import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/testimonials_repo.dart';
import 'package:v_card/app/data/model/testimonials/create_testimonials_model.dart';
import 'package:v_card/app/data/model/testimonials/delete_testimonials_model.dart';
import 'package:v_card/app/data/model/testimonials/testimonials_detail_model.dart';
import 'package:v_card/app/data/model/testimonials/testimonials_model.dart';
import 'package:v_card/app/data/model/testimonials/update_testimonials_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class TestimonialsController extends GetxController {
  final TestimonialsRepository testimonialsRepository;

  TestimonialsController({required this.testimonialsRepository}) {
    onInit();
  }

  @override
  void onInit() {
    super.onInit();
    getTestimonialsList('0');
  }

  final isShowingToast = false.obs;


  final Rx<TestimonialsModel?> testimonialsList = Rx<TestimonialsModel?>(null);
  final Rx<ApiState> testimonialsListState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;

  final Rx<File?> profileImageFile = Rx<File?>(null);
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();

  final RxBool isLoadingCreateTestimonials = false.obs;
  final Rx<CreateTestimonialsModel?> createDataresponse =
      Rx<CreateTestimonialsModel?>(null);

  final Rx<ApiState> createTestimonialsState = ApiState.initial().obs;

  final Rx<TestimonialsDetailModel?> testimonialsDataById =
      Rx<TestimonialsDetailModel?>(null);
  final RxBool isLoadingTestimonialsById = false.obs;
  final Rx<ApiState> testimonialsByIdState = ApiState.initial().obs;

  final Rx<DeleteTestimonialsModel?> deleteByIdData =
      Rx<DeleteTestimonialsModel?>(null);
  final RxBool isLoadingDeleteTestimonialsById = false.obs;
  final Rx<ApiState> deleteTestimonialsByIdState = ApiState.initial().obs;

  final Rx<UpdateTestimonialsModel?> updateTestimonialsResponse =
      Rx<UpdateTestimonialsModel?>(null);
  final Rx<ApiState> updateTestimonialsState = ApiState.initial().obs;

  late Rx<bool> isProcessingPWAIcon;

  Future<void> getTestimonialsList(String vCardId) async {
    try {
      isLoading.value = true;
      var response = await testimonialsRepository
          .getadminTestimonialsListRepository(vCardId);

      if (response.success) {
        testimonialsList.value = response;
        testimonialsListState.value = SuccessState(response);
      } else if (!response.success) {
        testimonialsListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      testimonialsListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createAdminTestimonials({
    required String vcardId,
    required String name,
    String? profileImg,
    required String description,
  }) async {
    try {
      Map<String, dynamic> formData = {
        "image": [profileImg],
        "name": name,
        "description": description,
        "vcard_id": vcardId,
      };

      isLoadingCreateTestimonials.value = true;

      var response = await testimonialsRepository
          .createAdminTestimonialsRepository(data: formData);

      if (response.success) {
        createDataresponse.value = response;
        createTestimonialsState.value = SuccessState(response);

        if (isCardCreated) {
          getTestimonialsList(vcardId);
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createIframePage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        createTestimonialsState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingCreateTestimonials.value = false;
    }
  }

  Future<void> getTestimonialsById({required int id}) async {
    try {
      isLoadingTestimonialsById.value = true;

      var response = await testimonialsRepository.getTestimonialsByIdRepository(
        id: id,
      );

      if (response.success) {
        testimonialsDataById.value = response;
        testimonialsByIdState.value = SuccessState(response);
      } else if (!response.success) {
        testimonialsByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      testimonialsByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingTestimonialsById.value = false;
    }
  }

  Future<void> deleteTestimonialsById({
    required int id,
    required String vcardId,
  }) async {
    try {
      isLoadingDeleteTestimonialsById.value = true;

      var response = await testimonialsRepository
          .deleteTestimonialsByIdRepository(id: id);

      if (response.success) {
        deleteByIdData.value = response;
        deleteTestimonialsByIdState.value = SuccessState(response);
        getTestimonialsList(vcardId);
        if (Get.isBottomSheetOpen == true) {
          NavigationService.navigateBack();
        }
        NavigationService.navigateBack();
      } else if (!response.success) {
        deleteTestimonialsByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      deleteTestimonialsByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingDeleteTestimonialsById.value = false;
    }
  }

  Future<void> updateTestimonials({
    required int testimonialsId,
    required String vcardId,
    required String name,
    String? profileImg,
    required String description,
  }) async {
    try {
      isLoadingCreateTestimonials.value = true;
      Map<String, dynamic> formData = {
        "image": [profileImg],
        "name": name,
        "description": description,
      };

      var response = await testimonialsRepository.updateTestimonialsRepository(
        testimonialsId: testimonialsId,
        data: formData,
      );

      if (response.success) {
        updateTestimonialsResponse.value = response;
        updateTestimonialsState.value = SuccessState(response);
        getTestimonialsList(vcardId);
        NavigationService.navigateBack();
      } else {
        updateTestimonialsState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingCreateTestimonials.value = false;
    }
  }

  void resetForm() {
    nameController.clear();
    descriptionController.clear();
    profileImageFile.value = null;
    testimonialsDataById.value = null;
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
