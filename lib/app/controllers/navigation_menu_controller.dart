import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/controllers/appointment_controller.dart';
import 'package:v_card/app/controllers/business_card_controller.dart';
import 'package:v_card/app/controllers/enquiries_controller.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class NavigationMenuController extends GetxController {
  NavigationMenuController() {
    onInit();
  }

  final RxInt currentIndex = 0.obs;
  late PersistentTabController persistentTabController;
  final List<int> _navigationHistory = [];

  final List<Widget> pages = [
    DashboardPage(),
    VcardPage(),
    BusinessCardPage(),
    EnquiriesPage(),
    AppointmentPage(),
  ];

  @override
  void onInit() {
    super.onInit();
    persistentTabController = PersistentTabController(
      initialIndex: currentIndex.value,
    );
    _navigationHistory.add(0);
  }

  void changePage(int index) {
    // Close search on current page before switching
    if (currentIndex.value == 1) {
      getIt<VcardController>().closeSearch();
    }
    if (currentIndex.value == 2) {
      getIt<BusinesscardController>().closeSearch();
    }
    if (currentIndex.value == 3) {
      getIt<EnquiriesController>().closeSearch();
    }
    if (currentIndex.value == 4) {
      getIt<AppointmentController>().closeSearch();
    }

    if (currentIndex.value != index) {
      _navigationHistory.add(index);
    }
    currentIndex.value = index;
    persistentTabController.jumpToTab(index);
  }

  void goToPreviousTab() {
    int newIndex = currentIndex.value - 1;
    if (newIndex < 0) {
      newIndex = pages.length - 1;
    }
    changePage(newIndex);
  }

  @override
  @i.disposeMethod
  void dispose() {
    persistentTabController.dispose();
    super.dispose();
  }
}
