import 'package:get/get.dart';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@lazySingleton
class OnboardingController extends GetxController {
  final RxInt currentPage = 0.obs;
  final RxInt pageCount = 4.obs;

  final PageController pageController = PageController(initialPage: 0);

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  void nextPage() {
    if (currentPage.value < pageCount.value - 1) {
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      completeOnboarding();
    }
  }

  void previousPage() {
    if (currentPage.value > 0) {
      pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      NavigationService.navigateWithSlideAnimation(AppRoutes.welcomePage);
    }
  }

  void skipOnboarding() {
    completeOnboarding();
  }

  void completeOnboarding() {
    getIt<SharedPreferences>().setOnboardingCompleted = true;
    NavigationService.navigateWithSlideAnimation(AppRoutes.login);
  }
}
