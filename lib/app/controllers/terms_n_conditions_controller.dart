// ignore_for_file: library_prefixes

import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:flutter_quill/quill_delta.dart' as quill;
import 'package:get/get.dart';
import 'package:html/parser.dart' as htmlParser;
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/terms_n_conditions_repo.dart';
import 'package:v_card/app/data/model/terms_n_conditions/terms_n_conditions_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:html/dom.dart' as dom;

@i.lazySingleton
@i.injectable
class TermsAndConditionsController extends GetxController {
  final TermsAndConditionsRepository termConditionRepository;

  TermsAndConditionsController({required this.termConditionRepository});

  quill.QuillController quillController = quill.QuillController.basic();

  final Rx<TermsAndConditionsModel?> termCondition =
      Rx<TermsAndConditionsModel?>(null);
  final Rx<ApiState> termConditionState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;
  final RxBool isSaveLoading = false.obs;

  Future<void> getTermsAndConditions(String vcardId) async {
    try {
      isLoading.value = true;
      var response = await termConditionRepository.getTermsAndConditions(
        vcardId,
      );

      if (response.success) {
        termCondition.value = response;

        _initializeQuillController(response.data.termCondition);

        termConditionState.value = SuccessState(response);
      } else if (!response.success) {
        termConditionState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      termConditionState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _initializeQuillController(String htmlContent) {
    try {
      final document = htmlParser.parse(htmlContent);
      final delta = quill.Delta();

      void parseNode(dom.Node node, {Map<String, dynamic>? currentStyle}) {
        currentStyle ??= {};

        if (node is dom.Element) {
          final newStyle = Map<String, dynamic>.from(currentStyle);

          switch (node.localName) {
            case 'b':
            case 'strong':
              newStyle['bold'] = true;
              break;
            case 'i':
            case 'em':
              newStyle['italic'] = true;
              break;
            case 'u':
              newStyle['underline'] = true;
              break;
            case 'span':
              for (var className in node.classes) {
                if (className.startsWith('ql-font-')) {
                  newStyle['font'] = className.replaceFirst('ql-font-', '');
                }
              }
              break;
          }

          for (var child in node.nodes) {
            parseNode(child, currentStyle: newStyle);
          }
        } else if (node is dom.Text) {
          final text = node.text;
          if (text.isNotEmpty) {
            delta.insert(text, currentStyle);
          }
        }
      }

      parseNode(document.body!);

      // Ensure the document ends with a newline.
      if (delta.isNotEmpty && !(delta.last.data as String).endsWith('\n')) {
        delta.insert('\n'); // Add a newline at the end of the content.
      }

      quillController = quill.QuillController(
        document: quill.Document.fromDelta(delta),
        selection: const TextSelection.collapsed(offset: 0),
      );
    } catch (e) {
      Logger.log('Manual HTML parse failed: ${e.toString()}');
      quillController = quill.QuillController.basic();
    }
  }

  // Add these to your TermsAndConditionsController
  String get plainText {
    if (termCondition.value?.data.termCondition == null) return '';
    return htmlParser
            .parse(termCondition.value!.data.termCondition)
            .body
            ?.text ??
        '';
  }

  String? get textFont {
    if (termCondition.value?.data.termCondition == null) return null;
    final document = htmlParser.parse(termCondition.value!.data.termCondition);
    final span = document.querySelector('span');
    if (span != null && span.classes.isNotEmpty) {
      for (var className in span.classes) {
        if (className.startsWith('ql-font-')) {
          return className.replaceFirst('ql-font-', '');
        }
      }
    }
    return '';
  }

  Future<void> updateTermsAndConditions(
    String vcardId,
    String termCondition,
  ) async {
    try {
      isSaveLoading.value = true;
      var success = await termConditionRepository.updateTermsAndConditions(
        termCondition: termCondition,
        vcardId: vcardId,
      );

      if (success) {
        termConditionState.value = SuccessState(success);

        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.manageSectionPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        throw Exception('Failed to update Term andCondition');
      }
    } catch (e) {
      termConditionState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'Terms And Conditions Update Failed',
          e.toString(),
        ),
      );
    } finally {
      isSaveLoading.value = false;
    }
  }

  @override
  @i.disposeMethod
  void dispose() {
    quillController.dispose();
    super.dispose();
  }
}
