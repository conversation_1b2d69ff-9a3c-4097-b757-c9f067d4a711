// File: app/controllers/custom_link_controller.dart

// ignore_for_file: deprecated_member_use

import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/custom_link_repo.dart';
import 'package:v_card/app/data/model/custom_link/custom_link_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class CustomLinkController extends GetxController {
  final CustomLinkRepository customLinkRepository;

  CustomLinkController({required this.customLinkRepository});

  // State variables
  final Rx<CustomLinkModel?> customLinkData = Rx<CustomLinkModel?>(null);
  final Rx<CustomLinkDetailModel?> customLinkDataById =
      Rx<CustomLinkDetailModel?>(null);
  final Rx<CustomLinkResponse?> createDataResponse = Rx<CustomLinkResponse?>(
    null,
  );
  final Rx<CustomLinkResponse?> updateOpenInNewTabResponse =
      Rx<CustomLinkResponse?>(null);
  final Rx<CustomLinkResponse?> updateShowAsButtonResponse =
      Rx<CustomLinkResponse?>(null);
  final Rx<ApiState> customLinkState = ApiState.initial().obs;
  final Rx<ApiState> customLinkCreateState = ApiState.initial().obs;
  final RxBool isLoadingCustomLinks = false.obs;
  final RxBool isLoadingCustomLinkById = false.obs;
  final RxBool isSubmittingCustomLink = false.obs;
  final RxBool isDeletingCustomLink = false.obs;

  // Form controllers
  final TextEditingController linkNameController = TextEditingController();
  final TextEditingController linkUrlController = TextEditingController();
  final Rx<String> buttonColor = "#0000FF".obs; // Default blue color
  final Rx<String> buttonType = "Square".obs; // Default square type
  final RxBool showAsButton = false.obs;
  final RxBool openInNewTab = false.obs;

  @override
  void onClose() {
    linkNameController.dispose();
    linkUrlController.dispose();
    super.onClose();
  }

  // Reset form fields
  void resetForm() {
    linkNameController.clear();
    linkUrlController.clear();
    buttonColor.value = "#0000FF"; // Default blue color
    buttonType.value = "Square"; // Default square type
    showAsButton.value = false;
    openInNewTab.value = false;
  }

  final isButtonTypeExpanded = false.obs;

  // Set button type
  void setButtonType(String type) {
    buttonType.value = type;
  }

  // Set button color
  void setButtonColor(String color) {
    buttonColor.value = color;
  }

  // Toggle show as button
  void toggleShowAsButton(bool value) {
    showAsButton.value = value;
  }

  // Toggle open in new tab
  void toggleOpenInNewTab(bool value) {
    openInNewTab.value = value;
  }

  // Get custom links for a vCard
  Future<void> getCustomLinksList(String vcardId) async {
    try {
      isLoadingCustomLinks.value = true;
      var response = await customLinkRepository.getCustomLinksRepo(vcardId);

      if (response.success) {
        customLinkData.value = response;
        customLinkState.value = SuccessState(response);
      } else {
        customLinkState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      customLinkState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'Error Loading Custom Links',
          'Failed to load custom links.',
        ),
      );
    } finally {
      isLoadingCustomLinks.value = false;
    }
  }

  // Get custom link by ID
  Future<void> getCustomLinkById(String linkId) async {
    try {
      isLoadingCustomLinkById.value = true;
      var response = await customLinkRepository.getCustomLinkByIdRepo(linkId);

      if (response.success) {
        customLinkDataById.value = response;
        customLinkState.value = SuccessState(response);
      } else {
        customLinkState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      customLinkState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'Error Loading Custom Link',
          'Failed to load custom link details.',
        ),
      );
    } finally {
      isLoadingCustomLinkById.value = false;
    }
  }

  // Create custom link
  Future<void> createCustomLink(String vcardId) async {
    try {
      isSubmittingCustomLink.value = true;

      final processedUrl = processProductUrl(
        linkUrlController.text.trim().toLowerCase(),
      );

      final Map<String, dynamic> data = {
        "vcard_id": vcardId,
        "link_name": linkNameController.text.trim(),
        "link": processedUrl,
        "show_as_button": showAsButton.value ? 1 : 0,
        "open_new_tab": openInNewTab.value ? 1 : 0,
        "button_color": buttonColor.value,
        "button_type": buttonType.value,
      };

      var response = await customLinkRepository.createCustomLinkRepo(
        data: data,
      );

      if (response.success) {
        createDataResponse.value = response;
        customLinkCreateState.value = SuccessState(response);

        if (isCardCreated) {
          NavigationService.navigateBack();
          getCustomLinksList(vcardId);
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.updateBannerPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }

        resetForm();
      } else {
        customLinkCreateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      customLinkCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isSubmittingCustomLink.value = false;
    }
  }

  // Update custom link
  Future<void> updateCustomLink({
    required String linkId,
    required String vcardId,
  }) async {
    try {
      isSubmittingCustomLink.value = true;

      final Map<String, dynamic> data = {
        "vcard_id": vcardId,
        "link_name": linkNameController.text.trim(),
        "link": linkUrlController.text.trim(),
        "show_as_button": showAsButton.value ? 1 : 0,
        "open_new_tab": openInNewTab.value ? 1 : 0,
        "button_color": buttonColor.value,
        "button_type": buttonType.value,
      };

      var response = await customLinkRepository.updateCustomLinkRepo(
        data: data,
        linkId: linkId,
      );

      if (response.success) {
        createDataResponse.value = response;
        customLinkCreateState.value = SuccessState(response);
        NavigationService.navigateBack();
        getCustomLinksList(vcardId);
        resetForm();
      } else {
        customLinkCreateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      customLinkCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isSubmittingCustomLink.value = false;
    }
  }

  // Delete custom link
  Future<bool> deleteCustomLink({
    required String linkId,
    required String vcardId,
  }) async {
    try {
      isDeletingCustomLink.value = true;

      var response = await customLinkRepository.deleteCustomLinkRepo(linkId);

      if (response.success) {
        customLinkState.value = SuccessState(response);
        getCustomLinksList(vcardId);
        NavigationService.navigateBack();
        return true;
      } else {
        customLinkState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
        return false;
      }
    } catch (e) {
      customLinkState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
      return false;
    } finally {
      isDeletingCustomLink.value = false;
    }
  }

  // Add these to your controller
  final RxMap<String, bool> updatingOpenNewTabMap = <String, bool>{}.obs;
  final RxMap<String, bool> updatingShowAsButtonMap = <String, bool>{}.obs;

  Future<void> updateOpenNewTab({
    required String linkId,
    required String vcardId,
    required bool value,
  }) async {
    try {
      // Optimistically update the local data
      final currentData = customLinkData.value;
      if (currentData != null) {
        final updatedLinks =
            currentData.data.map((link) {
              if (link.id.toString() == linkId) {
                return link.copyWith(openNewTab: value ? 1 : 0);
              }
              return link;
            }).toList();

        customLinkData.value = currentData.copyWith(data: updatedLinks);
      }

      updatingOpenNewTabMap[linkId] = true;

      final response = await customLinkRepository.updateUpdateOpenNewTabRepo(
        linkId: linkId,
        data: {"open_new_tab": value ? 1 : 0},
      );

      if (!response.success) {
        // Revert if API call fails
        if (currentData != null) {
          customLinkData.value = currentData;
        }
        throw Exception('API call failed');
      }
    } catch (e) {
      customLinkCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      updatingOpenNewTabMap.remove(linkId);
      // Optionally refresh to ensure sync with server
      // await getCustomLinksList(vcardId);
    }
  }

  Future<void> updateShowAsButton({
    required String linkId,
    required String vcardId,
    required bool value,
  }) async {
    try {
      // Optimistically update the local data
      final currentData = customLinkData.value;
      if (currentData != null) {
        final updatedLinks =
            currentData.data.map((link) {
              if (link.id.toString() == linkId) {
                return link.copyWith(showAsButton: value ? 1 : 0);
              }
              return link;
            }).toList();

        customLinkData.value = currentData.copyWith(data: updatedLinks);
      }

      updatingShowAsButtonMap[linkId] = true;

      final response = await customLinkRepository.updateShowAsbuttonRepo(
        linkId: linkId,
        data: {"show_as_button": value ? 1 : 0},
      );

      if (!response.success) {
        // Revert if API call fails
        if (currentData != null) {
          customLinkData.value = currentData;
        }
        throw Exception('API call failed');
      }
    } catch (e) {
      customLinkCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      updatingShowAsButtonMap.remove(linkId);
      // Optionally refresh to ensure sync with server
      // await getCustomLinksList(vcardId);
    }
  }
  // Modify the updateOpenNewTab method
  // Future<void> updateOpenNewTab({
  //   required String linkId,
  //   required String vcardId,
  //   required bool value,
  // }) async {
  //   try {
  //     updatingOpenNewTabMap[linkId] =
  //         true; // Set loading for this specific item

  //     final response = await customLinkRepository.updateUpdateOpenNewTabRepo(
  //       linkId: linkId,
  //       data: {"open_new_tab": value ? 1 : 0},
  //     );

  //     if (response.success) {
  //       updateOpenInNewTabResponse.value = response;
  //       customLinkCreateState.value = SuccessState(response);
  //       getCustomLinksList(vcardId); // Refresh the list
  //     } else {
  //       customLinkCreateState.value = FailedState(
  //         statusCode: 0,
  //         isRetirable: false,
  //         error: UserFriendlyError(
  //           AppStrings.T.apiError,
  //           AppStrings.T.apiErrorDescription,
  //         ),
  //       );
  //     }
  //   } catch (e) {
  //     customLinkCreateState.value = FailedState(
  //       statusCode: 0,
  //       isRetirable: false,
  //       error: UserFriendlyError(
  //         AppStrings.T.apiError,
  //         AppStrings.T.apiErrorDescription,
  //       ),
  //     );
  //   } finally {
  //     updatingOpenNewTabMap.remove(linkId); // Remove loading state
  //   }
  // }

  // // Similarly modify the updateShowAsButton method
  // Future<void> updateShowAsButton({
  //   required String linkId,
  //   required String vcardId,
  //   required bool value,
  // }) async {
  //   try {
  //     updatingShowAsButtonMap[linkId] =
  //         true; // Set loading for this specific item

  //     final response = await customLinkRepository.updateShowAsbuttonRepo(
  //       linkId: linkId,
  //       data: {"show_as_button": value ? 1 : 0},
  //     );

  //     if (response.success) {
  //       updateShowAsButtonResponse.value = response;
  //       customLinkCreateState.value = SuccessState(response);
  //       getCustomLinksList(vcardId); // Refresh the list
  //     } else {
  //       customLinkCreateState.value = FailedState(
  //         statusCode: 0,
  //         isRetirable: false,
  //         error: UserFriendlyError(
  //           AppStrings.T.apiError,
  //           AppStrings.T.apiErrorDescription,
  //         ),
  //       );
  //     }
  //   } catch (e) {
  //     customLinkCreateState.value = FailedState(
  //       statusCode: 0,
  //       isRetirable: false,
  //       error: UserFriendlyError(
  //         AppStrings.T.apiError,
  //         AppStrings.T.apiErrorDescription,
  //       ),
  //     );
  //   } finally {
  //     updatingShowAsButtonMap.remove(linkId); // Remove loading state
  //   }
  // }

  // Convert hex color string to Color object
  Color hexToColor(String hexString) {
    hexString = hexString.toUpperCase().replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    return Color(int.parse(hexString, radix: 16));
  }

  void updateQRCodeColor(Color color) {
    buttonColor.value =
        '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }
}
