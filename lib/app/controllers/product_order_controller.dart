import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/product_order_repo.dart';
import 'package:v_card/app/data/model/product_order/product_order_model.dart';
// ignore: unnecessary_import
import 'package:v_card/app/utils/helpers/exception/exception.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class ProductOrderController extends GetxController {
  final ProductOrderRepository productOrderRepository;

  ProductOrderController({required this.productOrderRepository}) {
    onInit();
  }
  final RxBool isLoading = false.obs;
  final Rx<ProductOrderModel?> enquiriesList = Rx<ProductOrderModel?>(null);
  final Rx<ApiState> enquiriesListState = ApiState.initial().obs;

  // In ProductOrderController
final _expandedIndices = <int>{}.obs;

bool isExpanded(int index) => _expandedIndices.contains(index);

void toggleExpansion(int index) {
  if (_expandedIndices.contains(index)) {
    _expandedIndices.remove(index);
  } else {
    _expandedIndices.add(index);
  }
} 

  Future<void> getProductOrderList() async {
    try {
      isLoading.value = true;
      var response = await productOrderRepository.getProductOrderRepository();
      if (response.success) {
        enquiriesList.value = response;
        enquiriesListState.value = SuccessState(response);
      } else if (!response.success) {
        enquiriesListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      enquiriesListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
