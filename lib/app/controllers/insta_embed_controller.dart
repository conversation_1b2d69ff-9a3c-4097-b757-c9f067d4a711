import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/insta_embed_repo.dart';
import 'package:v_card/app/data/model/insta_embed/insta_embed_detail_model.dart';
import 'package:v_card/app/data/model/insta_embed/insta_embed_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class InstaEmbedController extends GetxController {
  final InstaEmbedRepository instaEmbedRepository;

  InstaEmbedController({required this.instaEmbedRepository});

  // State variables
  final Rx<InstaEmbedModel?> instaEmbedData = Rx<InstaEmbedModel?>(null);
  final Rx<InstaEmbedDetailModel?> instaEmbedDataById =
      Rx<InstaEmbedDetailModel?>(null);
  final Rx<InstaEmbedCreateResponse?> createDataresponse =
      Rx<InstaEmbedCreateResponse?>(null);
  final Rx<ApiState> instaEmbedState = ApiState.initial().obs;
  final Rx<ApiState> instaEmbedCreateState = ApiState.initial().obs;
  final RxBool isLoadingEmbeds = false.obs;
  final RxBool isLoadingEmbedsById = false.obs;
  final RxBool isSubmittingEmbed = false.obs;
  final RxBool isDeletingEmbed = false.obs;

  // Form controllers
  final TextEditingController embedTagController = TextEditingController();
  final RxString selectedType = "0".obs;


  @override
  void onClose() {
    embedTagController.dispose();
    super.onClose();
  }

  // Reset form fields
  void resetForm() {
    embedTagController.clear();
    selectedType.value = "0";
  }

  // Get Instagram embeds
  Future<void> getInstaEmbeds(String vcardId) async {
    try {
      isLoadingEmbeds.value = true;
      var response = await instaEmbedRepository.getInstaEmbeds(vcardId);

      if (response.success) {
        instaEmbedData.value = response;
        instaEmbedState.value = SuccessState(response);
      } else {
        instaEmbedState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      instaEmbedState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'Error Loading Embeds',
          'Failed to load Instagram embeds.',
        ),
      );
    } finally {
      isLoadingEmbeds.value = false;
    }
  }

  Future<void> getInstaEmbedsById(String embedId) async {
    try {
      isLoadingEmbedsById.value = true;
      var response = await instaEmbedRepository.getInstaEmbedsById(embedId);

      if (response.success) {
        instaEmbedDataById.value = response;
        instaEmbedState.value = SuccessState(response);
      } else {
        instaEmbedState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      instaEmbedState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'Error Loading Embeds',
          'Failed to load Instagram embeds.',
        ),
      );
    } finally {
      isLoadingEmbedsById.value = false;
    }
  }

  // Create or update Instagram embed
  Future<void> createInstaEmbed(String vcardId) async {
    try {
      isSubmittingEmbed.value = true;

      final Map<String, dynamic> data = {
        "type": selectedType.value,
        "embedtag": embedTagController.text.trim(),
        "vcard_id": vcardId,
      };

      var response = await instaEmbedRepository.createInstaEmbed(data: data);

      if (response.success) {
        createDataresponse.value = response;
        instaEmbedCreateState.value = SuccessState(response);

        if (isCardCreated) {
          getInstaEmbeds(vcardId);
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createGalleriesPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
        resetForm();
      } else {
        instaEmbedCreateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      instaEmbedCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isSubmittingEmbed.value = false;
    }
  }

  Future<void> updateInstaEmbed({
    required String embedId,
    required String vcardId,
  }) async {
    try {
      isSubmittingEmbed.value = true;

      final Map<String, dynamic> data = {
        "type": selectedType.value,
        "embedtag": embedTagController.text.trim(),
      };

      var response = await instaEmbedRepository.updateInstaEmbed(
        data: data,
        id: embedId,
      );

      if (response.success) {
        createDataresponse.value = response;
        instaEmbedCreateState.value = SuccessState(response);
        NavigationService.navigateBack();
        getInstaEmbeds(vcardId);
        resetForm();
      } else {
        instaEmbedCreateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      instaEmbedCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isSubmittingEmbed.value = false;
    }
  }

  // Delete Instagram embed
  Future<bool> deleteInstaEmbed({
    required String embedId,
    required String vcardId,
  }) async {
    try {
      isDeletingEmbed.value = true;

      var response = await instaEmbedRepository.deleteInstaEmbed(embedId);

      if (response.success) {
        instaEmbedState.value = SuccessState(response);
        getInstaEmbeds(vcardId);
        NavigationService.navigateBack();
        return true;
      } else {
        instaEmbedState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
        return false;
      }
    } catch (e) {
      instaEmbedState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
      return false;
    } finally {
      isDeletingEmbed.value = false;
    }
  }
}
