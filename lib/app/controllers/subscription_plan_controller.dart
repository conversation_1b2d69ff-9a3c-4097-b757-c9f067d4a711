import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/subscription_plan_repo.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/payment_request_status_model.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/subscription_history_model.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/subscription_plan_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class SubscriptionPlanController extends GetxController {
  final SubscriptionPlanRepository subscriptionPlanRepository;

  SubscriptionPlanController({required this.subscriptionPlanRepository}) {
    // onInit();
  }
  RxList<bool> expandedTiles = <bool>[].obs;

  final Rx<SubscriptionHistoryModel?> subscriptionHistoryData =
      Rx<SubscriptionHistoryModel?>(null);
  final Rx<ApiState> subscriptionHistoryDataState = ApiState.initial().obs;
  final RxBool isLoadingsubscriptionHistoryData = false.obs;

  final Rx<SubscriptionPlanModel?> subscriptionPlanData =
      Rx<SubscriptionPlanModel?>(null);
  final Rx<ApiState> subscriptionPlanDataState = ApiState.initial().obs;
  final RxBool isLoadingsubscriptionPlanData = false.obs;

  final Rx<PaymentRequestStatusModel?> paymentRequestStatusData =
      Rx<PaymentRequestStatusModel?>(null);
  final Rx<ApiState> paymentRequestStatusState = ApiState.initial().obs;
  final RxBool isLoadingPaymentRequestStatusData = false.obs;

  Future<void> getSubscriptionHistoryData() async {
    try {
      isLoadingsubscriptionHistoryData.value = true;
      var response =
          await subscriptionPlanRepository.getSubscriptionHistoryRepository();

      if (response.success) {
        subscriptionHistoryData.value = response;
        subscriptionHistoryDataState.value = SuccessState(response);
        final plans = response.data;
        expandedTiles.value = List.generate(plans.length, (_) => false);
      } else {
        subscriptionHistoryDataState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      subscriptionHistoryDataState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingsubscriptionHistoryData.value = false;
    }
  }

  Future<void> getSubscriptionPlanData() async {
    try {
      isLoadingsubscriptionPlanData.value = true;
      var response =
          await subscriptionPlanRepository.getSubscriptionPlanRepository();

      if (response.success) {
        subscriptionPlanData.value = response;
        subscriptionPlanDataState.value = SuccessState(response);
        final plans = response.data;
        expandedTiles.value = List.generate(plans.length, (_) => false);
      } else {
        subscriptionPlanDataState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      subscriptionPlanDataState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingsubscriptionPlanData.value = false;
    }
  }

  Future<void> getPaymentRequestStatusData() async {
    try {
      isLoadingPaymentRequestStatusData.value = true;
      var response =
          await subscriptionPlanRepository
              .getPaymentRequestStatusModelRepository();

      if (response.success) {
        paymentRequestStatusData.value = response;
        paymentRequestStatusState.value = SuccessState(response);
      } else if (!response.success) {
        paymentRequestStatusState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      paymentRequestStatusState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingPaymentRequestStatusData.value = false;
    }
  }
}
