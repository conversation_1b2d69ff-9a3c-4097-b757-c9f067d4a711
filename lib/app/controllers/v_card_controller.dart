import 'dart:io';
import 'package:collection/collection.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:file_picker/file_picker.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:media_scanner/media_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:v_card/app/data/apis/repository/v_card_repo.dart';
import 'package:v_card/app/data/model/v_card/create_vcard_model.dart';
import 'package:v_card/app/data/model/v_card/single_vcard_model.dart';
import 'package:v_card/app/data/model/v_card/update_template_model.dart';
import 'package:v_card/app/data/model/v_card/v_card_model.dart';
import 'package:v_card/app/data/model/v_card/v_card_only_list_model.dart';
import 'package:v_card/app/data/model/v_card/v_card_qr_code_model.dart';
import 'package:v_card/app/data/model/v_card/vcard_basic_detail_moel.dart';
import 'package:v_card/app/data/model/v_card/vcard_delete_model.dart';
import 'package:v_card/app/data/model/v_card/vcard_tepmlate_model.dart';
import 'package:v_card/app/data/model/v_card/vcard_update_tepmlate_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class VcardController extends GetxController {
  final VcardRepository vcardRepository;

  VcardController({required this.vcardRepository}) {
    onInit();
  }

  final isConnected = true.obs;
  final isShowingToast = false.obs;
  final hasInitialData = false.obs;

  final Rx<VCardModel?> vCardList = Rx<VCardModel?>(null);
  final Rx<ApiState> vCardListState = ApiState.initial().obs;
  final Rx<SingleVCardModel?> vCardByIdData = Rx<SingleVCardModel?>(null);
  final Rx<VCardDeleteModel?> vCardDeleteResponse = Rx<VCardDeleteModel?>(null);
  final Rx<VCardQrCodeModel?> vCardQrCodeData = Rx<VCardQrCodeModel?>(null);
  final isDownloadingQr = false.obs;

  final Rx<ApiState> vCardByIdState = ApiState.initial().obs;
  final Rx<ApiState> vCardDeleteState = ApiState.initial().obs;
  final Rx<ApiState> vCardQrCodeState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;
  final RxBool isLoadingVcardById = false.obs;
  final RxBool isLoadingVcardDelete = false.obs;
  final RxBool isLoadingVcardQrCode = false.obs;

  final Rx<CreateVCardModel?> createDataresponse = Rx<CreateVCardModel?>(null);
  final RxBool isLoadingCreateVcard = false.obs;
  final Rx<ApiState> createVcardState = ApiState.initial().obs;

  final TextEditingController nameController = TextEditingController();
  final TextEditingController urlAliasController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController occupationController = TextEditingController();

  // For template update
  final Rx<UpdateVCardTemplateModel?> updateTemplateResponse =
      Rx<UpdateVCardTemplateModel?>(null);
  final Rx<ApiState> updateTemplateState = ApiState.initial().obs;
  final RxBool isLoadingUpdateTemplate = false.obs;

  // For basic details
  final Rx<VCardBasicDetailsModel?> vCardBasicDetails =
      Rx<VCardBasicDetailsModel?>(null);
  final Rx<ApiState> vCardBasicDetailsState = ApiState.initial().obs;
  final RxBool isLoadingBasicDetails = false.obs;

  // For templates
  final Rx<VCardTemplatesModel?> vCardTemplates = Rx<VCardTemplatesModel?>(
    null,
  );
  final Rx<VCardUpdateTemplatesModel?> vCardUpdateTemplates =
      Rx<VCardUpdateTemplatesModel?>(null);
  final Rx<ApiState> vCardTemplatesState = ApiState.initial().obs;
  final RxBool isLoadingTemplates = false.obs;
  final RxBool isLoadingUpdateBasicdetail = false.obs;
  final RxBool isLoadingUpdateTemplates = false.obs;
  final selectedTemplate = Rxn<VCardTemplate>();
  final RxBool isActive = false.obs;
  final RxBool isSelected = false.obs;

  late RxBool isProcessingPWAIcon;
  late RxBool isProcessingFaviconPWAIcon;
  late RxBool isProcessingCoverPWAIcon;

  final addBasicDetailUrlAliasController = TextEditingController();
  final addBasicDetailNameController = TextEditingController();
  final addBasicDetailFirstNameController = TextEditingController();
  final addBasicDetailLastNameController = TextEditingController();
  final addBasicDetailEmailController = TextEditingController();
  final addBasicDetailPhoneController = TextEditingController();
  final addBasicDetailDefaultLanguageController = TextEditingController();
  final addBasicDetailDescriptionController = TextEditingController();
  final addBasicDetailOccupationController = TextEditingController();
  final addBasicDetailRegionCodeController = '91'.obs;
  final addBasicDetailTemplateIdController = TextEditingController();
  var isLanguageEnabled = false.obs;
  var isStatusEnabled = false.obs;
  final alternativeEmailController = TextEditingController();
  final alternativePhoneController = TextEditingController();
  final alternativeRegionCodeController = '91'.obs;
  final locationUrlController = TextEditingController();
  final locationEmbededController = TextEditingController();
  final locationController = TextEditingController();
  final companyController = TextEditingController();
  final jobTitleController = TextEditingController();
  final madeByController = TextEditingController();
  final madeByUrlController = TextEditingController();
  final dobController = TextEditingController();
  final locationType = '0'.obs;
  final isLocationTypeExpanded = false.obs;

  final coverImageType = '0'.obs;
  final isCoverImageTypeExpanded = false.obs;

  final showQrCode = true.obs;
  final enableDownloadQrCode = true.obs;
  RxDouble qrCodeSize = 100.0.obs;
  double minQrSize = 100.0;
  double maxQrSize = 500.0;
  final double qrSizeInterval = 100.0;
  final Rx<String> coverMediaType = Rx<String>('0');

  final Rx<File?> profileImageFile = Rx<File?>(null);
  final Rx<File?> coverImageFile = Rx<File?>(null);
  final Rx<File?> faviconFile = Rx<File?>(null);
  final TextEditingController coverYoutubeUrlController =
      TextEditingController();

  final whatsappShare = false.obs;
  final enableContact = true.obs;
  final enableAffiliation = false.obs;
  final enableEnquiryForm = false.obs;
  final hideStickybar = false.obs;

  final RxBool addBasicDetailLanguageEnabled = false.obs;

  // Add search controller
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  final RxBool isSearchActive = false.obs;
  final RxString searchText = ''.obs;
  final GlobalKey<FormState> searchBarKey = GlobalKey();

  void closeSearch() {
    isSearchActive.value = false;
    searchText.value = '';
    searchController.clear();
    searchFocusNode.unfocus();
  }

  void toggleSearchMode() {
    isSearchActive.value = !isSearchActive.value;
    if (isSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        searchFocusNode.requestFocus();
      });
    } else {
      searchController.clear();
      searchText.value = '';
    }
  }

  var registerCountryflag = ''.obs;

  Future checkNetworkAndLoad() async {
    try {
      isLoading.value = true;
      final networkHelper = NetworkHelper();
      isConnected.value = await networkHelper.hasNetworkConnection();

      // Set up connectivity listener
      Connectivity().onConnectivityChanged.listen((_) async {
        isConnected.value = await networkHelper.hasNetworkConnection();
        if (isConnected.value) {
          await getVcardList();
        }
      });

      if (isConnected.value) {
        await getVcardList();
      }
    } catch (e) {
      debugPrint('Error in checkNetworkAndLoad: $e');
    } finally {
      isLoading.value = false;
      hasInitialData.value = true;
    }
  }
  // Future<void> checkNetworkAndLoad() async {
  //   try {
  //     final networkHelper = NetworkHelper();
  //     isConnected.value = await networkHelper.hasNetworkConnection();

  //     Connectivity().onConnectivityChanged.listen((_) async {
  //       isConnected.value = await networkHelper.hasNetworkConnection();
  //       if (isConnected.value) await getVcardList();
  //     });

  //     if (isConnected.value) await getVcardList();
  //   } finally {
  //     hasInitialData.value = true;
  //   }
  // }

  Future<void> getVcardList() async {
    try {
      isLoading.value = true;
      vCardListState.value = ApiState.initial();
      var response =
          await (getIt<SharedPreferences>().getRole.toString().toLowerCase() == 'admin'
              ? vcardRepository.getadminVcardListRepository()
              : vcardRepository.getsuperAdminVcardListRepository());

      if (response.success) {
        vCardList.value = response;
        vCardListState.value = SuccessState(response);
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> getVcardById({required String id}) async {
    try {
      isLoadingVcardById.value = true;

      var response = await vcardRepository.getVcardByIdRepository(id: id);

      if (response.success) {
        vCardByIdData.value = response;
        vCardByIdState.value = SuccessState(response);
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingVcardById.value = false;
    }
  }

  Future<void> getVcardQrCodeLink({required String id}) async {
    try {
      isLoadingVcardQrCode.value = true;

      var response = await vcardRepository.getVcardQrCodeRepository(id: id);

      if (response.success) {
        vCardQrCodeData.value = response;
        vCardQrCodeState.value = SuccessState(response);
      } else if (!response.success) {
        vCardQrCodeState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardQrCodeState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingVcardQrCode.value = false;
    }
  }

  Future<void> deleteVcardById({required int id}) async {
    try {
      isLoadingVcardDelete.value = true;
      String? authToken = getIt<SharedPreferences>().getToken;
      var data = await vcardRepository.deleteVcardByIdRepository(
        id: id,
        authToken: authToken!,
      );

      if (data.success) {
        vCardDeleteResponse.value = data;
        vCardDeleteState.value = SuccessState(data);
        getVcardList();
        NavigationService.navigateBack();
      }
    } catch (e) {
      vCardDeleteState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingVcardDelete.value = false;
    }
  }

  // In your VcardController
  final selectedCoverType = 0.obs; // 0 = image, 1 = video, 2 = youtube
  final isExpanded = false.obs;
  final youtubeLinkController = TextEditingController();
  Future<void> createAdminVcard({
    required String urlAlias,
    required String name,
    required String profileImg,
    required String coverImg,
    required String description,
    required String occupation,
    required String faviconImg,
    int coverType = 0, // Default to image (0)
    String? youtubeLink,
  }) async {
    try {
      // Prepare form data
      Map<String, dynamic> formData = {
        "url_alias": urlAlias,
        "name": name,
        "description": description,
        "occupation": occupation,
        "cover_type": coverType.toString(), // Convert to string as API expects
      };

      // Add YouTube link if cover type is 2
      if (coverType == 2) {
        if (youtubeLink == null || youtubeLink.isEmpty) {
          throw Exception(
            "YouTube link is required when cover type is YouTube URL",
          );
        }
        formData["youtube_link"] = youtubeLink;
      }

      final fileFields = {
        "profile_img": [profileImg],
        "favicon_url": [faviconImg],
      };

      // Only include cover image if cover type is image (0)
      if (coverType == 0) {
        fileFields["cover_img"] = [coverImg];
      } else if (coverType == 1) {
        // For video cover type
        fileFields["cover_video"] = [coverImg];
      }

      isLoadingCreateVcard.value = true;
      var response = await vcardRepository.createAdminVcardRepository(
        data: formData,
        fileFields: fileFields,
      );

      if (response.success == true) {
        createDataresponse.value = response;
        createVcardState.value = SuccessState(response);

        int vcardId = response.vcardId;

        nameController.clear();
        urlAliasController.clear();
        descriptionController.clear();
        occupationController.clear();

        isCardCreated = false;

        NavigationService.navigateWithSlideAnimation(
          AppRoutes.vcardAddBasicDetailPage,
          arguments: {'vcardId': vcardId.toString()},
        );

        profileImageFile.value = null;
        coverImageFile.value = null;
        faviconFile.value = null;
        selectedCoverType.value = 0;
        isExpanded.value = false;
        youtubeLinkController.clear();
        nameController.clear();
        urlAliasController.clear();
        descriptionController.clear();
        occupationController.clear();
        isLoadingCreateVcard.value = false;
      } else {
        createVcardState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      createVcardState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError("VCard Creation Failed", e.toString()),
      );

      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text(e.toString()),
        autoCloseDuration: const Duration(seconds: 3),
      );
    } finally {
      isLoadingCreateVcard.value = false;
    }
  }

  Future<void> updateVCardBasicdetail({
    required String vcardId,
    required String profilePath,
    required String coverPath,
    required String faviconPath,
  }) async {
    try {
      isLoadingUpdateBasicdetail.value = true;

      final processedLocationUrl = processProductUrl(
        locationController.text.trim().toLowerCase(),
      );

      final processedMadebyonUrl = processProductUrl(
        madeByUrlController.text.trim().toLowerCase(),
      );

      Map<String, dynamic> formData = {
        "url_alias": addBasicDetailUrlAliasController.text.trim(),
        "name": addBasicDetailNameController.text.trim(),
        "first_name": addBasicDetailFirstNameController.text.trim(),
        "last_name": addBasicDetailLastNameController.text.trim(),
        "email": addBasicDetailEmailController.text.trim(),
        "phone": addBasicDetailPhoneController.text.trim(),
        "region_code":
            addBasicDetailRegionCodeController.value.isNotEmpty
                ? addBasicDetailRegionCodeController.value
                : '91',
        "default_language": addBasicDetailDefaultLanguageController.text.trim(),
        "description": addBasicDetailDescriptionController.text.trim(),
        "occupation": addBasicDetailOccupationController.text.trim(),
        "language_enable": addBasicDetailLanguageEnabled.value ? "1" : "0",

        // New fields
        "alternative_email": alternativeEmailController.text.trim(),
        "alternative_phone": alternativePhoneController.text.trim(),
        "alternative_region_code": alternativeRegionCodeController.value,
        "location": locationUrlController.text.trim(),
        "location_url": processedLocationUrl,
        "location_embed_tag": locationEmbededController.text.trim(),
        "company": companyController.text.trim(),
        "job_title": jobTitleController.text.trim(),
        "made_by": madeByController.text.trim(),
        "made_by_url": processedMadebyonUrl,
        "dob": dobController.text.trim(),
        "location_type": locationType.value,
        "cover_image_type": coverImageType.value,
        "show_qr_code": showQrCode.value ? "1" : "0",
        "enable_download_qr_code": enableDownloadQrCode.value ? "1" : "0",
        "qr_code_download_size": qrCodeSize.value.toInt(),
        "whatsapp_share": whatsappShare.value ? "1" : "0",
        "enable_contact": enableContact.value ? "1" : "0",
        "enable_affiliation": enableAffiliation.value ? "1" : "0",
        "enable_enquiry_form": enableEnquiryForm.value ? "1" : "0",
        "hide_stickybar": hideStickybar.value ? "1" : "0",
        "cover_type": selectedCoverType.value.toString(),
      };

      final fileFields = {
        "profile_img": [profilePath],
        "favicon_url": [faviconPath],
      };

      if (selectedCoverType.value == 0) {
        fileFields['cover_img'] = [coverPath];
      } else if (selectedCoverType.value == 1) {
        fileFields['cover_video'] = [coverPath];
      }

      // YouTube link for cover type 2
      if (selectedCoverType.value == 2) {
        formData['youtube_link'] = youtubeLinkController.text.trim();
      }

      final response = await vcardRepository.updateVCardBasicdetailrepository(
        vcardId: vcardId,
        data: formData,
        fileFields: fileFields,
      );

      if (response.success) {
        updateTemplateResponse.value = response;

        if (isCardCreated) {
          NavigationService.navigateBack();
          // await getVcardList();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.vCardTemplatesPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }

        updateTemplateState.value = SuccessState(response);
      } else if (!response.success) {
        updateTemplateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      updateTemplateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError("Update Failed", e.toString()),
      );
    } finally {
      isLoadingUpdateBasicdetail.value = false;
    }
  }

  Future<VCardBasicDetailsModel?> getVCardBasicDetails({
    required String vcardId,
  }) async {
    try {
      isLoadingBasicDetails.value = true;
      final response = await vcardRepository.getVCardBasicDetails(
        vcardId: vcardId,
      );

      if (response.success) {
        vCardBasicDetails.value = response;
        vCardBasicDetailsState.value = SuccessState(response);

        // Set profile and favicon images
        // basicDetailProfileImageFile.value =
        //     vCardBasicDetails.value?.data.profileUrl != null
        //         ? File(vCardBasicDetails.value!.data.profileUrl)
        //         : null;

        // basicDetailCoverImageFile.value =
        //     vCardBasicDetails.value?.data.coverUrl != null
        //         ? File(vCardBasicDetails.value!.data.coverUrl)
        //         : null;

        // basicDetailfaviconImageFile.value =
        //     vCardBasicDetails.value?.data.faviconUrl != null
        //         ? File(vCardBasicDetails.value!.data.faviconUrl)
        //         : null;

        // Set all text fields
        addBasicDetailUrlAliasController.text =
            vCardBasicDetails.value?.data.urlAlias ?? '';
        String cleanDescription = stripHtmlTags(response.data.description);
        addBasicDetailUrlAliasController.text =
            vCardBasicDetails.value?.data.urlAlias ?? '';
        addBasicDetailNameController.text = response.data.name;
        addBasicDetailFirstNameController.text = response.data.firstName;
        addBasicDetailLastNameController.text = response.data.lastName;
        addBasicDetailEmailController.text = response.data.email;
        addBasicDetailPhoneController.text = response.data.phone;
        addBasicDetailRegionCodeController.value =
            response.data.regionCode.isNotEmpty
                ? response.data.regionCode
                : '91';
        addBasicDetailDefaultLanguageController.text =
            response.data.defaultLanguage.isEmpty
                ? "English"
                : response.data.defaultLanguage;
        addBasicDetailDescriptionController.text = cleanDescription;
        addBasicDetailOccupationController.text = response.data.occupation;
        addBasicDetailLanguageEnabled.value =
            response.data.languageEnabled == "1";
        // addBasicDetailLanguageEnabled.value = response.data.languageEnabled == 1;
        locationEmbededController.text = response.data.locationEmbedTag ?? '';
        alternativeEmailController.text = response.data.alternativeEmail ?? '';
        alternativePhoneController.text = response.data.alternativePhone ?? '';
        alternativeRegionCodeController.value =
            response.data.alternativeRegionCode.isNotEmpty
                ? response.data.alternativeRegionCode
                : '91';
        locationUrlController.text = response.data.location;
        locationController.text = response.data.locationUrl;
        companyController.text = response.data.company;
        jobTitleController.text = response.data.jobTitle;
        madeByController.text = response.data.madeBy ?? '';
        madeByUrlController.text = response.data.madeByUrl ?? '';
        dobController.text = response.data.dob;
        locationType.value = response.data.locationType == "1" ? "1" : "0";
        coverImageType.value = response.data.coverImageType == "1" ? "1" : "0";
        showQrCode.value = response.data.showQrCode == 1;
        enableDownloadQrCode.value = response.data.enableDownloadQrCode == 1;
        qrCodeSize.value = response.data.qrCodeDownloadSize.toDouble();

        whatsappShare.value = response.data.whatsappShare == 1;
        enableContact.value = response.data.enableContact == 1;
        enableAffiliation.value = response.data.enableAffiliation == 1;
        enableEnquiryForm.value = response.data.enableEnquiryForm == 1;
        hideStickybar.value = response.data.hideStickybar == 1;

        // Handle cover type
        if (response.data.coverType == '0') {
          // Image cover
          selectedCoverType.value = 0;
          // coverImageFile.value =
          //     vCardBasicDetails.value?.data.coverUrl != null
          //         ? File(vCardBasicDetails.value!.data.coverUrl)
          //         : null;
        } else if (response.data.coverType == '1') {
          // Video cover
          selectedCoverType.value = 1;

          // Check if coverUrl contains a video file (ends with video extension)
          if (vCardBasicDetails.value?.data.coverUrl != null &&
              [
                '.mp4',
                // '.mov', '.avi', '.mkv',
              ].any(
                (ext) => vCardBasicDetails.value!.data.coverUrl
                    .toLowerCase()
                    .endsWith(ext),
              )) {
            // basicDetailCoverImageFile.value = File(vCardBasicDetails.value!.data.coverUrl);
          } else {
            // If it's not a direct file, it might be a video link
            youtubeLinkController.text =
                vCardBasicDetails.value?.data.coverUrl ?? '';
          }
        } else if (response.data.coverType == '2') {
          // YouTube cover
          selectedCoverType.value = 2;
          youtubeLinkController.text = response.data.youtubeLink ?? '';
        }

        return vCardBasicDetails.value;
      }

      update();
    } catch (e) {
      vCardBasicDetailsState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError("Fetch Failed", e.toString()),
      );
    } finally {
      isLoadingBasicDetails.value = false;
    }
    return null;
  }

  Future<void> getVCardTemplates({required String vcardId}) async {
    try {
      isLoadingTemplates.value = true;
      final response = await vcardRepository.getVCardTemplates(
        vcardId: vcardId,
      );
      vCardTemplates.value = response;
      vCardTemplatesState.value = SuccessState(response);

      if (response.templates.isNotEmpty) {
        selectedTemplate.value = response.templates.firstWhereOrNull(
          (template) => template.isSelected,
        );
      }
    } catch (e) {
      vCardTemplatesState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError("Fetch Failed", e.toString()),
      );
    } finally {
      isLoadingTemplates.value = false;
    }
  }

  Future<void> updateVCardTemplate(String vcardId) async {
    isLoadingUpdateTemplates.value = true;

    if (selectedTemplate.value == null) return;

    try {
      var response = await vcardRepository.updateVCardTemplate(
        vcardId: vcardId,
        templateId: selectedTemplate.value!.id.toString(),
        isActive: isActive.value ? "1" : "0",
      );

      if (response.success) {
        vCardUpdateTemplates.value = response;
        vCardTemplatesState.value = SuccessState(response);

        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.vcardBusinessHoursPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else if (!response.success) {
        vCardTemplatesState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardTemplatesState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingUpdateTemplates.value = false;
    }
  }

  String stripHtmlTags(String htmlText) {
    return htmlText.replaceAll(RegExp(r'<[^>]*>'), '');
  }

  Future<void> pickCoverVideo() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.single.path != null) {
        coverImageFile.value = File(result.files.single.path!);
        // basicDetailCoverImageFile.value = File(result.files.single.path!);
      }
    } catch (e) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text("Failed to pick video: ${e.toString()}"),
        autoCloseDuration: const Duration(seconds: 3),
      );
    }
  }

  void reset() {
    // Reset main data states
    vCardList.value = null;
    vCardByIdData.value = null;
    vCardDeleteResponse.value = null;
    vCardQrCodeData.value = null;
    createDataresponse.value = null;
    updateTemplateResponse.value = null;
    vCardBasicDetails.value = null;
    vCardTemplates.value = null;
    vCardUpdateTemplates.value = null;

    // Reset all API states
    vCardListState.value = ApiState.initial();
    vCardByIdState.value = ApiState.initial();
    vCardDeleteState.value = ApiState.initial();
    vCardQrCodeState.value = ApiState.initial();
    createVcardState.value = ApiState.initial();
    updateTemplateState.value = ApiState.initial();
    vCardBasicDetailsState.value = ApiState.initial();
    vCardTemplatesState.value = ApiState.initial();

    // Reset loading states
    isLoading.value = false;
    isLoadingVcardById.value = false;
    isLoadingVcardDelete.value = false;
    isLoadingVcardQrCode.value = false;
    isLoadingCreateVcard.value = false;
    isLoadingUpdateTemplate.value = false;
    isLoadingBasicDetails.value = false;
    isLoadingTemplates.value = false;
    isLoadingUpdateBasicdetail.value = false;
    isLoadingUpdateTemplates.value = false;

    // Clear form controllers
    nameController.clear();
    urlAliasController.clear();
    descriptionController.clear();
    occupationController.clear();
    searchController.clear();
    addBasicDetailUrlAliasController.clear();
    addBasicDetailNameController.clear();
    addBasicDetailFirstNameController.clear();
    addBasicDetailLastNameController.clear();
    addBasicDetailEmailController.clear();
    addBasicDetailPhoneController.clear();
    addBasicDetailDefaultLanguageController.clear();
    addBasicDetailDescriptionController.clear();
    addBasicDetailOccupationController.clear();
    alternativeEmailController.clear();
    alternativePhoneController.clear();
    locationUrlController.clear();
    locationEmbededController.clear();
    locationController.clear();
    companyController.clear();
    jobTitleController.clear();
    madeByController.clear();
    madeByUrlController.clear();
    dobController.clear();
    youtubeLinkController.clear();

    // Reset dropdowns and selections
    addBasicDetailRegionCodeController.value = '91';
    alternativeRegionCodeController.value = '91';
    locationType.value = '0';
    coverImageType.value = '0';
    selectedCoverType.value = 0;
    selectedTemplate.value = null;
    isActive.value = false;
    isSelected.value = false;

    // Reset file selections
    profileImageFile.value = null;
    coverImageFile.value = null;
    faviconFile.value = null;

    // Reset toggle states
    isLanguageEnabled.value = false;
    isStatusEnabled.value = false;
    showQrCode.value = true;
    enableDownloadQrCode.value = true;
    whatsappShare.value = false;
    enableContact.value = true;
    enableAffiliation.value = false;
    enableEnquiryForm.value = false;
    hideStickybar.value = false;
    addBasicDetailLanguageEnabled.value = false;

    // Reset UI states
    qrCodeSize.value = 100.0;
    isExpanded.value = false;
    coverMediaType.value = '0';
    isCardCreated = false;
    registerCountryflag.value = '';

    // Reset search
    closeSearch();
    searchFocusNode.unfocus();
    isSearchActive.value = false;
    searchText.value = '';

    // Reset network state
    isConnected.value = true;
    hasInitialData.value = false;
  } // QR Download related properties

  // final RxBool isDownloadingQr = false.obs;
  final RxBool hasStoragePermission = false.obs;

  // ... your existing properties and methods

  /// Check storage permission status for Android 14+
  Future<void> checkStoragePermission() async {
    try {
      PermissionStatus status;

      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkInt = androidInfo.version.sdkInt;

        if (sdkInt >= 34) {
          // Android 14+ (API 34) - Check multiple permissions
          final photoStatus = await Permission.photos.status;

          // For downloads, we primarily need photos permission
          status =
              photoStatus.isGranted ? PermissionStatus.granted : photoStatus;
        } else if (sdkInt >= 33) {
          // Android 13 (API 33) - use mediaLibrary permission
          status = await Permission.mediaLibrary.status;
        } else if (sdkInt >= 30) {
          // Android 11-12 (API 30-32) - use manageExternalStorage
          status = await Permission.manageExternalStorage.status;
        } else {
          // Android 10 and below - use storage permission
          status = await Permission.storage.status;
        }
      } else {
        // iOS - use photos permission
        status = await Permission.photos.status;
      }

      hasStoragePermission.value = status.isGranted;
      debugPrint('Storage permission status: ${status.toString()}');
    } catch (e) {
      hasStoragePermission.value = false;
      debugPrint('Error checking storage permission: $e');
    }
  }

  /// Request storage permission for Android 14+
  Future<bool> requestStoragePermission() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkInt = androidInfo.version.sdkInt;

        debugPrint('Android SDK: $sdkInt');

        if (sdkInt >= 34) {
          // Android 14+ (API 34) - Request photos permission
          final photoStatus = await Permission.photos.request();

          if (photoStatus.isGranted) {
            hasStoragePermission.value = true;
            return true;
          } else if (photoStatus.isPermanentlyDenied) {
            hasStoragePermission.value = false;
            return false;
          } else {
            // If photos permission is denied, try mediaLibrary as fallback
            final mediaStatus = await Permission.mediaLibrary.request();
            hasStoragePermission.value = mediaStatus.isGranted;
            return mediaStatus.isGranted;
          }
        } else if (sdkInt >= 33) {
          // Android 13 (API 33) - use mediaLibrary permission
          final status = await Permission.mediaLibrary.request();
          hasStoragePermission.value = status.isGranted;
          return status.isGranted;
        } else if (sdkInt >= 30) {
          // Android 11-12 (API 30-32) - use manageExternalStorage
          final status = await Permission.manageExternalStorage.request();
          hasStoragePermission.value = status.isGranted;
          return status.isGranted;
        } else {
          // Android 10 and below - use storage permission
          final status = await Permission.storage.request();
          hasStoragePermission.value = status.isGranted;
          return status.isGranted;
        }
      } else {
        // iOS - use photos permission
        final status = await Permission.photos.request();
        hasStoragePermission.value = status.isGranted;
        return status.isGranted;
      }
    } catch (e) {
      hasStoragePermission.value = false;
      debugPrint('Error requesting storage permission: $e');
      return false;
    }
  }

  /// Download QR code with proper Android 14+ handling
  Future<String> downloadQrCodeFile(String url, String filename) async {
    try {
      isDownloadingQr.value = true;

      // Check permission first
      await checkStoragePermission();

      if (!hasStoragePermission.value) {
        final granted = await requestStoragePermission();
        if (!granted) {
          throw Exception(
            'Storage permission denied. Please grant permission to save QR codes.',
          );
        }
      }

      // Download the QR code
      final result = await downloadQrcode(url, filename);

      // Scan the downloaded file to make it appear in gallery (Android)
      if (Platform.isAndroid && result.isNotEmpty) {
        await _scanDownloadedFile(result);
      }

      return result;
    } catch (e) {
      debugPrint('Error downloading QR code: $e');
      rethrow;
    } finally {
      isDownloadingQr.value = false;
    }
  }

  /// Scan downloaded file to make it appear in gallery
  Future<void> _scanDownloadedFile(String filePath) async {
    try {
      // Method 1: Using media_scanner package
      await MediaScanner.loadMedia(path: filePath);
      debugPrint('Media scanner completed for: $filePath');
    } catch (e) {
      debugPrint('Error scanning media file: $e');

      // Method 2: Fallback - trigger media scan using intent
      try {
        final file = File(filePath);
        if (await file.exists()) {
          // This will trigger Android's media scanner
          await Process.run('am', [
            'broadcast',
            '-a',
            'android.intent.action.MEDIA_SCANNER_SCAN_FILE',
            '-d',
            'file://$filePath',
          ]);
        }
      } catch (intentError) {
        debugPrint('Error with intent media scan: $intentError');
      }
    }
  }

  /// Get appropriate download directory for Android 14+
  String getDownloadDirectory() {
    if (Platform.isAndroid) {
      // For Android 14+, use public Downloads directory
      return '/storage/emulated/0/Download';
    } else {
      // For iOS, use documents directory
      return Directory.systemTemp.path;
    }
  }

  /// Check if we need to show permission rationale
  Future<bool> shouldShowPermissionRationale() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 34) {
        final photoStatus = await Permission.photos.status;
        return photoStatus.isDenied && !photoStatus.isPermanentlyDenied;
      } else if (sdkInt >= 33) {
        final mediaStatus = await Permission.mediaLibrary.status;
        return mediaStatus.isDenied && !mediaStatus.isPermanentlyDenied;
      }
    }
    return false;
  }

  final Rx<VcardOnlyListModel?> vcardOnlyList = Rx<VcardOnlyListModel?>(null);
  final Rx<ApiState> vcardOnlyListState = ApiState.initial().obs;

  Future<void> getOnlyVcardList() async {
    try {
      final response = await vcardRepository.getOnlyVcardList();

      if (response.success ?? false) {
        vcardOnlyList.value = response;
        vcardOnlyListState.value = SuccessState(response);
      } else {
        vcardOnlyListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      vcardOnlyListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    }
  }

  @override
  void onInit() {
    super.onInit();
    checkStoragePermission();
  }

  @override
  @i.disposeMethod
  void dispose() {
    // Dispose controllers
    nameController.dispose();
    urlAliasController.dispose();
    descriptionController.dispose();
    occupationController.dispose();
    searchController.dispose();
    addBasicDetailUrlAliasController.dispose();
    addBasicDetailNameController.dispose();
    addBasicDetailFirstNameController.dispose();
    addBasicDetailLastNameController.dispose();
    addBasicDetailEmailController.dispose();
    addBasicDetailPhoneController.dispose();
    addBasicDetailDefaultLanguageController.dispose();
    addBasicDetailDescriptionController.dispose();
    addBasicDetailOccupationController.dispose();
    alternativeEmailController.dispose();
    alternativePhoneController.dispose();
    locationUrlController.dispose();
    locationEmbededController.dispose();
    locationController.dispose();
    companyController.dispose();
    jobTitleController.dispose();
    madeByController.dispose();
    madeByUrlController.dispose();
    dobController.dispose();
    youtubeLinkController.dispose();

    // Dispose focus nodes
    searchFocusNode.dispose();

    super.dispose();
  }
}
