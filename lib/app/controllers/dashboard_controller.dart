import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/dashboard_repo.dart';
import 'package:v_card/app/data/model/dashboard/admin/admin_dashboard_model.dart';
import 'package:v_card/app/data/model/dashboard/admin/admin_today_appointment_model.dart';
import 'package:v_card/app/data/model/dashboard/admin/dashboard_chart_data_model.dart';
import 'package:v_card/app/data/model/dashboard/super_admin/super_Admin_chart_data_model.dart';
import 'package:v_card/app/data/model/dashboard/super_admin/super_admin_dashboard_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'dart:async';

@i.lazySingleton
@i.injectable
class DashboardController extends GetxController {
  final DashboardRepository dashboardRepository;

  DashboardController({required this.dashboardRepository}) {
    onInit();
  }

  // Add these in your controller class
  final selectedDateRangeOption = DateRangeOption.thisWeek.obs;
  final selectedStartDate = DateTime.now().obs;
  final selectedEndDate = DateTime.now().obs;

  String? token = getIt<SharedPreferences>().getToken;

  final RxBool isDrawerExpanded = true.obs;
  final RxMap<int, bool> expandedAppointments = <int, bool>{}.obs;

  final Rx<ApiState> adminDashboardState = ApiState.initial().obs;
  final Rx<ApiState> superAdminDashboardState = ApiState.initial().obs;
  final Rx<AdminDashboard?> adminDashboard = Rx<AdminDashboard?>(null);
  final Rx<SuperAdminDashboard?> superAdminDashboard = Rx<SuperAdminDashboard?>(
    null,
  );

  final Rx<DashboardChartDataModel?> adminDashboardChart =
      Rx<DashboardChartDataModel?>(null);
  final Rx<ApiState> adminDashboardChartState = ApiState.initial().obs;
  final Rx<SuperAdminChartDataModel?> superAdminDashboardChart =
      Rx<SuperAdminChartDataModel?>(null);
  final Rx<ApiState> superAdminDashboardChartState = ApiState.initial().obs;
  final Rx<AdminTodayAppointmentModel?> adminTodatAppointment =
      Rx<AdminTodayAppointmentModel?>(null);
  final Rx<ApiState> adminTodatAppointmentState = ApiState.initial().obs;
  final RxBool isLoadingAdmin = false.obs;
  final RxBool isLoadingAdmiChart = false.obs;
  final RxBool isLoadingAdmiChartByDate = false.obs;
  final RxBool isLoadingAdminTodayAppointment = false.obs;
  final RxBool isLoadingSuperAdminData = false.obs;
  final RxBool isLoadingSuperAdminChart = false.obs;

  final RxList<bool> isAppointmentExpanded = <bool>[].obs;

  // Add connectivity related variables
  final RxBool isConnected = true.obs;
  final RxBool hasInitialData = false.obs;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  void setInitialDates() {
    final now = DateTime.now();
    final thisWeekStart = getSunday(now);
    selectedStartDate.value = thisWeekStart;
    selectedEndDate.value = thisWeekStart.add(Duration(days: 6));
  }

  DateTime getSunday(DateTime date) {
    return date.subtract(Duration(days: date.weekday % 7));
  }

  void updateDateRangeOption() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    final thisWeekStart = getSunday(today);
    final thisWeekEnd = thisWeekStart.add(Duration(days: 6));

    if (selectedStartDate.value.isAtSameMomentAs(thisWeekStart) &&
        selectedEndDate.value.isAtSameMomentAs(thisWeekEnd)) {
      selectedDateRangeOption.value = DateRangeOption.thisWeek;
      return;
    }

    final lastWeekStart = thisWeekStart.subtract(Duration(days: 7));
    final lastWeekEnd = lastWeekStart.add(Duration(days: 6));

    if (selectedStartDate.value.isAtSameMomentAs(lastWeekStart) &&
        selectedEndDate.value.isAtSameMomentAs(lastWeekEnd)) {
      selectedDateRangeOption.value = DateRangeOption.lastWeek;
      return;
    }

    selectedDateRangeOption.value = DateRangeOption.custom;
  }

  void initializedExpanded() {
    isAppointmentExpanded.value =
        List.generate(
          adminTodatAppointment.value?.data.length ?? 0,
          (_) => false,
        ).obs;
    update();
  }

  void setAppointmentExpanded(int index, bool value) {
    isAppointmentExpanded[index] = value;
  }

  Future<bool> checkConnectivity() async {
    try {
      final networkHelper = NetworkHelper();
      isConnected.value = await networkHelper.hasNetworkConnection();

      return isConnected.value;
    } catch (e) {
      isConnected.value = false;
      return false;
    }
  }

  Future<void> checkNetworkAndLoad() async {
    try {
      final hasConnection = await checkConnectivity();
      if (hasConnection) {
        await fetchDashboardData();
      }
    } finally {
      hasInitialData.value = true;
    }
  }

  Future<void> fetchDashboardData() async {
    if (!isConnected.value) {
      return;
    }

    final role = getIt<SharedPreferences>().getRole.toString().toLowerCase();

    if (role == "admin") {
      await Future.wait([
        getAdminDashboard(),
        getAdminDashboardIncomeChart(),
        getDashboardAdminAppointment(),
      ]);
    } else {
      await Future.wait([
        getSuperAdminDashboard(),
        getSuperAdminDashboardIncomeChart(),
      ]);
    }
  }

  Future<void> getDashboardAdminAppointment() async {
    try {
      isLoadingAdminTodayAppointment.value = true;
      var response =
          await dashboardRepository.getDashboardAdminAppointmentRepository();

      if (response.success) {
        adminTodatAppointment.value = response;
        adminTodatAppointmentState.value = SuccessState(response);
      } else if (!response.success) {
        adminTodatAppointmentState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      adminTodatAppointmentState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingAdminTodayAppointment.value = false;
    }
  }

  Future<void> getAdminDashboard() async {
    try {
      isLoadingAdmin.value = true;
      var response = await dashboardRepository.getAdminDashboardRepository();

      if (response.success) {
        adminDashboard.value = response;
        adminDashboardState.value = SuccessState(response);
      } else if (!response.success) {
        adminDashboardState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      adminDashboardState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingAdmin.value = false;
    }
  }

  Future<void> getSuperAdminDashboard() async {
    try {
      isLoadingSuperAdminData.value = true;
      var response =
          await dashboardRepository.getSuperAdminDashboardRepository();

      if (response.success) {
        superAdminDashboard.value = response;
        superAdminDashboardState.value = SuccessState(response);
      } else if (!response.success) {
        superAdminDashboardState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      superAdminDashboardState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingSuperAdminData.value = false;
    }
  }

  Future<void> getAdminDashboardIncomeChart() async {
    try {
      isLoadingAdmiChart.value = true;
      var response =
          await dashboardRepository.getAdminDashboarIncomeChartRepository();

      if (response.success) {
        adminDashboardChart.value = response;
        adminDashboardChartState.value = SuccessState(response);
      } else if (!response.success) {
        adminDashboardChartState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      adminDashboardChartState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingAdmiChart.value = false;
    }
  }

  Future<void> getAdminDashboardIncomeChartByDate() async {
    final hasConnection = await checkConnectivity();
    if (!hasConnection) {
      return;
    }

    try {
      isLoadingAdmiChartByDate.value = true;
      var response = await dashboardRepository
          .getAdminDashboarIncomeChartByDateRepository(
            startDate: selectedStartDate.value.toIso8601String().split('T')[0],
            endDate: selectedEndDate.value.toIso8601String().split('T')[0],
          );

      if (response.success) {
        adminDashboardChart.value = response;
        update();
        adminDashboardChartState.value = SuccessState(response);
      } else if (!response.success) {
        adminDashboardChartState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      adminDashboardChartState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingAdmiChartByDate.value = false;
    }
  }

  Future<void> getSuperAdminDashboardIncomeChart() async {
    try {
      isLoadingSuperAdminChart.value = true;
      var response =
          await dashboardRepository
              .getSuperAdminDashboarIncomeChartRepository();

      if (response.success) {
        superAdminDashboardChart.value = response;
        superAdminDashboardChartState.value = SuccessState(response);
      } else if (!response.success) {
        superAdminDashboardChartState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      superAdminDashboardChartState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingSuperAdminChart.value = false;
    }
  }

  Future<void> getSuperAdminDashboardIncomeChartByDate() async {
    final hasConnection = await checkConnectivity();
    if (!hasConnection) {
      return;
    }

    try {
      isLoadingSuperAdminChart.value = true;
      var response = await dashboardRepository
          .getSuperAdminDashboarIncomeChartByDateRepository(
            token!,
            startDate: selectedStartDate.value.toIso8601String().split('T')[0],
            endDate: selectedEndDate.value.toIso8601String().split('T')[0],
          );

      if (response.success) {
        superAdminDashboardChart.value = response;
        superAdminDashboardChartState.value = SuccessState(response);
      } else if (!response.success) {
        superAdminDashboardChartState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      superAdminDashboardChartState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingSuperAdminChart.value = false;
    }
  }

  void reset() {
    // Reset dashboard data states
    adminDashboard.value = null;
    superAdminDashboard.value = null;
    adminDashboardChart.value = null;
    superAdminDashboardChart.value = null;
    adminTodatAppointment.value = null;

    // Reset all API states
    adminDashboardState.value = ApiState.initial();
    superAdminDashboardState.value = ApiState.initial();
    adminDashboardChartState.value = ApiState.initial();
    superAdminDashboardChartState.value = ApiState.initial();
    adminTodatAppointmentState.value = ApiState.initial();

    // Reset loading states
    isLoadingAdmin.value = false;
    isLoadingAdmiChart.value = false;
    isLoadingAdmiChartByDate.value = false;
    isLoadingAdminTodayAppointment.value = false;
    isLoadingSuperAdminData.value = false;
    isLoadingSuperAdminChart.value = false;

    // Reset date range selections
    selectedDateRangeOption.value = DateRangeOption.thisWeek;
    setInitialDates(); // Reset to default date range

    // Reset UI states
    isDrawerExpanded.value = true;
    expandedAppointments.clear();
    isAppointmentExpanded.value = <bool>[].obs;

    // Clear local token reference
    token = null;
  }

  @override
  void onInit() {
    super.onInit();
    final now = DateTime.now();
    ever(selectedStartDate, (_) => updateDateRangeOption());
    ever(selectedEndDate, (_) => updateDateRangeOption());
    setInitialDates();
    selectedEndDate.value = now;

    // Initialize connectivity listener
    _initConnectivity();
    // Initial data load
    checkNetworkAndLoad();
  }

  void _initConnectivity() async {
    // Initial check
    await checkConnectivity();

    // Set up the listener
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen((
      _,
    ) async {
      final wasConnected = isConnected.value;
      final hasConnection = await checkConnectivity();

      // Only fetch new data if we're transitioning from offline to online
      if (!wasConnected && hasConnection) {
        await fetchDashboardData();
      }
    });
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}

enum DateRangeOption { thisWeek, lastWeek, custom }
