import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/affiliation_repo.dart';
import 'package:v_card/app/data/model/affiliation/afiiliation_detail_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class AffiliationController extends GetxController {
  final AffiliationRepository affiliationRepository;

  AffiliationController({required this.affiliationRepository}) {
    onInit();
  }

  final RxBool isgetAffiliationLoading = false.obs;
  final RxBool isAffiliationListLoading = false.obs;
  final RxBool isWithdrawalListLoading = false.obs;
  final RxBool iscreateWithdrawalLoading = false.obs;
  final RxBool isshowWithdrawalLoading = false.obs;
  final Rx<AffiliationModel?> affiliationData = Rx<AffiliationModel?>(null);
  final Rx<AffiliationListModel?> affiliationListData =
      Rx<AffiliationListModel?>(null);
  final Rx<WithdrawalModel?> withdrawalListData = Rx<WithdrawalModel?>(null);
  final Rx<CreateWithdrawalResponseModel?> withdrawalRequestResponse =
      Rx<CreateWithdrawalResponseModel?>(null);
  final Rx<WithdrawalDetailModel?> withdrawalRequestData =
      Rx<WithdrawalDetailModel?>(null);
  final Rx<ApiState> affiliationState = ApiState.initial().obs;

  final amountController = TextEditingController();
  final emailController = TextEditingController();
  final bankDetailsController = TextEditingController();

  void refreshData() {
    getAffiliationUrl();
    getAffiliationList();
    getWithdrawalList();
  }

  Future<void> getAffiliationUrl() async {
    try {
      isgetAffiliationLoading.value = true;
      var response = await affiliationRepository.getAffiliationUrl();
      if (response.success) {
        affiliationData.value = response;
        affiliationState.value = SuccessState(response);
      } else {
        affiliationState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      affiliationState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isgetAffiliationLoading.value = false;
    }
  }

  Future<void> getAffiliationList() async {
    try {
      isAffiliationListLoading.value = true;
      var response = await affiliationRepository.getAffiliationList();
      if (response.success) {
        affiliationListData.value = response;
        affiliationState.value = SuccessState(response);
      } else {
        affiliationState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      affiliationState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isAffiliationListLoading.value = false;
    }
  }

  Future<void> getWithdrawalList() async {
    try {
      isWithdrawalListLoading.value = true;
      var response = await affiliationRepository.getWithdrawalList();
      if (response.success) {
        withdrawalListData.value = response;
        affiliationState.value = SuccessState(response);
      } else {
        affiliationState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      affiliationState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isWithdrawalListLoading.value = false;
    }
  }

  Future<void> createWithdrawalRequest() async {
    try {
      iscreateWithdrawalLoading.value = true;
      final data = {
        "amount": double.tryParse(amountController.text).toString(),
        "paypal_email": emailController.text.trim().toString(),
        "bank_details": bankDetailsController.text.trim().toString(),
      };

      var response = await affiliationRepository.createWithdrawalRequest(data);
      if (response.success) {
        withdrawalRequestResponse.value = response;
        affiliationState.value = SuccessState(response);
        getWithdrawalList();
        NavigationService.navigateBack();
      } else {
        affiliationState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      affiliationState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      iscreateWithdrawalLoading.value = false;
    }
  }

  Future<void> showWithdrawalRequest(String withdrawalId) async {
    try {
      isshowWithdrawalLoading.value = true;
      var response = await affiliationRepository.showWithdrawalRequest(
        withdrawalId,
      );
      if (response.success) {
        withdrawalRequestData.value = response;
        affiliationState.value = SuccessState(response);
      } else {
        affiliationState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      affiliationState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isshowWithdrawalLoading.value = false;
    }
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
