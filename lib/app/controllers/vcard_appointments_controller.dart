import 'package:get/get.dart';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/repository/vcard_appointments_repo.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@lazySingleton
@injectable
class AppointmentsVcardController extends GetxController {
  final AppointmentsVcardrepository repository;

  AppointmentsVcardController({required this.repository});

  final RxList<AppointmentTimeSlot> timeSlots = <AppointmentTimeSlot>[].obs;
  final RxList<int> selectedWeekDays = <int>[].obs;
  final RxBool isPaidAppointment = false.obs;
  final TextEditingController appointmentPriceController =
      TextEditingController();
  final Rx<ApiState> appointmentsState = ApiState.initial().obs;
  final RxBool isLoadingAppointments = false.obs;
  final RxBool isLoadingCreateAppointments = false.obs;

  final List<String> timeOptions = [
    '12:00 AM',
    '12:30 AM',
    '01:00 AM',
    '01:30 AM',
    '02:00 AM',
    '02:30 AM',
    '03:00 AM',
    '03:30 AM',
    '04:00 AM',
    '04:30 AM',
    '05:00 AM',
    '05:30 AM',
    '06:00 AM',
    '06:30 AM',
    '07:00 AM',
    '07:30 AM',
    '08:00 AM',
    '08:30 AM',
    '09:00 AM',
    '09:30 AM',
    '10:00 AM',
    '10:30 AM',
    '11:00 AM',
    '11:30 AM',
    '12:00 PM',
    '12:30 PM',
    '01:00 PM',
    '01:30 PM',
    '02:00 PM',
    '02:30 PM',
    '03:00 PM',
    '03:30 PM',
    '04:00 PM',
    '04:30 PM',
    '05:00 PM',
    '05:30 PM',
    '06:00 PM',
    '06:30 PM',
    '07:00 PM',
    '07:30 PM',
    '08:00 PM',
    '08:30 PM',
    '09:00 PM',
    '09:30 PM',
    '10:00 PM',
    '10:30 PM',
    '11:00 PM',
    '11:30 PM',
  ];

  List<String> getFilteredEndTimes(String startTime) {
    final startIndex = timeOptions.indexOf(startTime);
    if (startIndex == -1) return timeOptions;
    return timeOptions.sublist(startIndex + 1);
  }

  void toggleWeekDay(int day) {
    if (selectedWeekDays.contains(day)) {
      selectedWeekDays.remove(day);
      timeSlots.removeWhere((slot) => slot.dayOfWeek == day);
    } else {
      selectedWeekDays.add(day);
      addTimeSlotForDay(day);
    }
  }

  void addTimeSlotForDay(int day) {
    timeSlots.add(
      AppointmentTimeSlot(
        dayOfWeek: day,
        startTime: "09:00 AM",
        endTime: "09:30 AM",
      ),
    );
  }

  void addNewTimeSlot() {
    if (selectedWeekDays.isNotEmpty) {
      addTimeSlotForDay(selectedWeekDays.first);
    }
  }

  void removeTimeSlot(AppointmentTimeSlot slot) {
    timeSlots.remove(slot);
  }

  void updateTimeSlot({
    required int index,
    required int dayOfWeek,
    required String startTime,
    required String endTime,
  }) {
    final startIndex = timeOptions.indexOf(startTime);
    final endIndex = timeOptions.indexOf(endTime);

    // Ensure end time is after start time
    if (endIndex <= startIndex) {
      endTime = timeOptions[startIndex + 1];
    }

    final slot = timeSlots.firstWhere(
      (slot) =>
          slot.dayOfWeek == dayOfWeek &&
          slot.startTime == timeSlots[index].startTime &&
          slot.endTime == timeSlots[index].endTime,
    );

    final slotIndex = timeSlots.indexOf(slot);
    if (slotIndex != -1) {
      timeSlots[slotIndex].startTime = startTime;
      timeSlots[slotIndex].endTime = endTime;
      timeSlots.refresh();
    }
  }

  Future<void> getVcardAppointments({required String vcardId}) async {
    try {
      isLoadingAppointments.value = true;

      timeSlots.clear();
      selectedWeekDays.clear();

      final response = await repository.getVcardAppointments(vcardId: vcardId);

      if (response.success) {
        final data = response.data;

        isPaidAppointment.value = data['is_paid'] == 1;
        if (data['price'] != null) {
          appointmentPriceController.text = data['price'].toString();
        }

        List<dynamic> slots = [];
        data.forEach((key, value) {
          if (key != 'is_paid' && key != 'price') {
            slots.add(value);
          }
        });

        for (var slot in slots) {
          final dayOfWeek = slot['day_of_week'];

          if (!selectedWeekDays.contains(dayOfWeek)) {
            selectedWeekDays.add(dayOfWeek);
          }

          timeSlots.add(
            AppointmentTimeSlot(
              id: slot['id'],
              dayOfWeek: dayOfWeek,
              startTime: slot['start_time'],
              endTime: slot['end_time'],
            ),
          );
        }

        appointmentsState.value = SuccessState(response);
      } else {
        appointmentsState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            "Fetch Failed",
            response.message ?? "Failed to fetch appointment data",
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingAppointments.value = false;
    }
  }

  Future<void> saveVcardAppointments({required String vcardId}) async {
    try {
      isLoadingCreateAppointments.value = true;

      Map<String, List<String>> startTimes = {};
      Map<String, List<String>> endTimes = {};

      for (int i = 1; i <= 7; i++) {
        startTimes[i.toString()] = [];
        endTimes[i.toString()] = [];
      }

      for (var slot in timeSlots) {
        final day = slot.dayOfWeek.toString();
        startTimes[day]!.add(slot.startTime);
        endTimes[day]!.add(slot.endTime);
      }

      final Map<String, dynamic> requestData = {
        "vcard_id": int.parse(vcardId),
        "checked_week_days": selectedWeekDays,
        "startTimes": startTimes,
        "endTimes": endTimes,
        "is_paid": isPaidAppointment.value ? "1" : "0",
        "price":
            isPaidAppointment.value ? appointmentPriceController.text : null,
      };

      final response = await repository.saveVcardAppointments(
        data: requestData,
      );

      if (response.success) {
        appointmentsState.value = SuccessState(response);

        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.socialConnectPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        appointmentsState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            "Save Failed",
            response.message ?? "Failed to save appointment data",
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingCreateAppointments.value = false;
    }
  }

  void resetController() {
    timeSlots.clear();
    selectedWeekDays.clear();
    isPaidAppointment.value = false;
    appointmentPriceController.clear();
    appointmentsState.value = ApiState.initial();
    isLoadingAppointments.value = false;
    isLoadingCreateAppointments.value = false;
  }

  @override
  void dispose() {
    appointmentPriceController.dispose();
    super.dispose();
  }
}
