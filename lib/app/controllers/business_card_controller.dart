import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/data/apis/repository/business_card_repo.dart';
import 'package:v_card/app/data/model/business/admin_group_model.dart';
import 'package:v_card/app/data/model/business/business_card_model.dart';
import 'package:v_card/app/data/model/business/group_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class BusinesscardController extends GetxController {
  final BusinesscardRepository businessCardRepository;

  BusinesscardController({required this.businessCardRepository}) {
    onInit();
  }

  final MobileScannerController mobileScannerController =
      MobileScannerController();

  final TextEditingController groupNameController = TextEditingController();

  var selectedRadio = (-1).obs;
  var selectedGroupId = (-1).obs;
  var selectedGroups = <GroupData>[].obs;

  final RxString scannedData = ''.obs;

  final Rx<GrouopModel?> groupList = Rx<GrouopModel?>(null);
  final Rx<ApiState> groupListState = ApiState.initial().obs;
  final RxBool isLoadingGroupList = false.obs;
  final RxBool isLoadingcreateGroup = false.obs;

  final Rx<AdminGrouopModel?> adminGroupList = Rx<AdminGrouopModel?>(null);
  final Rx<ApiState> adminGroupListState = ApiState.initial().obs;
  final RxBool isLoadingAdminGroupList = false.obs;

  final Rx<BusinessCardModel?> businessCardList = Rx<BusinessCardModel?>(null);
  final Rx<ApiState> businessCardListState = ApiState.initial().obs;
  final RxBool isLoadingBusinessCardList = false.obs;

  final Rx<ApiState> createBusinessCardState = ApiState.initial().obs;

  final RxBool isLoadingCreateBusiness = false.obs;
  final Rx<ApiState> businesCreateState = ApiState.initial().obs;

  final RxBool isLoadingDeleteGroup = false.obs;
  final Rx<ApiState> deleteGroupState = ApiState.initial().obs;

  // Add search controller
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();

  final RxBool isSearchActive = false.obs;
  final RxString searchText = ''.obs;

  final RxBool isConnected = true.obs;
  final RxBool hasInitialData = false.obs;

  bool isBottomSheetOpen = false;

  Future<void> checkNetworkAndLoad() async {
    try {
      final networkHelper = NetworkHelper();
      isConnected.value = await networkHelper.hasNetworkConnection();

      Connectivity().onConnectivityChanged.listen((_) async {
        isConnected.value = await networkHelper.hasNetworkConnection();
        if (isConnected.value) {
          await getGroupList();
          await getBusinessCardList();
        }
      });

      if (isConnected.value) {
        await getGroupList();
        await getBusinessCardList();
      }
    } finally {
      hasInitialData.value = true;
    }
  }

  void closeSearch() {
    isSearchActive.value = false;
    searchText.value = '';
    searchController.clear();
    searchFocusNode.unfocus();
  }

  void toggleSearchMode() {
    isSearchActive.value = !isSearchActive.value;
    if (isSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        searchFocusNode.requestFocus();
      });
    } else {
      searchController.clear();
      searchText.value = '';
    }
  }

  Future<void> createGroup(String groupName) async {
    if (isLoadingcreateGroup.value) return;
    try {
      isLoadingcreateGroup.value = true;
      if (groupNameController.text.isEmpty) return;

      businesCreateState.value = LoadingState();

      var response = await businessCardRepository.createGroupRepository(
        groupNameController.text,
      );

      if (response.success) {
        groupNameController.clear();
        getGroupList();
        NavigationService.navigateBack();
        businesCreateState.value = SuccessState(response);
      } else {
        businesCreateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      businesCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingcreateGroup.value = false;
    }
  }

  Future<void> getGroupList() async {
    try {
      isLoadingGroupList.value = true;
      groupListState.value = LoadingState();
      String? token = getIt<SharedPreferences>().getToken;

      var response = await businessCardRepository.getGroupListRepository(
        token!,
      );

      if (response.success) {
        groupList.value = response;
        groupListState.value = SuccessState(response);
      } else {
        groupListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      groupListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingGroupList.value = false;
    }
  }

  Future<void> getBusinessCardList() async {
    try {
      isLoadingBusinessCardList.value = true;
      businessCardListState.value = LoadingState();

      var response =
          await businessCardRepository.getBusinessCardListRepository();

      if (response.success) {
        businessCardList.value = response;
        businessCardListState.value = SuccessState(response);
      } else {
        businessCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      businessCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingBusinessCardList.value = false;
    }
  }

  Future<void> getFilteredBusinessCardList() async {
    try {
      businessCardList.value = null;
      isLoadingBusinessCardList.value = true;
      businessCardListState.value = LoadingState();

      if (selectedGroupId.value == -1) {
        await getBusinessCardList();
        return;
      }

      var response = await businessCardRepository
          .getFilteredBusinessCardListRepository(
            filterId: selectedGroupId.value.toString(),
          );

      if (response.success) {
        businessCardList.value = response;
        businessCardListState.value = SuccessState(response);
      } else if (!response.success) {
        businessCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      businessCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingBusinessCardList.value = false;
    }
  }

  Future<void> deleteGroup() async {
    try {
      isLoadingDeleteGroup.value = true;
      deleteGroupState.value = LoadingState();

      var response = await businessCardRepository.deletGroupApi(
        id: selectedGroupId.value.toString(),
      );

      if (response.success) {
       NavigationService.navigateBack();
        await getGroupList();
        await getBusinessCardList();

        deleteGroupState.value = SuccessState(response);
      } else if (!response.success) {
        deleteGroupState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      deleteGroupState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingDeleteGroup.value = false;
    }
  }

  Future<void> createBusinessCard(
    String urlAliasScannedData,
    String groupId,
  ) async {
    try {
      if (urlAliasScannedData.isEmpty || groupId.isEmpty) return;

      isLoadingCreateBusiness.value = true;
      createBusinessCardState.value = LoadingState();

      var response = await businessCardRepository.createBusinessCardRepository(
        urlAliasScannedData,
        groupId,
        // selectedVcardId.value == -1 ? '' : selectedVcardId.value.toString(),
      );

      if (response.success) {
        await getBusinessCardList();
        createBusinessCardState.value = SuccessState(response);
        selectedVcardId.value = -1;
        vcardController.clear();
       NavigationService.navigateBack();
      } else {
        createBusinessCardState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      createBusinessCardState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingCreateBusiness.value = false;
    }
  }

  final RxString vcardSearchText = ''.obs;
  final TextEditingController searchVcadrController = TextEditingController();
  final RxInt selectedVcardId = RxInt(-1);
  final vcardController = TextEditingController();

  void showVcardBottomSheet(BuildContext context) {
    vcardSearchText.value = '';
    final vcards = getIt<VcardController>().vcardOnlyList.value?.data ?? {};

    Get.bottomSheet(
      Container(
        height: Get.height * 0.5,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                margin: EdgeInsets.only(
                  left: 20.0.w,
                  right: 20.0.w,
                  top: 8.0.h,
                ),
                height: 5.0,
                width: 50.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  color: Get.theme.customColors.primaryColor?.withValues(
                    alpha: 0.6,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      AppStrings.T.lbl_select_vcard,
                      style: Get.theme.textTheme.bodyLarge,
                    ),
                    Gap(4.h),
                    // Add Search Field
                    Obx(
                      () => TextInputField(
                        onChanged: (value) => vcardSearchText.value = value,
                        hintLabel: AppStrings.T.lbl_search,
                        prefixIcon: CustomImageView(
                          imagePath: AssetConstants.icSearch,
                        ),
                        type: InputType.text,
                        controller: searchVcadrController,
                        suffixIcon:
                            vcardSearchText.value.isNotEmpty
                                ? IconButton(
                                  icon: Icon(
                                    Icons.close,
                                    color: Get.theme.customColors.greyTextColor,
                                  ),
                                  onPressed: () {
                                    searchVcadrController.clear();
                                    vcardSearchText.value = '';
                                  },
                                )
                                : null,
                      ),
                    ),
                    Gap(8.h),
                    Expanded(
                      child: Obx(() {
                        final searchQuery = vcardSearchText.value.toLowerCase();
                        final filteredEntries =
                            vcards.entries
                                .where(
                                  (entry) => entry.value.toLowerCase().contains(
                                    searchQuery,
                                  ),
                                )
                                .toList();

                        return ListView.separated(
                          itemCount: filteredEntries.length,
                          separatorBuilder: (_, __) => Divider(),
                          itemBuilder: (_, index) {
                            final entry = filteredEntries[index];
                            return ListTile(
                              title: Text(
                                entry.value,
                                style: Get.theme.textTheme.bodySmall,
                              ),
                              onTap: () {
                                selectedVcardId.value = int.parse(entry.key);
                                vcardController.text = entry.value;
                               NavigationService.navigateBack();
                              },
                            );
                          },
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),

      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
    );
  }

  void reset() {
    // Scanner controls
    mobileScannerController.stop();
    scannedData.value = '';

    // Text controllers
    groupNameController.clear();
    searchController.clear();

    // Radio selections
    selectedRadio.value = -1;
    selectedGroupId.value = -1;
    selectedGroups.clear();

    // Group list state
    groupList.value = null;
    groupListState.value = ApiState.initial();
    isLoadingGroupList.value = false;
    isLoadingcreateGroup.value = false;

    // Admin group state
    adminGroupList.value = null;
    adminGroupListState.value = ApiState.initial();
    isLoadingAdminGroupList.value = false;

    // Business card state
    businessCardList.value = null;
    businessCardListState.value = ApiState.initial();
    isLoadingBusinessCardList.value = false;

    // Creation states
    createBusinessCardState.value = ApiState.initial();
    isLoadingCreateBusiness.value = false;
    businesCreateState.value = ApiState.initial();

    // Delete state
    isLoadingDeleteGroup.value = false;
    deleteGroupState.value = ApiState.initial();

    // Search state
    searchFocusNode.unfocus();
    isSearchActive.value = false;
    searchText.value = '';

    // Network state
    isConnected.value = true;
    hasInitialData.value = false;
    isBottomSheetOpen = false;
  }

  @override
  @i.disposeMethod
  void dispose() {
    // Dispose controllers
    mobileScannerController.dispose();
    groupNameController.dispose();
    searchController.dispose();
    searchFocusNode.dispose();

    // Call parent dispose
    super.dispose();
  }
}
