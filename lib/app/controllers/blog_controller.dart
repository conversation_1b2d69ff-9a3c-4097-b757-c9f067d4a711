// ignore_for_file: library_prefixes

import 'dart:io';
import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;

import 'package:v_card/app/data/apis/repository/blog_repo.dart';
import 'package:v_card/app/data/model/blog/blog_detail_model.dart';
import 'package:v_card/app/data/model/blog/blog_model.dart';
import 'package:v_card/app/data/model/blog/create_blog_model.dart';
import 'package:v_card/app/data/model/blog/delete_Blog_model.dart';
import 'package:v_card/app/data/model/blog/update_blog_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:html/dom.dart' as dom;
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:flutter_quill/quill_delta.dart' as quill;
import 'package:html/parser.dart' as htmlParser;

@i.lazySingleton
@i.injectable
class BlogController extends GetxController {
  final BlogRepository blogRepository;

  BlogController({required this.blogRepository}) {
    onInit();
  }

  final isShowingToast = false.obs;


  quill.QuillController quillController = quill.QuillController.basic();

  // List State
  final Rx<BlogModel?> blogList = Rx<BlogModel?>(null);
  final Rx<ApiState> vCardListState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;

  // Blog Create
  final RxBool isLoadingCreateBlog = false.obs;
  final Rx<CreateBlogModel?> createDataresponse = Rx<CreateBlogModel?>(null);
  final Rx<ApiState> createBlogState = ApiState.initial().obs;

  // Blog Update
  final Rx<UpdateBlogModel?> updateBlogResponse = Rx<UpdateBlogModel?>(null);
  final Rx<ApiState> updateBlogState = ApiState.initial().obs;

  // Blog Detail
  final Rx<BlogDetailModel?> blogDataById = Rx<BlogDetailModel?>(null);
  final RxBool isLoadingBlogById = false.obs;
  final Rx<ApiState> blogByIdState = ApiState.initial().obs;

  // Blog Delete
  final Rx<DeleteBlogModel?> deleteByIdData = Rx<DeleteBlogModel?>(null);
  final RxBool isLoadingDeleteBlogById = false.obs;
  final Rx<ApiState> deleteBlogByIdState = ApiState.initial().obs;

  // Form Controllers
  final Rx<File?> profileImageFile = Rx<File?>(null);
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController blogUrlController = TextEditingController();

  late RxBool isProcessingPWAIcon;

  final RxBool isBlogIconExpanded = false.obs;
  final RxBool isBlogNameExpanded = false.obs;
  final RxBool isBlogDescriptionExpanded = false.obs;

  Future<void> getBlogList(String vCardId) async {
    try {
      isLoading.value = true;
      final response = await blogRepository.getadminBlogListRepository(vCardId);

      if (response.success) {
        blogList.value = response;
        vCardListState.value = SuccessState(response);
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  void _initializeQuillController(String htmlContent) {
    try {
      final document = htmlParser.parse(htmlContent);
      final delta = quill.Delta();

      void parseNode(dom.Node node, {Map<String, dynamic>? currentStyle}) {
        currentStyle ??= {};

        if (node is dom.Element) {
          final newStyle = Map<String, dynamic>.from(currentStyle);

          switch (node.localName) {
            case 'b':
            case 'strong':
              newStyle['bold'] = true;
              break;
            case 'i':
            case 'em':
              newStyle['italic'] = true;
              break;
            case 'u':
              newStyle['underline'] = true;
              break;
            case 'span':
              for (var className in node.classes) {
                if (className.startsWith('ql-font-')) {
                  newStyle['font'] = className.replaceFirst('ql-font-', '');
                }
              }
              break;
          }

          for (var child in node.nodes) {
            parseNode(child, currentStyle: newStyle);
          }
        } else if (node is dom.Text) {
          final text = node.text;
          if (text.isNotEmpty) {
            delta.insert(text, currentStyle);
          }
        }
      }

      parseNode(document.body!);

      // Ensure the document ends with a newline.
      if (delta.isNotEmpty && !(delta.last.data as String).endsWith('\n')) {
        delta.insert('\n'); // Add a newline at the end of the content.
      }

      quillController = quill.QuillController(
        document: quill.Document.fromDelta(delta),
        selection: const TextSelection.collapsed(offset: 0),
      );
    } catch (e) {
      Logger.log('Manual HTML parse failed: ${e.toString()}');
      quillController = quill.QuillController.basic();
    }
  }

  Future<void> createAdminBlog({
    required String vcardId,
    required String name,
    String? profileImg,
    required String description,
    required String blogUrl,
  }) async {
    try {
      isLoadingCreateBlog.value = true;

      final formData = {
        "blog_icon": [profileImg],
        "title": name,
        "description": description,
        "vcard_id": vcardId,
      };

      isLoadingCreateBlog.value = true;

      final response = await blogRepository.createAdminBlogRepository(
        data: formData,
      );

      if (response.success) {
        createDataresponse.value = response;
        createBlogState.value = SuccessState(response);
        if (isCardCreated) {
          getBlogList(vcardId);
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createTestimonialsPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else if (!response.success) {
        createBlogState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      createBlogState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingCreateBlog.value = false;
    }
  }

  Future<void> getBlogById({required int id}) async {
    try {
      isLoadingBlogById.value = true;

      final response = await blogRepository.getBlogByIdRepository(id: id);
      if (response.success) {
        blogDataById.value = response;
        blogByIdState.value = SuccessState(response);
        _initializeQuillController(response.data.description ?? '');
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingBlogById.value = false;
    }
  }

  Future<void> deleteBlogById({
    required int id,
    required String vcardId,
  }) async {
    try {
      isLoadingDeleteBlogById.value = true;
      final response = await blogRepository.deleteBlogByIdRepository(id: id);

      if (response.success) {
        deleteByIdData.value = response;
        deleteBlogByIdState.value = SuccessState(response);
        NavigationService.navigateBack();
        getBlogList(vcardId);
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingDeleteBlogById.value = false;
    }
  }

  Future<void> updateBlog({
    required int blogId,
    required String vcardId,
    required String name,
    String? profileImg,
    required String description,
    required String blogUrl,
  }) async {
    try {
      isLoadingCreateBlog.value = true;
      final formData = {
        "blog_icon": [profileImg],
        "title": name,
        "description": description,
      };

      final response = await blogRepository.updateBlogRepository(
        blogId: blogId,
        data: formData,
      );

      if (response.success) {
        updateBlogResponse.value = response;
        updateBlogState.value = SuccessState(response);
        getBlogList(vcardId);
        NavigationService.navigateBack();
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingCreateBlog.value = false;
    }
  }

  void resetForm() {
    quillController.clear();
    nameController.clear();
    descriptionController.clear();
    blogUrlController.clear();
    profileImageFile.value = null;
    blogDataById.value = null;
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
