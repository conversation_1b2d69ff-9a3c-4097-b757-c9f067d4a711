import 'dart:io';

import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/product_repo.dart';
import 'package:v_card/app/data/model/currency_model/currency_model.dart';
import 'package:v_card/app/data/model/product/create_product_model.dart';
import 'package:v_card/app/data/model/product/delete_product_model.dart';
import 'package:v_card/app/data/model/product/product_detail_model.dart';
import 'package:v_card/app/data/model/product/product_model.dart';
import 'package:v_card/app/data/model/product/update_product_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:path_provider/path_provider.dart';

@i.lazySingleton
@i.injectable
class ProductController extends GetxController {
  final ProductRepository productRepository;

  ProductController({required this.productRepository}) {
    onInit();
  }

  final RxList<File> productImageFiles = RxList<File>();
  final RxList<String> existingImageUrls = RxList<String>();
  final RxBool isProcessingImages = false.obs;

  @override
  void onInit() {
    super.onInit();
    getProductList('0');
  }

  final isShowingToast = false.obs;

  final Rx<ProductModel?> productList = Rx<ProductModel?>(null);
  final Rx<ApiState> productListState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;

  final TextEditingController nameController = TextEditingController();
  final TextEditingController currencyIdController = TextEditingController();
  final TextEditingController priceController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController productUrlController = TextEditingController();
  final RxBool isLoadingCreateProduct = false.obs;
  final Rx<CreateProductModel?> createDataresponse = Rx<CreateProductModel?>(
    null,
  );

  final Rx<ApiState> createProductState = ApiState.initial().obs;

  final Rx<ProductDetailModel?> productDataById = Rx<ProductDetailModel?>(null);
  final RxBool isLoadingProductById = false.obs;
  final Rx<ApiState> productByIdState = ApiState.initial().obs;

  final Rx<DeleteProductModel?> deleteByIdData = Rx<DeleteProductModel?>(null);
  final RxBool isLoadingDeleteProductById = false.obs;
  final Rx<ApiState> deleteProductByIdState = ApiState.initial().obs;

  final Rx<UpdateProductModel?> updateProductResponse = Rx<UpdateProductModel?>(
    null,
  );
  final Rx<ApiState> updateProductState = ApiState.initial().obs;

  final Rx<CurrencyModel?> currencyList = Rx<CurrencyModel?>(null);
  final RxBool isLoadingCurrencyList = false.obs;
  final Rx<ApiState> currencyListState = ApiState.initial().obs;
  final RxString selectedCurrencyId = '1'.obs;
  final RxString selectedCurrencyName = '\$ - USD US Dollar'.obs;

  late Rx<bool> isProcessingPWAIcon;

  Future<void> getProductList(String vCardId) async {
    try {
      isLoading.value = true;
      var response = await productRepository.getadminProductListRepository(
        vCardId,
      );

      if (response.success) {
        productList.value = response;
        productListState.value = SuccessState(response);
      } else if (!response.success) {
        productListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      productListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createAdminProduct({
    required String vcardId,
    required String name,
    required String currencyId,
    required String price,
    required String description,
    required String productUrl,
  }) async {
    try {
      isLoadingCreateProduct.value = true;

      final processedUrl = processProductUrl(productUrl);

      Map<String, dynamic> formData = getFormData(
        name: name,
        currencyId: currencyId,
        price: price,
        description: description,
        productUrl: processedUrl,
        vcardId: vcardId,
      );

      var response = await productRepository.createAdminServiceRepository(
        data: formData,
      );

      if (response.success) {
        createDataresponse.value = response;
        createProductState.value = SuccessState(response);

        if (isCardCreated) {
          getProductList(vcardId);
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createInstaEmbedPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        createProductState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log('Create Product Error: ${e.toString()}');
      if (e is DioException) {
        Logger.log('DioError Response: ${e.response?.data}');
      }
      createProductState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(e.toString(), 'Please try again'),
      );
    } finally {
      isLoadingCreateProduct.value = false;
    }
  }

  Future<void> updateProduct({
    required String productId,
    required String vcardId,
    required String name,
    required String currencyId,
    required String price,
    required String description,
    required String productUrl,
  }) async {
    try {
      isLoadingCreateProduct.value = true;

      // Prepare images before update
      await prepareImagesForUpdate();

      final processedUrl = processProductUrl(productUrl);

      Map<String, dynamic> formData = getFormData(
        name: name,
        currencyId: currencyId,
        price: price,
        description: description,
        productUrl: processedUrl,
      );

      var response = await productRepository.updateProductRepository(
        productId: productId,
        data: formData,
      );

      if (response.success) {
        updateProductResponse.value = response;
        updateProductState.value = SuccessState(response);

        getProductList(vcardId);
        NavigationService.navigateBack();
      } else {
        updateProductState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            response.message,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log('Update Product Error: ${e.toString()}');
      if (e is DioException) {
        Logger.log('DioError Response: ${e.response?.data}');
      }
      updateProductState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(e.toString(), 'Please try again'),
      );
    } finally {
      isLoadingCreateProduct.value = false;
    }
  }

  Future<void> getProductById({required int id}) async {
    try {
      isLoadingProductById.value = true;

      var response = await productRepository.getProductByIdRepository(id: id);

      if (response.success) {
        productDataById.value = response;
        productByIdState.value = SuccessState(response);

        // Set existing images
        if (response.data.productIcon != null &&
            response.data.productIcon!.isNotEmpty) {
          productImageFiles.clear();
          existingImageUrls.clear();
          existingImageUrls.addAll(response.data.productIcon!);
        }

        var cId = await productRepository.getCurrencyList();
        final currencyId = response.data.currencyId.toString();
        final currencyMap = cId.data ?? {};
        final currencyName = currencyMap[currencyId] ?? '';

        selectedCurrencyId.value = currencyId;
        selectedCurrencyName.value = currencyName;
        currencyIdController.text = currencyName;
      } else if (!response.success) {
        productByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      productByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingProductById.value = false;
    }
  }

  void removeImage(int index) {
    if (index < existingImageUrls.length) {
      existingImageUrls.removeAt(index);
    } else {
      final adjustedIndex = index - existingImageUrls.length;
      if (adjustedIndex < productImageFiles.length) {
        productImageFiles.removeAt(adjustedIndex);
      }
    }
  }

  Future<void> deleteProductById({
    required int id,
    required String vcardId,
  }) async {
    try {
      isLoadingDeleteProductById.value = true;

      var response = await productRepository.deleteVcardByIdRepository(id: id);

      if (response.success) {
        deleteByIdData.value = response;
        deleteProductByIdState.value = SuccessState(response);
        getProductList(vcardId);
        if (Get.isBottomSheetOpen == true) {
          NavigationService.navigateBack();
        }
        NavigationService.navigateBack();
      } else if (!response.success) {
        deleteProductByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      deleteProductByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingDeleteProductById.value = false;
    }
  }

  Future<void> getCurrencyList() async {
    try {
      isLoadingCurrencyList.value = true;
      var response = await productRepository.getCurrencyList();
      if (response.success) {
        currencyList.value = response;
        currencyListState.value = SuccessState(response);
      } else if (!response.success) {
        currencyListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      currencyListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingCurrencyList.value = false;
    }
  }

  Future<List<File>> convertUrlsToFiles(List<String> urls) async {
    List<File> files = [];
    for (String url in urls) {
      try {
        final response = await Dio().get(
          url,
          options: Options(responseType: ResponseType.bytes),
        );
        final tempDir = await getTemporaryDirectory();
        final fileName = url.split('/').last;
        final file = File('${tempDir.path}/$fileName');
        await file.writeAsBytes(response.data);
        files.add(file);
      } catch (e) {
        print('Error converting URL to File: $e');
      }
    }
    return files;
  }

  Future<void> prepareImagesForUpdate() async {
    try {
      isProcessingImages.value = true;

      // Convert existing image URLs to files if they exist
      if (existingImageUrls.isNotEmpty) {
        final convertedFiles = await convertUrlsToFiles(existingImageUrls);
        productImageFiles.insertAll(0, convertedFiles);
        existingImageUrls
            .clear(); // Clear URLs as they're now converted to files
      }
    } catch (e) {
      print('Error preparing images: $e');
    } finally {
      isProcessingImages.value = false;
    }
  }

  Map<String, dynamic> getFormData({
    required String name,
    required String currencyId,
    required String price,
    required String description,
    required String productUrl,
    String? vcardId,
  }) {
    final formData = {
      "product_icon":
          productImageFiles.isEmpty
              ? [[]]
              : [productImageFiles.map((file) => file.path).toList()],
      "name": name,
      "currency_id": currencyId,
      "price": price,
      "description": description,
      "product_url": productUrl,
    };

    if (vcardId != null) {
      formData["vcard_id"] = vcardId;
    }

    return formData;
  }

  void resetForm() {
    nameController.clear();
    currencyIdController.clear();
    selectedCurrencyId.value = '';
    selectedCurrencyName.value = '';
    priceController.clear();
    descriptionController.clear();
    productUrlController.clear();
    productDataById.value = null;
    productImageFiles.clear();
    existingImageUrls.clear();
  }

  void onDispose() {
    super.dispose();
    resetForm();
  }
}
