import 'dart:io';
import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/product_repo.dart';
import 'package:v_card/app/data/apis/repository/stting_repo.dart';
import 'package:v_card/app/data/model/setting/general_setting_model.dart';
import 'package:v_card/app/data/model/setting/payment_config_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class SettingsController extends GetxController {
  final SettingsRepository settingsRepository;

  SettingsController({required this.settingsRepository});

  final isShowingToast = false.obs;


  // General Settings
  final Rx<GeneralSettingModel?> generalSettings = Rx<GeneralSettingModel?>(
    null,
  );
  final Rx<UpdateGeneralSettingModel?> updateGeneralSettingsResponse =
      Rx<UpdateGeneralSettingModel?>(null);
  final Rx<UpdatePaymentConfigModel?> updatePaymentConfigResponse =
      Rx<UpdatePaymentConfigModel?>(null);
  final Rx<ApiState> getGeneralSettingState = ApiState.initial().obs;
  final Rx<ApiState> updateGeneralSettingState = ApiState.initial().obs;
  final Rx<ApiState> getPaymentConfigState = ApiState.initial().obs;
  final Rx<ApiState> updatePaymentConfigState = ApiState.initial().obs;

  final RxBool isLoadingGeneralSettings = false.obs;
  final RxBool isUpdatingGeneralSettings = false.obs;

  // Payment Configuration
  final Rx<PaymentConfigModel?> paymentConfig = Rx<PaymentConfigModel?>(null);
  final RxBool isLoadingPaymentConfig = false.obs;
  final RxBool isUpdatingPaymentConfig = false.obs;

  // General Settings Form controllers
  final Rx<File?> profileImageFile = Rx<File?>(null);
  final TextEditingController paypalEmailController = TextEditingController();
  final TextEditingController currencyIdController = TextEditingController();
  final TextEditingController subscriptionModalTimeController =
      TextEditingController();
  final RxString selectedCurrencyId = '1'.obs;
  final RxString selectedCurrencyName = '\$ - USD US Dollar'.obs;

  // Toggle settings for General Settings
  final RxBool timeFormat24Hour = false.obs; // 0 = 12 Hour, 1 = 24 Hour
  final RxBool askDetailsBeforeDownloadingContact = false.obs;
  final RxBool enableAttachmentForInquiry = false.obs;
  final RxBool enablePWA = false.obs;

  // Payment Configuration controllers
  // Toggle states for payment methods
  final RxBool stripeEnabled = false.obs;
  final RxBool paystackEnabled = false.obs;
  final RxBool flutterwaveEnabled = false.obs;
  final RxBool razorpayEnabled = false.obs;
  final RxBool phonePeEnabled = false.obs;
  final RxBool paypalEnabled = false.obs;
  final RxBool manuallyEnabled = false.obs;

  // Text controllers for payment configuration
  // Stripe
  final TextEditingController stripeKeyController = TextEditingController();
  final TextEditingController stripeSecretController = TextEditingController();

  // Paystack
  final TextEditingController paystackKeyController = TextEditingController();
  final TextEditingController paystackSecretController =
      TextEditingController();

  // Razorpay
  final TextEditingController razorpayKeyController = TextEditingController();
  final TextEditingController razorpaySecretController =
      TextEditingController();

  // Flutter Wave
  final TextEditingController flutterwaveKeyController =
      TextEditingController();
  final TextEditingController flutterwaveSecretController =
      TextEditingController();

  // PhonePe
  final TextEditingController phonePeMerchantIdController =
      TextEditingController();
  final TextEditingController phonePeMerchantUserIdController =
      TextEditingController();
  final TextEditingController phonePeEnvController = TextEditingController();
  final TextEditingController phonePeSaltKeyController =
      TextEditingController();
  final TextEditingController phonePeSaltIndexController =
      TextEditingController();

  // Paypal
  final TextEditingController paypalClientIdController =
      TextEditingController();
  final TextEditingController paypalSecretController = TextEditingController();
  final TextEditingController paypalModeController = TextEditingController();

  // Manually
  final TextEditingController manualPaymentGuideController =
      TextEditingController();

  late RxBool isProcessingPWAIcon;

  Future<void> fetchGeneralSettings() async {
    try {
      isLoadingGeneralSettings.value = true;
      final response = await settingsRepository.getGeneralSettings();
      generalSettings.value = response;

      // Update form controllers
      if (response.success) {
        final data = response.data.first;

        // Text fields
        paypalEmailController.text = data.paypalEmail;
        subscriptionModalTimeController.text = data.subscriptionModelTime;

        // Currency setup
        var cId = await getIt<ProductRepository>().getCurrencyList();
        final currencyId = response.data.first.currencyId.toString();
        final currencyMap = cId.data;
        final currencyName = currencyMap?[currencyId] ?? '';

        selectedCurrencyId.value = currencyId;
        selectedCurrencyName.value = currencyName;
        currencyIdController.text = currencyName;

        // Toggle settings
        timeFormat24Hour.value = data.timeFormat == '1';
        askDetailsBeforeDownloadingContact.value =
            data.askDetailsBeforeDownloadingContact == '1';
        enableAttachmentForInquiry.value =
            data.enableAttachmentForInquiry == '1';
        enablePWA.value = data.enablePwa == '1';
      }
    } catch (e) {
      getGeneralSettingState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingGeneralSettings.value = false;
    }
  }

  Future<void> updateGeneralSettings({String? pwaIcon}) async {
    try {
      isUpdatingGeneralSettings.value = true;
      final data = {
        'pwa_icon': [pwaIcon],
        'paypal_email': paypalEmailController.text,
        'currency_id': selectedCurrencyId.value,
        'subscription_model_time': subscriptionModalTimeController.text,
        'time_format': timeFormat24Hour.value ? '1' : '0',
        'ask_details_before_downloading_contact':
            askDetailsBeforeDownloadingContact.value ? '1' : '0',
        'enable_attachment_for_inquiry':
            enableAttachmentForInquiry.value ? '1' : '0',
        'enable_pwa': enablePWA.value ? '1' : '0',
      };

      final response = await settingsRepository.updateGeneralSettings(data);
      if (response.success) {
        updateGeneralSettingsResponse.value = response;
        updateGeneralSettingState.value = SuccessState(response);
        NavigationService.navigateBack();
      } else if (!response.success) {
        updateGeneralSettingState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      updateGeneralSettingState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isUpdatingGeneralSettings.value = false;
    }
  }

  Future<void> fetchPaymentConfig() async {
    try {
      isLoadingPaymentConfig.value = true;
      final response = await settingsRepository.getPaymentConfig();
      paymentConfig.value = response;

      final data = response.data;

      if (response.success) {
        // Set toggle states for payment methods
        stripeEnabled.value = data.stripeEnable == '1';
        paystackEnabled.value = data.payStackEnable == '1';
        flutterwaveEnabled.value = data.flutterwaveEnable == '1';
        razorpayEnabled.value = data.rozorpayEnable == '1';
        phonePeEnabled.value = data.phonepeEnable == '1';
        paypalEnabled.value = data.paypalEnable == '1';
        manuallyEnabled.value = data.manuallyEnable == '1';

        // // Set text fields for payment configuration
        flutterwaveKeyController.text = data.flutterwaveKey ?? '';
        flutterwaveSecretController.text = data.flutterwaveSecret ?? '';
        paypalClientIdController.text = data.paypalClientId ?? '';
        paypalSecretController.text = data.paypalSecret ?? '';
        paypalModeController.text = data.paypalMode ?? '';

        // Stripe
        stripeKeyController.text = data.stripeKey ?? '';
        stripeSecretController.text = data.stripeSecret ?? '';

        // Paystack
        paystackKeyController.text = data.paystackKey ?? '';
        paystackSecretController.text = data.paystackSecret ?? '';

        // Razorpay
        razorpayKeyController.text = data.razorpayKey ?? '';
        razorpaySecretController.text = data.razorpaySecret ?? '';

        // Flutterwave
        flutterwaveKeyController.text = data.flutterwaveKey ?? '';
        flutterwaveSecretController.text = data.flutterwaveSecret ?? '';

        // PhonePe
        phonePeMerchantIdController.text = data.phonepeMerchantId ?? '';
        phonePeMerchantUserIdController.text = data.phonepeMerchantUserId ?? '';
        phonePeEnvController.text = data.phonepeEnv ?? '';
        phonePeSaltKeyController.text = data.phonepeSaltKey ?? '';
        phonePeSaltIndexController.text = data.phonepeSaltIndex ?? '';

        // Paypal
        paypalClientIdController.text = data.paypalClientId ?? '';
        paypalSecretController.text = data.paypalSecret ?? '';
        paypalModeController.text = data.paypalMode ?? '';

        // Manually
        manualPaymentGuideController.text = data.manualPaymentGuide ?? '';
      }
    } catch (e) {
      getPaymentConfigState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingPaymentConfig.value = false;
    }
  }

  Future<void> updatePaymentConfig(Map<String, dynamic> data) async {
    try {
      isUpdatingPaymentConfig.value = true;
      final response = await settingsRepository.updatePaymentConfig(data);

      if (response.success) {
        updatePaymentConfigResponse.value = response;
        updatePaymentConfigState.value = SuccessState(response);
        NavigationService.navigateBack();
      } else if (!response.success) {
        updatePaymentConfigState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      updatePaymentConfigState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isUpdatingPaymentConfig.value = false;
    }
  }

  @override
  void onClose() {
    // Dispose of General Settings controllers
    paypalEmailController.dispose();
    currencyIdController.dispose();
    subscriptionModalTimeController.dispose();

    // Dispose of Payment Configuration controllers
    flutterwaveKeyController.dispose();
    flutterwaveSecretController.dispose();
    paypalClientIdController.dispose();
    paypalSecretController.dispose();
    paypalModeController.dispose();

    super.onClose();
  }
}
