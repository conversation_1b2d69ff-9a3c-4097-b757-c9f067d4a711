import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/banner_repo.dart';
import 'package:v_card/app/data/model/banner/banner_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class BannerController extends GetxController {
  final BannerRepository bannerRepository;

  BannerController({required this.bannerRepository}) {
    onInit();
  }

  // List State
  final Rx<BannerModel?> bannerList = Rx<BannerModel?>(null);
  final Rx<ApiState> vCardListState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;

  // Form Controllers
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController urlController = TextEditingController();
  final TextEditingController bannerButtonController = TextEditingController();

  // Banner Update
  final RxBool isLoadingUpdateBanner = false.obs;
  final Rx<UpdateBannerModel?> updateBannerResponse = Rx<UpdateBannerModel?>(
    null,
  );
  final Rx<ApiState> updateBannerState = ApiState.initial().obs;

  final RxBool isBannerActive = false.obs;

  void toggleBannerStatus(bool value) {
    isBannerActive.value = value;
  }

  Future<void> getBannerData(String vCardId) async {
    try {
      isLoading.value = true;
      final response = await bannerRepository.getBannerListRepository(vCardId);

      if (response.success) {
        bannerList.value = response;
        vCardListState.value = SuccessState(response);
        if (bannerList.value?.data.isNotEmpty == true) {
          isBannerActive.value = bannerList.value!.data.first.banner == "1";
        }
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateBanner({
    required String vcardId,
    required String title,
    required String description,
    required String url,
    required String bannerButton,
  }) async {
    try {
      isLoadingUpdateBanner.value = true;

      final processedUrl = processProductUrl(url.trim().toLowerCase());

      final formData = {
        "vcard_id": vcardId,
        "banner": isBannerActive.value ? "1" : "0",
        "title": title,
        "description": description,
        "url": processedUrl,
        "banner_button": bannerButton,
      };

      final response = await bannerRepository.updateBannerRepository(
        data: formData,
      );

      if (response.success) {
        updateBannerResponse.value = response;
        updateBannerState.value = SuccessState(response);

        if (isCardCreated) {
          NavigationService.navigateBack();
          getBannerData(vcardId);
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.advancedSettingPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingUpdateBanner.value = false;
    }
  }

  void resetForm() {
    titleController.clear();
    descriptionController.clear();
    urlController.clear();
    bannerButtonController.clear();
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
