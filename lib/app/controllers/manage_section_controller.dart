import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/controllers/navigation_menu_controller.dart';
import 'package:v_card/app/data/apis/repository/manage_section_repository.dart';
import 'package:v_card/app/data/model/manage_section/manage_section_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class ManageSectionController extends GetxController {
  final ManageSectionRepository repository;

  ManageSectionController({required this.repository}) {
    onInit();
  }
  // State management
  final Rx<ManageSectionModel?> manageSectionData = Rx<ManageSectionModel?>(
    null,
  );
  final RxBool isLoading = false.obs;
  final Rx<ApiState> manageSectionState = ApiState.initial().obs;

  final RxBool isUpdateLoading = false.obs;
  final Rx<ApiState> manageSectionUpdateState = ApiState.initial().obs;

  // Form values
  final RxBool header = false.obs;
  final RxBool contactList = false.obs;
  final RxBool services = false.obs;
  final RxBool products = false.obs;
  final RxBool instaEmbed = false.obs;
  final RxBool galleries = false.obs;
  final RxBool blogs = false.obs;
  final RxBool iframe = false.obs;
  final RxBool map = false.obs;
  final RxBool testimonials = false.obs;
  final RxBool businessHours = false.obs;
  final RxBool appointments = false.obs;
  final RxBool oneSignalNotification = false.obs;
  final RxBool banner = false.obs;
  final RxBool newsLatterPopup = false.obs;

  Future<void> getManageSection(String vcardId) async {
    try {
      isLoading.value = true;
      final response = await repository.getManageSection(vcardId);

      if (response.success) {
        manageSectionData.value = response;
        _initializeFormValues(response.data);
        manageSectionState.value = SuccessState(response);
      } else {
        manageSectionState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            "Failed to load manage section",
            response.message,
          ),
        );
      }
    } catch (e) {
      manageSectionState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          "Error occurred",
          "Failed to load manage section data",
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _initializeFormValues(ManageSectionData data) {
    header.value = data.header == '1';
    contactList.value = data.contactList == '1';
    services.value = data.services == '1';
    products.value = data.products == '1';
    instaEmbed.value = data.instaEmbed == '1';
    galleries.value = data.galleries == '1';
    blogs.value = data.blogs == '1';
    iframe.value = data.iframe == '1';
    map.value = data.map == '1';
    testimonials.value = data.testimonials == '1';
    businessHours.value = data.businessHours == '1';
    appointments.value = data.appointments == '1';
    oneSignalNotification.value = data.oneSignalNotification == '1';
    banner.value = data.banner == '1';
    newsLatterPopup.value = data.newsLatterPopup == '1';
  }

  Future<void> updateManageSection(String vcardId) async {
    try {
      isUpdateLoading.value = true;

      final data = {
        'vcard_id': vcardId,
        'header': header.value ? '1' : '0',
        'contact_list': contactList.value ? '1' : '0',
        'services': services.value ? '1' : '0',
        'products': products.value ? '1' : '0',
        'insta_embed': instaEmbed.value ? '1' : '0',
        'galleries': galleries.value ? '1' : '0',
        'blogs': blogs.value ? '1' : '0',
        'iframe': iframe.value ? '1' : '0',
        'map': map.value ? '1' : '0',
        'testimonials': testimonials.value ? '1' : '0',
        'business_hours': businessHours.value ? '1' : '0',
        'appointments': appointments.value ? '1' : '0',
        'one_signal_notification': oneSignalNotification.value ? '1' : '0',
        'banner': banner.value ? '1' : '0',
        'news_latter_popup': newsLatterPopup.value ? '1' : '0',
      };

      final response = await repository.updateManageSection(data: data);

      if (response.success) {
        manageSectionState.value = SuccessState(response);

        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          Get.offAllNamed(AppRoutes.nevigationMenu);
          final navigationController = getIt<NavigationMenuController>();
          navigationController.changePage(1);
        }
      } else {
        manageSectionState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError("Failed to update", response.message),
        );
      }
    } catch (e) {
      manageSectionState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          "Error occurred",
          "Failed to update manage section",
        ),
      );
    } finally {
      isUpdateLoading.value = false;
    }
  }

  @override
  @i.disposeMethod
  void dispose() {
    header.close();
    contactList.close();
    services.close();
    products.close();
    instaEmbed.close();
    galleries.close();
    blogs.close();
    iframe.close();
    map.close();
    testimonials.close();
    businessHours.close();
    appointments.close();
    oneSignalNotification.close();
    banner.close();
    newsLatterPopup.close();
    super.onClose();
    super.dispose();
  }
}

// // manage_section_controller.dart
// import 'package:get/get.dart';
// import 'package:injectable/injectable.dart';
// import 'package:v_card/app/data/apis/repository/manage_section_repository.dart';
// import 'package:v_card/app/data/model/manage_section/manage_section_model.dart';
// import 'package:v_card/app/utils/helpers/exception/exception.dart';

// @injectable
// class ManageSectionController extends GetxController {
//   final ManageSectionRepository repository;

//   ManageSectionController({required this.repository});

//   // State management
//   final Rx<ManageSectionModel?> manageSectionData = Rx<ManageSectionModel?>(null);
//   final RxBool isLoading = false.obs;
//   final Rx<ApiState> manageSectionState = ApiState.initial().obs;

//   // Form values
//   final RxBool header = false.obs;
//   final RxBool contactList = false.obs;
//   final RxBool services = false.obs;
//   final RxBool products = false.obs;
//   final RxBool instaEmbed = false.obs;
//   final RxBool galleries = false.obs;
//   final RxBool blogs = false.obs;
//   final RxBool iframe = false.obs;
//   final RxBool map = false.obs;
//   final RxBool testimonials = false.obs;
//   final RxBool businessHours = false.obs;
//   final RxBool appointments = false.obs;
//   final RxBool oneSignalNotification = false.obs;
//   final RxBool banner = false.obs;
//   final RxBool newsLatterPopup = false.obs;

//   Future<void> getManageSection(int vcardId) async {
//     try {
//       isLoading.value = true;
//       final response = await repository.getManageSection(vcardId);

//       if (response.success) {
//         manageSectionData.value = response;
//         _initializeFormValues(response.data);
//         manageSectionState.value = SuccessState(response);
//       } else {
//         manageSectionState.value = FailedState(
//           statusCode: 0,
//           isRetirable: false,
//           error: UserFriendlyError(
//             "Failed to load manage section",
//             response.message,
//           ),
//         );
//       }
//     } catch (e) {
//       manageSectionState.value = FailedState(
//         statusCode: 0,
//         isRetirable: false,
//         error: UserFriendlyError(
//           "Error occurred",
//           "Failed to load manage section data",
//         ),
//       );
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   void _initializeFormValues(ManageSectionData data) {
//     header.value = data.header == '1';
//     contactList.value = data.contactList == '1';
//     services.value = data.services == '1';
//     products.value = data.products == '1';
//     instaEmbed.value = data.instaEmbed == '1';
//     galleries.value = data.galleries == '1';
//     blogs.value = data.blogs == '1';
//     iframe.value = data.iframe == '1';
//     map.value = data.map == '1';
//     testimonials.value = data.testimonials == '1';
//     businessHours.value = data.businessHours == '1';
//     appointments.value = data.appointments == '1';
//     oneSignalNotification.value = data.oneSignalNotification == '1';
//     banner.value = data.banner == '1';
//     newsLatterPopup.value = data.newsLatterPopup == '1';
//   }

//   Future<void> updateManageSection(String vcardId) async {
//     try {
//       isLoading.value = true;

//       final data = {
//         'vcard_id': vcardId,
//         'header': header.value ? '1' : '0',
//         'contact_list': contactList.value ? '1' : '0',
//         'services': services.value ? '1' : '0',
//         'products': products.value ? '1' : '0',
//         'insta_embed': instaEmbed.value ? '1' : '0',
//         'galleries': galleries.value ? '1' : '0',
//         'blogs': blogs.value ? '1' : '0',
//         'iframe': iframe.value ? '1' : '0',
//         'map': map.value ? '1' : '0',
//         'testimonials': testimonials.value ? '1' : '0',
//         'business_hours': businessHours.value ? '1' : '0',
//         'appointments': appointments.value ? '1' : '0',
//         'one_signal_notification': oneSignalNotification.value ? '1' : '0',
//         'banner': banner.value ? '1' : '0',
//         'news_latter_popup': newsLatterPopup.value ? '1' : '0',
//       };

//       final response = await repository.updateManageSection(
//         data: data,
//       );

//       if (response.success) {
//         manageSectionState.value = SuccessState(response);
//         NavigationService.navigateBack();
//       } else {
//         manageSectionState.value = FailedState(
//           statusCode: 0,
//           isRetirable: false,
//           error: UserFriendlyError(
//             "Failed to update",
//             response.message,
//           ),
//         );
//       }
//     } catch (e) {
//       manageSectionState.value = FailedState(
//         statusCode: 0,
//         isRetirable: false,
//         error: UserFriendlyError(
//           "Error occurred",
//           "Failed to update manage section",
//         ),
//       );
//     } finally {
//       isLoading.value = false;
//     }
//   }

//   @override
//   void onClose() {
//     // Dispose all Rx variables
//     header.close();
//     contactList.close();
//     services.close();
//     products.close();
//     instaEmbed.close();
//     galleries.close();
//     blogs.close();
//     iframe.close();
//     map.close();
//     testimonials.close();
//     businessHours.close();
//     appointments.close();
//     oneSignalNotification.close();
//     banner.close();
//     newsLatterPopup.close();
//     super.onClose();
//   }
// }
