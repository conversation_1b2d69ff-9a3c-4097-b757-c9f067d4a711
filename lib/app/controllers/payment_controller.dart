import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/subscription_plan_repo.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/payment_check_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class PaymentController extends GetxController {
  final SubscriptionPlanRepository subscriptionPlanRepository;

  PaymentController({required this.subscriptionPlanRepository}) {
    // onInit();
  }
  final TextEditingController noteController = TextEditingController();
  final RxString buttonText = AppStrings.T.lbl_purchase_plan.obs;

  final Rx<SubscriptionPlanBuyDataModel?> subscriptionPlanBuyData =
      Rx<SubscriptionPlanBuyDataModel?>(null);
  final Rx<ApiState> subscriptionPlanBuyState = ApiState.initial().obs;
  final RxBool isLoadingsubscriptionPlanBuyData = false.obs;

  final RxBool isTileExpanded = false.obs;
  final RxString selectedPaymentType = AppStrings.T.lbl_manually.obs;

  void changePaymentType(String type) {
    selectedPaymentType.value = type;
  }

  Future<void> subscriptionPlanBuy(String id) async {
    try {
      isLoadingsubscriptionPlanBuyData.value = true;

      var response = await subscriptionPlanRepository
          .subscriptionPlanBuyRepository(id);

      if (response.success) {
        NavigationService.navigateWithSlideAnimation(
          AppRoutes.paymentApprovalPage,
        );
        subscriptionPlanBuyData.value = response;
        subscriptionPlanBuyState.value = SuccessState(response);
        isLoadingsubscriptionPlanBuyData.value = false;
      }
    } catch (e) {
      subscriptionPlanBuyState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingsubscriptionPlanBuyData.value = false;
    }
  }

  // @override
  // @i.disposeMethod
  // void dispose() {
  //   super.dispose();
  // }
}
