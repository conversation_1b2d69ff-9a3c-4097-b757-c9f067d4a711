import 'dart:io';
import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/galleries_repo.dart';
import 'package:v_card/app/data/model/galleries/create_galleries_model.dart';
import 'package:v_card/app/data/model/galleries/delete_galleries_model.dart';
import 'package:v_card/app/data/model/galleries/galleries_detail_model.dart';
import 'package:v_card/app/data/model/galleries/galleries_model.dart';
import 'package:v_card/app/data/model/galleries/update_galleries_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class GalleriesController extends GetxController {
  final GalleriesRepository galleriesRepository;

  GalleriesController({required this.galleriesRepository}) {
    onInit();
  }

  final isShowingToast = false.obs;


  final isExpanded = false.obs;

  // List State
  final Rx<GalleriesModel?> galleriesList = Rx<GalleriesModel?>(null);
  final Rx<ApiState> vCardListState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;

  // Galleries Create
  final RxBool isLoadingCreateGalleries = false.obs;
  final Rx<CreateGalleriesModel?> createDataresponse =
      Rx<CreateGalleriesModel?>(null);
  final Rx<ApiState> createGalleriesState = ApiState.initial().obs;

  // Galleries Update
  final Rx<UpdateGalleriesModel?> updateGalleriesResponse =
      Rx<UpdateGalleriesModel?>(null);
  final Rx<ApiState> updateGalleriesState = ApiState.initial().obs;

  // Galleries Detail
  final Rx<GalleriesDetailModel?> galleriesDataById = Rx<GalleriesDetailModel?>(
    null,
  );
  final RxBool isLoadingGalleriesById = false.obs;
  final Rx<ApiState> galleriesByIdState = ApiState.initial().obs;

  // Galleries Delete
  final Rx<DeleteGalleriesModel?> deleteByIdData = Rx<DeleteGalleriesModel?>(
    null,
  );
  final RxBool isLoadingDeleteGalleriesById = false.obs;
  final Rx<ApiState> deleteGalleriesByIdState = ApiState.initial().obs;

  // Form Controllers
  final RxInt selectedGalleryType = (-1).obs;
  final Rx<File?> profileImageFile = Rx<File?>(null);
  final Rx<File?> galleryUploadFile = Rx<File?>(null);
  final Rx<File?> videoFile = Rx<File?>(null);
  final Rx<File?> audioFile = Rx<File?>(null);
  final TextEditingController nameController = TextEditingController();
  final TextEditingController galleriesUrlController = TextEditingController();

  late RxBool isProcessingPWAIcon;

  Future<void> getgalleriesList(String vCardId) async {
    try {
      isLoading.value = true;
      final response = await galleriesRepository.getGalleriesListRepository(
        vCardId,
      );

      if (response.success) {
        galleriesList.value = response;
        vCardListState.value = SuccessState(response);
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createAdminGalleries({
    required String vcardId,
    required String name,
    required int type,
    String? profileImg,
    String? galleryFile,
    String? videoFile,
    String? audioFile,
    required String galleriesUrl,
  }) async {
    try {
      isLoadingCreateGalleries.value = true;

      Map<String, dynamic> formData = {};

      // Add the appropriate file based on the type
      switch (type) {
        case 0: // Image
          if (profileImg != null) {
            formData = {
              "image": [profileImg],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
        case 1: // YouTube
          formData = {
            "link": galleriesUrl,
            "vcard_id": vcardId,
            "type": type.toString(),
          };
          break;
        case 2: // File
          if (galleryFile != null) {
            formData = {
              "gallery_upload_file": [galleryFile],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
        case 3: // Video
          if (videoFile != null) {
            formData = {
              "video_file": [videoFile],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
        case 4: // Audio
          if (audioFile != null) {
            formData = {
              "audio_file": [audioFile],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
      }

      final response = await galleriesRepository.createGalleriesRepository(
        data: formData,
      );

      if (response.success) {
        createDataresponse.value = response;
        createGalleriesState.value = SuccessState(response);

        if (isCardCreated) {
          getgalleriesList(vcardId);
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createBlogPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingCreateGalleries.value = false;
    }
  }

  Future<void> getGalleriesById({required int id}) async {
    try {
      isLoadingGalleriesById.value = true;

      final response = await galleriesRepository.getGalleriesByIdRepository(
        id: id,
      );
      if (response.success) {
        galleriesDataById.value = response;
        galleriesByIdState.value = SuccessState(response);
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingGalleriesById.value = false;
    }
  }

  Future<void> deleteGalleriesById({
    required int id,
    required String vcardId,
  }) async {
    try {
      isLoadingDeleteGalleriesById.value = true;
      final response = await galleriesRepository.deleteGalleriesByIdRepository(
        id: id,
      );

      if (response.success) {
        deleteByIdData.value = response;
        deleteGalleriesByIdState.value = SuccessState(response);
        NavigationService.navigateBack();
        getgalleriesList(vcardId);
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingDeleteGalleriesById.value = false;
    }
  }

  Future<void> updateGalleries({
    required int galleriesId,
    required String vcardId,
    required String name,
    required int type,
    String? profileImg,
    String? galleryFile,
    String? videoFile,
    String? audioFile,
    String? galleriesUrl,
  }) async {
    try {
      isLoadingCreateGalleries.value = true;

      Map<String, dynamic> formData = {};

      // Add the appropriate file based on the type
      switch (type) {
        case 0: // Image
          if (profileImg != null) {
            formData = {
              "image": [profileImg],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
        case 1: // YouTube
          formData = {
            "link": galleriesUrl,
            "vcard_id": vcardId,
            "type": type.toString(),
          };
          break;
        case 2: // File
          if (galleryFile != null) {
            formData = {
              "gallery_upload_file": [galleryFile],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
        case 3: // Video
          if (videoFile != null) {
            formData = {
              "video_file": [videoFile],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
        case 4: // Audio
          if (audioFile != null) {
            formData = {
              "audio_file": [audioFile],
              "vcard_id": vcardId,
              "type": type.toString(),
            };
          }
          break;
      }

      final response = await galleriesRepository.updateGalleriesRepository(
        galleriesId: galleriesId,
        data: formData,
      );

      if (response.success) {
        updateGalleriesResponse.value = response;
        updateGalleriesState.value = SuccessState(response);
        getgalleriesList(vcardId);
        NavigationService.navigateBack();
      } else if (!response.success) {
        vCardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      vCardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingCreateGalleries.value = false;
    }
  }

  void resetForm() {
    nameController.clear();
    galleriesUrlController.clear();
    profileImageFile.value = null;
    galleriesDataById.value = null;
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
