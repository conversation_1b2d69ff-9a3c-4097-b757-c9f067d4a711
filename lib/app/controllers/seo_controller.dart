import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/seo_repo.dart';
import 'package:v_card/app/data/model/seo/seo_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class SeoController extends GetxController {
  final SeoRepository seoRepository;

  SeoController({required this.seoRepository}) {
    onInit();
  }

  // List State
  final Rx<SeoModel?> seoList = Rx<SeoModel?>(null);
  final Rx<ApiState> seoListState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;

  // Form Controllers
  final TextEditingController siteTitleController = TextEditingController();
  final TextEditingController homeTitleController = TextEditingController();
  final TextEditingController metaKeywordController = TextEditingController();
  final TextEditingController metaDescriptionController =
      TextEditingController();
  final TextEditingController googleAnalyticsController =
      TextEditingController();

  // SEO Update
  final RxBool isLoadingUpdateSeo = false.obs;
  final Rx<UpdateSeoModel?> updateSeoResponse = Rx<UpdateSeoModel?>(null);
  final Rx<ApiState> updateSeoState = ApiState.initial().obs;

  Future<void> getSeoData(String vCardId) async {
    try {
      isLoading.value = true;
      final response = await seoRepository.getSeoListRepository(vCardId);

      if (response.success) {
        seoList.value = response;
        seoListState.value = SuccessState(response);
      } else if (!response.success) {
        seoListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            "Failed to load SEO data",
            "Please try again later",
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateSeo({
    required String vcardId,
    required String siteTitle,
    required String homeTitle,
    required String metaKeyword,
    required String metaDescription,
    required String googleAnalytics,
  }) async {
    try {
      isLoadingUpdateSeo.value = true;

      final formData = {
        "vcard_id": vcardId,
        "site_title": siteTitle.trim(),
        "home_title": homeTitle.trim(),
        "meta_keyword":
            metaKeyword.trim().isNotEmpty ? metaKeyword.trim() : null,
        "meta_description":
            metaDescription.trim().isNotEmpty ? metaDescription.trim() : null,
        "google_analytics":
            googleAnalytics.trim().isNotEmpty ? googleAnalytics.trim() : null,
      };

      final response = await seoRepository.updateSeoRepository(data: formData);

      if (response.success) {
        updateSeoResponse.value = response;
        updateSeoState.value = SuccessState(response);
        if (isCardCreated) {
          NavigationService.navigateBack();
          getSeoData(vcardId);
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.privacyPolicyPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else if (!response.success) {
        seoListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            "Failed to update SEO settings",
            response.message,
          ),
        );
      }
    } catch (e) {
      seoListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError("Error occurred", "Please try again later"),
      );
    } finally {
      isLoadingUpdateSeo.value = false;
    }
  }

  void resetForm() {
    siteTitleController.clear();
    homeTitleController.clear();
    metaKeywordController.clear();
    metaDescriptionController.clear();
    googleAnalyticsController.clear();
  }

  @override
  @i.disposeMethod
  void dispose() {
    siteTitleController.dispose();
    homeTitleController.dispose();
    metaKeywordController.dispose();
    metaDescriptionController.dispose();
    googleAnalyticsController.dispose();
    super.dispose();
  }
}
