// ignore_for_file: deprecated_member_use

import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/customize_qr_code_repo.dart';
import 'package:v_card/app/data/model/customize_qr_code/customize_qr_code_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class CustomizeQRCodeController extends GetxController {
  final CustomizeQRCodeRepository customizeQRCodeRepository;

  CustomizeQRCodeController({required this.customizeQRCodeRepository});

  final Rx<CustomizeQRCodeModel?> customizeQRCodeData =
      Rx<CustomizeQRCodeModel?>(null);
  final Rx<ApiState> fontState = ApiState.initial().obs;
  final RxBool isLoadingGetFont = false.obs;
  final RxBool isLoadingSave = false.obs;

  // QR Code customization options
  final RxString qrcodeColor = '#FFFFFF'.obs;
  final RxString backgroundColor = '#000000'.obs;
  final RxString style = 'dot'.obs;
  final RxString eyeStyle = 'circle'.obs;
  final RxInt applySetting = 0.obs;

  // Custom styles
  final RxList<Map<String, dynamic>> styleOptions =
      [
        {'value': 'square', 'label': AppStrings.T.lbl_square},
        {'value': 'dot', 'label': AppStrings.T.lbl_dot},
        {'value': 'round', 'label': AppStrings.T.lbl_round},
      ].obs;

  final RxList<Map<String, dynamic>> eyeStyleOptions =
      [
        {'value': 'square', 'label': AppStrings.T.lbl_square},
        {'value': 'circle', 'label': AppStrings.T.lbl_circle},
      ].obs;

  final isStyleExpanded = false.obs;
  final isEyeStyleExpanded = false.obs;

  String getStyleLabel(String value) {
    for (var option in styleOptions) {
      if (option['value'] == value) {
        return option['label'];
      }
    }
    return 'Select Style';
  }

  String getEyeStyleLabel(String value) {
    for (var option in eyeStyleOptions) {
      if (option['value'] == value) {
        return option['label'];
      }
    }
    return 'Select Eye Style';
  }

  void updateQRCodeColor(Color color) {
    qrcodeColor.value =
        '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  void updateBackgroundColor(Color color) {
    backgroundColor.value =
        '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }

  void updateStyle(String newStyle) {
    style.value = newStyle;
  }

  void updateEyeStyle(String newEyeStyle) {
    eyeStyle.value = newEyeStyle;
  }

  void toggleApplySetting() {
    applySetting.value = applySetting.value == 0 ? 1 : 0;
  }

  // Convert hex color string to Color object
  Color hexToColor(String hexString) {
    hexString = hexString.toUpperCase().replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString';
    }
    return Color(int.parse(hexString, radix: 16));
  }

  Future<void> getCustomizeQRCode(String vcardId) async {
    try {
      isLoadingGetFont.value = true;
      var response = await customizeQRCodeRepository.getCustomizeQRCodeRepo(
        vcardId,
      );
      if (response.success) {
        customizeQRCodeData.value = response;

        // Set values from API response
        for (var setting in response.data) {
          switch (setting.key) {
            case 'qrcode_color':
              qrcodeColor.value = setting.value;
              break;
            case 'background_color':
              backgroundColor.value = setting.value;
              break;
            case 'style':
              style.value = setting.value;
              break;
            case 'eye_style':
              eyeStyle.value = setting.value;
              break;
            case 'applySetting':
              applySetting.value = int.tryParse(setting.value) ?? 0;
              break;
          }
        }

        fontState.value = SuccessState(response);
      } else if (!response.success) {
        fontState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingGetFont.value = false;
    }
  }

  Future<void> updateCustomizeQRCode(String vcardId) async {
    try {
      isLoadingSave.value = true;
      var response = await customizeQRCodeRepository.updateCustomizeQRCodeRepo(
        data: {
          "qrcode_color": qrcodeColor.value,
          "background_color": backgroundColor.value,
          "style": style.value,
          "eye_style": eyeStyle.value,
          "applySetting": applySetting.value.toString(),
        },
        vcardId: vcardId,
      );

      if (response.success) {
        fontState.value = SuccessState(null);

        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createServicePage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        fontState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            'QR Code Update Failed',
            'Failed to update Customize QR Code',
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      fontState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'QR Code Update Failed',
          'Failed to update Customize QR Code',
        ),
      );
    } finally {
      isLoadingSave.value = false;
    }
  }
}
