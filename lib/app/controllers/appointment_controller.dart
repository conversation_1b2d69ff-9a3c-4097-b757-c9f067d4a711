import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/appointment_repo.dart';
import 'package:v_card/app/data/model/appointment/appointment_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class AppointmentController extends GetxController {
  final AppointmentRepository appointmentRepository;

  AppointmentController({required this.appointmentRepository}) {
    onInit();
  }

  // Main appointment list variables (for appointment_page.dart)
  final Rx<AppointmentModel?> appointmentList = Rx<AppointmentModel?>(null);
  final RxList<AppointmentData> fullAppointmentList = <AppointmentData>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool hasInitialData = false.obs;
  final RxString selectedType = 'All'.obs;
  final RxString selectedStatus = 'All'.obs;

  // Filtered appointment list variables (for fap.dart)
  final Rx<AppointmentModel?> filteredAppointmentList = Rx<AppointmentModel?>(
    null,
  );
  final RxList<AppointmentData> fullFilteredAppointmentList =
      <AppointmentData>[].obs;
  final RxBool isFilteredLoading = false.obs;
  final RxBool hasFilteredInitialData = false.obs;
  final RxString selectedFilteredType = 'All'.obs;
  final RxString selectedFilteredStatus = 'All'.obs;

  // Common variables
  final Rx<ApiState> appointmentListState = ApiState.initial().obs;
  final Rx<AppointmentModel?> appointmentIdData = Rx<AppointmentModel?>(null);
  final Rx<DeleteAppointmentResponse?> appointmentDeleteData =
      Rx<DeleteAppointmentResponse?>(null);
  final Rx<UpdateAppointmentResponse?> updateApointmentResponse =
      Rx<UpdateAppointmentResponse?>(null);
  final Rx<ApiState> appointmentByIdState = ApiState.initial().obs;
  final Rx<ApiState> appointmentDeleteState = ApiState.initial().obs;
  final RxBool isLoadingAppointmentById = false.obs;
  final RxBool isLoadingAppointmentDelete = false.obs;
  final RxBool isLoadingAppointmentUpdate = false.obs;
  final RxMap<int, bool> updatingAppointments = <int, bool>{}.obs;
  final RxBool isConnected = true.obs;
  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();
  final RxBool isSearchActive = false.obs;
  final RxString searchText = ''.obs;

  void closeSearch() {
    isSearchActive.value = false;
    searchText.value = '';
    searchController.clear();
    searchFocusNode.unfocus();
  }

  void toggleSearchMode() {
    isSearchActive.value = !isSearchActive.value;
    if (isSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        searchFocusNode.requestFocus();
      });
    } else {
      searchController.clear();
      searchText.value = '';
    }
  }

  final TextEditingController fapSearchController = TextEditingController();
  final FocusNode fapSearchFocusNode = FocusNode();
  final RxBool isFapSearchActive = false.obs;
  final RxString fapSearchText = ''.obs;

  void closeFapSearch() {
    isFapSearchActive.value = false;
    fapSearchText.value = '';
    fapSearchController.clear();
    fapSearchFocusNode.unfocus();
  }

  void toggleFapSearchMode() {
    isFapSearchActive.value = !isFapSearchActive.value;
    if (isFapSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        fapSearchFocusNode.requestFocus();
      });
    } else {
      fapSearchController.clear();
      fapSearchText.value = '';
    }
  }

  // Methods for appointment_page.dart
  Future<void> checkNetworkAndLoad() async {
    try {
      final networkHelper = NetworkHelper();
      isConnected.value = await networkHelper.hasNetworkConnection();

      Connectivity().onConnectivityChanged.listen((_) async {
        isConnected.value = await networkHelper.hasNetworkConnection();
        if (isConnected.value) await getAppointmentList();
      });

      if (isConnected.value) await getAppointmentList();
    } finally {
      hasInitialData.value = true;
    }
  }

  void applyFilters({required String type, required String status}) {
    selectedType.value = type;
    selectedStatus.value = status;
    fetchFilteredData();
  }

  void fetchFilteredData() {
    if (fullAppointmentList.isEmpty) return;

    List<AppointmentData> filteredList = List<AppointmentData>.from(
      fullAppointmentList,
    );

    if (selectedType.value != 'All') {
      filteredList =
          filteredList.where((appointment) {
            bool isPaid = appointment.paidAmount?.isNotEmpty ?? false;
            return selectedType.value == 'Paid' ? isPaid : !isPaid;
          }).toList();
    }

    if (selectedStatus.value != 'All') {
      filteredList =
          filteredList.where((appointment) {
            int status = appointment.status ?? 0;
            return selectedStatus.value == 'Completed'
                ? status == 1
                : status == 0;
          }).toList();
    }

    appointmentList.value = appointmentList.value?.copyWith(data: filteredList);
  }

  void resetFilters() {
    selectedType.value = 'All';
    selectedStatus.value = 'All';
    if (appointmentList.value != null) {
      appointmentList.value = appointmentList.value?.copyWith(
        data: fullAppointmentList,
      );
    }
  }

  // Methods for fap.dart
  Future<void> checkFilteredNetworkAndLoad() async {
    try {
      final networkHelper = NetworkHelper();
      isConnected.value = await networkHelper.hasNetworkConnection();

      Connectivity().onConnectivityChanged.listen((_) async {
        isConnected.value = await networkHelper.hasNetworkConnection();
        if (isConnected.value) await getFilteredAppointmentList();
      });

      if (isConnected.value) await getFilteredAppointmentList();
    } finally {
      hasFilteredInitialData.value = true;
    }
  }

  void applyFilteredFilters({required String type, required String status}) {
    selectedFilteredType.value = type;
    selectedFilteredStatus.value = status;
    fetchFilteredAppointmentData();
  }

  void fetchFilteredAppointmentData() {
    if (fullFilteredAppointmentList.isEmpty) return;

    List<AppointmentData> filteredList = List<AppointmentData>.from(
      fullFilteredAppointmentList,
    );

    if (selectedFilteredType.value != 'All') {
      filteredList =
          filteredList.where((appointment) {
            bool isPaid = appointment.paidAmount?.isNotEmpty ?? false;
            return selectedFilteredType.value == 'Paid' ? isPaid : !isPaid;
          }).toList();
    }

    if (selectedFilteredStatus.value != 'All') {
      filteredList =
          filteredList.where((appointment) {
            int status = appointment.status ?? 0;
            return selectedFilteredStatus.value == 'Completed'
                ? status == 1
                : status == 0;
          }).toList();
    }

    filteredAppointmentList.value = filteredAppointmentList.value?.copyWith(
      data: filteredList,
    );
  }

  void resetFilteredFilters() {
    selectedFilteredType.value = 'All';
    selectedFilteredStatus.value = 'All';
    if (filteredAppointmentList.value != null) {
      filteredAppointmentList.value = filteredAppointmentList.value?.copyWith(
        data: fullFilteredAppointmentList,
      );
    }
  }

  Future<void> getAppointmentList() async {
    try {
      isLoading.value = true;
      var response = await appointmentRepository.getAppointmentListRepository();
      if (response.success) {
        appointmentList.value = response;
        appointmentListState.value = SuccessState(response);
        fullAppointmentList.value = response.data;
        applyFilters(type: selectedType.value, status: selectedStatus.value);
      } else {
        appointmentListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      appointmentListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> getFilteredAppointmentList() async {
    try {
      isFilteredLoading.value = true;
      var response = await appointmentRepository.getAppointmentListRepository();
      if (response.success) {
        filteredAppointmentList.value = response;
        appointmentListState.value = SuccessState(response);
        fullFilteredAppointmentList.value = response.data;
        applyFilteredFilters(
          type: selectedFilteredType.value,
          status: selectedFilteredStatus.value,
        );
      } else {
        appointmentListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      appointmentListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isFilteredLoading.value = false;
    }
  }

  Future<void> getAppointmentById({required int id}) async {
    try {
      isLoadingAppointmentById.value = true;

      String? authToken = getIt<SharedPreferences>().getToken;
      var response = await appointmentRepository.getAppointmentByIdRepository(
        id: id.toString(),
        authToken: authToken!,
      );

      if (response.success) {
        appointmentIdData.value = response;
        appointmentByIdState.value = SuccessState(response);
      } else {
        appointmentByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      appointmentByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingAppointmentById.value = false;
    }
  }

  Future<void> updateAppointmentById({required String appointmentId}) async {
    try {
      final id = int.tryParse(appointmentId) ?? 0;
      updatingAppointments[id] = true;

      // Update both lists optimistically
      _updateAppointmentStatus(id, appointmentList, fullAppointmentList);
      _updateAppointmentStatus(
        id,
        filteredAppointmentList,
        fullFilteredAppointmentList,
      );

      var response = await appointmentRepository
          .updateAdminAppointmentRepository(
            status: '1',
            appointmentId: appointmentId,
          );

      if (!response.success) {
        // Revert both lists if failed
        _revertAppointmentStatus(id, appointmentList, fullAppointmentList);
        _revertAppointmentStatus(
          id,
          filteredAppointmentList,
          fullFilteredAppointmentList,
        );
        throw Exception('API call failed');
      }
    } catch (e) {
      appointmentDeleteState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      updatingAppointments.value = {};
      isLoadingAppointmentUpdate.value = false;
    }
  }

  void _updateAppointmentStatus(
    int id,
    Rx<AppointmentModel?> list,
    RxList<AppointmentData> fullList,
  ) {
    final originalList = list.value?.data ?? [];
    final index = originalList.indexWhere((a) => a.id == id);
    if (index != -1) {
      final originalAppointment = originalList[index];
      final updatedAppointment = originalAppointment.copyWith(status: 1);
      final updatedList = List<AppointmentData>.from(originalList)
        ..[index] = updatedAppointment;
      list.value = list.value?.copyWith(data: updatedList);

      final fullIndex = fullList.indexWhere((a) => a.id == id);
      if (fullIndex != -1) {
        fullList[fullIndex] = updatedAppointment;
      }
    }
  }

  void _revertAppointmentStatus(
    int id,
    Rx<AppointmentModel?> list,
    RxList<AppointmentData> fullList,
  ) {
    final originalList = list.value?.data ?? [];
    final index = originalList.indexWhere((a) => a.id == id);
    if (index != -1) {
      final originalAppointment = originalList[index];
      final revertedAppointment = originalAppointment.copyWith(status: 0);
      final revertedList = List<AppointmentData>.from(originalList)
        ..[index] = revertedAppointment;
      list.value = list.value?.copyWith(data: revertedList);

      final fullIndex = fullList.indexWhere((a) => a.id == id);
      if (fullIndex != -1) {
        fullList[fullIndex] = revertedAppointment;
      }
    }
  }

  Future<void> deleteAppointmentById({required int id}) async {
    try {
      isLoadingAppointmentDelete.value = true;

      // Delete from both lists optimistically
      _deleteAppointmentFromList(id, appointmentList, fullAppointmentList);
      _deleteAppointmentFromList(
        id,
        filteredAppointmentList,
        fullFilteredAppointmentList,
      );

      String? authToken = getIt<SharedPreferences>().getToken;
      var response = await appointmentRepository.deleteVcardByIdRepository(
        id: id,
        authToken: authToken!,
      );

      if (response.success) {
        NavigationService.navigateBack();
        appointmentDeleteState.value = SuccessState(response);
      } else {
        // Revert both lists if failed
        _revertDeletedAppointment(id, appointmentList, fullAppointmentList);
        _revertDeletedAppointment(
          id,
          filteredAppointmentList,
          fullFilteredAppointmentList,
        );
        appointmentDeleteState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      _revertDeletedAppointment(id, appointmentList, fullAppointmentList);
      _revertDeletedAppointment(
        id,
        filteredAppointmentList,
        fullFilteredAppointmentList,
      );
      appointmentDeleteState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingAppointmentDelete.value = false;
    }
  }

  void _deleteAppointmentFromList(
    int id,
    Rx<AppointmentModel?> list,
    RxList<AppointmentData> fullList,
  ) {
    final originalData = list.value?.data ?? [];
    final updatedData = List<AppointmentData>.from(originalData)
      ..removeWhere((e) => e.id == id);
    list.value = list.value?.copyWith(data: updatedData);
    fullList.removeWhere((e) => e.id == id);
  }

  void _revertDeletedAppointment(
    int id,
    Rx<AppointmentModel?> list,
    RxList<AppointmentData> fullList,
  ) {
    final originalData = list.value?.data ?? [];
    final itemToRestore = fullList.firstWhereOrNull((e) => e.id == id);
    if (itemToRestore != null) {
      final revertedData = List<AppointmentData>.from(originalData)
        ..add(itemToRestore);
      list.value = list.value?.copyWith(data: revertedData);
      if (!fullList.contains(itemToRestore)) {
        fullList.add(itemToRestore);
      }
    }
  }

  void reset() {
    // Reset main appointment state
    appointmentList.value = null;
    fullAppointmentList.clear();
    isLoading.value = false;
    hasInitialData.value = false;
    selectedType.value = 'All';
    selectedStatus.value = 'All';

    // Reset filtered appointment state
    filteredAppointmentList.value = null;
    fullFilteredAppointmentList.clear();
    isFilteredLoading.value = false;
    hasFilteredInitialData.value = false;
    selectedFilteredType.value = 'All';
    selectedFilteredStatus.value = 'All';

    // Reset common states
    appointmentListState.value = ApiState.initial();
    appointmentIdData.value = null;
    appointmentDeleteData.value = null;
    updateApointmentResponse.value = null;
    appointmentByIdState.value = ApiState.initial();
    appointmentDeleteState.value = ApiState.initial();
    isLoadingAppointmentById.value = false;
    isLoadingAppointmentDelete.value = false;
    isLoadingAppointmentUpdate.value = false;
    updatingAppointments.clear();
    isConnected.value = true;

    // Clear search inputs and focus
    searchController.clear();
    fapSearchController.clear();
    searchFocusNode.unfocus();
    fapSearchFocusNode.unfocus();
    isSearchActive.value = false;
    isFapSearchActive.value = false;
    searchText.value = '';
    fapSearchText.value = '';
  }

  @override
  @i.disposeMethod
  void dispose() {
    // Dispose controllers and focus nodes
    searchController.dispose();
    fapSearchController.dispose();
    searchFocusNode.dispose();
    fapSearchFocusNode.dispose();

    super.dispose();
  }
}
