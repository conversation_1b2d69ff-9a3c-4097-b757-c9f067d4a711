// ignore_for_file: library_prefixes

import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:flutter_quill/quill_delta.dart' as quill;
import 'package:get/get.dart';
import 'package:html/parser.dart' as htmlParser;
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/privacy_policy_repo.dart';
import 'package:v_card/app/data/model/privacy_policy/privacy_policy_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:html/dom.dart' as dom;

@i.lazySingleton
@i.injectable
class PrivacyPolicyController extends GetxController {
  final PrivacyPolicyRepository privacyPolicyRepository;

  PrivacyPolicyController({required this.privacyPolicyRepository});

  quill.QuillController quillController = quill.QuillController.basic();

  final Rx<PrivacyPolicyModel?> privacyPolicy = Rx<PrivacyPolicyModel?>(null);
  final Rx<ApiState> getPrivacyPolicyState = ApiState.initial().obs;
  final Rx<ApiState> updatePrivacyPolicyState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;
  final RxBool isSaveLoading = false.obs;

  Future<void> getPrivacyPolicy(String vcardId) async {
    try {
      isLoading.value = true;
      var response = await privacyPolicyRepository.getPrivacyPolicy(vcardId);
      if (response.success) {
        privacyPolicy.value = response;
        getPrivacyPolicyState.value = SuccessState(response);
        _initializeQuillController(response.data.privacyPolicy);
      } else if (!response.success) {
        getPrivacyPolicyState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }

      getPrivacyPolicyState.value = SuccessState(response);
    } catch (e) {
      getPrivacyPolicyState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  void _initializeQuillController(String htmlContent) {
    try {
      final document = htmlParser.parse(htmlContent);
      final delta = quill.Delta();

      void parseNode(dom.Node node, {Map<String, dynamic>? currentStyle}) {
        currentStyle ??= {};

        if (node is dom.Element) {
          final newStyle = Map<String, dynamic>.from(currentStyle);

          switch (node.localName) {
            case 'b':
            case 'strong':
              newStyle['bold'] = true;
              break;
            case 'i':
            case 'em':
              newStyle['italic'] = true;
              break;
            case 'u':
              newStyle['underline'] = true;
              break;
            case 'span':
              for (var className in node.classes) {
                if (className.startsWith('ql-font-')) {
                  newStyle['font'] = className.replaceFirst('ql-font-', '');
                }
              }
              break;
          }

          for (var child in node.nodes) {
            parseNode(child, currentStyle: newStyle);
          }
        } else if (node is dom.Text) {
          final text = node.text;
          if (text.isNotEmpty) {
            delta.insert(text, currentStyle);
          }
        }
      }

      parseNode(document.body!);

      // Ensure the document ends with a newline.
      if (delta.isNotEmpty && !(delta.last.data as String).endsWith('\n')) {
        delta.insert('\n'); // Add a newline at the end of the content.
      }

      quillController = quill.QuillController(
        document: quill.Document.fromDelta(delta),
        selection: const TextSelection.collapsed(offset: 0),
      );
    } catch (e) {
      Logger.log('Manual HTML parse failed: ${e.toString()}');
      quillController = quill.QuillController.basic();
    }
  }

  String get plainText {
    if (privacyPolicy.value?.data.privacyPolicy == null) return '';
    return htmlParser
            .parse(privacyPolicy.value!.data.privacyPolicy)
            .body
            ?.text ??
        '';
  }

  String? get textFont {
    if (privacyPolicy.value?.data.privacyPolicy == null) return null;
    final document = htmlParser.parse(privacyPolicy.value!.data.privacyPolicy);
    final span = document.querySelector('span');
    if (span != null && span.classes.isNotEmpty) {
      for (var className in span.classes) {
        if (className.startsWith('ql-font-')) {
          return className.replaceFirst('ql-font-', '');
        }
      }
    }
    return '';
  }

  Future<void> updatePrivacyPolicy(String vcardId, String privacyPolicy) async {
    try {
      isSaveLoading.value = true;
      var response = await privacyPolicyRepository.updatePrivacyPolicy(
        privacyPolicy: privacyPolicy,
        vcardId: vcardId,
      );

      if (response.success) {
        updatePrivacyPolicyState.value = SuccessState(response);
        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.termsAndConditionsPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
        quillController.clear();
      } else {
        updatePrivacyPolicyState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      updatePrivacyPolicyState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError('Privacy Policy Update Failed', e.toString()),
      );
    } finally {
      isSaveLoading.value = false;
    }
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
    quillController.clear();
  }
}
