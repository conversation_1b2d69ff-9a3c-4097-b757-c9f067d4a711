import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/advanced_setting_repo.dart';
import 'package:v_card/app/data/model/advanced_setting/advannced_setting_detail_model.dart';
import 'package:v_card/app/data/model/advanced_setting/advannced_setting_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class AdvancedSettingController extends GetxController {
  final AdvancedSettingsRepository advancedSettingsRepository;

  AdvancedSettingController({required this.advancedSettingsRepository}) {
    onInit();
  }

  final TextEditingController passwordController = TextEditingController();
  final TextEditingController customCssController = TextEditingController();
  final TextEditingController customJsController = TextEditingController();
  final RxBool branding = false.obs;
  final RxBool isPasswordObsecure = true.obs;

  final RxBool isLoadingCreateAdvanceSettings = false.obs;
  final Rx<CreateAdvancedSettingModel?> createDataresponse =
      Rx<CreateAdvancedSettingModel?>(null);
  final Rx<ApiState> createAdvanceSettingsState = ApiState.initial().obs;

  final Rx<AdvancedSettingDetailModel?> advanceSettingsDataById =
      Rx<AdvancedSettingDetailModel?>(null);
  final RxBool isLoadingAdvanceSettingsById = false.obs;
  final Rx<ApiState> advanceSettingsByIdState = ApiState.initial().obs;

  Future<void> createAdvanceSettings({
    required String vcardId,
    required String password,
    required String customCss,
    required String customJs,
    required String branding,
  }) async {
    try {
      Map<String, dynamic> formData = {
        "vcard_id": vcardId,
        "custom_js": customJs,
        "custom_css": customCss,
        "branding": branding,
        "password": password,
      };

      isLoadingCreateAdvanceSettings.value = true;

      var response = await advancedSettingsRepository
          .createAdvanceSettingsRepository(data: formData);

      if (response.success) {
        createDataresponse.value = response;
        createAdvanceSettingsState.value = SuccessState(response);
        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.fontPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        createAdvanceSettingsState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingCreateAdvanceSettings.value = false;
    }
  }

  Future<void> getAdvanceSettingsById({required String id}) async {
    try {
      isLoadingAdvanceSettingsById.value = true;

      var response = await advancedSettingsRepository
          .getAdvanceSettingsByIdRepository(id: id);

      if (response.success) {
        advanceSettingsDataById.value = response;
        advanceSettingsByIdState.value = SuccessState(response);
      } else if (!response.success) {
        advanceSettingsByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      advanceSettingsByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingAdvanceSettingsById.value = false;
    }
  }

  void resetForm() {
    passwordController.clear();
    customCssController.clear();
    customJsController.clear();
    advanceSettingsDataById.value = null;
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
