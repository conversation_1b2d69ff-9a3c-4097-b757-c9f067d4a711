import 'dart:io';

import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/service_repo.dart';
import 'package:v_card/app/data/model/service/create_service_model.dart';
import 'package:v_card/app/data/model/service/delete_service_model.dart';
import 'package:v_card/app/data/model/service/service_model.dart';
import 'package:v_card/app/data/model/service/service_detail_model.dart';
import 'package:v_card/app/data/model/service/service_slider_model.dart';
import 'package:v_card/app/data/model/service/update_service_model.dart';

import 'package:v_card/app/utils/helpers/exporter.dart';


@i.lazySingleton
@i.injectable
class ServiceController extends GetxController {
  final ServiceRepository serviceRepository;

  ServiceController({required this.serviceRepository}) {
    onInit();
  }

  @override
  void onInit() {
    super.onInit();
    getServiceList('0');
  }

  final isShowingToast = false.obs;


  final Rx<ServiceModel?> servicetList = Rx<ServiceModel?>(null);
  final Rx<ApiState> serviceListState = ApiState.initial().obs;
  final RxBool isLoading = false.obs;

  final Rx<File?> profileImageFile = Rx<File?>(null);
  final TextEditingController nameController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController serviceUrlController = TextEditingController();
  final RxBool isLoadingCreateService = false.obs;
  final Rx<CreateServiceModel?> createDataresponse = Rx<CreateServiceModel?>(
    null,
  );

  late RxBool isProcessingPWAIcon;

  final Rx<ApiState> createServiceState = ApiState.initial().obs;

  final Rx<ServiceDetailModel?> serviceDataById = Rx<ServiceDetailModel?>(null);
  final RxBool isLoadingServiceById = false.obs;
  final Rx<ApiState> serviceByIdState = ApiState.initial().obs;

  final Rx<DeleteServiceModel?> deleteByIdData = Rx<DeleteServiceModel?>(null);
  final RxBool isLoadingDeleteServiceById = false.obs;
  final Rx<ApiState> deleteServiceByIdState = ApiState.initial().obs;

  final Rx<UpdateServiceModel?> updateServiceResponse = Rx<UpdateServiceModel?>(
    null,
  );
  final Rx<ApiState> updateServiceState = ApiState.initial().obs;
  final RxBool serviceSlider = false.obs;

  final Rx<ServiceSliderModel?> updateServiceSliderResponse =
      Rx<ServiceSliderModel?>(null);
  final RxBool isLoadingServiceSlider = false.obs;
  final Rx<ApiState> updateServiceSliderState = ApiState.initial().obs;

  Future<void> getServiceList(String vCardId) async {
    try {
      isLoading.value = true;
      var response = await serviceRepository.getadminServiceListRepository(
        vCardId,
      );

      if (response.success) {
        servicetList.value = response;
        serviceListState.value = SuccessState(response);
      } else if (!response.success) {
        serviceListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      serviceListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> createAdminService({
    required String vcardId,
    required String name,
    String? profileImg,
    required String description,
    required String serviceUrl,
  }) async {
    try {
      isLoadingCreateService.value = true;

      final processedUrl = processProductUrl(serviceUrl.trim().toLowerCase());
      Map<String, dynamic> formData = {
        "service_icon": [profileImg],
        "name": name,
        "description": description,
        "service_url": processedUrl,
        "vcard_id": vcardId,
      };

      var response = await serviceRepository.createAdminServiceRepository(
        data: formData,
      );
      if (response.success) {
        createDataresponse.value = response;
        createServiceState.value = SuccessState(response);
        // getServiceList(vcardId);

        nameController.clear();
        descriptionController.clear();
        serviceUrlController.clear();
        profileImageFile.value = null;

        if (isCardCreated) {
          getServiceList(vcardId);
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createProductPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      } else {
        createServiceState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingCreateService.value = false;
    }
  }

  Future<void> getServiceById({required int id}) async {
    try {
      isLoadingServiceById.value = true;

      var response = await serviceRepository.getServiceByIdRepository(id: id);

      if (response.success) {
        serviceDataById.value = response;
        serviceByIdState.value = SuccessState(response);
      } else if (!response.success) {
        serviceByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      serviceByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingServiceById.value = false;
    }
  }

  Future<void> deleteServiceById({
    required int id,
    required String vcardId,
  }) async {
    try {
      isLoadingDeleteServiceById.value = true;

      var response = await serviceRepository.deleteServiceByIdRepository(
        id: id,
      );
      if (response.success) {
        deleteByIdData.value = response;
        deleteServiceByIdState.value = SuccessState(response);
        getServiceList(vcardId);
        if (Get.isBottomSheetOpen == true) {
          NavigationService.navigateBack();
        }
        NavigationService.navigateBack();
      } else if (!response.success) {
        deleteServiceByIdState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      deleteServiceByIdState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingDeleteServiceById.value = false;
    }
  }

  Future<void> updateService({
    required int serviceId,
    required String vcardId,
    required String name,
    String? profileImg,
    required String description,
    required String serviceUrl,
  }) async {
    try {
      isLoadingCreateService.value = true;
      Map<String, dynamic> formData = {
        "service_icon": [profileImg],
        "name": name,
        "description": description,
        "service_url": serviceUrl,
      };

      var response = await serviceRepository.updateServiceRepository(
        serviceId: serviceId.toString(),
        data: formData,
      );
      if (response.success) {
        updateServiceResponse.value = response;
        updateServiceState.value = SuccessState(response);
        getServiceList(vcardId);
        NavigationService.navigateBack();
      }
    } catch (e) {
      Logger.log(e.toString());
      updateServiceState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError("Pervice Update Failed", e.toString()),
      );
    } finally {
      isLoadingCreateService.value = false;
    }
  }

  Future<void> updateServiceSlider({required String vcardId}) async {
    try {
      isLoadingServiceSlider.value = true;

      var response = await serviceRepository.serviceSliderRepository(
        vcardId: vcardId,
      );
      if (response.success) {
        updateServiceSliderResponse.value = response;
        updateServiceSliderState.value = SuccessState(response);
      }
    } catch (e) {
      Logger.log(e.toString());
      updateServiceSliderState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(AppStrings.T.apiError, e.toString()),
      );
    } finally {
      isLoadingServiceSlider.value = false;
    }
  }

  void resetForm() {
    nameController.clear();
    descriptionController.clear();
    serviceUrlController.clear();
    profileImageFile.value = null;
    serviceDataById.value = null;
  }

  @override
  @i.disposeMethod
  void dispose() {
    super.dispose();
  }
}
