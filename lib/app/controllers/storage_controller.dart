import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/storage_repo.dart';
import 'package:v_card/app/data/model/storage/storage_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class StorageController extends GetxController {
  final StorageRepository storageRepository;

  StorageController({required this.storageRepository});

  // NFC Cards List
  final Rx<StorageResponseModel?> storageData = Rx<StorageResponseModel?>(null);
  final Rx<ApiState> storageState = ApiState.initial().obs;
  final RxBool isLoadingStorage = false.obs;

  Future<void> getStorageData() async {
    try {
      isLoadingStorage.value = true;
      final response = await storageRepository.getStorageDataRepo();

      if (response.success) {
        storageData.value = response;
        storageState.value = SuccessState(response);
      } else {
        storageState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      storageState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingStorage.value = false;
    }
  }
}
