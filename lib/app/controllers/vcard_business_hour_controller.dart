import 'dart:developer';
import 'dart:math' show min;

import 'package:get/get.dart';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/repository/vcard_business_hour_repo.dart';
import 'package:v_card/app/data/model/vcard_business_hour/vcard_business_hour_model.dart';
import 'package:v_card/app/global/global.dart';
import 'package:v_card/app/routes/app_routes.dart';
import 'package:v_card/app/services/navigation_service.dart';

@lazySingleton
@injectable
class VcardBusinessHoursController extends GetxController {
  final VcardBusinessHoursRepository repository;

  VcardBusinessHoursController({required this.repository});

  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;

  RxList<bool> isExpanded = List.generate(7, (_) => false).obs;

  void setDayExpanded(int index, bool value) {
    isExpanded[index] = value;
  }

  // Days are indexed from 0-6 (Monday to Sunday) for easier UI handling
  // API uses 1-7 format, so we'll convert when calling the API
  final List<RxBool> isDayEnabled = List.generate(7, (_) => false.obs);
  final List<RxString> startTimes = List.generate(7, (_) => '12:00 AM'.obs);
  final List<RxString> endTimes = List.generate(7, (_) => '12:30 AM'.obs);

  // Time options for dropdowns
  final List<String> timeOptions = [
    '12:00 AM',
    '12:30 AM',
    '01:00 AM',
    '01:30 AM',
    '02:00 AM',
    '02:30 AM',
    '03:00 AM',
    '03:30 AM',
    '04:00 AM',
    '04:30 AM',
    '05:00 AM',
    '05:30 AM',
    '06:00 AM',
    '06:30 AM',
    '07:00 AM',
    '07:30 AM',
    '08:00 AM',
    '08:30 AM',
    '09:00 AM',
    '09:30 AM',
    '10:00 AM',
    '10:30 AM',
    '11:00 AM',
    '11:30 AM',
    '12:00 PM',
    '12:30 PM',
    '01:00 PM',
    '01:30 PM',
    '02:00 PM',
    '02:30 PM',
    '03:00 PM',
    '03:30 PM',
    '04:00 PM',
    '04:30 PM',
    '05:00 PM',
    '05:30 PM',
    '06:00 PM',
    '06:30 PM',
    '07:00 PM',
    '07:30 PM',
    '08:00 PM',
    '08:30 PM',
    '09:00 PM',
    '09:30 PM',
    '10:00 PM',
    '10:30 PM',
    '11:00 PM',
    '11:30 PM',
  ];

  void toggleDay(int dayIndex) {
    isDayEnabled[dayIndex].value = !isDayEnabled[dayIndex].value;
  }

  void updateStartTime(int dayIndex, String time) {
    startTimes[dayIndex].value = time;

    if (!isDayEnabled[dayIndex].value) {
      isDayEnabled[dayIndex].value = true;
    }

    final startIndex = timeOptions.indexOf(time);
    final endIndex = timeOptions.indexOf(endTimes[dayIndex].value);
    if (endIndex <= startIndex) {
      endTimes[dayIndex].value =
          timeOptions[min(startIndex + 1, timeOptions.length - 1)];
    }
  }

  void updateEndTime(int dayIndex, String time) {
    endTimes[dayIndex].value = time;

    if (!isDayEnabled[dayIndex].value) {
      isDayEnabled[dayIndex].value = true;
    }
  }

  List<String> getFilteredEndTimes(String startTime) {
    final startIndex = timeOptions.indexOf(startTime);
    if (startIndex == -1) return timeOptions;
    return timeOptions.sublist(startIndex + 1);
  }

  Future<void> fetchBusinessHours(String vcardId) async {
    try {
      isLoading.value = true;

      for (int i = 0; i < 7; i++) {
        isDayEnabled[i].value = false;
        startTimes[i].value = '09:00 AM';
        endTimes[i].value = '09:30 AM';
      }

      VcardBusinessHoursModel response = await repository.getBusinessHours(
        vcardId,
      );

      if (response.success) {
        for (var businessHour in response.data) {
          int dayIndex = businessHour.dayOfWeek - 1;

          if (dayIndex >= 0 && dayIndex < 7) {
            isDayEnabled[dayIndex].value = true;
            startTimes[dayIndex].value = businessHour.startTime;
            endTimes[dayIndex].value = businessHour.endTime;
            isExpanded[dayIndex] = true;
          }
        }
      }
    } catch (e) {
      log("Error fetching business hours: $e");
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> saveBusinessHours(String vcardId) async {
    try {
      isSaving.value = true;

      List<int> enabledDays = [];
      Map<String, String> startTimeMap = {};
      Map<String, String> endTimeMap = {};

      for (int i = 0; i < 7; i++) {
        int apiDayIndex = i + 1;

        if (isDayEnabled[i].value) {
          enabledDays.add(apiDayIndex);
        }

        startTimeMap[apiDayIndex.toString()] = startTimes[i].value;
        endTimeMap[apiDayIndex.toString()] = endTimes[i].value;
      }

      Map<String, dynamic> requestData = {
        "vcard_id": vcardId,
        "startTime": startTimeMap,
        "endTime": endTimeMap,
        "days": enabledDays,
      };

      var response = await repository.saveBusinessHours(requestData);

      if (response.success) {
        if (isCardCreated) {
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.customizeQRCodePage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }
      }
    } catch (e) {
      log("Error saving business hours: $e");
    } finally {
      isSaving.value = false;
    }
  }
}
