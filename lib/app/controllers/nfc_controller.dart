import 'dart:io';

import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/nfc_repo.dart';
import 'package:v_card/app/data/model/nfc/nfc_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class NfcController extends GetxController {
  final NfcRepository nfcRepository;

  NfcController({required this.nfcRepository});

  // NFC Cards List
  final Rx<NfcCardListModel?> nfcCardsList = Rx<NfcCardListModel?>(null);
  final Rx<ApiState> nfcListState = ApiState.initial().obs;
  final RxBool isLoadingNfcList = false.obs;

  // NFC Card Details
  final Rx<NfcCardListModel?> nfcCardDetails = Rx<NfcCardListModel?>(null);
  final Rx<ApiState> nfcDetailsState = ApiState.initial().obs;
  final RxBool isLoadingNfcDetails = false.obs;

  // Order NFC List
  final Rx<OrderNfcModel?> orderNfcList = Rx<OrderNfcModel?>(null);
  final Rx<ApiState> orderNfcState = ApiState.initial().obs;
  final RxBool isLoadingOrderNfc = false.obs;

  // Vcard List
  final Rx<VcardListModel?> vcardList = Rx<VcardListModel?>(null);
  final Rx<ApiState> vcardListState = ApiState.initial().obs;

  // Payment Types
  final Rx<PaymentTypesModel?> paymentTypes = Rx<PaymentTypesModel?>(null);
  final Rx<ApiState> paymentTypesState = ApiState.initial().obs;

  // Form fields
  // final cardTypeController = TextEditingController();
  final quantityController = TextEditingController(text: '1');
  final companyNameController = TextEditingController();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final designationController = TextEditingController();
  final addressController = TextEditingController();

  final registerCountryCode = '91'.obs;
  final registerCountryflag = '🇮🇳'.obs;

  // New controllers for dropdown replacement
  final vcardController = TextEditingController();
  final paymentMethodController = TextEditingController();

  late RxBool isProcessingPWAIcon;
  final isShowingToast = false.obs;

  // Dropdown values
  final RxInt selectedVcardId = RxInt(-1);
  final RxInt selectedPaymentMethod = RxInt(-1);
  final Rx<File?> selectedLogo = Rx<File?>(null);

  // Validation
  final Rx<ApiState> createNfcOrderState = ApiState.initial().obs;
  final RxBool isLoadingcreateNfcOrder = false.obs;

  final Rx<CreateOrderNfcResponse?> createNfcOrderRsponse =
      Rx<CreateOrderNfcResponse?>(null);

  final TextEditingController searchController = TextEditingController();
  final FocusNode searchFocusNode = FocusNode();

  final RxBool isSearchActive = false.obs;
  final RxString searchText = ''.obs;

  void toggleSearchMode() {
    isSearchActive.value = !isSearchActive.value;
    if (isSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        searchFocusNode.requestFocus();
      });
    } else {
      searchController.clear();
      searchText.value = '';
    }
  }

  final TextEditingController searchNfcController = TextEditingController();
  final FocusNode searchNfcFocusNode = FocusNode();

  final RxBool isNfcSearchActive = false.obs;
  final RxString searchNfcText = ''.obs;

  void toggleNfcSearchMode() {
    isNfcSearchActive.value = !isNfcSearchActive.value;
    if (isNfcSearchActive.value) {
      Future.delayed(const Duration(milliseconds: 50), () {
        searchNfcFocusNode.requestFocus();
      });
    } else {
      searchNfcController.clear();
      searchNfcText.value = '';
    }
  }

  // In NfcController
  final RxString vcardSearchText = ''.obs;
  final TextEditingController searchVcadrController = TextEditingController();

  void showVcardBottomSheet(BuildContext context) {
    vcardSearchText.value = '';
    final vcards = vcardList.value?.data ?? {};

    Get.bottomSheet(
      Container(
        height: Get.height * 0.5,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Center(
              child: Container(
                margin: EdgeInsets.only(
                  left: 20.0.w,
                  right: 20.0.w,
                  top: 8.0.h,
                ),
                height: 5.0,
                width: 50.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  color: Get.theme.customColors.primaryColor?.withValues(
                    alpha: 0.6,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppStrings.T.lbl_select_vcard,
                      style: Get.theme.textTheme.bodyLarge,
                    ),
                    Gap(4.h),
                    // Add Search Field
                    Obx(
                      () => TextInputField(
                        onChanged: (value) => vcardSearchText.value = value,
                        hintLabel: AppStrings.T.lbl_search,
                        prefixIcon: CustomImageView(
                          imagePath: AssetConstants.icSearch,
                        ),
                        type: InputType.text,
                        controller: searchVcadrController,
                        suffixIcon:
                            vcardSearchText.value.isNotEmpty
                                ? IconButton(
                                  icon: Icon(
                                    Icons.close,
                                    color: Get.theme.customColors.greyTextColor,
                                  ),
                                  onPressed: () {
                                    searchVcadrController.clear();
                                    vcardSearchText.value = '';
                                  },
                                )
                                : null,
                      ),
                    ),
                    Gap(8.h),
                    Expanded(
                      child: Obx(() {
                        final searchQuery = vcardSearchText.value.toLowerCase();
                        final filteredEntries =
                            vcards.entries
                                .where(
                                  (entry) => entry.value.toLowerCase().contains(
                                    searchQuery,
                                  ),
                                )
                                .toList();

                        return ListView.separated(
                          itemCount: filteredEntries.length,
                          separatorBuilder: (_, __) => Divider(),
                          itemBuilder: (_, index) {
                            final entry = filteredEntries[index];
                            return ListTile(
                              title: Text(
                                entry.value,
                                style: Get.theme.textTheme.bodySmall,
                              ),
                              onTap: () {
                                selectedVcardId.value = int.parse(entry.key);
                                vcardController.text = entry.value;
                                NavigationService.navigateBack();
                              },
                            );
                          },
                        );
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),

      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
    );
  }

  void showPaymentMethodBottomSheet(BuildContext context) {
    final paymentTypesList = paymentTypes.value?.data ?? {};

    Get.bottomSheet(
      Container(
        height: Get.height * 0.5,
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.T.lbl_select_payment_method,
              style: Get.theme.textTheme.bodyLarge,
            ),
            Gap(4.h),
            Expanded(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: paymentTypesList.length,
                separatorBuilder: (context, index) => Divider(),
                itemBuilder: (context, index) {
                  String key = paymentTypesList.keys.elementAt(index);
                  String value = paymentTypesList[key]!;
                  return ListTile(
                    title: Text(value, style: Get.theme.textTheme.bodyLarge),
                    onTap: () {
                      paymentMethodController.text = value;
                      selectedPaymentMethod.value = int.parse(key);
                      NavigationService.navigateBack();
                      update(); // Add this to trigger UI update
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
    );
  }
  // void showPaymentMethodBottomSheet(BuildContext context) {
  //   final paymentTypesList = paymentTypes.value?.data ?? {};

  //   Get.bottomSheet(
  //     Container(
  //       height: Get.height * 0.5,
  //       padding: EdgeInsets.all(16.w),
  //       decoration: BoxDecoration(
  //         color: Colors.white,
  //         borderRadius: BorderRadius.only(
  //           topLeft: Radius.circular(20),
  //           topRight: Radius.circular(20),
  //         ),
  //       ),
  //       child: Column(
  //         mainAxisSize: MainAxisSize.min,
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           Text(
  //             AppStrings.T.lbl_select_payment_method,
  //             style: Get.theme.textTheme.bodyLarge,
  //           ),
  //           Gap(4.h),
  //           Expanded(
  //             child: ListView.separated(
  //               shrinkWrap: true,
  //               itemCount: paymentTypesList.length,
  //               separatorBuilder: (context, index) => Divider(),
  //               itemBuilder: (context, index) {
  //                 String key = paymentTypesList.keys.elementAt(index);
  //                 String value = paymentTypesList[key]!;
  //                 return ListTile(
  //                   title: Text(value, style: Get.theme.textTheme.bodyLarge),
  //                   onTap: () {
  //                     paymentMethodController.text = value;
  //                     selectedPaymentMethod.value = int.parse(key);
  //                     NavigationService.navigateBack();
  //                   },
  //                 );
  //               },
  //             ),
  //           ),
  //         ],
  //       ),
  //     ),
  //     isScrollControlled: true,
  //     shape: RoundedRectangleBorder(
  //       borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
  //     ),
  //   );
  // }

  Future<void> pickLogoFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
      );

      if (image != null) {
        selectedLogo.value = File(image.path);
      }
    } catch (e) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text(AppStrings.T.lbl_failed_to_pick_image),
        autoCloseDuration: const Duration(seconds: 3),
      );
    }
  }

  Future<void> getNfcCardsList() async {
    try {
      isLoadingNfcList.value = true;
      final response = await nfcRepository.getNfcCardsList();

      if (response.success ?? false) {
        nfcCardsList.value = response;
        nfcListState.value = SuccessState(response);
      } else {
        nfcListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      nfcListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingNfcList.value = false;
    }
  }

  Future<void> getNfcCardById(int id) async {
    try {
      isLoadingNfcDetails.value = true;
      final response = await nfcRepository.getNfcCardById(id);

      if (response.success ?? false) {
        nfcCardDetails.value = response;
        nfcDetailsState.value = SuccessState(response);
      } else {
        nfcDetailsState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      nfcDetailsState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingNfcDetails.value = false;
    }
  }

  Future<void> getOrderNfcList() async {
    try {
      isLoadingOrderNfc.value = true;
      final response = await nfcRepository.getOrderNfcList();

      if (response.success ?? false) {
        orderNfcList.value = response;
        orderNfcState.value = SuccessState(response);
      } else {
        orderNfcState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      orderNfcState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isLoadingOrderNfc.value = false;
    }
  }

  Future<void> getVcardList() async {
    try {
      final response = await nfcRepository.getVcardList();

      if (response.success ?? false) {
        vcardList.value = response;
        vcardListState.value = SuccessState(response);
      } else {
        vcardListState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      vcardListState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    }
  }

  Future<void> getPaymentTypes() async {
    try {
      final response = await nfcRepository.getPaymentTypes();

      if (response.success ?? false) {
        paymentTypes.value = response;
        paymentTypesState.value = SuccessState(response);
      } else {
        paymentTypesState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
      paymentTypesState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    }
  }

  Future<void> createNfcOrder(String nfcId) async {
    try {
      isLoadingcreateNfcOrder.value = true;

      Map<String, dynamic> formData = {
        'logo': [selectedLogo.value?.path],
        'vcard_id': selectedVcardId,
        'card_type': nfcId,
        'quantity': quantityController.text.trim(),
        'company_name': companyNameController.text.trim(),
        'name': nameController.text.trim(),
        'email': emailController.text.trim(),
        'phone': phoneController.text.trim(),
        'region_code': registerCountryCode.trim(),
        'designation': designationController.text,
        'address': addressController.text,
        'payment_method': selectedPaymentMethod,
      };

      var response = await nfcRepository.orderNfcCard(data: formData);

      if (response.success) {
        createNfcOrderRsponse.value = response;
        createNfcOrderState.value = SuccessState(response);
        NavigationService.navigateBack();
      } else {
        createNfcOrderState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      Logger.log(e.toString());
    } finally {
      isLoadingcreateNfcOrder.value = false;
    }
  }

  void navigateToOrderNfcPage() {
    NavigationService.navigateWithSlideAnimation(AppRoutes.orderNfcPage);
  }
}
