import 'package:get/get.dart';
import 'package:injectable/injectable.dart' as i;
import 'package:v_card/app/data/apis/repository/iframe_repo.dart';
import 'package:v_card/app/data/model/iframe/iframe_detail_model.dart';
import 'package:v_card/app/data/model/iframe/iframe_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

@i.lazySingleton
@i.injectable
class IframeController extends GetxController {
  final IframeRepository iframeRepository;

  IframeController({required this.iframeRepository});

  // State variables
  final Rx<IframeModel?> iframeData = Rx<IframeModel?>(null);
  final Rx<IframeDetailModel?> iframDataById = Rx<IframeDetailModel?>(null);
  final Rx<IframeCreateResponse?> createDataresponse =
      Rx<IframeCreateResponse?>(null);
  final Rx<ApiState> iframeState = ApiState.initial().obs;
  final Rx<ApiState> iframeCreateState = ApiState.initial().obs;
  final RxBool isLoadingIframe = false.obs;
  final RxBool isLoadingIframeById = false.obs;
  final RxBool isSubmittingIframe = false.obs;
  final RxBool isDeletingIframe = false.obs;

  // Form controllers
  final TextEditingController iframeUrlController = TextEditingController();


  @override
  void onClose() {
    iframeUrlController.dispose();
    super.onClose();
  }

  // Reset form fields
  void resetForm() {
    iframeUrlController.clear();
  }

  // Get Instagram embeds
  Future<void> getIframeList(String vcardId) async {
    try {
      isLoadingIframe.value = true;
      var response = await iframeRepository.getIframeRepo(vcardId);

      if (response.success) {
        iframeData.value = response;
        iframeState.value = SuccessState(response);
      } else {
        iframeState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      iframeState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'Error Loading Embeds',
          'Failed to load Instagram embeds.',
        ),
      );
    } finally {
      isLoadingIframe.value = false;
    }
  }

  Future<void> getIframeById(String embedId) async {
    try {
      isLoadingIframeById.value = true;
      var response = await iframeRepository.getIframeByIdRepo(embedId);

      if (response.success) {
        iframDataById.value = response;
        iframeState.value = SuccessState(response);
      } else {
        iframeState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      iframeState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          'Error Loading Iframe',
          'Failed to load Iframe.',
        ),
      );
    } finally {
      isLoadingIframeById.value = false;
    }
  }

  // Create or update Instagram embed
  Future<void> createIframe(String vcardId) async {
    try {
      isSubmittingIframe.value = true;

      final processedUrl = processProductUrl(
        iframeUrlController.text.trim().toLowerCase(),
      );

      final Map<String, dynamic> data = {
        "url": processedUrl,
        "vcard_id": vcardId,
      };

      var response = await iframeRepository.createIframeRepo(data: data);

      if (response.success) {
        createDataresponse.value = response;
        iframeCreateState.value = SuccessState(response);

        if (isCardCreated) {
          getIframeList(vcardId);
          NavigationService.navigateBack();
        } else {
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.vcardAppointmentsPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        }

        resetForm();
      } else {
        iframeCreateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      iframeCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isSubmittingIframe.value = false;
    }
  }

  Future<void> updateIframe({
    required String embedId,
    required String vcardId,
  }) async {
    try {
      isSubmittingIframe.value = true;

      final processedUrl = processProductUrl(
        iframeUrlController.text.trim().toLowerCase(),
      );

      final Map<String, dynamic> data = {"url": processedUrl};

      var response = await iframeRepository.updateIframeRepo(
        data: data,
        iframeId: embedId,
      );

      if (response.success) {
        createDataresponse.value = response;
        iframeCreateState.value = SuccessState(response);
        NavigationService.navigateBack();
        getIframeList(vcardId);
        resetForm();
      } else {
        iframeCreateState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
      }
    } catch (e) {
      iframeCreateState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
    } finally {
      isSubmittingIframe.value = false;
    }
  }

  // Delete Instagram embed
  Future<bool> deleteIframe({
    required String embedId,
    required String vcardId,
  }) async {
    try {
      isDeletingIframe.value = true;

      var response = await iframeRepository.deleteIframeRepo(embedId);

      if (response.success) {
        iframeState.value = SuccessState(response);
        getIframeList(vcardId);
        NavigationService.navigateBack();
        return true;
      } else {
        iframeState.value = FailedState(
          statusCode: 0,
          isRetirable: false,
          error: UserFriendlyError(
            AppStrings.T.apiError,
            AppStrings.T.apiErrorDescription,
          ),
        );
        return false;
      }
    } catch (e) {
      iframeState.value = FailedState(
        statusCode: 0,
        isRetirable: false,
        error: UserFriendlyError(
          AppStrings.T.apiError,
          AppStrings.T.apiErrorDescription,
        ),
      );
      return false;
    } finally {
      isDeletingIframe.value = false;
    }
  }
}
