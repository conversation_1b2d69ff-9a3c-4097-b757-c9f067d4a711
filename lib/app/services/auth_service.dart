import 'dart:developer';
import 'package:google_sign_in/google_sign_in.dart';

class AuthService {
  AuthService._privateConstructor();
  static final AuthService instance = AuthService._privateConstructor();

  final GoogleSignIn googleSignIn = GoogleSignIn(scopes: ['email']);

  Future<GoogleUser?> signInWithGoogle() async {
    try {
      GoogleSignInAccount? googleSignInAccount = await googleSignIn.signIn();
      if (googleSignInAccount != null) {
        GoogleSignInAuthentication googleSignInAuthentication = await googleSignInAccount.authentication;

        log('email --> ${googleSignInAccount.email}');
        log('displayName --> ${googleSignInAccount.displayName}');
        log('accessToken --> ${googleSignInAuthentication.accessToken}');
        log('idToken --> ${googleSignInAuthentication.idToken}');

        return GoogleUser(email: googleSignInAccount.email, displayName: googleSignInAccount.displayName);
      }
    } catch (e) {
      log('Catch error in Verify User : $e');
    }
    return null;
  }

  // Future<String?> signInWithGoogle() async {
  //   try {
  //     GoogleSignInAccount? googleSignInAccount = await googleSignIn.signIn();
  //     if (googleSignInAccount != null) {
  //       GoogleSignInAuthentication googleSignInAuthentication =
  //           await googleSignInAccount.authentication;

  //       getIt<SharedPreferences>().setEmail = googleSignInAccount.email;

  //       log('email --> ${googleSignInAccount.email}');
  //       log('accessToken --> ${googleSignInAuthentication.accessToken}');
  //       log('idToken --> ${googleSignInAuthentication.idToken}');

  //       return googleSignInAuthentication.accessToken;
  //     } else {
  //       return null;
  //     }
  //   } catch (e) {
  //     log('Catch error in Verify User : $e');
  //     return null;
  //   }
  // }

  Future<void> signOutGoogle() async {
    try {
      await googleSignIn.signOut();
    } catch (e) {
      log('Error signing out from Google: $e');
    }
  }
}

class GoogleUser {
  final String email;
  final String? displayName;

  GoogleUser({required this.email, this.displayName});
}
