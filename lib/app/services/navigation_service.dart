import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NavigationService {
  static void navigateBack<T>({T? result}) {
    Get.back(result: result);
  }

  static Future<T?>? navigateWithSlideAnimation<T>(
    String route, {
    dynamic arguments,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return Get.to<T>(
      () => _buildPage(route, arguments),
      arguments: arguments,
      duration: duration,
      transition: Transition.rightToLeftWithFade,
      curve: Curves.easeInOut,
      routeName: route,
      preventDuplicates: false,
    );
  }

  static Future<T?>? navigateWithFadeAnimation<T>(
    String route, {
    dynamic arguments,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return Get.to<T>(
      () => _buildPage(route, arguments),
      arguments: arguments,
      duration: duration,
      transition: Transition.fade,
      curve: Curves.easeInOut,
      routeName: route,
      preventDuplicates: false,
    );
  }

  static Future<T?>? navigateWithScaleAnimation<T>(
    String route, {
    dynamic arguments,
    Duration duration = const Duration(milliseconds: 1000),
  }) {
    return Get.to<T>(
      () => _buildPage(route, arguments),
      arguments: arguments,
      duration: duration,
      transition: Transition.zoom,
      curve: Curves.easeInOut,
      routeName: route,
      preventDuplicates: false,
    );
  }

  static Widget _buildPage(String route, dynamic arguments) {
    final routeMatch = Get.routeTree.matchRoute(route);
    final page = routeMatch.route?.page;

    if (page != null) {
      return page();
    }

    return Scaffold(
      appBar: AppBar(
        leading: BackButton(onPressed: () => NavigationService.navigateBack()),
      ),
      body: Center(child: Text('Route not found: $route')),
    );
  }
}
