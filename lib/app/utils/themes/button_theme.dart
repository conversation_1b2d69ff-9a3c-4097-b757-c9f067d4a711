import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ButtonThemeHelper {
  // Primary Button Theme
  static ButtonStyle primaryButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.r)),
      padding: EdgeInsets.zero,
      textStyle: Theme.of(
        context,
      ).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w700),
    );
  }

  // Secondary Button Theme
  static ButtonStyle secondaryButtonStyle(BuildContext context) {
    return ElevatedButton.styleFrom(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15.r),
        side: BorderSide(
          color: Get.theme.customColors.primaryColor!,
          width: 1.w,
        ),
      ),
      padding: EdgeInsets.zero,
      elevation: 0,
      backgroundColor: Get.theme.customColors.white,
      overlayColor: Colors.transparent,
      foregroundColor: Colors.transparent,
      shadowColor: Colors.transparent,
    );
  }

  
}
