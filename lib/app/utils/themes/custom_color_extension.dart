import 'package:flutter/material.dart';

@immutable
class CustomColors extends ThemeExtension<CustomColors> {
  const CustomColors({
    required this.greyTextColor,
    required this.darkGreyTextColor,
    required this.textfieldFillColor,
    required this.lightGreyBackgroundColor,
    required this.transparent,
    required this.red,
    required this.black,
    required this.white,
    required this.blackColor,
    required this.primaryColor,
    required this.secondaryColor,
    required this.bgOneColor,
    required this.bgTwoColor,
    required this.chatbgcolor,
    required this.textblcolor,
    required this.navigationbarcolor,
    required this.dividercolor,
    required this.blueColor,
    required this.redColor,
    required this.darkColor,
    required this.greenColor,
    required this.shadowColor,
    required this.borderColor,
    required this.darkBlueColor,
    required this.greyBorderColor,
  });

  final Color? greyTextColor;
  final Color? darkGreyTextColor;
  final Color? textfieldFillColor;
  final Color? lightGreyBackgroundColor;
  final Color? transparent;
  final Color? red;
  final Color? black;
  final Color? white;
  final Color? blackColor;
  final Color? primaryColor;
  final Color? secondaryColor;
  final Color? bgOneColor;
  final Color? bgTwoColor;
  final Color? chatbgcolor;
  final Color? textblcolor;
  final Color? navigationbarcolor;
  final Color? dividercolor;
  final Color? blueColor;
  final Color? redColor;
  final Color? darkColor;
  final Color? greenColor;
  final Color? shadowColor;
  final Color? borderColor;
  final Color? darkBlueColor;
  final Color? greyBorderColor;

  static const light = CustomColors(
    primaryColor: Color(0xffFF7731),
    secondaryColor: Color(0xff0F3D4D),
    greyTextColor: Color(0xFF595959),
    darkGreyTextColor: Color(0xFF333333),
    textfieldFillColor: Color(0xFFF7F7F7),
    lightGreyBackgroundColor: Color(0xFFE7E9EB),
    transparent: Colors.transparent,
    red: Color(0xffFF7B7B),
    black: Colors.black,
    white: Colors.white,
    blackColor: Colors.black,
    bgOneColor: Colors.white,
    bgTwoColor: Color(0xffF8F9FA),
    chatbgcolor: Color(0xffF8F6FD),
    textblcolor: Color(0xff6A707C),
    navigationbarcolor: Color(0xff6444FF),
    dividercolor: Color(0xffB6B6B6),
    blueColor: Color(0xff1F69F6),
    redColor: Color(0xffF35050),
    darkColor: Color(0xff333237),
    greenColor: Color(0xff00B96D),
    shadowColor: Color(0xFF063336),
    borderColor: Color(0xFFE8ECF4),
    darkBlueColor: Color(0xFF0601B4),
    greyBorderColor: Color(0xFF979797),
  );

  static const dark = CustomColors(
    primaryColor: Color(0xffFF7731),
    secondaryColor: Color(0xff0F3D4D),
    greyTextColor: Color(0xFF595959),
    darkGreyTextColor: Color(0xFF333333),
    textfieldFillColor: Color(0xFFF7F7F7),
    lightGreyBackgroundColor: Color(0xFFE7E9EB),
    transparent: Colors.transparent,
    red: Color(0xffFF7B7B),
    black: Colors.black,
    white: Colors.white,
    blackColor: Colors.black,
    bgOneColor: Colors.black,
    bgTwoColor: Color(0xffF8F9FA),
    chatbgcolor: Color(0xffF8F6FD),
    textblcolor: Color(0xff6A707C),
    navigationbarcolor: Color(0xff6444FF),
    dividercolor: Color(0xffB6B6B6),
    blueColor: Color(0xff1F69F6),
    redColor: Color(0xffF35050),
    darkColor: Color(0xff333237),
    greenColor: Color(0xff00B96D),
    shadowColor: Color(0xFF063336),
    borderColor: Color(0xFFE8ECF4),
    darkBlueColor: Color(0xFF0601B4),
    greyBorderColor: Color(0xFF979797),
  );

  @override
  CustomColors copyWith({
    Color? greyTextColor,
    Color? darkGreyTextColor,
    Color? textfieldFillColor,
    Color? lightGreyBackgroundColor,
    Color? transparent,
    Color? red,
    Color? black,
    Color? white,
    Color? blackColor,
    Color? primaryColor,
    Color? secondaryColor,
    Color? bgOneColor,
    Color? bgTwoColor,
    Color? chatbgcolor,
    Color? textblcolor,
    Color? navigationbarcolor,
    Color? dividercolor,
    Color? blueColor,
    Color? redColor,
    Color? darkColor,
    Color? greenColor,
    Color? shadowColor,
    Color? borderColor,
    Color? darkBlueColor,
    Color? greyBorderColor,
  }) {
    return CustomColors(
      greyTextColor: greyTextColor ?? this.greyTextColor,
      darkGreyTextColor: darkGreyTextColor ?? this.darkGreyTextColor,
      textfieldFillColor: textfieldFillColor ?? this.textfieldFillColor,
      lightGreyBackgroundColor: lightGreyBackgroundColor ?? this.lightGreyBackgroundColor,
      transparent: transparent ?? this.transparent,
      red: red ?? this.red,
      black: black ?? this.black,
      white: white ?? this.white,
      blackColor: blackColor ?? this.blackColor,
      primaryColor: primaryColor ?? this.primaryColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      bgOneColor: bgOneColor ?? this.bgOneColor,
      bgTwoColor: bgTwoColor ?? this.bgTwoColor,
      chatbgcolor: chatbgcolor ?? this.chatbgcolor,
      textblcolor: textblcolor ?? this.textblcolor,
      navigationbarcolor: navigationbarcolor ?? this.navigationbarcolor,
      dividercolor: dividercolor ?? this.dividercolor,
      blueColor: blueColor ?? this.blueColor,
      redColor: redColor ?? this.redColor,
      darkColor: darkColor ?? this.darkColor,
      greenColor: greenColor ?? this.greenColor,
      shadowColor: shadowColor ?? this.shadowColor,
      borderColor: borderColor ?? this.borderColor,
      darkBlueColor: darkBlueColor ?? this.darkBlueColor,
      greyBorderColor: greyBorderColor ?? this.greyBorderColor,
    );
  }

  @override
  CustomColors lerp(ThemeExtension<CustomColors>? other, double t) {
    if (other is! CustomColors) return this;
    return CustomColors(
      greyTextColor: Color.lerp(greyTextColor, other.greyTextColor, t),
      darkGreyTextColor: Color.lerp(darkGreyTextColor, other.darkGreyTextColor, t),
      textfieldFillColor: Color.lerp(textfieldFillColor, other.textfieldFillColor, t),
      lightGreyBackgroundColor: Color.lerp(lightGreyBackgroundColor, other.lightGreyBackgroundColor, t),
      transparent: Color.lerp(transparent, other.transparent, t),
      red: Color.lerp(red, other.red, t),
      black: Color.lerp(black, other.black, t),
      white: Color.lerp(white, other.white, t),
      blackColor: Color.lerp(blackColor, other.blackColor, t),
      primaryColor: Color.lerp(primaryColor, other.primaryColor, t),
      secondaryColor: Color.lerp(secondaryColor, other.secondaryColor, t),
      bgOneColor: Color.lerp(bgOneColor, other.bgOneColor, t),
      bgTwoColor: Color.lerp(bgTwoColor, other.bgTwoColor, t),
      chatbgcolor: Color.lerp(chatbgcolor, other.chatbgcolor, t),
      textblcolor: Color.lerp(textblcolor, other.textblcolor, t),
      navigationbarcolor: Color.lerp(navigationbarcolor, other.navigationbarcolor, t),
      dividercolor: Color.lerp(dividercolor, other.dividercolor, t),
      blueColor: Color.lerp(blueColor, other.blueColor, t),
      redColor: Color.lerp(redColor, other.redColor, t),
      darkColor: Color.lerp(darkColor, other.darkColor, t),
      greenColor: Color.lerp(greenColor, other.greenColor, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      darkBlueColor: Color.lerp(darkBlueColor, other.darkBlueColor, t),
      greyBorderColor: Color.lerp(greyBorderColor, other.greyBorderColor, t),
    );
  }
}

extension ThemeDataCustomColors on ThemeData {
  CustomColors get customColors {
    final customColors = extension<CustomColors>();
    if (customColors == null) {
      return CustomColors.light;
    }
    return customColors;
  }
}
