import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class TextFieldThemeHelper {
  static InputDecoration borderless({required String hintText}) {
    return InputDecoration(
      hintText: hintText,
      hintStyle: Get.textTheme.bodyMedium?.copyWith(
        fontSize: 14.sp,
        color: Get.theme.customColors.greyTextColor,
        fontWeight: FontWeight.w700,
      ),
      labelStyle: Get.textTheme.bodyMedium?.copyWith(
        fontSize: 14.sp,
        color: Get.theme.customColors.greyTextColor,
        fontWeight: FontWeight.w700,
      ),
      floatingLabelStyle: Get.textTheme.bodyMedium?.copyWith(
        fontSize: 14.sp,
        color: Get.theme.customColors.greyTextColor,
        fontWeight: FontWeight.w700,
      ),
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      fillColor: Colors.transparent,
      filled: true,
      contentPadding: EdgeInsets.symmetric(vertical: 14.0, horizontal: 12.0),
    );
  }

  static InputDecoration borderlessWithLabel({required String labelText, Widget? suffixIcon}) {
    return InputDecoration(
      labelText: labelText,
      labelStyle: Get.textTheme.bodyMedium?.copyWith(
        fontSize: 14.sp,
        color: Get.theme.customColors.greyTextColor,
        fontWeight: FontWeight.w700,
      ),
      floatingLabelStyle: Get.textTheme.bodyMedium?.copyWith(
        fontSize: 14.sp,
        color: Get.theme.customColors.greyTextColor,
        fontWeight: FontWeight.w700,
      ),
      border: InputBorder.none,
      enabledBorder: InputBorder.none,
      focusedBorder: InputBorder.none,
      fillColor: Colors.transparent,
      filled: true,
      suffixIcon: suffixIcon,
      contentPadding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
    );
  }

  static InputDecoration dropDownInputDecoration({required String labelText}) {
    final theme = Get.theme;
    final customColors = theme.customColors;

    return InputDecoration(
      labelText: labelText,
      labelStyle: theme.textTheme.bodyMedium?.copyWith(
        fontSize: 14.sp,
        color: customColors.greyTextColor,
        fontWeight: FontWeight.w700,
      ),
      border: OutlineInputBorder(),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0.r),
        borderSide: BorderSide(color: customColors.greyBorderColor!),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0.r),
        borderSide: BorderSide(color: customColors.primaryColor!),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0.r),
        borderSide: BorderSide(color: customColors.redColor!),
      ),
      disabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0.r),
        borderSide: BorderSide(color: customColors.greyBorderColor!),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(10.0.r),
        borderSide: BorderSide(color: customColors.redColor!),
      ),
    );
  }

 
}
