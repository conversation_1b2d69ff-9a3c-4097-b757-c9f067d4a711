// TextTheme textTheme = Get.theme.textTheme;
// ColorScheme colorScheme = Get.theme.colorScheme;

import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class AppTheme {
  AppTheme._();

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme(
      brightness: Brightness.light,
      primary: const Color(0xffFF7731),
      onPrimary: const Color(0xffffffff),
      primaryContainer: Get.theme.customColors.bgOneColor,
      secondary: const Color(0xff0F3D4D),
      onSecondary: const Color(0xff04163C),
      secondaryContainer: Get.theme.customColors.greyTextColor,
      error: const Color(0xffFF7B7B),
      onError: const Color(0xffFF7B7B),
      surface: const Color(0xffF8F9FA),
      onSurface: const Color(0xffFF7731),
      outline: Get.theme.customColors.primaryColor,
    ),
    extensions: const <ThemeExtension<dynamic>>[CustomColors.light],
    dividerTheme: const DividerThemeData(
      color: Color(0xffF5F5F8),
      thickness: 1,
    ),
    scaffoldBackgroundColor: Get.theme.customColors.white,
    datePickerTheme: DatePickerThemeData(
      cancelButtonStyle: ButtonStyle(
        textStyle: WidgetStatePropertyAll(
          TextStyle(
            fontFamily: 'Inter',
            color: Get.theme.customColors.primaryColor,
            fontSize: 15.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      confirmButtonStyle: ButtonStyle(
        textStyle: WidgetStatePropertyAll(
          TextStyle(
            fontFamily: 'Inter',
            color: Get.theme.customColors.primaryColor,
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      headerHelpStyle: TextStyle(
        fontFamily: 'Inter',
        color: const Color(0XFF252520),
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
      ),
      headerHeadlineStyle: TextStyle(
        fontFamily: 'Inter',
        color: const Color(0XFF252520),
        fontSize: 24.sp,
        fontWeight: FontWeight.w500,
      ),
      weekdayStyle: TextStyle(
        fontFamily: 'Inter',
        color: const Color(0XFF252520),
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
      ),
      dayStyle: TextStyle(
        fontFamily: 'Inter',
        color: const Color(0XFF252520),
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        disabledBackgroundColor: const Color(0xffFF7731).withValues(alpha: 0.5),
        backgroundColor: const Color(0xffFF7731),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
      ),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Get.theme.customColors.white; // Active thumb color
        }
        return Get.theme.customColors.red; // Inactive thumb color
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Get.theme.customColors.greyTextColor?.withValues(alpha: 0.5);
        }
        return Get.theme.customColors.red?.withValues(alpha: 0.5);
      }),
      thumbIcon: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Icon(Icons.done, color: Get.theme.customColors.primaryColor);
        }
        return null;
      }),
    ),

    /// Whenever your use the AppBar make sure most of the scenario your AppBar theme is must be sat here.
    appBarTheme: AppBarTheme(
      elevation: 0,
      color: Get.theme.customColors.white,
      surfaceTintColor: Get.theme.customColors.transparent,
      titleTextStyle: TextStyle(
        fontFamily: 'inter',
        fontSize: 20.sp,
        fontWeight: FontWeight.w700,
        color: Get.theme.customColors.black,
      ),
      iconTheme: IconThemeData(color: Get.theme.customColors.white),
    ),

    /// If you app supports a single FontFamily, So this is the best way to change FontFamily for allover the app.
    fontFamily: 'inter',
    checkboxTheme: CheckboxThemeData(
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      visualDensity: VisualDensity.compact,
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Get.theme.customColors.primaryColor;
        }
        return Get.theme.customColors.white;
      }),
    ),

    /// InputDecorationTheme is used for make you TextFormField, DropDownFormField and many more widget.
    /// Those Widget Which is depended on InputDecorationTheme.
    inputDecorationTheme: InputDecorationTheme(
      floatingLabelBehavior: FloatingLabelBehavior.auto,
      floatingLabelAlignment: FloatingLabelAlignment.start,
      border: UnderlineInputBorder(
        borderSide: const BorderSide(color: Color(0xff000000), width: 1),
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: const BorderSide(color: Color(0xff000000), width: 1),
      ),
      errorMaxLines: 2,
      focusedBorder: UnderlineInputBorder(
        borderSide: const BorderSide(color: Color(0xffFFBA99), width: 2),
      ),
      errorBorder: UnderlineInputBorder(
        borderSide: const BorderSide(color: Colors.redAccent, width: 2),
      ),
      contentPadding: const EdgeInsets.all(16),
      errorStyle: TextStyle(
        color: Get.theme.customColors.red,
        fontSize: 12.sp,
        fontWeight: FontWeight.w600,
      ),

      hintStyle: WidgetStateTextStyle.resolveWith((states) {
        if (states.contains(WidgetState.error)) {
          return Get.theme.textTheme.bodyMedium!.copyWith(
            fontSize: 14.sp,
            color: Get.theme.customColors.greyTextColor,
            fontWeight: FontWeight.w700,
          );
        }
        return Get.theme.textTheme.bodyMedium!.copyWith(
          fontSize: 14.sp,
          color: Get.theme.customColors.greyTextColor,
          fontWeight: FontWeight.w700,
        );
      }),
      suffixIconColor: Get.theme.customColors.primaryColor?.withValues(alpha: 0.7),
    ),
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: Get.theme.customColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
    ),
    dividerColor: Get.theme.customColors.bgOneColor,
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: Get.theme.customColors.primaryColor,
    ),
    // textTheme: TextTheme(
    //   bodyLarge: TextStyle(
    //     fontFamily: GoogleFonts.poppins().fontFamily,
    //     fontSize: 16,
    //     fontWeight: FontWeight.w500,
    //     color: const Color(0xff333333),
    //   ),
    //   displayLarge: TextStyle(
    //     fontFamily: GoogleFonts.rubik().fontFamily,
    //     fontSize: 20,
    //     fontWeight: FontWeight.w500,
    //     color: const Color(0xffFF800B),
    //   ),
    // ),
    textTheme: TextTheme(
      displayLarge: GoogleFonts.rubik(
        fontSize: 32.sp,
        fontWeight: FontWeight.w700,
        color: const Color(0xffFF800B),
      ),
      displayMedium: GoogleFonts.rubik(
        fontSize: 28.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xffFF800B),
      ),
      displaySmall: GoogleFonts.rubik(
        fontSize: 24.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xffFF800B),
      ),
      headlineLarge: GoogleFonts.poppins(
        fontSize: 22.sp,
        fontWeight: FontWeight.w700,
        color: const Color(0xff333333),
      ),
      headlineMedium: GoogleFonts.poppins(
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xff333333),
      ),
      headlineSmall: GoogleFonts.poppins(
        fontSize: 18.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      titleLarge: GoogleFonts.poppins(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xff333333),
      ),
      titleMedium: GoogleFonts.poppins(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      titleSmall: GoogleFonts.poppins(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
      bodyLarge: GoogleFonts.poppins(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      bodyMedium: GoogleFonts.poppins(
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
      bodySmall: GoogleFonts.poppins(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
      labelLarge: GoogleFonts.poppins(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xff333333),
      ),
      labelMedium: GoogleFonts.poppins(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      labelSmall: GoogleFonts.poppins(
        fontSize: 10.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme(
      brightness: Brightness.dark,
      primary: const Color(0xffFF7731),
      onPrimary: const Color(0xffffffff),
      primaryContainer: Get.theme.customColors.bgOneColor,
      secondary: const Color(0xff0F3D4D),
      onSecondary: const Color(0xff04163C),
      secondaryContainer: Get.theme.customColors.greyTextColor,
      error: const Color(0xffFF7B7B),
      onError: const Color(0xffFF7B7B),
      surface: const Color(0xffF8F9FA),
      onSurface: const Color(0xffFF7731),
      outline: Get.theme.customColors.primaryColor,
    ),
    extensions: const <ThemeExtension<dynamic>>[CustomColors.dark],
    scaffoldBackgroundColor: Get.theme.customColors.black,
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        disabledBackgroundColor: const Color(0xffFF7731).withValues(alpha: 0.5),
        backgroundColor: const Color(0xffFF7731),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.r),
        ),
      ),
    ),
    switchTheme: SwitchThemeData(
      thumbColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Get.theme.customColors.white; // Active thumb color
        }
        return Get.theme.customColors.red; // Inactive thumb color
      }),
      trackColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Get.theme.customColors.greyTextColor?.withValues(alpha: 0.5);
        }
        return Get.theme.customColors.red?.withValues(alpha: 0.5);
      }),
      thumbIcon: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Icon(Icons.done, color: Get.theme.customColors.primaryColor);
        }
        return null;
      }),
    ),
    appBarTheme: AppBarTheme(
      elevation: 0,
      color: Get.theme.customColors.primaryColor,
      titleTextStyle: TextStyle(
        fontFamily: 'inter',
        fontSize: 24.sp,
        fontWeight: FontWeight.w600,
        color: Get.theme.customColors.white,
      ),
      iconTheme: IconThemeData(color: Get.theme.customColors.black),
    ),
    fontFamily: 'inter',
    checkboxTheme: CheckboxThemeData(
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
      visualDensity: VisualDensity.compact,
      fillColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.selected)) {
          return Get.theme.customColors.primaryColor;
        }
        return Get.theme.customColors.black;
      }),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Get.theme.customColors.textfieldFillColor,
      border: OutlineInputBorder(
        borderSide: BorderSide.none,
        borderRadius: BorderRadius.circular(12.r),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide.none,
        borderRadius: BorderRadius.circular(12.r),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: const BorderSide(color: Color(0xffFF7B7B)),
      ),
      contentPadding: const EdgeInsets.all(16),
      errorStyle: TextStyle(
        color: Get.theme.customColors.red,
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
      ),
      hintStyle: WidgetStateTextStyle.resolveWith((states) {
        if (states.contains(WidgetState.error)) {
          return TextStyle(
            color: Get.theme.customColors.red,
            fontSize: 18.sp,
            fontWeight: FontWeight.w400,
          );
        }
        return TextStyle(
          color: Get.theme.customColors.greyTextColor,
          fontSize: 18.sp,
          fontWeight: FontWeight.w400,
        );
      }),
      floatingLabelBehavior: FloatingLabelBehavior.auto,
    ),
    bottomSheetTheme: BottomSheetThemeData(
      backgroundColor: Get.theme.customColors.black,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
    ),
    dividerColor: Get.theme.customColors.bgOneColor,
    progressIndicatorTheme: ProgressIndicatorThemeData(
      color: Get.theme.customColors.primaryColor,
    ),
    textTheme: TextTheme(
      displayLarge: GoogleFonts.rubik(
        fontSize: 32.sp,
        fontWeight: FontWeight.w700,
        color: const Color(0xffFF800B),
      ),
      displayMedium: GoogleFonts.rubik(
        fontSize: 28.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xffFF800B),
      ),
      displaySmall: GoogleFonts.rubik(
        fontSize: 24.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xffFF800B),
      ),
      headlineLarge: GoogleFonts.poppins(
        fontSize: 22.sp,
        fontWeight: FontWeight.w700,
        color: const Color(0xff333333),
      ),
      headlineMedium: GoogleFonts.poppins(
        fontSize: 20.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xff333333),
      ),
      headlineSmall: GoogleFonts.poppins(
        fontSize: 18.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      titleLarge: GoogleFonts.poppins(
        fontSize: 16.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xff333333),
      ),
      titleMedium: GoogleFonts.poppins(
        fontSize: 14.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      titleSmall: GoogleFonts.poppins(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
      bodyLarge: GoogleFonts.poppins(
        fontSize: 16.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      bodyMedium: GoogleFonts.poppins(
        fontSize: 14.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
      bodySmall: GoogleFonts.poppins(
        fontSize: 12.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
      labelLarge: GoogleFonts.poppins(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: const Color(0xff333333),
      ),
      labelMedium: GoogleFonts.poppins(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
        color: const Color(0xff333333),
      ),
      labelSmall: GoogleFonts.poppins(
        fontSize: 10.sp,
        fontWeight: FontWeight.w400,
        color: const Color(0xff333333),
      ),
    ),
  );
}
