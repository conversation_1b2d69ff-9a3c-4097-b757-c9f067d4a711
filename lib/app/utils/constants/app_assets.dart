import 'package:v_card/app/utils/helpers/generated/assets.gen.dart';

class AssetConstants {
  //images/svg/other/
  // static String svgSplash = Assets.images.png.other.pngAppLogo.path;
  static String paymentPending = Assets.images.svg.other.paymentPending.path;
  // static String svgSplash = Assets.images.svg.other.svgSplash.path;

  //images/svg/onboarding/
  static String svgWlcomeBg = Assets.images.svg.onboarding.welcomeBg.path;
  static String svgWlcomeNextButton =
      Assets.images.svg.onboarding.welcomeNext.path;
  static String svgOnboardingBg =
      Assets.images.svg.onboarding.onboardingBg.path;
  static String svgWelcome = Assets.images.svg.onboarding.svgWelcome.path;

  //images/png/other/ 
  static String pngSplash = Assets.images.png.other.pngAppLogo.path;
  static String imageNotFound = Assets.images.png.other.imageNotFound.path;
  static String pngAppLogoWhite = Assets.images.png.other.pngAppLogoWhite.path;
  static String pngWelcome = Assets.images.png.other.pngWelcome.path;
  static String pngInsta = Assets.images.png.other.pngInsta.path;
  static String ob1 = Assets.images.png.other.ob1.path;
  static String ob2 = Assets.images.png.other.ob2.path;
  static String ob3 = Assets.images.png.other.ob3.path;
  static String ob4 = Assets.images.png.other.ob4.path;
  static String noData = Assets.images.png.other.noData.path;
  static String noInternet = Assets.images.png.other.noInternet.path;
  static String nfcInfo = Assets.images.png.other.nfcInfo.path;

  //images/png/icon/
  static String icMessage = Assets.images.png.icons.icMessage.path;

  //images/icon/
  static String icAppbarBack = Assets.images.icon.icAppbarBack.path;
  static String icClose = Assets.images.icon.icClose.path;
  static String icAppbarLeadingIcon =
      Assets.images.icon.icAppbarLeadingIcon.path;
  static String icAppbaTraillingIconSvg =
      Assets.images.icon.icAppbaTraillingIconSvg.path;
  static String icRightArrow = Assets.images.icon.icRightArrow.path;
  static String icRightArrow2 = Assets.images.icon.icRightArrow2.path;
  static String icDownArrow = Assets.images.icon.icDownArrow.path;
  static String icDownArrow2 = Assets.images.icon.icDownArrow2.path;
  static String icUpArrow = Assets.images.icon.icUpArrow.path;
  static String icQrCode = Assets.images.icon.icQrCode.path;
  static String icAppointment = Assets.images.icon.icAppointment.path;
  static String icEnquiries = Assets.images.icon.icEnquiries.path;
  static String icDelete = Assets.images.icon.icDelete.path;
  static String icAppLogo = Assets.images.icon.icAppLogo.path;
  static String icDashboard = Assets.images.icon.icDashboard.path;
  static String icVcard = Assets.images.icon.icVcard.path;
  static String icEnquiries2 = Assets.images.icon.icEnquiries2.path;
  static String icAppointment2 = Assets.images.icon.icAppointment2.path;
  static String icBusinessCard = Assets.images.icon.icBusinessCard.path;
  static String icSetting = Assets.images.icon.icSetting.path;
  static String icLogout = Assets.images.icon.icLogout.path;
  static String icProfile = Assets.images.icon.icProfile.path;
  static String icPerson = Assets.images.icon.icPerson.path;
  static String icCopy = Assets.images.icon.icCopy.path;
  // static String icMessage = Assets.images.icon.icMessage.path;
  static String icPrivacyPolicy = Assets.images.icon.icPrivacyPolicy.path;
  static String icLanguage3 = Assets.images.icon.icLanguage3.path;
  static String icCamera = Assets.images.icon.icCamera.path;
  static String icTotalActiveVcard = Assets.images.icon.icTotalActiveVcard.path;
  static String icTotalDeactiveVcard =
      Assets.images.icon.icTotalDeactiveVcard.path;
  static String icTotalEnquires = Assets.images.icon.icTotalEnquires.path;
  static String icTotalAppointment = Assets.images.icon.icTotalAppointment.path;
  static String icClock = Assets.images.icon.icClock.path;
  static String icMenu = Assets.images.icon.icMenu.path;
  static String icEdit = Assets.images.icon.icEdit.path;
  static String icDelete2 = Assets.images.icon.icDelete2.path;
  static String icQinfo = Assets.images.icon.icQinfo.path;
  static String icFilter = Assets.images.icon.icFilter.path;
  static String icWp2 = Assets.images.icon.icWp2.path;
  static String icCall = Assets.images.icon.icCall.path;
  static String icMessage2 = Assets.images.icon.icMessage2.path;
  static String icView = Assets.images.icon.icView.path;
  static String icShare = Assets.images.icon.icShare.path;
  static String icDashboard2 = Assets.images.icon.icDashboard2.path;
  static String icVCard2 = Assets.images.icon.icVCard2.path;
  static String icSetting2 = Assets.images.icon.icSetting2.path;
  static String icCircleInsta = Assets.images.icon.icCircleInsta.path;
  static String icSearch = Assets.images.icon.icSearch.path;
  static String icDash = Assets.images.icon.icDash.path;
  static String icDashVcard = Assets.images.icon.icDashVcard.path;
  static String icDashBusinessCard = Assets.images.icon.icDashBusinessCard.path;
  static String icDashAppointment = Assets.images.icon.icDashAppointment.path;
  static String icDashEnquiries = Assets.images.icon.icDashEnquiries.path;
  static String icShareLink = Assets.images.icon.icShareLink.path;
  static String icQrCode2 = Assets.images.icon.icQrCode2.path;

  //images/icon/vCard
  static String icBasicDetail = Assets.images.icon.vCard.icBasicDetail.path;
  static String icVcardTemplate = Assets.images.icon.vCard.icVcardTemplate.path;
  static String icBusinessHour = Assets.images.icon.vCard.icBusinessHour.path;
  static String icService = Assets.images.icon.vCard.icService.path;
  static String icProducts = Assets.images.icon.vCard.icProducts.path;
  static String icStorage = Assets.images.icon.icStorage.path;
  static String icTestimonial = Assets.images.icon.vCard.icTestimonial.path;
  static String icAppointmentV = Assets.images.icon.vCard.icAppointment.path;
  static String icSocialLink = Assets.images.icon.vCard.icSocialLink.path;
  static String icAdvancedSetting =
      Assets.images.icon.vCard.icAdvancedSetting.path;
  static String icFont = Assets.images.icon.vCard.icFont.path;
  static String icGallary = Assets.images.icon.vCard.icGallary.path;
  static String icBlog = Assets.images.icon.vCard.icBlog.path;
  static String icPrivacyPolicyV =
      Assets.images.icon.vCard.icPrivacyPolicy.path;
  static String icTermAndCondition =
      Assets.images.icon.vCard.icTermAndCondition.path;

  static String icArabic = Assets.images.icon.flag.icArabic.path;
  static String icChinese = Assets.images.icon.flag.icChinese.path;
  static String icEnglish = Assets.images.icon.flag.icEnglish.path;
  static String icFrench = Assets.images.icon.flag.icFrench.path;
  static String icGerman = Assets.images.icon.flag.icGerman.path;
  static String icPortuguese = Assets.images.icon.flag.icPortuguese.path;
  static String icRussian = Assets.images.icon.flag.icRussian.path;
  static String icSpanish = Assets.images.icon.flag.icSpanish.path;
  static String icTurkish = Assets.images.icon.flag.icTurkish.path;

  static String svgEmail = Assets.images.svg.auth.svgEmail.path;
  static String svgCloseEye = Assets.images.svg.auth.svgCloseEye.path;
  static String svgEye = Assets.images.svg.auth.svgEye.path;
  static String svgPassword = Assets.images.svg.auth.svgPassword.path;
  static String svglock = Assets.images.svg.auth.svgLock.path;

  static String icGoogle = Assets.images.png.icons.icGoogle.path;
  static String icTwitter = Assets.images.png.icons.icTwitter.path;
  static String icFb = Assets.images.png.icons.icFacebook.path;
  static String icInsta = Assets.images.png.icons.icInstagram.path;
  static String icReddit = Assets.images.png.icons.icReddit.path;
  static String icTumblr = Assets.images.png.icons.icTumblr.path;
  static String icYoutube = Assets.images.png.icons.icYoutube.path;
  static String icLinkedin = Assets.images.png.icons.icLinkedin.path;
  static String icWp = Assets.images.png.icons.icWp.path;
  static String icPinterest = Assets.images.png.icons.icPinterest.path;
  static String icTiktok = Assets.images.png.icons.icTikTok.path;
  static String icAppLogoPng = Assets.images.png.icons.icAppLogo.path;
}
