import 'package:flutter/material.dart';

class TopRight extends Align {
  const TopRight({
    super.key,
    super.child,
  }) : super(alignment: Alignment.topRight);
}

class TopCenter extends Align {
  const TopCenter({
    super.key,
    super.child,
  }) : super(alignment: Alignment.topCenter);
}

class TopLeft extends Align {
  const TopLeft({
    super.key,
    super.child,
  }) : super(alignment: Alignment.topLeft);
}
