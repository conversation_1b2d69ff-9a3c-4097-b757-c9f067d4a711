import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

extension SharedPreferencesX on SharedPreferences {
  bool get getOnboardingCompleted => getBool('setOnboardingCompleted') ?? false;

  set setOnboardingCompleted(bool value) {
    setBool('setOnboardingCompleted', value);
  }

  bool get getLoggedIn => getBool('isLoggedIn') ?? false;

  set setLoggedIn(bool value) {
    setBool('isLoggedIn', value);
  }

  bool get isDrawerLarge => getBool('isDrawerLarge') ?? false;

  set isDrawerLarge(bool value) {
    setBool('isDrawerLarge', value);
  }

  String? get getRole {
    return getString('role');
  }

  set setRole(String? value) {
    if (value == null) {
      remove('role');
    } else {
      setString('role', value);
    }
  }

  String? get getUserId {
    return getString('user_id');
  }

  set setUserId(String? value) {
    if (value == null) {
      remove('user_id');
    } else {
      setString('user_id', value);
    }
  }

  String? get getToken {
    return getString('token');
  }

  set setToken(String? value) {
    if (value == null) {
      remove('token');
    } else {
      setString('token', value);
    }
  }

  String? get getEmail {
    return getString('email');
  }

  set setEmail(String? value) {
    if (value == null) {
      remove('email');
    } else {
      setString('email', value);
    }
  }

  String? get getAppLocal {
    return getString('appLocal');
  }

  set setAppLocal(String? value) {
    if (value == null) {
      remove('appLocal');
    } else {
      setString('appLocal', value);
    }
  }
}

extension StringX on String {
  String get convertMd5 => md5.convert(utf8.encode(this)).toString();
}

extension BuildContextX on BuildContext {
  ColorScheme get colorScheme => Theme.of(this).colorScheme;
}
