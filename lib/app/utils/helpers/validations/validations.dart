import 'package:get/get.dart';
import 'package:v_card/app/utils/constants/app_strings.dart';

class AppValidations {
  AppValidations._();

  static String? verificationCodeValidation(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.T.emptyVerificationCode;
    }
    return null;
  }

  static String? phoneNumberValidation(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.T.emptyPhoneNumber;
    }

    value = value.trim();

    final regex = RegExp(r'^\d{10}$');
    if (!regex.hasMatch(value)) {
      return AppStrings.T.msg_enter_phone;
    }

    return null;
  }

  static String? nameValidation(String? value) {
    if (value == null || value.isEmpty) return AppStrings.T.emptyName;
    return null;
  }

  static String? passwordValidation(String? value) {
    if (value == null || value.isEmpty) return AppStrings.T.emptyPassword;
    return null;
  }

  static String? confirmPasswordValidation(
    String? value,
    String otherPasswordValue,
  ) {
    if (value == null || value.isEmpty) {
      return AppStrings.T.emptyConfirmPassword;
    }
    if (otherPasswordValue.isEmpty) return null;
    if (otherPasswordValue != value) return AppStrings.T.passwordMismatch;
    return null;
  }

  static String? emailValidation(String? value) {
    if (value == null || value.isEmpty) return AppStrings.T.emptyEmail;
    if (!value.isEmail) return AppStrings.T.invalidEmail;
    return null;
  }

  /// Validates if the field is not empty
  static String? validateRequired(
    String? value, {
    String fieldName = 'This field',
  }) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    return null;
  }

  static String? vCardNameValidation(
    String? value, {
    String fieldName = 'vCard name',
  }) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required.';
    }
    if (value.trim().length <= 5) {
      return '$fieldName must be at least 6 characters.';
    }
    return null;
  }

  static String? urlPathValidation(String? value) {
    if (value == null || value.trim().isEmpty) {
      return AppStrings.T.lbl_empty_url;
    }

    final pattern =
        r'^(https?:\/\/)?(www\.)?([a-zA-Z0-9\-]+\.)+[a-zA-Z]{2,}(\/\S*)?$';
    final regex = RegExp(pattern);

    if (!regex.hasMatch(value.trim())) {
      return AppStrings.T.lbl_invalid_url;
    }
    return null;
  }

  static String? urlWithEmptyValidation(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null;
    }

    final pattern =
        r'^(https?:\/\/)?(www\.)?([a-zA-Z0-9\-]+\.)+[a-zA-Z]{2,}(\/\S*)?$';
    final regex = RegExp(pattern);

    if (!regex.hasMatch(value.trim())) {
      return AppStrings.T.lbl_invalid_url;
    }
    return null;
  }
}
