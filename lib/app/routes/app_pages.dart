import 'package:v_card/app/utils/helpers/exporter.dart';

class AppPages {
  static final routes = [
    GetPage(name: AppRoutes.splash, page: () => const SplashPage()),
    // Payment Approval Page
    GetPage(name: AppRoutes.paymentPage, page: () => const PaymentPage()),
    GetPage(
      name: AppRoutes.paymentApprovalPage,
      page: () => const PaymentApprovalPage(),
    ),
    GetPage(
      name: AppRoutes.subscriptionHistoryPage,
      page: () => const SubscriptionHistoryPage(),
    ),
    GetPage(name: AppRoutes.noInternetPage, page: () => const NoInternetPage()),
    // Onboarding Page
    GetPage(name: AppRoutes.welcomePage, page: () => const WelcomePage()),
    GetPage(name: AppRoutes.onboardingPage, page: () => const OnboardingPage()),
    // Login Page
    GetPage(name: AppRoutes.login, page: LoginPage.new),
    // Register Page
    GetPage(name: AppRoutes.register, page: RegisterPage.new),
    // Nevugation Menu
    GetPage(name: AppRoutes.nevigationMenu, page: NavigationMenu.new),
    // Dashboard Page
    GetPage(name: AppRoutes.dashboard, page: DashboardPage.new),
    // Forgot Password Page
    GetPage(name: AppRoutes.forgotPasswordPage, page: ForgotPasswordPage.new),
    // Reset Password Page
    GetPage(name: AppRoutes.resetPasswordPage, page: ResetPasswordPage.new),
    // Change Password Page
    GetPage(name: AppRoutes.changePasswordPage, page: ChangePasswordPage.new),
    // Vcard Page
    GetPage(name: AppRoutes.vcardPage, page: VcardPage.new),
    // Appointment Page
    GetPage(name: AppRoutes.appointmentPage, page: AppointmentPage.new),
    GetPage(
      name: AppRoutes.filterdAppointmentPage,
      page: FilterdAppointmentPage.new,
    ),
    // Enquiries Page
    GetPage(name: AppRoutes.enquiriesPage, page: EnquiriesPage.new),
    GetPage(
      name: AppRoutes.filterdEnquiriesPage,
      page: FilterdEnquiriesPage.new,
    ),
    // Profile Page
    GetPage(name: AppRoutes.accountSettingPage, page: AccountSettingPage.new),
    // Edit Profile Page
    GetPage(name: AppRoutes.editProfilegPage, page: EditProfilePage.new),
    // Language Page
    GetPage(name: AppRoutes.languagePage, page: LanguagePage.new),
    // Businesscard Page
    GetPage(name: AppRoutes.businesscardPage, page: BusinessCardPage.new),
    // Subscription Plan Page
    // GetPage(
    //   name: AppRoutes.subscriptionPlanPage,
    //   page: SubscriptionPlanPage.new,
    // ),
    // Create Vcard Page
    GetPage(name: AppRoutes.createVcardPage, page: CreateVcardPage.new),
    // Edit Vcard Page
    GetPage(name: AppRoutes.editVcardPage, page: EditVcardPage.new),
    // Vcard Add Basic Detail Page
    GetPage(
      name: AppRoutes.vcardAddBasicDetailPage,
      page: VcardAddBasicDetailPage.new,
    ),
    // VCard Templates Page
    GetPage(name: AppRoutes.vCardTemplatesPage, page: VCardTemplatesPage.new),
    // Product List Page
    GetPage(name: AppRoutes.productPage, page: ProductPage.new),
    // Create Service Page
    GetPage(name: AppRoutes.createServicePage, page: CreateServicePage.new),
    // Service List Page
    GetPage(name: AppRoutes.servicePage, page: ServicePage.new),
    // Create Product Page
    GetPage(name: AppRoutes.createProductPage, page: CreateProductPage.new),
    // Testimonials List Page
    GetPage(name: AppRoutes.testimonialsPage, page: TestimonialsPage.new),
    // Create Testimonials Page
    GetPage(
      name: AppRoutes.createTestimonialsPage,
      page: CreateTestimonialsPage.new,
    ),
    // Vcard Business Hours Page
    GetPage(
      name: AppRoutes.vcardBusinessHoursPage,
      page: VcardBusinessHoursPage.new,
    ),
    // Vcard Appointments Hours Page
    GetPage(
      name: AppRoutes.vcardAppointmentsPage,
      page: VcardAppointmentsPage.new,
    ),
    // Social Connect Page
    GetPage(name: AppRoutes.socialConnectPage, page: SocialConnectPage.new),
    // Blog List Page
    GetPage(name: AppRoutes.blogPage, page: BlogPage.new),
    // Create Blog Page
    GetPage(name: AppRoutes.createBlogPage, page: CreateBlogPage.new),
    // Font Page
    GetPage(name: AppRoutes.fontPage, page: FontPage.new),
    // Galleries List Page
    GetPage(name: AppRoutes.galleriesPage, page: GalleriesPage.new),
    // Create Galleries Page
    GetPage(name: AppRoutes.createGalleriesPage, page: CreateGalleriesPage.new),
    // Privacy Policy Page
    GetPage(name: AppRoutes.privacyPolicyPage, page: PrivacyPolicyPage.new),
    // TermsAnd Conditions Page
    GetPage(
      name: AppRoutes.termsAndConditionsPage,
      page: TermsAndConditionsPage.new,
    ),
    // Customize QRCode Page
    GetPage(name: AppRoutes.customizeQRCodePage, page: CustomizeQRCodePage.new),
    // Insta Embed Page
    GetPage(name: AppRoutes.instaEmbedPage, page: InstaEmbedPage.new),
    GetPage(
      name: AppRoutes.createInstaEmbedPage,
      page: CreateInstaEmbedPage.new,
    ),
    // Iframe Page
    GetPage(name: AppRoutes.iframePage, page: IframePage.new),
    GetPage(name: AppRoutes.createIframePage, page: CreateIframePage.new),
    // Banner Page
    GetPage(name: AppRoutes.bannerPage, page: BannerPage.new),
    GetPage(name: AppRoutes.updateBannerPage, page: UpdateBannerPage.new),
    // Seo Page
    GetPage(name: AppRoutes.seoPage, page: SeoPage.new),
    GetPage(name: AppRoutes.updateSeoPage, page: UpdateSeoPage.new),
    // ManageSection Page
    GetPage(name: AppRoutes.manageSectionPage, page: ManageSectionPage.new),
    // Advanced Setting Page
    GetPage(name: AppRoutes.advancedSettingPage, page: AdvancedSettingPage.new),
    // product Order Page
    GetPage(name: AppRoutes.productOrderPage, page: ProductOrderPage.new),
    // Affiliation Page
    GetPage(name: AppRoutes.affiliationPage, page: AffiliationPage.new),
    // Withdrawal Request Page
    GetPage(
      name: AppRoutes.withdrawalRequestPage,
      page: WithdrawalRequestPage.new,
    ),
    // NFC Page
    GetPage(name: AppRoutes.nfcCardsPage, page: NfcCardsPage.new),
    GetPage(name: AppRoutes.orderNfcPage, page: OrderNfcPage.new),
    GetPage(name: AppRoutes.orderNfcFormPage, page: OrderNfcFormPage.new),
    // Storage Page
    GetPage(name: AppRoutes.storagePage, page: StoragePage.new),
    // Storage Page
    GetPage(name: AppRoutes.generalSettingPage, page: GeneralSettingPage.new),
    GetPage(name: AppRoutes.paymentConfigPage, page: PaymentConfigPage.new),
    // Custom Link Page
    GetPage(name: AppRoutes.customLinkPage, page: CustomLinkPage.new),
    GetPage(
      name: AppRoutes.createCustomLinkPage,
      page: CreateCustomLinkPage.new,
    ),
    // Manage Subscription Page
    // GetPage(name: AppRoutes.manageSubscriptionFirstPage, page: ManageSubscriptionFirstPage.new),
    GetPage(
      name: AppRoutes.manageSubscriptionPage,
      page: ManageSubscriptionPage.new,
    ),
    GetPage(name: AppRoutes.blogDetailPage, page: BlogDetailPage.new),
  ];
}
