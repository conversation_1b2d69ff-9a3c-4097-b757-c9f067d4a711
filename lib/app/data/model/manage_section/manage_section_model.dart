class ManageSectionModel {
  final bool success;
  final ManageSectionData data;
  final String message;

  ManageSectionModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory ManageSectionModel.fromJson(Map<String, dynamic> json) {
    return ManageSectionModel(
      success: json['success'],
      data: ManageSectionData.from<PERSON>son(json['data']),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.toJson(),
      'message': message,
    };
  }
}

class ManageSectionData {
  final int vcardId;
  final String header;
  final String contactList;
  final String services;
  final String products;
  final String instaEmbed;
  final String galleries;
  final String blogs;
  final String iframe;
  final String map;
  final String testimonials;
  final String businessHours;
  final String appointments;
  final String oneSignalNotification;
  final String banner;
  final String newsLatterPopup;

  ManageSectionData({
    required this.vcardId,
    required this.header,
    required this.contactList,
    required this.services,
    required this.products,
    required this.instaEmbed,
    required this.galleries,
    required this.blogs,
    required this.iframe,
    required this.map,
    required this.testimonials,
    required this.businessHours,
    required this.appointments,
    required this.oneSignalNotification,
    required this.banner,
    required this.newsLatterPopup,
  });

  factory ManageSectionData.fromJson(Map<String, dynamic> json) {
    return ManageSectionData(
      vcardId: json['vcard_id'] ?? 0,
      header: json['header'] ?? '0',
      contactList: json['contact_list'] ?? '0',
      services: json['services'] ?? '0',
      products: json['products'] ?? '0',
      instaEmbed: json['insta_embed'] ?? '0',
      galleries: json['galleries'] ?? '0',
      blogs: json['blogs'] ?? '0',
      iframe: json['iframe'] ?? '0',
      map: json['map'] ?? '0',
      testimonials: json['testimonials'] ?? '0',
      businessHours: json['business_hours'] ?? '0',
      appointments: json['appointments'] ?? '0',
      oneSignalNotification: json['one_signal_notification'] ?? '0',
      banner: json['banner'] ?? '0',
      newsLatterPopup: json['news_latter_popup'] ?? '0',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'vcard_id': vcardId,
      'header': header,
      'contact_list': contactList,
      'services': services,
      'products': products,
      'insta_embed': instaEmbed,
      'galleries': galleries,
      'blogs': blogs,
      'iframe': iframe,
      'map': map,
      'testimonials': testimonials,
      'business_hours': businessHours,
      'appointments': appointments,
      'one_signal_notification': oneSignalNotification,
      'banner': banner,
      'news_latter_popup': newsLatterPopup,
    };
  }
}

class UpdateManageSectionResponse {
  final bool success;
  final String message;

  UpdateManageSectionResponse({
    required this.success,
    required this.message,
  });

  factory UpdateManageSectionResponse.fromJson(Map<String, dynamic> json) {
    return UpdateManageSectionResponse(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
    };
  }
}