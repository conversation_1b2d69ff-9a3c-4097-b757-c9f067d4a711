class PrivacyPolicyModel {
  final bool success;
  final PrivacyPolicyData data;
  final String message;

  PrivacyPolicyModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory PrivacyPolicyModel.fromJson(Map<String, dynamic> json) {
    return PrivacyPolicyModel(
      success: json['success'] ?? false,
      data: PrivacyPolicyData.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }
}

class PrivacyPolicyData {
  final int id;
  final String privacyPolicy;
  final int vcardId;
  final String createdAt;
  final String updatedAt;

  PrivacyPolicyData({
    required this.id,
    required this.privacyPolicy,
    required this.vcardId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PrivacyPolicyData.fromJson(Map<String, dynamic> json) {
    return PrivacyPolicyData(
      id: json['id'] ?? 0,
      privacyPolicy: json['privacy_policy'] ?? '',
      vcardId: json['vcard_id'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }
}

class UpdatePrivacyPolicyModel {
  final bool success;
  final String message;

  UpdatePrivacyPolicyModel({required this.success, required this.message});

  factory UpdatePrivacyPolicyModel.fromJson(Map<String, dynamic> json) {
    return UpdatePrivacyPolicyModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }
}
