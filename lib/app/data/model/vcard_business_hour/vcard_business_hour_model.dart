class VcardBusinessHoursModel {
  final bool success;
  final List<BusinessHour> data;
  final String message;

  VcardBusinessHoursModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory VcardBusinessHoursModel.fromJson(Map<String, dynamic> json) {
    return VcardBusinessHoursModel(
      success: json['success'] ?? false,
      data:
          json['data'] != null
              ? List<BusinessHour>.from(
                json['data'].map((x) => BusinessHour.fromJson(x)),
              )
              : [],
      message: json['message'] ?? '',
    );
  }
}

class BusinessHour {
  final int id;
  final int vcardId;
  final int dayOfWeek;
  final String startTime;
  final String endTime;

  BusinessHour({
    required this.id,
    required this.vcardId,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
  });

  factory BusinessHour.fromJson(Map<String, dynamic> json) {
    return BusinessHour(
      id: json['id'] ?? 0,
      vcardId: json['vcard_id'] ?? 0,
      dayOfWeek: json['day_of_week'] ?? 0,
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
    );
  }
}

// business_hours_response_model.dart
class BusinessHoursResponseModel {
  final bool success;
  final String message;

  BusinessHoursResponseModel({required this.success, required this.message});

  factory BusinessHoursResponseModel.fromJson(Map<String, dynamic> json) {
    return BusinessHoursResponseModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }
}
