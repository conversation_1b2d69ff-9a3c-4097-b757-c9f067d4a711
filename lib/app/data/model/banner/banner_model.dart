class BannerModel {
  final bool success;
  final List<BannerData> data;
  final String message;

  BannerModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory BannerModel.fromJson(Map<String, dynamic> json) {
    return BannerModel(
      success: json['success'],
      data: (json['data'] as List).map((item) => BannerData.fromJson(item)).toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class BannerData {
  final String url;
  final String title;
  final String description;
  final String bannerButton;
  final int vcardId;
  final String banner;

  BannerData({
    required this.url,
    required this.title,
    required this.description,
    required this.bannerButton,
    required this.vcardId,
    required this.banner,
  });

  factory BannerData.fromJson(Map<String, dynamic> json) {
    return BannerData(
      url: json['url'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      bannerButton: json['banner_button'] as String,
      vcardId: json['vcard_id'] as int,
      banner: json['banner'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'url': url,
      'title': title,
      'description': description,
      'banner_button': bannerButton,
      'vcard_id': vcardId,
      'banner': banner,
    };
  }
}



class UpdateBannerModel {
  final bool success;
  final String message;

  UpdateBannerModel({required this.success, required this.message});

  factory UpdateBannerModel.fromJson(Map<String, dynamic> json) {
    return UpdateBannerModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
