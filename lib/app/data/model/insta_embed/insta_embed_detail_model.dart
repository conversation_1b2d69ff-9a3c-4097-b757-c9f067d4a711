class InstaEmbedDetailModel {
  final bool success;
  final InstaEmbedDetail? data;
  final String message;

  InstaEmbedDetailModel({
    required this.success,
    this.data,
    required this.message,
  });

  factory InstaEmbedDetailModel.fromJson(Map<String, dynamic> json) {
    return InstaEmbedDetailModel(
      success: json['success'] ?? false,
      data: InstaEmbedDetail.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] ?? '',
    );
  }
}

class InstaEmbedDetail {
  final int? id;
  final String? type;
  final String? embedtag;
  final int? vcardId;
  final String? createdAt;
  final String? updatedAt;

  InstaEmbedDetail({
    this.id,
    this.type,
    this.embedtag,
    this.vcardId,
    this.createdAt,
    this.updatedAt,
  });

  factory InstaEmbedDetail.fromJson(Map<String, dynamic> json) {
    return InstaEmbedDetail(
      id: json['id'],
      type: json['type'],
      embedtag: json['embedtag'],
      vcardId: json['vcard_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}
