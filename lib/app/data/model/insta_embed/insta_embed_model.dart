class InstaEmbedModel {
  final bool success;
  final List<InstaEmbed>? data;
  final String message;

  InstaEmbedModel({
    required this.success,
    this.data,
    required this.message,
  });

  factory InstaEmbedModel.fromJson(Map<String, dynamic> json) {
    return InstaEmbedModel(
      success: json['success'] ?? false,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => InstaEmbed.fromJson(e))
          .toList(),
      message: json['message'] ?? '',
    );
  }
}

class InstaEmbed {
  final int? id;
  final String? type;
  final String? embedtag;
  final int? vcardId;
  final String? createdAt;
  final String? updatedAt;

  InstaEmbed({
    this.id,
    this.type,
    this.embedtag,
    this.vcardId,
    this.createdAt,
    this.updatedAt,
  });

  factory InstaEmbed.fromJson(Map<String, dynamic> json) {
    return InstaEmbed(
      id: json['id'],
      type: json['type'],
      embedtag: json['embedtag'],
      vcardId: json['vcard_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}

class InstaEmbedCreateResponse {
  final bool success;
  final String message;
  final InstaEmbed? data;

  InstaEmbedCreateResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory InstaEmbedCreateResponse.fromJson(Map<String, dynamic> json) {
    return InstaEmbedCreateResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? InstaEmbed.fromJson(json['data']) : null,
    );
  }
}
