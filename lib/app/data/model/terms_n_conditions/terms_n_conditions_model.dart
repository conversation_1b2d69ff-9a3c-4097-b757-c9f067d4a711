class TermsAndConditionsModel {
  final bool success;
  final TermsAndConditionsData data;
  final String message;

  TermsAndConditionsModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory TermsAndConditionsModel.fromJson(Map<String, dynamic> json) {
    return TermsAndConditionsModel(
      success: json['success'] ?? false,
      data: TermsAndConditionsData.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }
}

class TermsAndConditionsData {
  final int id;
  final String termCondition;
  final int vcardId;
  final String createdAt;
  final String updatedAt;

  TermsAndConditionsData({
    required this.id,
    required this.termCondition,
    required this.vcardId,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TermsAndConditionsData.fromJson(Map<String, dynamic> json) {
    return TermsAndConditionsData(
      id: json['id'] ?? 0,
      termCondition: json['term_condition'] ?? '',
      vcardId: json['vcard_id'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }
}
