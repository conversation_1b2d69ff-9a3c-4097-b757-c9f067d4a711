// lib/app/data/model/affiliation/affiliation_model.dart

class AffiliationModel {
  final bool success;
  final List<AffiliationData> data;
  final String message;

  AffiliationModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory AffiliationModel.fromJson(Map<String, dynamic> json) {
    return AffiliationModel(
      success: json['success'] as bool? ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => AffiliationData.fromJson(e))
              .toList() ??
          [],
      message: json['message'] as String? ?? '',
    );
  }
}

class AffiliationData {
  final String affiliationUrl;
  final dynamic totalAmount;
  final int currentAmount;
  final String note;
  final String? howItWorks;

  AffiliationData({
    required this.affiliationUrl,
    required this.totalAmount,
    required this.currentAmount,
    required this.note,
    this.howItWorks,
  });

  factory AffiliationData.fromJson(Map<String, dynamic> json) {
    return AffiliationData(
      affiliationUrl: json['affiliation_url'] as String? ?? '',
      totalAmount: json['total_amount'] as dynamic ?? 0,
      currentAmount: json['current_amount'] as int? ?? 0,
      note: json['note'] as String? ?? '',
      howItWorks: json['how_it_works'] as String?,
    );
  }
}

class AffiliationListModel {
  final bool success;
  final List<AffiliationListData> data;
  final String message;

  AffiliationListModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory AffiliationListModel.fromJson(Map<String, dynamic> json) {
    return AffiliationListModel(
      success: json['success'] as bool? ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => AffiliationListData.fromJson(e))
              .toList() ??
          [],
      message: json['message'] as String? ?? '',
    );
  }
}

class AffiliationListData {
  final int id;
  final int affiliatedBy;
  final int userId;
  final int isVerified;
  final int amount;
  final String createdAt;
  final String updatedAt;
  final String totalAmount;
  final AffiliatedUser? user;
  final AffiliatedUser? affiliatedByUser;

  AffiliationListData({
    required this.id,
    required this.affiliatedBy,
    required this.userId,
    required this.isVerified,
    required this.amount,
    required this.createdAt,
    required this.updatedAt,
    required this.totalAmount,
    this.user,
    this.affiliatedByUser,
  });

  factory AffiliationListData.fromJson(Map<String, dynamic> json) {
    return AffiliationListData(
      id: json['id'] as int? ?? 0,
      affiliatedBy: json['affiliated_by'] as int? ?? 0,
      userId: json['user_id'] as int? ?? 0,
      isVerified: json['is_verified'] as int? ?? 0,
      amount: json['amount'] as int? ?? 0,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      totalAmount: json['total_amount'] as String? ?? '0',
      user: json['user'] != null ? AffiliatedUser.fromJson(json['user']) : null,
      affiliatedByUser:
          json['affiliated_by_user'] != null
              ? AffiliatedUser.fromJson(json['affiliated_by_user'])
              : null,
    );
  }
}

class AffiliatedUser {
  final int id;
  final String firstName;
  final String lastName;
  final String email;
  final String contact;
  final String? regionCode;
  final int isActive;
  final int steps;
  final String language;
  final int enableTwoFactorAuthentication;
  final dynamic google2faSecret;
  final String emailVerifiedAt;
  final String affiliateCode;
  final int themeMode;
  final String tenantId;
  final String createdAt;
  final String updatedAt;
  final int vcardTableViewType;
  final String fullName;
  final String profileImage;
  final String planName;
  final String multiLanguageDate;
  final List<dynamic> media;
  final List<Subscription> subscriptions;

  AffiliatedUser({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.contact,
    this.regionCode,
    required this.isActive,
    required this.steps,
    required this.language,
    required this.enableTwoFactorAuthentication,
    this.google2faSecret,
    required this.emailVerifiedAt,
    required this.affiliateCode,
    required this.themeMode,
    required this.tenantId,
    required this.createdAt,
    required this.updatedAt,
    required this.vcardTableViewType,
    required this.fullName,
    required this.profileImage,
    required this.planName,
    required this.multiLanguageDate,
    required this.media,
    required this.subscriptions,
  });

  factory AffiliatedUser.fromJson(Map<String, dynamic> json) {
    return AffiliatedUser(
      id: json['id'] as int? ?? 0,
      firstName: json['first_name'] as String? ?? '',
      lastName: json['last_name'] as String? ?? '',
      email: json['email'] as String? ?? '',
      contact: json['contact'] as String? ?? '',
      regionCode: json['region_code'] as String?,
      isActive: json['is_active'] as int? ?? 0,
      steps: json['steps'] as int? ?? 0,
      language: json['language'] as String? ?? 'en',
      enableTwoFactorAuthentication:
          json['enable_two_factor_authentication'] as int? ?? 0,
      google2faSecret: json['google2fa_secret'],
      emailVerifiedAt: json['email_verified_at'] as String? ?? '',
      affiliateCode: json['affiliate_code'] as String? ?? '',
      themeMode: json['theme_mode'] as int? ?? 0,
      tenantId: json['tenant_id'] as String? ?? '',
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      vcardTableViewType: json['vcard_table_view_type'] as int? ?? 0,
      fullName: json['full_name'] as String? ?? '',
      profileImage: json['profile_image'] as String? ?? '',
      planName: json['plan_name'] as String? ?? '',
      multiLanguageDate: json['multi_language_date'] as String? ?? '',
      media: json['media'] as List<dynamic>? ?? [],
      subscriptions:
          (json['subscriptions'] as List<dynamic>?)
              ?.map((e) => Subscription.fromJson(e))
              .toList() ??
          [],
    );
  }
}

class Subscription {
  final int id;
  final String tenantId;
  final int planId;
  final dynamic transactionId;
  final dynamic planAmount;
  final dynamic discount;
  final dynamic payableAmount;
  final int planFrequency;
  final String startsAt;
  final String endsAt;
  final String? trialEndsAt;
  final int noOfVcards;
  final dynamic notes;
  final int status;
  final dynamic couponCodeMeta;
  final String? paymentType;
  final String createdAt;
  final String updatedAt;
  final String attachment;
  final List<dynamic> media;
  final Plan? plan;

  Subscription({
    required this.id,
    required this.tenantId,
    required this.planId,
    this.transactionId,
    this.planAmount,
    this.discount,
    this.payableAmount,
    required this.planFrequency,
    required this.startsAt,
    required this.endsAt,
    this.trialEndsAt,
    required this.noOfVcards,
    this.notes,
    required this.status,
    this.couponCodeMeta,
    this.paymentType,
    required this.createdAt,
    required this.updatedAt,
    required this.attachment,
    required this.media,
    this.plan,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'] as int? ?? 0,
      tenantId: json['tenant_id'] as String? ?? '',
      planId: json['plan_id'] as int? ?? 0,
      transactionId: json['transaction_id'],
      planAmount: json['plan_amount'],
      discount: json['discount'],
      payableAmount: json['payable_amount'],
      planFrequency: json['plan_frequency'] as int? ?? 0,
      startsAt: json['starts_at'] as String? ?? '',
      endsAt: json['ends_at'] as String? ?? '',
      trialEndsAt: json['trial_ends_at'] as String?,
      noOfVcards: json['no_of_vcards'] as int? ?? 0,
      notes: json['notes'],
      status: json['status'] as int? ?? 0,
      couponCodeMeta: json['coupon_code_meta'],
      paymentType: json['payment_type'] as String?,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      attachment: json['attachment'] as String? ?? '',
      media: json['media'] as List<dynamic>? ?? [],
      plan: json['plan'] != null ? Plan.fromJson(json['plan']) : null,
    );
  }
}

class Plan {
  final int id;
  final String name;
  final int noOfVcards;
  final int storageLimit;
  final int currencyId;
  final int price;
  final int status;
  final int frequency;
  final int isDefault;
  final int trialDays;
  final String createdAt;
  final String updatedAt;
  final int customSelect;
  final String customVcardNumber;
  final String customVcardPrice;
  final int? noOfWhatsappStore;

  Plan({
    required this.id,
    required this.name,
    required this.noOfVcards,
    required this.storageLimit,
    required this.currencyId,
    required this.price,
    required this.status,
    required this.frequency,
    required this.isDefault,
    required this.trialDays,
    required this.createdAt,
    required this.updatedAt,
    required this.customSelect,
    required this.customVcardNumber,
    required this.customVcardPrice,
    this.noOfWhatsappStore,
  });

  factory Plan.fromJson(Map<String, dynamic> json) {
    return Plan(
      id: json['id'] as int? ?? 0,
      name: json['name'] as String? ?? '',
      noOfVcards: json['no_of_vcards'] as int? ?? 0,
      storageLimit: json['storage_limit'] as int? ?? 0,
      currencyId: json['currency_id'] as int? ?? 0,
      price: json['price'] as int? ?? 0,
      status: json['status'] as int? ?? 0,
      frequency: json['frequency'] as int? ?? 0,
      isDefault: json['is_default'] as int? ?? 0,
      trialDays: json['trial_days'] as int? ?? 0,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      customSelect: json['custom_select'] as int? ?? 0,
      customVcardNumber: json['custom_vcard_number'] as String? ?? '',
      customVcardPrice: json['custom_vcard_price'] as String? ?? '',
      noOfWhatsappStore: json['no_of_whatsapp_store'] as int?,
    );
  }
}

class WithdrawalModel {
  final bool success;
  final List<WithdrawalData> data;
  final String message;

  WithdrawalModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory WithdrawalModel.fromJson(Map<String, dynamic> json) {
    return WithdrawalModel(
      success: json['success'] as bool? ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => WithdrawalData.fromJson(e))
              .toList() ??
          [],
      message: json['message'] as String? ?? '',
    );
  }
}

class WithdrawalData {
  final int id;
  final int userId;
  final String email;
  final String bankDetails;
  final int amount;
  final int isApproved;
  final String? rejectionNote;
  final String createdAt;
  final String updatedAt;
  final AffiliatedUser? user;

  WithdrawalData({
    required this.id,
    required this.userId,
    required this.email,
    required this.bankDetails,
    required this.amount,
    required this.isApproved,
    this.rejectionNote,
    required this.createdAt,
    required this.updatedAt,
    this.user,
  });

  factory WithdrawalData.fromJson(Map<String, dynamic> json) {
    return WithdrawalData(
      id: json['id'] as int? ?? 0,
      userId: json['user_id'] as int? ?? 0,
      email: json['email'] as String? ?? '',
      bankDetails: json['bank_details'] as String? ?? '',
      amount: json['amount'] as int? ?? 0,
      isApproved: json['is_approved'] as int? ?? 0,
      rejectionNote: json['rejection_note'] as String?,
      createdAt: json['created_at'] as String? ?? '',
      updatedAt: json['updated_at'] as String? ?? '',
      user: json['user'] != null ? AffiliatedUser.fromJson(json['user']) : null,
    );
  }
}

class WithdrawalDetailModel {
  final bool success;
  final WithdrawalData data;
  final String message;

  WithdrawalDetailModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory WithdrawalDetailModel.fromJson(Map<String, dynamic> json) {
    return WithdrawalDetailModel(
      success: json['success'] as bool? ?? false,
      data: WithdrawalData.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String? ?? '',
    );
  }
}

class CreateWithdrawalResponseModel {
  final bool success;
  final String message;

  CreateWithdrawalResponseModel({required this.success, required this.message});

  factory CreateWithdrawalResponseModel.fromJson(Map<String, dynamic> json) {
    return CreateWithdrawalResponseModel(
      success: json['success'] as bool? ?? false,
      message: json['message'] as String? ?? '',
    );
  }
}
