class SeoModel {
  final bool success;
  final List<Seo> data;
  final String message;

  SeoModel({required this.success, required this.data, required this.message});

  factory SeoModel.fromJson(Map<String, dynamic> json) {
    return SeoModel(
      success: json['success'],
      data: (json['data'] as List).map((item) => Seo.fromJson(item)).toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class Seo {
  final String? siteTitle;
  final String? homeTitle;
  final String? metaKeyword;
  final String? metaDescription;
  final String? googleAnalytics;

  Seo({
    this.siteTitle,
    this.homeTitle,
    this.metaKeyword,
    this.metaDescription,
    this.googleAnalytics,
  });

  factory Seo.fromJson(Map<String, dynamic> json) {
    return Seo(
      siteTitle: json['site_title'],
      homeTitle: json['home_title'],
      metaKeyword: json['meta_keyword'],
      metaDescription: json['meta_description'],
      googleAnalytics: json['google_analytics'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'site_title': siteTitle,
      'home_title': homeTitle,
      'meta_keyword': metaKeyword,
      'meta_description': metaDescription,
      'google_analytics': googleAnalytics,
    };
  }
}

class UpdateSeoModel {
  final bool success;
  final String message;

  UpdateSeoModel({required this.success, required this.message});

  factory UpdateSeoModel.fromJson(Map<String, dynamic> json) {
    return UpdateSeoModel(success: json['success'], message: json['message']);
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
