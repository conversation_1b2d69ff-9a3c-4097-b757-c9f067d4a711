class ProfileModel {
  final bool success;
  final List<ProfileData> data;
  final String message;

  ProfileModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory ProfileModel.fromJson(Map<String, dynamic> json) {
    return ProfileModel(
      success: json['success'],
    data: json.containsKey('data') && json['data'] is List<dynamic> 
          ? List<ProfileData>.from(json['data'].map((x) => ProfileData.fromJson(x))) 
          : [],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}



class ProfileData {
  final String? profileImage;
  final String? firstName;
  final String? lastName;
  final String? email;
  final String? regionCode;
  final String? contact;
  final String? language;
  final String? manualPaymentGuide;

  ProfileData({
    required this.profileImage,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.regionCode,
    required this.contact,
    required this.language,
    required this.manualPaymentGuide,
  });

  factory ProfileData.fromJson(Map<String, dynamic> json) => ProfileData(
    profileImage: json["profile_image"] as String?,
    firstName: json["first_name"] as String?,
    lastName: json["last_name"] as String?,
    email: json["email"] as String?,
    regionCode: json["region_code"] as String?,
    contact: json["contact"] as String?,
    language: json["language"] as String?,
    manualPaymentGuide: json["manual_payment_guide"] as String?,
  );

  Map<String, dynamic> toJson() => {
    "profile_image": profileImage,
    "first_name": firstName,
    "last_name": lastName,
    "email": email,
    "region_code": regionCode,
    "contact": contact,
    "language": language,
    "manual_payment_guide": manualPaymentGuide,
  };
}
