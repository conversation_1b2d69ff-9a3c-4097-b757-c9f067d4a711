  class RegistrationModel {
  final bool success;
  final String message;
  // final RegisterData? data;

  RegistrationModel({
    required this.success,
    required this.message,
    // this.data,
  });

  factory RegistrationModel.fromJson(Map<String, dynamic> json) {
    return RegistrationModel(
      success: json['success'],
      message: json['message'],
      // data: json['data'] != null ? RegisterData.fromJson(json['data']) : null,
    );
  }
}

// class RegisterData {
//   final String token;
//   final int userId;
//   final String role;

//   RegisterData({
//     required this.token,
//     required this.userId,
//     required this.role,
//   });

//   factory RegisterData.fromJson(Map<String, dynamic> json) {
//     return RegisterData(
//       token: json['token'],
//       userId: json['user_id'],
//       role: json['role'],
//     );
//   }
// }