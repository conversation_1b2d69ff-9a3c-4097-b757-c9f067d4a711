  class ForgotPasswordModel {
  final bool success;
  final String message;
  final ForgotPasswordData? data;

  ForgotPasswordModel({
    required this.success,
    required this.message,
    this.data,
  });

  factory ForgotPasswordModel.fromJson(Map<String, dynamic> json) {
    return ForgotPasswordModel(
      success: json['success'],
      message: json['message'],
      data: json['data'] != null ? ForgotPasswordData.fromJson(json['data']) : null,
    );
  }
}

class ForgotPasswordData {
  final String token;
  final int userId;
  final String role;

  ForgotPasswordData({
    required this.token,
    required this.userId,
    required this.role,
  });

  factory ForgotPasswordData.fromJson(Map<String, dynamic> json) {
    return ForgotPasswordData(
      token: json['token'],
      userId: json['user_id'],
      role: json['role'],
    );
  }
}