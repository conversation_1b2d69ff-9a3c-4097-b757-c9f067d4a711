class AppointmentModel {
  final bool success;
  final List<AppointmentData> data;
  final String message;

  AppointmentModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    return AppointmentModel(
      success: json['success'],
      data:
          (json['data'] as List)
              .map((item) => AppointmentData.fromJson(item))
              .toList(),
      message: json['message'],
    );
  }

  AppointmentModel copyWith({
    bool? success,
    List<AppointmentData>? data,
    String? message,
  }) {
    return AppointmentModel(
      success: success ?? this.success,
      data: data ?? this.data,
      message: message ?? this.message,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class AppointmentData {
  final int? id;
  final String? vcardName;
  final String? name;
  final String? email;
  final String? phone;
  final String? date;
  final String? fromTime;
  final String? toTime;
  final int? status;
  final String? paidAmount;

  AppointmentData({
    this.id,
    this.vcardName,
    this.name,
    this.email,
    this.phone,
    this.date,
    this.fromTime,
    this.toTime,
    this.status,
    this.paidAmount,
  });

  AppointmentData copyWith({
    int? id,
    String? vcardName,
    String? name,
    String? email,
    String? phone,
    String? date,
    String? fromTime,
    String? toTime,
    int? status,
    String? paidAmount,
  }) {
    return AppointmentData(
      id: id ?? this.id,
      vcardName: vcardName ?? this.vcardName,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      date: date ?? this.date,
      fromTime: fromTime ?? this.fromTime,
      toTime: toTime ?? this.toTime,
      status: status ?? this.status,
      paidAmount: paidAmount ?? this.paidAmount,
    );
  }

  factory AppointmentData.fromJson(Map<String, dynamic> json) {
    return AppointmentData(
      id: json['id'] as int?,
      vcardName: json['vcard_name'] as String?,
      name: json['name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      date: json['date'] as String?,
      fromTime: json['from_time'] as String?,
      toTime: json['to_time'] as String?,
      status: json['status'] as int?,
      paidAmount: json['paid_amount'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vcard_name': vcardName,
      'name': name,
      'email': email,
      'phone': phone,
      'date': date,
      'from_time': fromTime,
      'to_time': toTime,
      'status': status,
      'paid_amount': paidAmount,
    };
  }
}

class UpdateAppointmentResponse {
  final bool success;
  final String message;

  UpdateAppointmentResponse({required this.success, required this.message});

  factory UpdateAppointmentResponse.fromJson(Map<String, dynamic> json) {
    return UpdateAppointmentResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }

  // Add copyWith method for consistency
  UpdateAppointmentResponse copyWith({bool? success, String? message}) {
    return UpdateAppointmentResponse(
      success: success ?? this.success,
      message: message ?? this.message,
    );
  }
}

class DeleteAppointmentResponse {
  final bool success;
  final String message;

  DeleteAppointmentResponse({required this.success, required this.message});

  factory DeleteAppointmentResponse.fromJson(Map<String, dynamic> json) {
    return DeleteAppointmentResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }

  // Add copyWith method for consistency
  DeleteAppointmentResponse copyWith({bool? success, String? message}) {
    return DeleteAppointmentResponse(
      success: success ?? this.success,
      message: message ?? this.message,
    );
  }
}
