class PaymentConfigModel {
  final bool success;
  final PaymentConfigData data;
  final String message;

  PaymentConfigModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory PaymentConfigModel.fromJson(Map<String, dynamic> json) {
    return PaymentConfigModel(
      success: json['success'],
      data: PaymentConfigData.fromJson(json['data']),
      message: json['message'],
    );
  }
}

class PaymentConfigData {
  final String stripeEnable;
  final String? stripeKey;
  final String? stripeSecret;
  final String paypalEnable;
  final String? paypalClientId;
  final String? paypalSecret;
  final String? paypalMode;
  final String currencyId;
  final String timeFormat;
  final String enableAffiliation;
  final String paypalEmail;
  final String whatsappShare;
  final String enableContact;
  final String hideStickybar;
  final String productOrderSendMailCustomer;
  final String productOrderSendMailUser;
  final String subscriptionModelTime;
  final String enablePwa;
  final String isEdit;
  final String pwaIcon;
  final String enableAttachmentForInquiry;
  final String askDetailsBeforeDownloadingContact;
  final String? paystackKey;
  final String? paystackSecret;
  final String? flutterwaveKey;
  final String? flutterwaveSecret;
  final String? razorpayKey;
  final String? razorpaySecret;
  final String? phonepeMerchantId;
  final String? phonepeMerchantUserId;
  final String? phonepeEnv;
  final String? phonepeSaltKey;
  final String? phonepeSaltIndex;
  final String? manualPaymentGuide;
  final String payStackEnable;
  final String flutterwaveEnable;
  final String phonepeEnable;
  final String rozorpayEnable;
  final String manuallyEnable;
  final String manuallyPayment;
  final String notifationEnable;
  final String? mpPublicKey;
  final String? mpAccessToken;

  PaymentConfigData({
    required this.stripeEnable,
    this.stripeKey,
    this.stripeSecret,
    required this.paypalEnable,
    this.paypalClientId,
    this.paypalSecret,
    this.paypalMode,
    required this.currencyId,
    required this.timeFormat,
    required this.enableAffiliation,
    required this.paypalEmail,
    required this.whatsappShare,
    required this.enableContact,
    required this.hideStickybar,
    required this.productOrderSendMailCustomer,
    required this.productOrderSendMailUser,
    required this.subscriptionModelTime,
    required this.enablePwa,
    required this.isEdit,
    required this.pwaIcon,
    required this.enableAttachmentForInquiry,
    required this.askDetailsBeforeDownloadingContact,
    this.paystackKey,
    this.paystackSecret,
    this.flutterwaveKey,
    this.flutterwaveSecret,
    this.razorpayKey,
    this.razorpaySecret,
    this.phonepeMerchantId,
    this.phonepeMerchantUserId,
    this.phonepeEnv,
    this.phonepeSaltKey,
    this.phonepeSaltIndex,
    this.manualPaymentGuide,
    required this.payStackEnable,
    required this.flutterwaveEnable,
    required this.phonepeEnable,
    required this.rozorpayEnable,
    required this.manuallyPayment,
    required this.notifationEnable,
    this.mpPublicKey,
    this.mpAccessToken,
    required this.manuallyEnable,
  });

  factory PaymentConfigData.fromJson(Map<String, dynamic> json) {
    return PaymentConfigData(
      stripeEnable: json['stripe_enable'] ?? '0',
      stripeKey: json['stripe_key'],
      stripeSecret: json['stripe_secret'],
      paypalEnable: json['paypal_enable'] ?? '0',
      paypalClientId: json['paypal_client_id'],
      paypalSecret: json['paypal_secret'],
      paypalMode: json['paypal_mode'],
      currencyId: json['currency_id'] ?? '20',
      timeFormat: json['time_format'] ?? '1',
      enableAffiliation: json['enable_affiliation'] ?? '0',
      paypalEmail: json['paypal_email'] ?? '',
      whatsappShare: json['whatsapp_share'] ?? '1',
      enableContact: json['enable_contact'] ?? '1',
      hideStickybar: json['hide_stickybar'] ?? '1',
      productOrderSendMailCustomer:
          json['product_order_send_mail_customer'] ?? '1',
      productOrderSendMailUser: json['product_order_send_mail_user'] ?? '1',
      subscriptionModelTime: json['subscription_model_time'] ?? '5',
      enablePwa: json['enable_pwa'] ?? '1',
      isEdit: json['is_edit'] ?? '1',
      pwaIcon: json['pwa_icon'] ?? '',
      enableAttachmentForInquiry: json['enable_attachment_for_inquiry'] ?? '0',
      askDetailsBeforeDownloadingContact:
          json['ask_details_before_downloading_contact'] ?? '0',
      paystackKey: json['paystack_key'],
      paystackSecret: json['paystack_secret'],
      flutterwaveKey: json['flutterwave_key'],
      flutterwaveSecret: json['flutterwave_secret'],
      razorpayKey: json['razorpay_key'],
      razorpaySecret: json['razorpay_secret'],
      phonepeMerchantId: json['phonepe_merchant_id'],
      phonepeMerchantUserId: json['phonepe_merchant_user_id'],
      phonepeEnv: json['phonepe_env'],
      phonepeSaltKey: json['phonepe_salt_key'],
      phonepeSaltIndex: json['phonepe_salt_index'],
      manualPaymentGuide: json['manual_payment_guide'],
      flutterwaveEnable: json['flutterwave_enable'] ?? '0',
      payStackEnable: json['paytack_enable'] ?? '0',
      phonepeEnable: json['phonepe_enable'] ?? '0',
      rozorpayEnable: json['rozorpay_enable'] ?? '0',
      manuallyPayment: json['manually_payment'] ?? '0',
      notifationEnable: json['notifation_enable'] ?? '0',
      mpPublicKey: json['mp_public_key'],
      mpAccessToken: json['mp_access_token'],
      manuallyEnable: json['manually_enable'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stripe_enable': stripeEnable,
      'stripe_key': stripeKey,
      'stripe_secret': stripeSecret,
      'paypal_enable': paypalEnable,
      'paypal_client_id': paypalClientId,
      'paypal_secret': paypalSecret,
      'paypal_mode': paypalMode,
      'currency_id': currencyId,
      'time_format': timeFormat,
      'enable_affiliation': enableAffiliation,
      'paypal_email': paypalEmail,
      'whatsapp_share': whatsappShare,
      'enable_contact': enableContact,
      'hide_stickybar': hideStickybar,
      'product_order_send_mail_customer': productOrderSendMailCustomer,
      'product_order_send_mail_user': productOrderSendMailUser,
      'subscription_model_time': subscriptionModelTime,
      'enable_pwa': enablePwa,
      'is_edit': isEdit,
      'pwa_icon': pwaIcon,
      'enable_attachment_for_inquiry': enableAttachmentForInquiry,
      'ask_details_before_downloading_contact':
          askDetailsBeforeDownloadingContact,
      'paysFtack_key': paystackKey,
      'paystack_secret': paystackSecret,
      'flutterwave_key': flutterwaveKey,
      'flutterwave_secret': flutterwaveSecret,
      'razorpay_key': razorpayKey,
      'razorpay_secret': razorpaySecret,
      'phonepe_merchant_id': phonepeMerchantId,
      'phonepe_merchant_user_id': phonepeMerchantUserId,
      'phonepe_env': phonepeEnv,
      'phonepe_salt_key': phonepeSaltKey,
      'phonepe_salt_index': phonepeSaltIndex,
      'manual_payment_guide': manualPaymentGuide,
      'flutterwave_enable': flutterwaveEnable,
      'paytack_enable': payStackEnable,
      'phonepe_enable': phonepeEnable,
      'rozorpay_enable': rozorpayEnable,
      'manually_payment': manuallyPayment,
      'notifation_enable': notifationEnable,
      'mp_public_key': mpPublicKey,
      'mp_access_token': mpAccessToken,
      'manually_enable': manuallyEnable,
    };
  }
}

class UpdatePaymentConfigModel {
  final bool success;
  final String message;

  UpdatePaymentConfigModel({required this.success, required this.message});

  factory UpdatePaymentConfigModel.fromJson(Map<String, dynamic> json) {
    return UpdatePaymentConfigModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
