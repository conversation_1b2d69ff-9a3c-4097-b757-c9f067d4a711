class GeneralSettingModel {
  final bool success;
  final List<GeneralSettingData> data;
  final String message;

  GeneralSettingModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory GeneralSettingModel.fromJson(Map<String, dynamic> json) {
    return GeneralSettingModel(
      success: json['success'],
      data:
          (json['data'] as List)
              .map((item) => GeneralSettingData.fromJson(item))
              .toList(),
      message: json['message'],
    );
  }
}

class GeneralSettingData {
  final String paypalEmail;
  final String currencyId;
  final String subscriptionModelTime;
  final String timeFormat;
  final String askDetailsBeforeDownloadingContact;
  final String enableAttachmentForInquiry;
  final String enablePwa;
  final String pwaIcon;
  final String language;

  GeneralSettingData({
    required this.paypalEmail,
    required this.currencyId,
    required this.subscriptionModelTime,
    required this.timeFormat,
    required this.askDetailsBeforeDownloadingContact,
    required this.enableAttachmentForInquiry,
    required this.enablePwa,
    required this.pwaIcon,
    required this.language,
  });

  factory GeneralSettingData.fromJson(Map<String, dynamic> json) {
    return GeneralSettingData(
      paypalEmail: json['paypal_email'] ?? '',
      currencyId: json['currency_id'] ?? '',
      subscriptionModelTime: json['subscription_model_time'] ?? '',
      timeFormat: json['time_format'] ?? '',
      askDetailsBeforeDownloadingContact:
          json['ask_details_before_downloading_contact'] ?? '',
      enableAttachmentForInquiry: json['enable_attachment_for_inquiry'] ?? '',
      enablePwa: json['enable_pwa'] ?? '',
      pwaIcon: json['pwa_icon'] ?? '',
      language: json['language'] ?? 'English',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'paypal_email': paypalEmail,
      'currency_id': currencyId,
      'subscription_model_time': subscriptionModelTime,
      'time_format': timeFormat,
      'ask_details_before_downloading_contact':
          askDetailsBeforeDownloadingContact,
      'enable_attachment_for_inquiry': enableAttachmentForInquiry,
      'enable_pwa': enablePwa,
      'pwa_icon': pwaIcon,
      'language': language,
    };
  }
}

class UpdateGeneralSettingModel {
  final bool success;
  final String message;

  UpdateGeneralSettingModel({required this.success, required this.message});

  factory UpdateGeneralSettingModel.fromJson(Map<String, dynamic> json) {
    return UpdateGeneralSettingModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
