class SuperAdminChartDataModel {
  final bool success;
  final String message;
  final SuperAdminChartData data;

  SuperAdminChartDataModel({
    required this.success,
    required this.message,
    required this.data,
  });

  factory SuperAdminChartDataModel.fromJson(Map<String, dynamic> json) =>
      SuperAdminChartDataModel(
        success: json["success"],
        message: json["message"],
        data: SuperAdminChartData.from<PERSON>son(json["data"]),
      );
}

class SuperAdminChartData {
  final List<String> labels;
  final List<BreakDown> breakDown;

  SuperAdminChartData({required this.labels, required this.breakDown});
  factory SuperAdminChartData.empty() {
    return SuperAdminChartData(labels: [], breakDown  : []);
  }
  factory SuperAdminChartData.fromJson(Map<String, dynamic> json) =>
      SuperAdminChartData(
        labels: List<String>.from(json["labels"].map((x) => x)),
        breakDown: List<BreakDown>.from(
          json["breakDown"].map((x) => BreakDown.fromJson(x)),
        ),
      );
}

class BreakDown {
  final String label;
  final List<double> data;
  final List<String> backgroundColor;
  final List<String> borderColor;
  final double lineTension;
  final double radius;

  BreakDown({
    required this.label,
    required this.data,
    required this.backgroundColor,
    required this.borderColor,
    required this.lineTension,
    required this.radius,
  });

  factory BreakDown.fromJson(Map<String, dynamic> json) => BreakDown(
    label: json["label"],
    data: List<double>.from(json["data"].map((x) => double.parse(x))),
    backgroundColor: List<String>.from(json["backgroundColor"].map((x) => x)),
    borderColor: List<String>.from(json["borderColor"].map((x) => x)),
    lineTension: (json["lineTension"] as num).toDouble(),
    radius: (json["radius"] as num).toDouble(),
  );
}
