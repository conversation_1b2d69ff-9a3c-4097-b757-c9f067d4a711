class SuperAdminDashboard {
  final bool success;
  final SuperAdminDashboardData data;
  final String message;

  SuperAdminDashboard({
    required this.success,
    required this.data,
    required this.message,
  });

  factory SuperAdminDashboard.fromJson(Map<String, dynamic> json) {
    return SuperAdminDashboard(
      success: json['success'],
      data: SuperAdminDashboardData.fromJson(json['data']),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data.toJson(), 'message': message};
  }
}

class SuperAdminDashboardData {
  final int? activeVcard;
  final int? deActiveVcard;
  final int? enquiry;
  final int? appointment;

  SuperAdminDashboardData({
    required this.activeVcard,
    required this.deActiveVcard,
    required this.enquiry,
    required this.appointment,
  });

  factory SuperAdminDashboardData.fromJson(Map<String, dynamic> json) {
    return SuperAdminDashboardData(
      activeVcard: json['activeUsersCount'] as int?,
      deActiveVcard: json['deActiveUsersCount'] as int?,
      enquiry: json['activeVcard'] as int?,
      appointment: json['deActiveVcard'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'activeUsersCount': activeVcard,
      'deActiveUsersCount': deActiveVcard,
      'activeVcard': enquiry,
      'deActiveVcard': appointment,
    };
  }

  factory SuperAdminDashboardData.empty() {
    return SuperAdminDashboardData(
      activeVcard: 0,
      deActiveVcard: 0,
      enquiry: 0,
      appointment: 0,
    );
  }
}
