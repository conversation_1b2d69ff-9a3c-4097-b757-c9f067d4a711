class AdminTodayAppointmentModel {
  final bool success;
  final List<AppointmentData> data;
  final String message;

  AdminTodayAppointmentModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory AdminTodayAppointmentModel.fromJson(Map<String, dynamic> json) {
    return AdminTodayAppointmentModel(
      success: json['success'] ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => AppointmentData.fromJson(e))
              .toList() ??
          [],
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "success": success,
      "data": data.map((e) => e.toJson()).toList(),
      "message": message,
    };
  }
}
class AppointmentData {
  final int id;
  final String vcardName;
  final String name;
  final String phone;
  final String email;
  final String date;
  final String fromTime;
  final String toTime;
  final int status;
  final String paidAmount;

  AppointmentData({
    required this.id,
    required this.vcardName,
    required this.name,
    required this.phone,
    required this.email,
    required this.date,
    required this.fromTime,
    required this.toTime,
    required this.status,
    required this.paidAmount,
  });

  factory AppointmentData.fromJson(Map<String, dynamic> json) {
    return AppointmentData(
      id: json['id'] ?? 0,
      vcardName: json['vcard_name'] ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      date: json['date'] ?? '',
      fromTime: json['from_time'] ?? '',
      toTime: json['to_time'] ?? '',
      status: json['status'] ?? 0,
      paidAmount: json['paid_amount'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "id": id,
      "vcard_name": vcardName,
      "name": name,
      "phone": phone,
      "email": email,
      "date": date,
      "from_time": fromTime,
      "to_time": toTime,
      "status": status,
      "paid_amount": paidAmount,
    };
  }
}
