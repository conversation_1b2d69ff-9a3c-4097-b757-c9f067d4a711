import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
class DashboardChartDataModel {
  final bool success;
  final ChartData data;
  final String message;
  DashboardChartDataModel({
    required this.success,
    required this.data,
    required this.message,
  });
  factory DashboardChartDataModel.fromJson(Map<String, dynamic>? json) {
    return DashboardChartDataModel(
      success: json?['success'] as bool? ?? false,
      data: ChartData.fromJson(json?['data'] ?? {}),
      message: json?['message'] as String? ?? '',
    );
  }
  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data.toJson(), 'message': message};
  }
}
class ChartData {
  final List<String> weeklyLabels;
  final List<ChartItem> data;
  ChartData({required this.weeklyLabels, required this.data});
  factory ChartData.fromJson(Map<String, dynamic>? json) {
    return ChartData(
      weeklyLabels:
          (json?['weeklyLabels'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList() ??
          [],
      data:
          (json?['data'] as List<dynamic>?)
              ?.map((item) => ChartItem.fromJson(item))
              .toList() ??
          [],
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'weeklyLabels': weeklyLabels,
      'data': data.map((item) => item.toJson()).toList(),
    };
  }
  factory ChartData.empty() {
    return ChartData(weeklyLabels: [], data: []);
  }
}
class ChartItem {
  final Color backgroundColor;
  final String label;
  final List<int> data;
  final double lineTension;
  final double radius;
  final Color borderColor;
  ChartItem({
    required this.backgroundColor,
    required this.label,
    required this.data,
    required this.lineTension,
    required this.radius,
    required this.borderColor,
  });
  factory ChartItem.fromJson(Map<String, dynamic>? json) {
    return ChartItem(
      backgroundColor:
          _parseColor(json?['backgroundColor'] as String?) ??
          Get.theme.customColors.blueColor!,
      label: json?['label'] as String? ?? '',
      data:
          (json?['data'] as List<dynamic>?)
              ?.map((e) => int.tryParse(e.toString()) ?? 0)
              .toList() ??
          [],
      lineTension: (json?['lineTension'] as num?)?.toDouble() ?? 0.4,
      radius: (json?['radius'] as num?)?.toDouble() ?? 3.0,
      borderColor:
          _parseColor(json?['borderColor'] as String?) ??
          Get.theme.customColors.black!,
    );
  }
  Map<String, dynamic> toJson() {
    return {
      'backgroundColor': _colorToHex(backgroundColor),
      'label': label,
      'data': data,
      'lineTension': lineTension,
      'radius': radius,
      'borderColor': _colorToHex(borderColor),
    };
  }
  static Color? _parseColor(String? color) {
    if (color == null) return null;
    final rgbaPattern = RegExp(
      r'rgba\((\d+),\s*(\d+),\s*(\d+)(?:,\s*(\d*\.?\d+))?\)',
    );
    final match = rgbaPattern.firstMatch(color);
    if (match != null) {
      int r = int.parse(match.group(1)!);
      int g = int.parse(match.group(2)!);
      int b = int.parse(match.group(3)!);
      double opacity =
          match.group(4) != null ? double.parse(match.group(4)!) : 1.0;
      return Color.fromRGBO(r, g, b, opacity);
    }
    return null;
  }
  static String _colorToHex(Color color) {
    // ignore: deprecated_member_use
    return 'rgba(${color.red}, ${color.green}, ${color.blue}, ${color.opacity.toStringAsFixed(2)})';
  }
}