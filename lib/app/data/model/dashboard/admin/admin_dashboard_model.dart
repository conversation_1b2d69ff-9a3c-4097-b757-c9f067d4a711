class AdminDashboard {
  final bool success;
  final DashboardData data;
  final String message;

  AdminDashboard({
    required this.success,
    required this.data,
    required this.message,
  });

  factory AdminDashboard.fromJson(Map<String, dynamic> json) {
    return AdminDashboard(
      success: json['success'],
      data: DashboardData.from<PERSON><PERSON>(json['data']),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data.toJson(), 'message': message};
  }
}

class DashboardData {
  final int? activeVcard;
  final int? deActiveVcard;
  final int? enquiry;
  final int? appointment;

  DashboardData({
    required this.activeVcard,
    required this.deActiveVcard,
    required this.enquiry,
    required this.appointment,
  });

  factory DashboardData.fromJson(Map<String, dynamic> json) {
    return DashboardData(
      activeVcard: json['activeVcard'] as int?,
      deActiveVcard: json['deActiveVcard'] as int?,
      enquiry: json['enquiry'] as int?,
      appointment: json['appointment'] as int?,
    );
  }

  Map<String, dynamic> to<PERSON><PERSON>() {
    return {
      'activeVcard': activeVcard,
      'deActiveVcard': deActiveVcard,
      'enquiry': enquiry,
      'appointment': appointment,
    };
  }

  factory DashboardData.empty() {
    return DashboardData(
      activeVcard: 0,
      deActiveVcard: 0,
      enquiry: 0,
      appointment: 0,
    );
  }
}
