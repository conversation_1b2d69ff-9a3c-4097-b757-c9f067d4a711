class IframeDetailModel {
  final bool success;
  final IframeDetail? data;
  final String message;

  IframeDetailModel({
    required this.success,
    this.data,
    required this.message,
  });

  factory IframeDetailModel.fromJson(Map<String, dynamic> json) {
    return IframeDetailModel(
      success: json['success'] ?? false,
      data: json['data'] != null
          ? IframeDetail.fromJson(json['data'] as Map<String, dynamic>)
          : null,
      message: json['message'] ?? '',
    );
  }
}

class IframeDetail {
  final int? id;
  final String? url;
  final int? vcardId;
  final String? createdAt;
  final String? updatedAt;

  IframeDetail({
    this.id,
    this.url,
    this.vcardId,
    this.createdAt,
    this.updatedAt,
  });

  factory IframeDetail.fromJson(Map<String, dynamic> json) {
    return IframeDetail(
      id: json['id'],
      url: json['url'],
      vcardId: json['vcard_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}
