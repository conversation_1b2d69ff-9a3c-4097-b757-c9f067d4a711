class IframeModel {
  final bool success;
  final List<Iframe>? data;
  final String message;

  IframeModel({required this.success, this.data, required this.message});

  factory IframeModel.fromJson(Map<String, dynamic> json) {
    return IframeModel(
      success: json['success'] ?? false,
      data:
          (json['data'] as List<dynamic>?)
              ?.map((e) => Iframe.fromJson(e))
              .toList(),
      message: json['message'] ?? '',
    );
  }
}

class Iframe {
  final int? id;
  final String? url;
  final int? vcardId;
  final String? createdAt;
  final String? updatedAt;

  Iframe({this.id, this.url, this.vcardId, this.createdAt, this.updatedAt});

  factory Iframe.fromJson(Map<String, dynamic> json) {
    return Iframe(
      id: json['id'],
      url: json['url'],
      vcardId: json['vcard_id'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }
}

class IframeCreateResponse {
  final bool success;
  final String message;
  final Iframe? data;

  IframeCreateResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory IframeCreateResponse.fromJson(Map<String, dynamic> json) {
    return IframeCreateResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? Iframe.fromJson(json['data']) : null,
    );
  }
}
