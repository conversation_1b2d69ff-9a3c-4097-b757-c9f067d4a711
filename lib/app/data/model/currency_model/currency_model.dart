class CurrencyModel {
  bool success;
  Map<String, String>? data;
  String? message;

  CurrencyModel({this.success=false, this.data, this.message});

  CurrencyModel.fromJson(Map<String, dynamic> json)
      : success = json['success'] ?? false {
    if (json['data'] != null) {
      data = Map<String, String>.from(json['data']);
    }
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    if (this.data != null) {
      data['data'] = this.data;
    }
    data['message'] = message;
    return data;
  }
}