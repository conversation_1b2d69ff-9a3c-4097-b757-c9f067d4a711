class ServiceSliderModel {
  final bool success;
  final String message;

  ServiceSliderModel({required this.success, required this.message});

  factory ServiceSliderModel.fromJson(Map<String, dynamic> json) {
    return ServiceSliderModel(
      success: json['success'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
