class ServiceModel {
  final bool success;
  final List<Service> data;
  final String message;

  ServiceModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory ServiceModel.fromJson(Map<String, dynamic> json) {
    return ServiceModel(
      success: json['success'],
      data:
          (json['data'] as List).map((item) => Service.fromJson(item)).toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class Service {
  final int id;
  final String name;
  final String? description;
  final String serviceIcon;
  final int vcardId;
  final String? serviceUrl;

  Service({
    required this.id,
    required this.name,
    this.description,
    required this.serviceIcon,
    required this.vcardId,
    this.serviceUrl,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    return Service(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      serviceIcon: json['service_icon'] as String,
      vcardId: json['vcard_id'] as int,
      serviceUrl: json['service_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'service_icon': serviceIcon,
      'vcard_id': vcardId,
      'service_url': serviceUrl,
    };
  }
}
