class UpdateServiceModel {
  final bool success;
  final String message;

  UpdateServiceModel({required this.success, required this.message});

  factory UpdateServiceModel.fromJson(Map<String, dynamic> json) {
    return UpdateServiceModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
