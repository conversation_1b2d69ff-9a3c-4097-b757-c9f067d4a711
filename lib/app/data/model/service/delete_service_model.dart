class DeleteServiceModel {
  final bool success;
  final String message;

  DeleteServiceModel({required this.success, required this.message});

  factory DeleteServiceModel.fromJson(Map<String, dynamic> json) {
    return DeleteServiceModel(
      success: json['success'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
