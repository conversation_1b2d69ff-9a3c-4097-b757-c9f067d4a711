class CreateServiceModel {
  final bool success;
  final String message;

  CreateServiceModel({required this.success, required this.message});

  factory CreateServiceModel.fromJson(Map<String, dynamic> json) {
    return CreateServiceModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
