class UpdateProductModel {
  final bool success;
  final String message;

  UpdateProductModel({required this.success, required this.message});

  factory UpdateProductModel.fromJson(Map<String, dynamic> json) {
    return UpdateProductModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
