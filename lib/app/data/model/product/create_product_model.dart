class CreateProductModel {
  final bool success;
  final String message;

  CreateProductModel({required this.success, required this.message});

  factory CreateProductModel.fromJson(Map<String, dynamic> json) {
    return CreateProductModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
