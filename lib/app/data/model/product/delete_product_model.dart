class DeleteProductModel {
  final bool success;
  final String message;

  DeleteProductModel({required this.success, required this.message});

  factory DeleteProductModel.fromJson(Map<String, dynamic> json) {
    return DeleteProductModel(
      success: json['success'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
