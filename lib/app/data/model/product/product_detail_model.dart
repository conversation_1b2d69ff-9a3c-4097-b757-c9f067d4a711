class ProductDetailModel {
  final bool success;
  final Product data;
  final String? message;

  ProductDetailModel({required this.success, required this.data, this.message});

  factory ProductDetailModel.fromJson(Map<String, dynamic> json) {
    return ProductDetailModel(
      success: json['success'] ?? false,
      data: Product.fromJson(json['data'] ?? {}),
      message: json['message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data.toJson(), 'message': message};
  }
}

class Product {
  final int id;
  final String name;
  final int? currencyId;
  final String? price;
  final String? description;
  final int vcardId;
  final String? productUrl;
  final List<String>? productIcon;

  Product({
    required this.id,
    required this.name,
    this.currencyId,
    this.price,
    this.description,
    required this.vcardId,
    this.productUrl,
    this.productIcon,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'] as int,
      name: json['name'] as String,
      currencyId: json['currency_id'] as int?,
      price: json['price'] as String?,
      description: json['description'] as String?,
      vcardId: json['vcard_id'] as int,
      productUrl: json['product_url'] as String?,
      productIcon:
          (json['product_icon'] as List<dynamic>?)
              ?.map((e) => e.toString())
              .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'currency_id': currencyId,
      'price': price,
      'description': description,
      'vcard_id': vcardId,
      'product_url': productUrl,
      'product_icon': productIcon,
    };
  }
}
