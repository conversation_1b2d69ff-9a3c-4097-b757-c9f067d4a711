class BlogModel {
  final bool success;
  final List<Blog> data;
  final String message;

  BlogModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory BlogModel.fromJson(Map<String, dynamic> json) {
    return BlogModel(
      success: json['success'],
      data: (json['data'] as List).map((item) => Blog.fromJson(item)).toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class Blog {
  final int id;
  final String title;
  final String? description;
  final String blogIcon;
  final int vcardId;

  Blog({
    required this.id,
    required this.title,
    this.description,
    required this.blogIcon,
    required this.vcardId,
  });

  factory Blog.fromJson(Map<String, dynamic> json) {
    return Blog(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      blogIcon: json['blog_icon'] as String,
      vcardId: json['vcard_id'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'blog_icon': blogIcon,
      'vcard_id': vcardId,
    };
  }
}
