class BlogDetailModel {
  final bool success;
  final Blog data;
  final String message;

  BlogDetailModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory BlogDetailModel.fromJson(Map<String, dynamic> json) {
    return BlogDetailModel(
      success: json['success'] as bool,
        data: Blog.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data.toJson(), 'message': message};
  }
}

class Blog {
  final int id;
  final String title;
  final String? description;
  final String blogIcon;
  final int vcardId;

  Blog({
    required this.id,
    required this.title,
    this.description,
    required this.blogIcon,
    required this.vcardId,
  });

  factory Blog.fromJson(Map<String, dynamic> json) {
    return Blog(
      id: json['id'] as int,
      title: json['title'] as String,
      description: json['description'] as String?,
      blogIcon: json['blog_icon'] as String,
      vcardId: json['vcard_id'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'blog_icon': blogIcon,
      'vcard_id': vcardId,
    };
  }
}
