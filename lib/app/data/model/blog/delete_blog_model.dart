class DeleteBlogModel {
  final bool success;
  final String message;

  DeleteBlogModel({required this.success, required this.message});

  factory DeleteBlogModel.fromJson(Map<String, dynamic> json) {
    return DeleteBlogModel(
      success: json['success'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
