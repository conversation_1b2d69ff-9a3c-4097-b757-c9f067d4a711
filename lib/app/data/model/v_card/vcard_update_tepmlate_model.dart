class VCardUpdateTemplatesModel {
  final bool success;
  final String message;

  VCardUpdateTemplatesModel({required this.success, required this.message});

  factory VCardUpdateTemplatesModel.fromJson(Map<String, dynamic> json) {
    return VCardUpdateTemplatesModel(
      success: json['success'],
      message: json['message'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
