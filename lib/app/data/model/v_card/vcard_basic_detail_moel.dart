class VCardBasicDetailsModel {
  final bool success;
  final String message;
  final VCardBasicDetails data;

  VCardBasicDetailsModel({
    required this.success,
    required this.message,
    required this.data,
  });

  factory VCardBasicDetailsModel.fromJson(Map<String, dynamic> json) {
    return VCardBasicDetailsModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: VCardBasicDetails.fromJson(json['data'] ?? {}),
    );
  }
}

class VCardBasicDetails {
  final int id;
  final String urlAlias;
  final String name;
  final String occupation;
  final String description;
  final String firstName;
  final String lastName;
  final String email;
  final String regionCode;
  final String phone;
  final String coverType;
  final String? youtubeLink;
  final String? locationEmbedTag;
  final String locationType;
  final String location;
  final String locationUrl;
  final String? madeBy;
  final String? madeByUrl;
  final int templateId;
  final int shareBtn;
  final int status;
  final String company;
  final String jobTitle;
  final String dob;
  final String password;
  final int branding;
  final String fontFamily;
  final String fontSize;
  final String? customCss;
  final String? customJs;
  final String? siteTitle;
  final String? homeTitle;
  final String? metaKeyword;
  final String? metaDescription;
  final String? googleAnalytics;
  final String tenantId;
  final int qrCodeDownloadSize;
  final int enableDownloadQrCode;
  final String languageEnabled;
  final int whatsappShare;
  final int hideStickybar;
  final int enableContact;
  final int enableAffiliation;
  final int enableEnquiryForm;
  final String defaultLanguage;
  final String coverImageType;
  final String createdAt;
  final String updatedAt;
  final int isDefault;
  final String? alternativePhone;
  final String? alternativeEmail;
  final String alternativeRegionCode;
  final int showQrCode;
  final int isVerified;
  final int servicesSliderView;
  final String profileUrl;
  final String coverUrl;
  final String profileUrlBase64;
  final String fullName;
  final String faviconUrl;

  VCardBasicDetails({
    this.id = 0,
    this.urlAlias = '',
    this.name = '',
    this.occupation = '',
    this.description = '',
    this.firstName = '',
    this.lastName = '',
    this.email = '',
    this.regionCode = '',
    this.phone = '',
    this.coverType = '',
    this.youtubeLink,
    this.locationEmbedTag,
    this.locationType = '',
    this.location = '',
    this.locationUrl = '',
    this.madeBy,
    this.madeByUrl,
    this.templateId = 0,
    this.shareBtn = 0,
    this.status = 0,
    this.company = '',
    this.jobTitle = '',
    this.dob = '',
    this.password = '',
    this.branding = 0,
    this.fontFamily = '',
    this.fontSize = '',
    this.customCss,
    this.customJs,
    this.siteTitle,
    this.homeTitle,
    this.metaKeyword,
    this.metaDescription,
    this.googleAnalytics,
    this.tenantId = '',
    this.qrCodeDownloadSize = 0,
    this.enableDownloadQrCode = 0,
    this.languageEnabled = '',
    this.whatsappShare = 0,
    this.hideStickybar = 0,
    this.enableContact = 0,
    this.enableAffiliation = 0,
    this.enableEnquiryForm = 0,
    this.defaultLanguage = '',
    this.coverImageType = '',
    this.createdAt = '',
    this.updatedAt = '',
    this.isDefault = 0,
    this.alternativePhone,
    this.alternativeEmail,
    this.alternativeRegionCode = '',
    this.showQrCode = 0,
    this.isVerified = 0,
    this.servicesSliderView = 0,
    this.profileUrl = '',
    this.coverUrl = '',
    this.profileUrlBase64 = '',
    this.fullName = '',
    this.faviconUrl = '',
  });

  factory VCardBasicDetails.fromJson(Map<String, dynamic> json) {
    return VCardBasicDetails(
      id: json['id'] ?? 0,
      urlAlias: json['url_alias'] ?? '',
      name: json['name'] ?? '',
      occupation: json['occupation'] ?? '',
      description: json['description'] ?? '',
      firstName: json['first_name'] ?? '',
      lastName: json['last_name'] ?? '',
      email: json['email'] ?? '',
      regionCode: json['region_code'] ?? '',
      phone: json['phone'] ?? '',
      coverType: json['cover_type'] ?? '',
      youtubeLink: json['youtube_link'],
      locationEmbedTag: json['location_embed_tag'],
      locationType: json['location_type'] ?? '',
      location: json['location'] ?? '',
      locationUrl: json['location_url'] ?? '',
      madeBy: json['made_by'],
      madeByUrl: json['made_by_url'],
      templateId: json['template_id'] ?? 0,
      shareBtn: json['share_btn'] ?? 0,
      status: json['status'] ?? 0,
      company: json['company'] ?? '',
      jobTitle: json['job_title'] ?? '',
      dob: json['dob'] ?? '',
      password: json['password'] ?? '',
      branding: json['branding'] ?? 0,
      fontFamily: json['font_family'] ?? '',
      fontSize: json['font_size'] ?? '',
      customCss: json['custom_css'],
      customJs: json['custom_js'],
      siteTitle: json['site_title'],
      homeTitle: json['home_title'],
      metaKeyword: json['meta_keyword'],
      metaDescription: json['meta_description'],
      googleAnalytics: json['google_analytics'],
      tenantId: json['tenant_id'] ?? '',
      qrCodeDownloadSize: json['qr_code_download_size'] ?? 0,
      enableDownloadQrCode: json['enable_download_qr_code'] ?? 0,
      languageEnabled: json['language_enable'] ?? '',
      whatsappShare: json['whatsapp_share'] ?? 0,
      hideStickybar: json['hide_stickybar'] ?? 0,
      enableContact: json['enable_contact'] ?? 0,
      enableAffiliation: json['enable_affiliation'] ?? 0,
      enableEnquiryForm: json['enable_enquiry_form'] ?? 0,
      defaultLanguage: json['default_language'] ?? '',
      coverImageType: json['cover_image_type'] ?? '',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
      isDefault: json['is_default'] ?? 0,
      alternativePhone: json['alternative_phone'],
      alternativeEmail: json['alternative_email'],
      alternativeRegionCode: json['alternative_region_code'] ?? '',
      showQrCode: json['show_qr_code'] ?? 0,
      isVerified: json['is_verified'] ?? 0,
      servicesSliderView: json['services_slider_view'] ?? 0,
      profileUrl: json['profile_url'] ?? '',
      coverUrl: json['cover_url'] ?? '',
      profileUrlBase64: json['profile_url_base64'] ?? '',
      fullName: json['full_name'] ?? '',
      faviconUrl: json['favicon_url'] ?? '',
    );
  }
}
