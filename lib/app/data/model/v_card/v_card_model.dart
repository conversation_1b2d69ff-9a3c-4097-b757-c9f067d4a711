class VCardModel {
  final bool success;
  final List<VCardData> data;
  final String message;

  VCardModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory VCardModel.fromJson(Map<String, dynamic> json) {
    return VCardModel(
      success: json['success'],
      data:
          (json['data'] as List)
              .map((item) => VCardData.fromJson(item))
              .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class VCardData {
  final int? id;
  final String? name;
  final String? urlAlias;
  final String? occupation;
  final String? image;
  final String? createdAt;

  VCardData({
    required this.id,
    required this.name,
    required this.urlAlias,
    required this.occupation,
    required this.image,
    required this.createdAt,
  });

  factory VCardData.fromJson(Map<String, dynamic> json) {
    return VCardData(
      id: json['id'] as int?,
      name: json['name'] as String?,
      urlAlias: json['url_alias'] as String?,
      occupation: json['occupation'] as String?,
      image: json['image'] as String?,
      createdAt: json['created_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url_alias': urlAlias,
      'occupation': occupation,
      'image': image,
      'created_at': createdAt,
    };
  }
}
