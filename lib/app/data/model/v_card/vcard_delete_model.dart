class VCardDeleteModel {
  final bool success;
  final String message;

  VCardDeleteModel({
    required this.success,
    required this.message,
  });

  factory VCardDeleteModel.fromJson(Map<String, dynamic> json) {
    return VCardDeleteModel(
      success: json['success'],
    
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
    };
  }
}
