class CreateVCardModel {
  final bool success;
  final String message;
  final int vcardId;

  CreateVCardModel({
    required this.success,
    required this.message,
    required this.vcardId,
  });

  factory CreateVCardModel.fromJson(Map<String, dynamic> json) {
    return CreateVCardModel(
      success: json['success'],
      message: json['message'],
      vcardId: json['data']['vcard_id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': {'vcard_id': vcardId},
    };
  }
}
