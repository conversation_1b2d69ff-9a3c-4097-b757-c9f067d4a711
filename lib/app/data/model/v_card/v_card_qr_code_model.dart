
class VCardQrCodeModel {
  final bool success;
  final List<VCardQrCodeData> data;
  final String message;

  VCardQrCodeModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory VCardQrCodeModel.fromJson(Map<String, dynamic> json) {
    return VCardQrCodeModel(
      success: json['success'],
      data:
          (json['data'] as List)
              .map((item) => VCardQrCodeData.fromJson(item))
              .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class VCardQrCodeData {
  final String? qrCodeUrl;

  VCardQrCodeData({required this.qrCodeUrl});

  factory VCardQrCodeData.fromJson(Map<String, dynamic> json) {
    return VCardQrCodeData(qrCodeUrl: json['url'] as String?);
  }

  Map<String, dynamic> toJson() {
    return {'url': qrCodeUrl};
  }
}



// Future<String> downloadQrcode(
//   String url,
//   String name,
// ) async {
//   // Add your function code here!
//   int randomNumber = Random().nextInt(1000000);
//   String imageName = '$name-$randomNumber';
//   Directory directory1 = Directory('/storage/emulated/0/Download');
//   // Directory directory1 = Directory('/storage/emulated/0/Documents/Vcard');

//   if (!await directory1.exists()) {
//     await directory1.create(recursive: true);
//   }
//   final filePath = '${directory1.path}/$imageName.png';
//   try {
//     var img1 = QRImage(
//       url,
//       backgroundColor: ColorUint8.rgb(255, 255, 255),
//       size: 300,
//     ).generate();

//     File imageFile = File(filePath);
//     await imageFile.writeAsBytes(encodePng(img1));
//     return 'Image downloaded successfully at: Document/Vcard';
//   } catch (e) {
//     return '';
//   }
// }
