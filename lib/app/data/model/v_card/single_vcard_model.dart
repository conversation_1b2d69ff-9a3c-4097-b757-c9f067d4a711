class SingleVCardModel {
  final bool success;
  final SingleVCardData data;
  final String message;

  SingleVCardModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory SingleVCardModel.fromJson(Map<String, dynamic> json) {
    return SingleVCardModel(
      success: json['success'],
      data: SingleVCardData.from<PERSON>son(json['data']),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.toJson(),
      'message': message,
    };
  }
}

class SingleVCardData {
  final int? id;
  final String? name;
  final String? urlAlias;
  final String? occupation;
  final String? image;
  final String? createdAt;
  final int? servicesSliderView;

  SingleVCardData({
    required this.id,
    required this.name,
    required this.urlAlias,
    required this.occupation,
    required this.image,
    required this.createdAt,
    required this.servicesSliderView,
  });

  factory SingleVCardData.fromJson(Map<String, dynamic> json) {
    return SingleVCardData(
      id: json['id'] as int?,
      name: json['name'] as String?,
      urlAlias: json['url_alias'] as String?, // still included if ever present
      occupation: json['occupation'] as String?,
      image: json['image'] as String?,
      createdAt: json['created_at'] as String?,
      servicesSliderView: json['services_slider_view'] as int?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url_alias': urlAlias,
      'occupation': occupation,
      'image': image,
      'created_at': createdAt,
      'services_slider_view': servicesSliderView,
    };
  }
}
