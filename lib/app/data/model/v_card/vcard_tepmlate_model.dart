class VCardTemplatesModel {
  final bool success;
  final String message;
  final List<VCardTemplate> templates;
  final int? status;

  VCardTemplatesModel({
    required this.success,
    required this.message,
    required this.templates,
    this.status,
  });

  factory VCardTemplatesModel.fromJson(Map<String, dynamic> json) {
    final templates = (json['data'] as List)
        .where((item) => item is Map<String, dynamic> && item['id'] != null)
        .map((item) => VCardTemplate.fromJson(item))
        .toList();

    final statusItem = (json['data'] as List)
        .firstWhere((item) => item['status'] != null, orElse: () => {});

    return VCardTemplatesModel(
      success: json['success'],
      message: json['message'],
      templates: templates,
      status: statusItem['status'],
    );
  }
}

class VCardTemplate {
  final int id;
  final String name;
  final String templateUrl;
  final bool isSelected;

  VCardTemplate({
    required this.id,
    required this.name,
    required this.templateUrl,
    required this.isSelected,
  });

  factory VCardTemplate.fromJson(Map<String, dynamic> json) {
    return VCardTemplate(
      id: json['id'],
      name: json['name'],
      templateUrl: json['template_url'],
      isSelected: json['is_selected'],
    );
  }
}