class AdvancedSettingDetailModel {
  final bool success;
  final AdvancedSetting? data;
  final String? message;

  AdvancedSettingDetailModel({
    this.success = false,
    this.data,
    this.message,
  });

  factory AdvancedSettingDetailModel.fromJson(Map<String, dynamic> json) {
    return AdvancedSettingDetailModel(
      success: json['success'],
      data: json['data'] != null ? AdvancedSetting.fromJson(json['data'] as Map<String, dynamic>) : null,
      message: json['message'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data?.toJson(),
      'message': message,
    };
  }
}

class AdvancedSetting {
  final String? customJs;
  final String? customCss;
  final int? branding;
  final String? password;

  AdvancedSetting({
    this.customJs,
    this.customCss,
    this.branding,
    this.password,
  });

  factory AdvancedSetting.fromJson(Map<String, dynamic> json) {
    return AdvancedSetting(
      customJs: json['custom_js'] as String?,
      customCss: json['custom_css'] as String?,
      branding: json['branding'] as int?,
      password: json['password'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'custom_js': customJs,
      'custom_css': customCss,
      'branding': branding,
      'password': password,
    };
  }
}
