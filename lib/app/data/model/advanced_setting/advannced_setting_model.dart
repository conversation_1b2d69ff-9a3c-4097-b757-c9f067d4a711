class CreateAdvancedSettingModel {
  final bool success;
  final String message;

  CreateAdvancedSettingModel({required this.success, required this.message});

  factory CreateAdvancedSettingModel.fromJson(Map<String, dynamic> json) {
    return CreateAdvancedSettingModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
