class FontListModel {
  final bool success;
  final Map<String, String> fonts;
  final String message;

  FontListModel({
    required this.success,
    required this.fonts,
    required this.message,
  });

  factory FontListModel.fromJson(Map<String, dynamic> json) {
    return FontListModel(
      success: json['success'] ?? false,
      fonts: Map<String, String>.from(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }
}

class FontModel {
  final bool success;
  final String fontFamily;
  final String fontSize;
  final String message;

  FontModel({
    required this.success,
    required this.fontFamily,
    required this.fontSize,
    required this.message,
  });

  factory FontModel.fromJson(Map<String, dynamic> json) {
    return FontModel(
      success: json['success'] ?? false,
      fontFamily: json['data']['font_family'] ?? 'Default',
      fontSize: json['data']['font_size'] ?? '16',
      message: json['message'] ?? '',
    );
  }
}

class FontUpdateModel {
  final bool success;
  final String message;

  FontUpdateModel({required this.success, required this.message});

  factory FontUpdateModel.fromJson(Map<String, dynamic> json) {
    return FontUpdateModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }
}
