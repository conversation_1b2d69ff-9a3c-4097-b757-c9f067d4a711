// nfc_model.dart
class NfcCardListModel {
  bool? success;
  List<NfcCardData>? data;
  String? message;

  NfcCardListModel({this.success, this.data, this.message});

  NfcCardListModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    if (json['data'] != null) {
      data = <NfcCardData>[];
      json['data'].forEach((v) {
        data!.add(NfcCardData.fromJson(v));
      });
    }
    message = json['message'];
  }
}

class NfcCardData {
  int? id;
  String? name;
  String? cardType;
  String? vcard;
  String? companyName;
  String? address;
  String? orderStatus;
  String? createdAt;
  String? paymentStatus;
  String? paymentType;
  String? logo;

  NfcCardData({
    this.id,
    this.name,
    this.cardType,
    this.vcard,
    this.companyName,
    this.address,
    this.orderStatus,
    this.createdAt,
    this.paymentStatus,
    this.paymentType,
    this.logo,
  });

  factory NfcCardData.fromJson(Map<String, dynamic> json) => NfcCardData(
    id: json['id'],
    name: json['name'],
    cardType: json['card_type'],
    vcard: json['vcard'],
    companyName: json['company_name'],
    address: json['address'],
    orderStatus: json['order_status'],
    createdAt: json['created_at'],
    paymentStatus: json['payment_status'],
    paymentType: json['payment_type'],
    logo: json['logo'],
  );
}

class OrderNfcModel {
  bool? success;
  List<OrderNfcData>? data;
  String? message;

  OrderNfcModel({this.success, this.data, this.message});

  OrderNfcModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    if (json['data'] != null) {
      data = <OrderNfcData>[];
      json['data'].forEach((v) {
        data!.add(OrderNfcData.fromJson(v));
      });
    }
    message = json['message'];
  }
}

class OrderNfcData {
  int? id;
  String? name;
  String? description;
  String? price;
  int? isDefault;
  String? nfcImage;
  String? nfcBackImage;

  OrderNfcData({
    this.id,
    this.name,
    this.description,
    this.price,
    this.isDefault,
    this.nfcImage,
    this.nfcBackImage,
  });

  factory OrderNfcData.fromJson(Map<String, dynamic> json) => OrderNfcData(
    id: json['id'],
    name: json['name'],
    description: json['description'],
    price: json['price'],
    isDefault: json['is_default'],
    nfcImage: json['nfc_image'],
    nfcBackImage: json['nfc_back_image'],
  );
}

class VcardListModel {
  bool? success;
  Map<String, String>? data;
  String? message;

  VcardListModel({this.success, this.data, this.message});

  VcardListModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    data = Map<String, String>.from(json['data']);
    message = json['message'];
  }
}

class PaymentTypesModel {
  bool? success;
  Map<String, String>? data;
  String? message;

  PaymentTypesModel({this.success, this.data, this.message});

  PaymentTypesModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    data = Map<String, String>.from(json['data']);
    message = json['message'];
  }
}

class CreateOrderNfcResponse {
  final bool success;
  final CreateOrderNfcData data;
  final String message;

  CreateOrderNfcResponse({
    required this.success,
    required this.data,
    required this.message,
  });

  factory CreateOrderNfcResponse.fromJson(Map<String, dynamic> json) {
    return CreateOrderNfcResponse(
      success: json['success'] ?? false,
      data: CreateOrderNfcData.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }
}

class CreateOrderNfcData {
  final String paymentMethod;

  CreateOrderNfcData({required this.paymentMethod});

  factory CreateOrderNfcData.fromJson(Map<String, dynamic> json) {
    return CreateOrderNfcData(paymentMethod: json['payment_method'] ?? '');
  }
}
