class ProductOrderModel {
  final bool success;
  final List<ProductOrder> data;
  final String message;

  ProductOrderModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory ProductOrderModel.fromJson(Map<String, dynamic> json) {
    return ProductOrderModel(
      success: json['success'] ?? false,
      data: (json['data'] as List<dynamic>)
          .map((item) => ProductOrder.fromJson(item))
          .toList(),
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class ProductOrder {
  final int? id;
  final String? productName;
  final String? name;
  final String? email;
  final String? phone;
  final String? orderAt;
  final int? paymentType;
  final String? amount;
  final int? type;
  final int? status;
  final String? address;

  ProductOrder({
    this.id,
    this.productName,
    this.name,
    this.email,
    this.phone,
    this.orderAt,
    this.paymentType,
    this.amount,
    this.type,
    this.status,
    this.address,
  });

  factory ProductOrder.fromJson(Map<String, dynamic> json) {
    return ProductOrder(
      id: json['id'] as int?,
      productName: json['product_name'] as String?,
      name: json['name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      orderAt: json['order_at'] as String?,
      paymentType: json['payment_type'] as int?,
      amount: json['amount'] as String?,
      type: json['type'] as int?,
      status: json['status'] as int?,
      address: json['address'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_name': productName,
      'name': name,
      'email': email,
      'phone': phone,
      'order_at': orderAt,
      'payment_type': paymentType,
      'amount': amount,
      'type': type,
      'status': status,
      'address': address,
    };
  }
}
