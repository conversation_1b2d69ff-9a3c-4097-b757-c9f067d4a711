class SocialConnectModel {
  final bool success;
  final SocialConnect data;
  final String message;

  SocialConnectModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory SocialConnectModel.fromJson(Map<String, dynamic> json) {
    return SocialConnectModel(
      success: json['success'] ?? false,
      data: SocialConnect.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] ?? '',
    );
  }
}

class SocialConnect {
  final int id;
  final int vcardId;
  final String? website;
  final String? twitter;
  final String? facebook;
  final String? instagram;
  final String? youtube;
  final String? reddit;
  final String? tumblr;
  final String? linkedin;
  final String? whatsapp;
  final String? pinterest;
  final String? tiktok;
  final String? snapchat;
  final String? socialIcon;

  SocialConnect({
    required this.id,
    required this.vcardId,
    this.website,
    this.twitter,
    this.facebook,
    this.instagram,
    this.youtube,
    this.reddit,
    this.tumblr,
    this.linkedin,
    this.whatsapp,
    this.pinterest,
    this.tiktok,
    this.snapchat,
    this.socialIcon,
  });

  factory SocialConnect.fromJson(Map<String, dynamic> json) {
    return SocialConnect(
      id: json['id'] ?? 0,
      vcardId: json['vcard_id'] ?? 0,
      website: json['website'],
      twitter: json['twitter'],
      facebook: json['facebook'],
      instagram: json['instagram'],
      youtube: json['youtube'],
      reddit: json['reddit'],
      tumblr: json['tumblr'],
      linkedin: json['linkedin'],
      whatsapp: json['whatsapp'],
      pinterest: json['pinterest'],
      tiktok: json['tiktok'],
      snapchat: json['snapchat'],
      socialIcon: json['social_icon'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vcard_id': vcardId,
      'website': website,
      'twitter': twitter,
      'facebook': facebook,
      'instagram': instagram,
      'youtube': youtube,
      'reddit': reddit,
      'tumblr': tumblr,
      'linkedin': linkedin,
      'whatsapp': whatsapp,
      'pinterest': pinterest,
      'tiktok': tiktok,
      'snapchat': snapchat,
      'social_icon': socialIcon,
    };
  }
}

class SocialConnectResponseModel {
  final bool success;
  // final List<SocialConnect> data;
  final String message;

  SocialConnectResponseModel({
    required this.success,
    // required this.data,
    required this.message,
  });

  factory SocialConnectResponseModel.fromJson(Map<String, dynamic> json) {
    return SocialConnectResponseModel(
      success: json['success'] ?? false,
      // data:
      //     json['data'] != null
      //         ? List<SocialConnect>.from(
      //           json['data'].map((x) => SocialConnect.fromJson(x)),
      //         )
      //         : [],
      message: json['message'] ?? '',
    );
  }
}
