class CreateBusinessCardModel {
  final bool success;
  // final List<BusinessCardData> data;
  final String message;

  CreateBusinessCardModel({
    required this.success,
    // required this.data,
    required this.message,
  });

  factory CreateBusinessCardModel.fromJson(Map<String, dynamic> json) {
    return CreateBusinessCardModel(
      success: json['success'],
      // data:
      //     (json['data'] as List)
      //         .map((item) => BusinessCardData.fromJson(item))
      //         .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      // 'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

// class BusinessCardData {
//   final int? id;
//   final String? name;
//   final String? url;
//   final String? occupation;
//   final String? image;
//   final String? groupName;

//   BusinessCardData({
//     required this.id,
//     required this.name,
//     required this.url,
//     required this.occupation,
//     required this.image,
//     required this.groupName,
//   });

//   factory BusinessCardData.fromJson(Map<String, dynamic> json) {
//     return BusinessCardData(
//       id: json['id'] as int?,
//       name: json['name'] as String?,
//       url: json['url'] as String?,
//       occupation: json['occupation'] as String?,
//       image: json['vcard_image'] as String?,
//       groupName: json['group_name'] as String?,
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'url': url,
//       'occupation': occupation,
//       'image': image,
//       'group_name': groupName,
//     };
//   }
// }
