class AdminGrouopModel {
  final bool success;
  final List<AdminGroupData> data;
  final String message;

  AdminGrouopModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory AdminGrouopModel.fromJson(Map<String, dynamic> json) {
    return AdminGrouopModel(
      success: json['success'],
      data:
          (json['data'] as List)
              .map((item) => AdminGroupData.fromJson(item))
              .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class AdminGroupData {
  final int? id;
  final String? name;
  final String? tenantId;
  final String? createdAt;

  AdminGroupData({
    required this.id,
    required this.name,
    required this.tenantId,
    required this.createdAt,
  });

  factory AdminGroupData.fromJson(Map<String, dynamic> json) {
    return AdminGroupData(
      id: json['id'] as int?,
      name: json['name'] as String?,
      tenantId: json['tenant_id'] as String?,
      createdAt: json['created_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'tenant_id': tenantId,
      'created_at': createdAt,
    };
  }
}
