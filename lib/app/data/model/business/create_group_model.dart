class CreateGrouopModel {
  final bool success;
  final String message;

  CreateGrouopModel({required this.success, required this.message});

  factory CreateGrouopModel.fromJson(Map<String, dynamic> json) {
    return CreateGrouopModel(
      success: json['success'],

      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
