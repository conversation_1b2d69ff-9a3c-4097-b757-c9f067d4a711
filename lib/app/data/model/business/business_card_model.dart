class BusinessCardModel {
  final bool success;
  final List<BusinessCardData> data;
  final String message;

  BusinessCardModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory BusinessCardModel.fromJson(Map<String, dynamic> json) {
    return BusinessCardModel(
      success: json['success'] ?? false,
      data: (json['data'] as List)
          .map((item) => BusinessCardData.fromJson(item))
          .toList(),
      message: json['message'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class BusinessCardData {
  final int? id;
  final int? vcardId;
  final String? url;
  final String? name;
  final String? occupation;
  final String? phone;
  final DateTime? createdAt;
  final String? groupName;
  final String? vcardImage;

  BusinessCardData({
    required this.id,
    required this.vcardId,
    required this.url,
    required this.name,
    required this.occupation,
    required this.phone,
    required this.createdAt,
    required this.groupName,
    required this.vcardImage,
  });

  factory BusinessCardData.fromJson(Map<String, dynamic> json) {
    return BusinessCardData(
      id: json['id'] as int?,
      vcardId: json['vcard_id'] as int?,
      url: json['url'] as String?,
      name: json['name'] as String?,
      occupation: json['occupation'] as String?,
      phone: json['phone'] as String?,
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      groupName: json['group_name'] as String?,
      vcardImage: json['vcard_image'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vcard_id': vcardId,
      'url': url,
      'name': name,
      'occupation': occupation,
      'phone': phone,
      'created_at': createdAt?.toIso8601String(),
      'group_name': groupName,
      'vcard_image': vcardImage,
    };
  }
}
