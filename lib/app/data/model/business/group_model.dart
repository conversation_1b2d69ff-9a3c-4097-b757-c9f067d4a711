class GrouopModel {
  final bool success;
  final List<GroupData> data;
  final String message;

  GrouopModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory GrouopModel.fromJson(Map<String, dynamic> json) {
    return GrouopModel(
      success: json['success'],
      data:
          (json['data'] as List)
              .map((item) => GroupData.fromJson(item))
              .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class GroupData {
  final int? id;
  final String? name;
  final String? tenantId;
  final String? createdAt;

  GroupData({
    required this.id,
    required this.name,
    required this.tenantId,
    required this.createdAt,
  });

  factory GroupData.fromJson(Map<String, dynamic> json) {
    return GroupData(
      id: json['id'] as int?,
      name: json['name'] as String?,
      tenantId: json['tenant_id'] as String?,
      createdAt: json['created_at'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'tenant_id': tenantId,
      'created_at': createdAt,
    };
  }
}
