class TestimonialsModel {
  final bool success;
  final List<Testimonials> data;
  final String message;

  TestimonialsModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory TestimonialsModel.fromJson(Map<String, dynamic> json) {
    return TestimonialsModel(
      success: json['success'],
      data: (json['data'] as List)
          .map((item) => Testimonials.fromJson(item))
          .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class Testimonials {
  final int id;
  final String name;
  final String? description;
  final int vcardId;
  final String? imageUrl;

  Testimonials({
    required this.id,
    required this.name,
    this.description,
    required this.vcardId,
    this.imageUrl,
  });

  factory Testimonials.fromJson(Map<String, dynamic> json) {
    return Testimonials(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      vcardId: json['vcard_id'] as int,
      imageUrl: json['image_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'vcard_id': vcardId,
      'image_url': imageUrl,
    };
  }
}
