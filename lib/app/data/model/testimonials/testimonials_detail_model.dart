class TestimonialsDetailModel {
  final bool success;
  final Testimonials data;
  final String message;

  TestimonialsDetailModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory TestimonialsDetailModel.fromJson(Map<String, dynamic> json) {
    return TestimonialsDetailModel(
      success: json['success'] as bool,
      data: Testimonials.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data.toJson(), 'message': message};
  }
}

class Testimonials {
  final int id;
  final String name;
  final String? description;
  final int vcardId;
  final String? imageUrl;

  Testimonials({
    required this.id,
    required this.name,
    this.description,
    required this.vcardId,
    this.imageUrl,
  });

  factory Testimonials.fromJson(Map<String, dynamic> json) {
    return Testimonials(
      id: json['id'] as int,
      name: json['name'] as String,
      description: json['description'] as String?,
      vcardId: json['vcard_id'] as int,
      imageUrl: json['image_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'vcard_id': vcardId,
      'image_url': imageUrl,
    };
  }
}
