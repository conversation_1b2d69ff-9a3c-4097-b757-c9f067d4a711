class DeleteTestimonialsModel {
  final bool success;
  final String message;

  DeleteTestimonialsModel({required this.success, required this.message});

  factory DeleteTestimonialsModel.fromJson(Map<String, dynamic> json) {
    return DeleteTestimonialsModel(
      success: json['success'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
