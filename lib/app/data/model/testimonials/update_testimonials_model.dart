class UpdateTestimonialsModel {
  final bool success;
  final String message;

  UpdateTestimonialsModel({required this.success, required this.message});

  factory UpdateTestimonialsModel.fromJson(Map<String, dynamic> json) {
    return UpdateTestimonialsModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
