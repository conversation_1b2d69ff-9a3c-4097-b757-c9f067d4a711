class CreateTestimonialsModel {
  final bool success;
  final String message;

  CreateTestimonialsModel({required this.success, required this.message});

  factory CreateTestimonialsModel.fromJson(Map<String, dynamic> json) {
    return CreateTestimonialsModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
