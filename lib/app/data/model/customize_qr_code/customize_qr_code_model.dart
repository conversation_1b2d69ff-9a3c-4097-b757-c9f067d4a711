class CustomizeQRCodeModel {
  final bool success;
  final List<QRCodeSetting> data;
  final String message;

  CustomizeQRCodeModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory CustomizeQRCodeModel.fromJson(Map<String, dynamic> json) {
    return CustomizeQRCodeModel(
      success: json['success'] ?? false,
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => QRCodeSetting.fromJson(e))
              .toList() ??
          [],
      message: json['message'] ?? '',
    );
  }
}

class QRCodeSetting {
  final String key;
  final String value;
  final int vcardId;

  QRCodeSetting({
    required this.key,
    required this.value,
    required this.vcardId,
  });

  factory QRCodeSetting.fromJson(Map<String, dynamic> json) {
    return QRCodeSetting(
      key: json['key'] ?? '',
      value: json['value'] ?? '',
      vcardId: json['vcard_id'] ?? 0,
    );
  }
}




class UpdateCustomizeQRCodeModel {
  final bool success;
  final String message;

  UpdateCustomizeQRCodeModel({required this.success, required this.message});

  factory UpdateCustomizeQRCodeModel.fromJson(Map<String, dynamic> json) {
    return UpdateCustomizeQRCodeModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }
}