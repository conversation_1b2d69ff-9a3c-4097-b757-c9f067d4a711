class SubscriptionPlanModel {
  final bool success;
  final List<SubscriptionData> data;
  final String message;

  SubscriptionPlanModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory SubscriptionPlanModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanModel(
      success: json['success'],
      data: (json['data'] as List)
          .map((item) => SubscriptionData.fromJson(item))
          .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class SubscriptionData {
  final int id;
  final String name;
  final String currency;
  final int noOfVcards;
  final int trialDays;
  final int frequency;
  final int price;
  final String? startDate;
  final String? endDate;
  final Features features;
  final bool currentActiveSubscription;

  SubscriptionData({
    required this.id,
    required this.name,
    required this.currency,
    required this.noOfVcards,
    required this.trialDays,
    required this.frequency,
    required this.price,
    required this.startDate,
    required this.endDate,
    required this.features,
    required this.currentActiveSubscription,
  });

  factory SubscriptionData.fromJson(Map<String, dynamic> json) {
    return SubscriptionData(
      id: json['id'],
      name: json['name'],
      currency: json['currency'],
      noOfVcards: json['no_of_vcards'],
      trialDays: json['trial_days'],
      frequency: json['frequency'],
      price: json['price'],
      startDate: json['start_date'],
      endDate: json['end_date'],
      features: Features.fromJson(json['features']),
      currentActiveSubscription: json['current_active_subscription'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'currency': currency,
      'no_of_vcards': noOfVcards,
      'trial_days': trialDays,
      'frequency': frequency,
      'price': price,
      'start_date': startDate,
      'end_date': endDate,
      'features': features.toJson(),
      'current_active_subscription': currentActiveSubscription,
    };
  }
}

class Features {
  final int productsServices;
  final int testimonials;
  final int hideBranding;
  final int enquiryForm;
  final int socialLinks;
  final int password;
  final int customFonts;
  final int products;
  final int appointments;
  final int gallery;
  final int analytics;
  final int seo;
  final int blog;
  final int affiliation;
  final int customQrcode;
  final int instaEmbed;
  final int iframes;
  final int allowCustomDomain;
  final int customLinks;
  final int customCss;
  final int customJs;
  final int dynamicVcard;
  final int whatsappStore;

  Features({
    required this.productsServices,
    required this.testimonials,
    required this.hideBranding,
    required this.enquiryForm,
    required this.socialLinks,
    required this.password,
    required this.customFonts,
    required this.products,
    required this.appointments,
    required this.gallery,
    required this.analytics,
    required this.seo,
    required this.blog,
    required this.affiliation,
    required this.customQrcode,
    required this.instaEmbed,
    required this.iframes,
    required this.allowCustomDomain,
    required this.customLinks,
    required this.customCss,
    required this.customJs,
    required this.dynamicVcard,
    required this.whatsappStore,
  });

  factory Features.fromJson(Map<String, dynamic> json) {
    return Features(
      productsServices: json['products_services'],
      testimonials: json['testimonials'],
      hideBranding: json['hide_branding'],
      enquiryForm: json['enquiry_form'],
      socialLinks: json['social_links'],
      password: json['password'],
      customFonts: json['custom_fonts'],
      products: json['products'],
      appointments: json['appointments'],
      gallery: json['gallery'],
      analytics: json['analytics'],
      seo: json['seo'],
      blog: json['blog'],
      affiliation: json['affiliation'],
      customQrcode: json['custom_qrcode'],
      instaEmbed: json['insta_embed'],
      iframes: json['iframes'],
      allowCustomDomain: json['allow_custom_domain'],
      customLinks: json['custom_links'],
      customCss: json['custom_css'],
      customJs: json['custom_js'],
      dynamicVcard: json['dynamic_vcard'],
      whatsappStore: json['whatsapp_store'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'products_services': productsServices,
      'testimonials': testimonials,
      'hide_branding': hideBranding,
      'enquiry_form': enquiryForm,
      'social_links': socialLinks,
      'password': password,
      'custom_fonts': customFonts,
      'products': products,
      'appointments': appointments,
      'gallery': gallery,
      'analytics': analytics,
      'seo': seo,
      'blog': blog,
      'affiliation': affiliation,
      'custom_qrcode': customQrcode,
      'insta_embed': instaEmbed,
      'iframes': iframes,
      'allow_custom_domain': allowCustomDomain,
      'custom_links': customLinks,
      'custom_css': customCss,
      'custom_js': customJs,
      'dynamic_vcard': dynamicVcard,
      'whatsapp_store': whatsappStore,
    };
  }
}

// class SubscriptionPlanModel {
//   final bool success;
//   final List<SubscriptionData> data;
//   final String message;

//   SubscriptionPlanModel({
//     required this.success,
//     required this.data,
//     required this.message,
//   });

//   factory SubscriptionPlanModel.fromJson(Map<String, dynamic> json) {
//     return SubscriptionPlanModel(
//       success: json['success'],
//       data: (json['data'] as List)
//           .map((item) => SubscriptionData.fromJson(item))
//           .toList(),
//       message: json['message'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'success': success,
//       'data': data.map((item) => item.toJson()).toList(),
//       'message': message,
//     };
//   }
// }

// class SubscriptionData {
//   final int id;
//   final String name;
//   final String currency;
//   final int noOfVcards;
//   final int trialDays;
//   final int frequency;
//   final int price;
//   final Features features;
//   final bool currentActiveSubscription;

//   SubscriptionData({
//     required this.id,
//     required this.name,
//     required this.currency,
//     required this.noOfVcards,
//     required this.trialDays,
//     required this.frequency,
//     required this.price,
//     required this.features,
//     required this.currentActiveSubscription,
//   });

//   factory SubscriptionData.fromJson(Map<String, dynamic> json) {
//     return SubscriptionData(
//       id: json['id'],
//       name: json['name'],
//       currency: json['currency'],
//       noOfVcards: json['no_of_vcards'],
//       trialDays: json['trial_days'],
//       frequency: json['frequency'],
//       price: json['price'],
//       features: Features.fromJson(json['features']),
//       currentActiveSubscription: json['current_active_subscription'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'id': id,
//       'name': name,
//       'currency': currency,
//       'no_of_vcards': noOfVcards,
//       'trial_days': trialDays,
//       'frequency': frequency,
//       'price': price,
//       'features': features.toJson(),
//       'current_active_subscription': currentActiveSubscription,
//     };
//   }
// }

// class Features {
//   final int productsServices;
//   final int testimonials;
//   final int hideBranding;
//   final int enquiryForm;
//   final int socialLinks;
//   final int password;
//   final int customFonts;
//   final int products;
//   final int appointments;
//   final int gallery;
//   final int analytics;
//   final int seo;
//   final int blog;
//   final int affiliation;
//   final int customQrcode;
//   final int instaEmbed;
//   final int iframes;
//   final int allowCustomDomain;
//   final int customLinks;
//   final int customCss;
//   final int customJs;
//   final int dynamicVcard;

//   Features({
//     required this.productsServices,
//     required this.testimonials,
//     required this.hideBranding,
//     required this.enquiryForm,
//     required this.socialLinks,
//     required this.password,
//     required this.customFonts,
//     required this.products,
//     required this.appointments,
//     required this.gallery,
//     required this.analytics,
//     required this.seo,
//     required this.blog,
//     required this.affiliation,
//     required this.customQrcode,
//     required this.instaEmbed,
//     required this.iframes,
//     required this.allowCustomDomain,
//     required this.customLinks,
//     required this.customCss,
//     required this.customJs,
//     required this.dynamicVcard,
//   });

//   factory Features.fromJson(Map<String, dynamic> json) {
//     return Features(
//       productsServices: json['products_services'],
//       testimonials: json['testimonials'],
//       hideBranding: json['hide_branding'],
//       enquiryForm: json['enquiry_form'],
//       socialLinks: json['social_links'],
//       password: json['password'],
//       customFonts: json['custom_fonts'],
//       products: json['products'],
//       appointments: json['appointments'],
//       gallery: json['gallery'],
//       analytics: json['analytics'],
//       seo: json['seo'],
//       blog: json['blog'],
//       affiliation: json['affiliation'],
//       customQrcode: json['custom_qrcode'],
//       instaEmbed: json['insta_embed'],
//       iframes: json['iframes'],
//       allowCustomDomain: json['allow_custom_domain'],
//       customLinks: json['custom_links'],
//       customCss: json['custom_css'],
//       customJs: json['custom_js'],
//       dynamicVcard: json['dynamic_vcard'],
//     );
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'products_services': productsServices,
//       'testimonials': testimonials,
//       'hide_branding': hideBranding,
//       'enquiry_form': enquiryForm,
//       'social_links': socialLinks,
//       'password': password,
//       'custom_fonts': customFonts,
//       'products': products,
//       'appointments': appointments,
//       'gallery': gallery,
//       'analytics': analytics,
//       'seo': seo,
//       'blog': blog,
//       'affiliation': affiliation,
//       'custom_qrcode': customQrcode,
//       'insta_embed': instaEmbed,
//       'iframes': iframes,
//       'allow_custom_domain': allowCustomDomain,
//       'custom_links': customLinks,
//       'custom_css': customCss,
//       'custom_js': customJs,
//       'dynamic_vcard': dynamicVcard,
//     };
//   }
// }
