class SubscriptionHistoryModel {
  final bool success;
  final List<SubscriptionHistoryData> data;
  final String message;

  SubscriptionHistoryModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory SubscriptionHistoryModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionHistoryModel(
      success: json['success'],
      data: (json['data'] as List)
          .map((item) => SubscriptionHistoryData.fromJson(item))
          .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class SubscriptionHistoryData {
  final String planName;
  final int amount;
  final String subscribedDate;
  final String expiredDate;
  final String status;

  SubscriptionHistoryData({
    required this.planName,
    required this.amount,
    required this.subscribedDate,
    required this.expiredDate,
    required this.status,
  });

  factory SubscriptionHistoryData.fromJson(Map<String, dynamic> json) {
    return SubscriptionHistoryData(
      planName: json['plan_name'],
      amount: json['amount'],
      subscribedDate: json['subscribed_date'],
      expiredDate: json['expired_date'],
      status: json['status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'plan_name': planName,
      'amount': amount,
      'subscribed_date': subscribedDate,
      'expired_date': expiredDate,
      'status': status,
    };
  }
}
