class SubscriptionPlanBuyDataModel {
  final bool success;
  final String message;

  SubscriptionPlanBuyDataModel({required this.success, required this.message});

  factory SubscriptionPlanBuyDataModel.fromJson(Map<String, dynamic> json) {
    return SubscriptionPlanBuyDataModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
