class PaymentRequestStatusModel {
  final bool success;
  final String data;
  final String message;

  PaymentRequestStatusModel({
    required this.success,
    required this.message,
    required this.data,
  });

  factory PaymentRequestStatusModel.fromJson(Map<String, dynamic> json) {
    return PaymentRequestStatusModel(
      success: json['success'],
      data: json['data'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data, 'message': message};
  }
}
