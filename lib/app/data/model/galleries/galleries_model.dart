class GalleriesModel {
  final bool success;
  final List<Gallery> data;
  final String message;

  GalleriesModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory GalleriesModel.fromJson(Map<String, dynamic> json) {
    return GalleriesModel(
      success: json['success'],
      data: (json['data'] as List)
          .map((item) => Gallery.fromJson(item))
          .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }
}

class Gallery {
  final int id;
  final String type;
  final String? link;
  final int vcardId;
  final String galleryImage;
  final String typeName;

  Gallery({
    required this.id,
    required this.type,
    this.link,
    required this.vcardId,
    required this.galleryImage,
    required this.typeName,
  });

  factory Gallery.fromJson(Map<String, dynamic> json) {
    return Gallery(
      id: json['id'],
      type: json['type'],
      link: json['link'],
      vcardId: json['vcard_id'],
      galleryImage: json['gallery_image'],
      typeName: json['type_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'link': link,
      'vcard_id': vcardId,
      'gallery_image': galleryImage,
      'type_name': typeName,
    };
  }
}
