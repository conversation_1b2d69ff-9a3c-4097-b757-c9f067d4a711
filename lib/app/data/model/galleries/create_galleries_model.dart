class CreateGalleriesModel {
  final bool success;
  final String message;

  CreateGalleriesModel({required this.success, required this.message});

  factory CreateGalleriesModel.fromJson(Map<String, dynamic> json) {
    return CreateGalleriesModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
