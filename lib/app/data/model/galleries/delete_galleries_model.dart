class DeleteGalleriesModel {
  final bool success;
  final String message;

  DeleteGalleriesModel({required this.success, required this.message});

  factory DeleteGalleriesModel.fromJson(Map<String, dynamic> json) {
    return DeleteGalleriesModel(
      success: json['success'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
