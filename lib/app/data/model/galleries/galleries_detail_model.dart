class GalleriesDetailModel {
  final bool success;
  final Gallery data;
  final String message;

  GalleriesDetailModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory GalleriesDetailModel.fromJson(Map<String, dynamic> json) {
    return GalleriesDetailModel(
      success: json['success'] as bool,
      data: Gallery.fromJson(json['data'] as Map<String, dynamic>),
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'data': data.toJson(), 'message': message};
  }
}

class Gallery {
  final int id;
  final String type;
  final String? link;
  final int vcardId;
  final String galleryImage;
  final String typeName;

  Gallery({
    required this.id,
    required this.type,
    this.link,
    required this.vcardId,
    required this.galleryImage,
    required this.typeName,
  });

  factory Gallery.fromJson(Map<String, dynamic> json) {
    return Gallery(
      id: json['id'] as int,
      type: json['type'] as String,
      link: json['link'] as String?,
      vcardId: json['vcard_id'] as int,
      galleryImage: json['gallery_image'] as String,
      typeName: json['type_name'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'link': link,
      'vcard_id': vcardId,
      'gallery_image': galleryImage,
      'type_name': typeName,
    };
  }
}
