class UpdateGalleriesModel {
  final bool success;
  final String message;

  UpdateGalleriesModel({required this.success, required this.message});

  factory UpdateGalleriesModel.fromJson(Map<String, dynamic> json) {
    return UpdateGalleriesModel(
      success: json['success'],
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
