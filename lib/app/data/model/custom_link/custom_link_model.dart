class CustomLinkModel {
  final bool success;
  final List<CustomLink> data;
  final String message;

  CustomLinkModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory CustomLinkModel.fromJson(Map<String, dynamic> json) {
    return CustomLinkModel(
      success: json['success'] ?? false,
      data: json['data'] != null
          ? List<CustomLink>.from(
              json['data'].map((x) => CustomLink.fromJson(x)),
            )
          : [],
      message: json['message'] ?? '',
    );
  }

  // Add copyWith method for optimistic updates
  CustomLinkModel copyWith({
    bool? success,
    List<CustomLink>? data,
    String? message,
  }) {
    return CustomLinkModel(
      success: success ?? this.success,
      data: data ?? this.data,
      message: message ?? this.message,
    );
  }
}

class CustomLink {
  final int id;
  final int vcardId;
  final String linkName;
  final int showAsButton;
  final int openNewTab;
  final String link;
  final String buttonColor;
  final String buttonType;

  CustomLink({
    required this.id,
    required this.vcardId,
    required this.linkName,
    required this.showAsButton,
    required this.openNewTab,
    required this.link,
    required this.buttonColor,
    required this.buttonType,
  });

  factory CustomLink.fromJson(Map<String, dynamic> json) {
    return CustomLink(
      id: json['id'] ?? 0,
      vcardId: int.tryParse(json['vcard_id'].toString()) ?? 0,
      linkName: json['link_name'] ?? '',
      showAsButton: int.tryParse(json['show_as_button'].toString()) ?? 0,
      openNewTab: int.tryParse(json['open_new_tab'].toString()) ?? 0,
      link: json['link'] ?? '',
      buttonColor: json['button_color'] ?? '#000000',
      buttonType: json['button_type'] ?? 'rounded',
    );
  }

  // Add copyWith method for optimistic updates
  CustomLink copyWith({
    int? id,
    int? vcardId,
    String? linkName,
    int? showAsButton,
    int? openNewTab,
    String? link,
    String? buttonColor,
    String? buttonType,
  }) {
    return CustomLink(
      id: id ?? this.id,
      vcardId: vcardId ?? this.vcardId,
      linkName: linkName ?? this.linkName,
      showAsButton: showAsButton ?? this.showAsButton,
      openNewTab: openNewTab ?? this.openNewTab,
      link: link ?? this.link,
      buttonColor: buttonColor ?? this.buttonColor,
      buttonType: buttonType ?? this.buttonType,
    );
  }
}

class CustomLinkDetailModel {
  final bool success;
  final CustomLinkDetail data;
  final String message;

  CustomLinkDetailModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory CustomLinkDetailModel.fromJson(Map<String, dynamic> json) {
    return CustomLinkDetailModel(
      success: json['success'] ?? false,
      data: CustomLinkDetail.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }

  // Add copyWith method for optimistic updates
  CustomLinkDetailModel copyWith({
    bool? success,
    CustomLinkDetail? data,
    String? message,
  }) {
    return CustomLinkDetailModel(
      success: success ?? this.success,
      data: data ?? this.data,
      message: message ?? this.message,
    );
  }
}

class CustomLinkDetail {
  final int id;
  final int vcardId;
  final String linkName;
  final int showAsButton;
  final int openNewTab;
  final String link;
  final String buttonColor;
  final String buttonType;
  final String createdAt;
  final String updatedAt;

  CustomLinkDetail({
    required this.id,
    required this.vcardId,
    required this.linkName,
    required this.showAsButton,
    required this.openNewTab,
    required this.link,
    required this.buttonColor,
    required this.buttonType,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CustomLinkDetail.fromJson(Map<String, dynamic> json) {
    return CustomLinkDetail(
      id: json['id'] ?? 0,
      vcardId: int.tryParse(json['vcard_id'].toString()) ?? 0,
      linkName: json['link_name'] ?? '',
      showAsButton: int.tryParse(json['show_as_button'].toString()) ?? 0,
      openNewTab: int.tryParse(json['open_new_tab'].toString()) ?? 0,
      link: json['link'] ?? '',
      buttonColor: json['button_color'] ?? '#000000',
      buttonType: json['button_type'] ?? 'rounded',
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  // Add copyWith method for optimistic updates
  CustomLinkDetail copyWith({
    int? id,
    int? vcardId,
    String? linkName,
    int? showAsButton,
    int? openNewTab,
    String? link,
    String? buttonColor,
    String? buttonType,
    String? createdAt,
    String? updatedAt,
  }) {
    return CustomLinkDetail(
      id: id ?? this.id,
      vcardId: vcardId ?? this.vcardId,
      linkName: linkName ?? this.linkName,
      showAsButton: showAsButton ?? this.showAsButton,
      openNewTab: openNewTab ?? this.openNewTab,
      link: link ?? this.link,
      buttonColor: buttonColor ?? this.buttonColor,
      buttonType: buttonType ?? this.buttonType,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class CustomLinkResponse {
  final bool success;
  final String message;

  CustomLinkResponse({required this.success, required this.message});

  factory CustomLinkResponse.fromJson(Map<String, dynamic> json) {
    return CustomLinkResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
    );
  }

  // Add copyWith method for consistency
  CustomLinkResponse copyWith({
    bool? success,
    String? message,
  }) {
    return CustomLinkResponse(
      success: success ?? this.success,
      message: message ?? this.message,
    );
  }
}
// // File: app/data/model/custom_link/custom_link_model.dart

// class CustomLinkModel {
//   final bool success;
//   final List<CustomLink> data;
//   final String message;

//   CustomLinkModel({
//     required this.success,
//     required this.data,
//     required this.message,
//   });

//   factory CustomLinkModel.fromJson(Map<String, dynamic> json) {
//     return CustomLinkModel(
//       success: json['success'] ?? false,
//       data:
//           json['data'] != null
//               ? List<CustomLink>.from(
//                 json['data'].map((x) => CustomLink.fromJson(x)),
//               )
//               : [],
//       message: json['message'] ?? '',
//     );
//   }
// }

// class CustomLink {
//   final int id;
//   final int vcardId;
//   final String linkName;
//   final int showAsButton;
//   final int openNewTab;
//   final String link;
//   final String buttonColor;
//   final String buttonType;

//   CustomLink({
//     required this.id,
//     required this.vcardId,
//     required this.linkName,
//     required this.showAsButton,
//     required this.openNewTab,
//     required this.link,
//     required this.buttonColor,
//     required this.buttonType,
//   });
  

//   factory CustomLink.fromJson(Map<String, dynamic> json) {
//     return CustomLink(
//       id: json['id'] ?? 0,
//       vcardId: int.tryParse(json['vcard_id'].toString()) ?? 0,
//       linkName: json['link_name'] ?? '',
//       showAsButton: int.tryParse(json['show_as_button'].toString()) ?? 0,
//       openNewTab: int.tryParse(json['open_new_tab'].toString()) ?? 0,
//       link: json['link'] ?? '',
//       buttonColor: json['button_color'] ?? '#000000',
//       buttonType: json['button_type'] ?? 'rounded',
//     );
//   }
// }

// class CustomLinkDetailModel {
//   final bool success;
//   final CustomLinkDetail data;
//   final String message;

//   CustomLinkDetailModel({
//     required this.success,
//     required this.data,
//     required this.message,
//   });

//   factory CustomLinkDetailModel.fromJson(Map<String, dynamic> json) {
//     return CustomLinkDetailModel(
//       success: json['success'] ?? false,
//       data: CustomLinkDetail.fromJson(json['data'] ?? {}),
//       message: json['message'] ?? '',
//     );
//   }
// }

// class CustomLinkDetail {
//   final int id;
//   final int vcardId;
//   final String linkName;
//   final int showAsButton;
//   final int openNewTab;
//   final String link;
//   final String buttonColor;
//   final String buttonType;
//   final String createdAt;
//   final String updatedAt;

//   CustomLinkDetail({
//     required this.id,
//     required this.vcardId,
//     required this.linkName,
//     required this.showAsButton,
//     required this.openNewTab,
//     required this.link,
//     required this.buttonColor,
//     required this.buttonType,
//     required this.createdAt,
//     required this.updatedAt,
//   });

//   factory CustomLinkDetail.fromJson(Map<String, dynamic> json) {
//     return CustomLinkDetail(
//       id: json['id'] ?? 0,
//       vcardId: int.tryParse(json['vcard_id'].toString()) ?? 0,
//       linkName: json['link_name'] ?? '',
//       showAsButton: int.tryParse(json['show_as_button'].toString()) ?? 0,
//       openNewTab: int.tryParse(json['open_new_tab'].toString()) ?? 0,
//       link: json['link'] ?? '',
//       buttonColor: json['button_color'] ?? '#000000',
//       buttonType: json['button_type'] ?? 'rounded',
//       createdAt: json['created_at'] ?? '',
//       updatedAt: json['updated_at'] ?? '',
//     );
//   }
// }

// class CustomLinkResponse {
//   final bool success;
//   final String message;

//   CustomLinkResponse({required this.success, required this.message});

//   factory CustomLinkResponse.fromJson(Map<String, dynamic> json) {
//     return CustomLinkResponse(
//       success: json['success'] ?? false,
//       message: json['message'] ?? '',
//     );
//   }
// }
