class AppointmentsResponse {
  final bool success;
  final dynamic data;
  final String? message;

  AppointmentsResponse({
    required this.success,
    this.data,
    this.message,
  });

  factory AppointmentsResponse.fromJson(Map<String, dynamic> json) {
    return AppointmentsResponse(
      success: json['success'] ?? false,
      data: json['data'],
      message: json['message'],
    );
  }
}