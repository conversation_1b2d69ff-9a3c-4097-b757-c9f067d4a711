class StorageResponseModel {
  final bool success;
  final StorageData data;
  final String message;

  StorageResponseModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory StorageResponseModel.fromJson(Map<String, dynamic> json) {
    return StorageResponseModel(
      success: json['success'] ?? false,
      data: StorageData.fromJson(json['data'] ?? {}),
      message: json['message'] ?? '',
    );
  }
}

class StorageData {
  final Map<String, double> storageData;
  final double totalUsedMb;
  final double storageLimitMb;
  final double storagePercentageUsed;
  final ChartData chartData;

  StorageData({
    required this.storageData,
    required this.totalUsedMb,
    required this.storageLimitMb,
    required this.storagePercentageUsed,
    required this.chartData,
  });

  factory StorageData.fromJson(Map<String, dynamic> json) {
    return StorageData(
      storageData: Map<String, double>.from((json['storage_data'] ?? {}).map(
        (key, value) => MapEntry(key, (value as num).toDouble()),
      )),
      totalUsedMb: (json['total_used_mb'] as num?)?.toDouble() ?? 0.0,
      storageLimitMb: (json['storage_limit_mb'] as num?)?.toDouble() ?? 0.0,
      storagePercentageUsed:
          (json['storage_percentage_used'] as num?)?.toDouble() ?? 0.0,
      chartData: ChartData.fromJson(json['chart_data'] ?? {}),
    );
  }
}

class ChartData {
  final List<String> labels;
  final List<double> data;

  ChartData({
    required this.labels,
    required this.data,
  });

  factory ChartData.fromJson(Map<String, dynamic> json) {
    return ChartData(
      labels: List<String>.from(json['labels'] ?? []),
      data: List<double>.from(
        (json['data'] ?? []).map((e) => (e as num).toDouble()),
      ),
    );
  }
}
