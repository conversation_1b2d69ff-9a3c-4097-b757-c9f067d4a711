class EnquiriesModel {
  final bool success;
  final List<EnquiriesData> data;
  final String message;

  EnquiriesModel({
    required this.success,
    required this.data,
    required this.message,
  });

  factory EnquiriesModel.fromJson(Map<String, dynamic> json) {
    return EnquiriesModel(
      success: json['success'],
      data:
          (json['data'] as List)
              .map((item) => EnquiriesData.fromJson(item))
              .toList(),
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'data': data.map((item) => item.toJson()).toList(),
      'message': message,
    };
  }

  EnquiriesModel copyWith({
    bool? success,
    List<EnquiriesData>? data,
    String? message,
  }) {
    return EnquiriesModel(
      success: success ?? this.success,
      data: data ?? this.data,
      message: message ?? this.message,
    );
  }
}

class EnquiriesData {
  final int? id;
  final String? vcardName;
  final String? name;
  final String? email;
  final int? phone;
  final String? message;
  final DateTime? createdAt;

  EnquiriesData({
    this.id,
    this.vcardName,
    this.name,
    this.email,
    this.phone,
    this.message,
    this.createdAt,
  });

  factory EnquiriesData.fromJson(Map<String, dynamic> json) {
    return EnquiriesData(
      id: json['id'] as int?,
      vcardName: json['vcard_name'] as String?,
      name: json['name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as int?,
      message: json['message'] as String?,
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'vcard_name': vcardName,
      'name': name,
      'email': email,
      'phone': phone,
      'message': message,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  EnquiriesData copyWith({
    int? id,
    String? vcardName,
    String? name,
    String? email,
    int? phone,
    String? message,
  }) {
    return EnquiriesData(
      id: id ?? this.id,
      vcardName: vcardName ?? this.vcardName,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      message: message ?? this.message,
    );
  }
}

class DeleteEnquiriesModel {
  final bool success;
  final String message;

  DeleteEnquiriesModel({required this.success, required this.message});

  factory DeleteEnquiriesModel.fromJson(Map<String, dynamic> json) {
    return DeleteEnquiriesModel(
      success: json['success'] as bool,
      message: json['message'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {'success': success, 'message': message};
  }
}
