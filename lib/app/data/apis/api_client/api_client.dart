import 'dart:developer';

import 'package:google_fonts/google_fonts.dart';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

// ignore: constant_identifier_names
enum RequestType { GET, POST, PUT, DELETE, MULTIPART_POST, MULTIPART_PUT }

@lazySingleton
class ApiClient {
  final Dio dio;

  ApiClient() : dio = Dio(BaseOptions(headers: buildHeaders())) {
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          options.headers = buildHeaders();
          return handler.next(options);
        },
      ),
    );
    dio.interceptors.add(
      PrettyDioLogger(
        requestHeader: true,
        requestBody: true,
        responseBody: true,
        responseHeader: true,
        error: true,
        compact: true,
        maxWidth: 100,
      ),
    );
  }

  static Map<String, String> buildHeaders() {
    final header = <String, String>{
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    };
    // String? deviceToken = PreferenceUtil.boxPreferences?.get(PreferencesKeys.DEVICETOKEN) ?? '';

    String? deviceToken = getIt<SharedPreferences>().getToken;

    log('========Device Token=====$deviceToken');

    if (deviceToken != null && deviceToken.isNotEmpty) {
      header['Authorization'] = 'Bearer $deviceToken';
    }

    return header;
  }

  Future<Map<String, dynamic>> buildRequest(
    RequestType requestType,
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? multipartData,
    Map<String, dynamic>? multipartPutData,
    bool withoutMessage = false,
  }) async {
    Response response;

    try {
      switch (requestType) {
        case RequestType.GET:
          response = await dio.get(path);
          break;
        case RequestType.POST:
          response = await dio.post(path, data: data);
          break;
        case RequestType.PUT:
          response = await dio.put(path, data: data);
          break;
        case RequestType.MULTIPART_POST:
          FormData formData = FormData();
          if (multipartData != null && multipartData.isNotEmpty) {
            bool isFirstEntryProcessed = false;

            for (var entry in multipartData.entries) {
              if (entry.value is List && entry.value.isNotEmpty) {
                if (!isFirstEntryProcessed) {
                  for (var filepath in entry.value) {
                    formData.files.add(
                      MapEntry(
                        entry.key,
                        await MultipartFile.fromFile(filepath),
                      ),
                    );
                  }
                  isFirstEntryProcessed = true;
                } else {
                  formData.fields.add(
                    MapEntry(entry.key, entry.value.toString()),
                  );
                }
              } else {
                formData.fields.add(
                  MapEntry(entry.key, entry.value.toString()),
                );
              }
            }
          }
          response = await dio.post(path, data: formData);
          break;
        case RequestType.MULTIPART_PUT:
          FormData formData = FormData();
          if (multipartPutData != null && multipartPutData.isNotEmpty) {
            bool isFirstEntryProcessed = false;

            for (var entry in multipartPutData.entries) {
              if (entry.value is List && entry.value.isNotEmpty) {
                if (!isFirstEntryProcessed) {
                  for (var filepath in entry.value) {
                    formData.files.add(
                      MapEntry(
                        entry.key,
                        await MultipartFile.fromFile(filepath),
                      ),
                    );
                  }
                  isFirstEntryProcessed = true;
                } else {
                  formData.fields.add(
                    MapEntry(entry.key, entry.value.toString()),
                  );
                }
              } else {
                formData.fields.add(
                  MapEntry(entry.key, entry.value.toString()),
                );
              }
            }
          }
          response = await dio.put(path, data: formData);

          break;
        case RequestType.DELETE:
          response = await dio.delete(path);
          break;
      }

      return withoutMessage
          ? responseWithoutMessage(response)
          : responseOfBodyRequest(response);
    } catch (error) {
      if (error is DioException) {
        log('==============Response body Error===============$error');
        return withoutMessage ? {} : showError(error);
        // return withoutMessage ? showError(error) : showError(error);
      } else {
        rethrow;
      }
    }
  }

  Future<Map<String, dynamic>> buildMultipartWithMultipleFilesRequest({
    required String path,
    required Map<String, dynamic> fields,
    required Map<String, List<String>> fileFields,
    RequestType method = RequestType.MULTIPART_POST,
    bool withoutMessage = false,
  }) async {
    Response response;
    try {
      FormData formData = FormData();

      // Add multiple files for each key
      for (final entry in fileFields.entries) {
        for (final filePath in entry.value) {
          formData.files.add(
            MapEntry(entry.key, await MultipartFile.fromFile(filePath)),
          );
        }
      }

      // Add form fields
      fields.forEach((key, value) {
        formData.fields.add(MapEntry(key, value.toString()));
      });

      // Use correct method for request
      switch (method) {
        case RequestType.MULTIPART_POST:
          response = await dio.post(path, data: formData);
          break;
        case RequestType.MULTIPART_PUT:
          response = await dio.put(path, data: formData);
          break;
        default:
          throw Exception('Unsupported method for multipart request');
      }

      return withoutMessage
          ? responseWithoutMessage(response)
          : responseOfBodyRequest(response);
    } catch (error) {
      if (error is DioException) {
        log('==============Multipart Error===============$error');
        return withoutMessage ? {} : showError(error);
      } else {
        rethrow;
      }
    }
  }

  Future<Map<String, dynamic>> buildProductRequest(
    RequestType requestType,
    String path, {
    required Map<String, dynamic> data,
    bool withoutMessage = false,
  }) async {
    Response response;
    try {
      FormData formData = FormData();

      // Handle product_icon list specially
      if (data['product_icon'] != null && data['product_icon'] is List) {
        List<String> imagePaths = List<String>.from(data['product_icon'].first);
        if (imagePaths.isNotEmpty) {
          List<MultipartFile> files = [];
          for (String path in imagePaths) {
            files.add(await MultipartFile.fromFile(path));
          }
          formData.files.addAll(
            files.map((file) => MapEntry('product_icon[]', file)).toList(),
          );
        }
      }

      // Add other fields
      data.forEach((key, value) {
        if (key != 'product_icon') {
          formData.fields.add(MapEntry(key, value.toString()));
        }
      });

      log('FormData fields: ${formData.fields}');
      log('FormData files length: ${formData.files.length}');
      log('FormData files: ${formData.files.map((f) => f.key).toList()}');

      switch (requestType) {
        case RequestType.MULTIPART_POST:
          response = await dio.post(path, data: formData);
          break;
        case RequestType.MULTIPART_PUT:
          response = await dio.put(path, data: formData);
          break;
        default:
          throw Exception('Unsupported method for product request');
      }

      return withoutMessage
          ? responseWithoutMessage(response)
          : responseOfBodyRequest(response);
    } catch (error) {
      if (error is DioException) {
        log('==============Product Request Error===============$error');
        if (error.response != null) {
          log('Response data: ${error.response?.data}');
          log('Response headers: ${error.response?.headers}');
        }
        return withoutMessage ? {} : showError(error);
      } else {
        rethrow;
      }
    }
  }

  Map<String, dynamic> responseOfBodyRequest(Response response) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      toastification.show(
        style: ToastificationStyle.flatColored,
        type: ToastificationType.success,
        showProgressBar: false,
        alignment: Alignment.topCenter,
        description: Text(
          response.data['message'] ?? AppStrings.T.lbl_successful,
          style: GoogleFonts.koHo(
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      log('Response ------ ${response.statusCode}');
      log('Response Message ------ ${response.data['message']}');
      return response.data;
    } else {
      throw errorOfBodyRequest(response);
    }
  }

  Map<String, dynamic> errorOfBodyRequest(Response response) {
    if (response.statusCode == 400) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        showProgressBar: false,
        alignment: Alignment.topCenter,
        description: Text(
          response.data['message'],
          style: GoogleFonts.koHo(
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
      );
    } else if (response.statusCode == 401) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        showProgressBar: false,
        description: Text(
          response.data['message'],
          style: GoogleFonts.koHo(
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );

      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.unknown,
      );
    } else if (response.statusCode == 422) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        showProgressBar: false,
        description: Text(
          response.data['message'],
          style: GoogleFonts.koHo(
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );

      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.unknown,
      );
    } else {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        showProgressBar: false,
        alignment: Alignment.topCenter,
        description: Text(
          response.data['message'] ?? "",
          style: GoogleFonts.koHo(
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );

      throw DioException(
        requestOptions: response.requestOptions,
        response: response,
        type: DioExceptionType.badResponse,
      );
    }
  }

  Map<String, dynamic> responseWithoutMessage(Response response) {
    if (response.statusCode == 200 || response.statusCode == 201) {
      return response.data;
    } else {
      throw errorOfBodyRequest(response);
    }
  }

  Map<String, dynamic> showError(DioException error) {
    if (error.response != null) {
      try {
        return errorOfBodyRequest(error.response!);
      } catch (e) {
        // Fallback for malformed responses
        // final message =
        //     error.response?.data is Map
        //         ? error.response?.data['message']
        //         : error.response?.statusMessage ?? 'Unknown error occurred';

        // toastification.show(
        //   type: ToastificationType.error,
        //   style: ToastificationStyle.flatColored,
        //   showProgressBar: false,
        //   alignment: Alignment.topCenter,
        //   description: Text(
        //     message.toString(),
        //     style: GoogleFonts.koHo(
        //       fontSize: 16.0.sp,
        //       fontWeight: FontWeight.w500,
        //     ),
        //   ),
        //   autoCloseDuration: const Duration(seconds: 3),
        // );
        throw DioException(
          requestOptions: error.requestOptions,
          error: error.error,
          type: DioExceptionType.unknown,
        );
      }
    } else {
      // Handle cases with no response
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        showProgressBar: false,
        alignment: Alignment.topCenter,
        description: Text(
          error.message ?? 'Network error occurred',
          style: GoogleFonts.koHo(
            fontSize: 16.0.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        autoCloseDuration: const Duration(seconds: 3),
      );
      throw error;
    }
  }
}
