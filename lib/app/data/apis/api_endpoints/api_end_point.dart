class ApiEndPoint {
  // Base URLs
  // TODO: add Add API Base URL Here
  static String get baseUrl => 'https://vcards.infyom.com/api';

  // Authentication
  static String get registerUrl => '$baseUrl/register';
  static String get loginUrl => '$baseUrl/login';
  static String get logoutUrl => '$baseUrl/logout';
  static String get forgotPasswordUrl => '$baseUrl/forgot-password';
  static String get resetPasswordByMailUrl => '$baseUrl/password';
  static String get resetPasswordUrl => '$baseUrl/reset-password';
  static String get deleteAccountUrl => '$baseUrl/admin/delete-user';
  static String get googleLoginUrl => '$baseUrl/login/google';

  // Dashboard
  static String get superDashboardUrl => '$baseUrl/dashboard';
  static String get adminDashboardUrl => '$baseUrl/admin/dashboard';
  static String get superAdminIncomeChartUrl => '$baseUrl/income-chart';
  static String get adminIncomeChartUrl => '$baseUrl/admin/income-chart';

  // Profile
  static String get profileEditUrl => '$baseUrl/profile-edit';
  static String get profileUpdateUrl => '$baseUrl/profile-update';

  // VCards
  static String get superAdminVcardUrl => '$baseUrl/vcard';
  static String get adminVcardUrl => '$baseUrl/admin/vcard';
  static String get singleVcardUrl =>
      '$baseUrl/admin/vcard'; // Append ID: '$baseUrl/vcard/$id'
  static String get deleteVcardUrl =>
      '$baseUrl/admin/vcard-delete'; // Append ID: '$baseUrl/admin/vcard-delete/$id'
  static String get superAdminVcardQrCodeUrl =>
      '$baseUrl/vcard-qrcode'; // Append ID: '$baseUrl/vcard-qrcode/$id'
  static String get adminCreateVcardUrl => '$baseUrl/admin/create-vcard';
  static String get updateVCardBasicDetail => '$baseUrl/admin/vcard';
  static String get getVCardBasicDetails =>
      '$baseUrl/admin/vcard-basic-details';
  static String get getvcardTemplates => '$baseUrl/admin/vcard-templates';
  static String get updateVCardTemplateModel => '$baseUrl/admin/vcard/template';

  // Appointments
  static String get appointmentUrl => '$baseUrl/admin/appointment';
  static String get singleAppointmentUrl => '$baseUrl/admin/appointment';
  static String get deleteAppointmentUrl => '$baseUrl/admin/appointment-delete';
  static String get todayAppointmentUrl => '$baseUrl/admin/today-appointment';
  static String get appointmentCompletedUrl =>
      '$baseUrl/admin/appointment-completed';

  // Enquiries
  static String get enquiryUrl => '$baseUrl/admin/enquiries';
  static String get singleEnquiryUrl => '$baseUrl/admin/enquiries';
  static String get deleteEnquiryUrl => '$baseUrl/admin/enquiries-delete';
  static String get vcardEnquiryUrl => '$baseUrl/admin/vcard-enquires';

  // Groups
  static String get adminGroupUrl => '$baseUrl/admin/groups';
  static String get groupCreateUrl => '$baseUrl/admin/groups-create';
  static String get deleteGroupUrl => '$baseUrl/admin/group-delete';

  // Business Cards
  static String get businessCardCreateUrl =>
      '$baseUrl/admin/business-cards-create';
  static String get businessCardUrl => '$baseUrl/admin/business-cards';
  static String get filterBusinessCardUrl => '$baseUrl/admin/business-cards';

  // Subscription
  static String get subscriptionHistoryUrl =>
      '$baseUrl/admin/manage-subscription';
  static String get subscriptionPlanUrl => '$baseUrl/admin/subscription-plan';
  static String get subscriptionPlanBuyUrl => '$baseUrl/admin/plans-buy';
  static String get paymentRequestStatusUrl =>
      '$baseUrl/admin/payment-is-pending';

  //Services
  static String get servicesUrl => '$baseUrl/admin/vcard-services';
  static String get servicesByIdUrl => '$baseUrl/admin/services';
  static String get createServicesUrl => '$baseUrl/admin/services';
  static String get deleteServicesUrl => '$baseUrl/admin/services';
  static String get serviceSlidersUrl =>
      '$baseUrl/admin/services/service-slider';

  //Products
  static String get productsUrl => '$baseUrl/admin/vcard-products';
  static String get productsByIdUrl => '$baseUrl/admin/products';
  static String get createPoductsUrl => '$baseUrl/admin/products';
  static String get deletePoductsUrl => '$baseUrl/admin/products';

  //Testimonials
  static String get testimonialssUrl => '$baseUrl/admin/vcard-testimonials';
  static String get testimonialsByIdUrl => '$baseUrl/admin/testimonials';
  static String get createTestimonialsUrl => '$baseUrl/admin/testimonials';
  static String get deleteTestimonialsUrl => '$baseUrl/admin/testimonials';

  //Vcard Businee Hour
  static String get businessHoursUrl => '$baseUrl/admin/business-hours';

  //Vcard Appointments Hour
  static String get adminVcardAppointmentsUrl =>
      '$baseUrl/admin/vcard-appointments';

  //Social connect
  static String get socialconnectUrl => '$baseUrl/admin/social-links';

  //Advanced Settings
  static String get advancedSettingsUrl => '$baseUrl/admin/advanced-settings';

  //Services
  static String get blogUrl => '$baseUrl/admin/vcard-blogs';
  static String get blogByIdUrl => '$baseUrl/admin/blogs';
  static String get createBlogUrl => '$baseUrl/admin/blogs';
  static String get deleteBlogUrl => '$baseUrl/admin/blogs';

  //Banner connect
  static String get bannerUrl => '$baseUrl/admin/banner';
  static String get updateBannerUrl => '$baseUrl/admin/update-banner';

  //Seo connect
  static String get seoUrl => '$baseUrl/admin/seo';
  static String get updateSeoUrl => '$baseUrl/admin/update-seo';

  //Manage-section connect
  static String get manageSectionUrl => '$baseUrl/admin/manage-section';
  static String get updateManageSectionUrl =>
      '$baseUrl/admin/update-manage-section';

  //Font connect
  static String get fontUrl => '$baseUrl/admin/fonts';

  //Galleries
  static String get galleryListUrl => '$baseUrl/admin/gallery-list';
  static String get galleryUrl => '$baseUrl/admin/gallery';

  //Privacy Policy Url
  static String get privacyPolicyUrl => '$baseUrl/admin/privacy-policies';

  //Terms and Condition
  static String get termsNConditionPolicyUrl =>
      '$baseUrl/admin/terms-conditions';

  //Currency List Url
  static String get currencyListUrl => '$baseUrl/admin/currency-list';

  //Currency List Url
  static String get productOrderUrl => '$baseUrl/admin/product-orders';

  //Affiliation Url
  static String get getAffiliationUrl => '$baseUrl/admin/get-affiliation';
  static String get getAffiliatioListUrl =>
      '$baseUrl/admin/get-affiliation-list';
  static String get withdrawalListUrl => '$baseUrl/admin/withdrawal-list';
  static String get withdrawaRequestUrl => '$baseUrl/admin/withdrawa-request';
  static String get showWithdrawaRequestUrl =>
      '$baseUrl/admin/show-withdrawa-request';

  //Customize QR Code
  static String get customizeQrCodeUrl => '$baseUrl/admin/qr-code';

  //Customize QR Code
  static String get getInstaEmbed => '$baseUrl/admin/get-insta-embed';
  static String get storeInstaEmbed => '$baseUrl/admin/store-insta-embed';
  static String get deleteInstaEmbed => '$baseUrl/admin/delete-insta-embed';
  static String get updateInstaEmbed => '$baseUrl/admin/update-insta-embed';
  static String get getInstaEmbedById => '$baseUrl/admin/insta-embed';

  //Customize QR Code
  static String get iframeListUrl => '$baseUrl/admin/iframe-list';
  static String get iframeUrl => '$baseUrl/admin/iframe';

  //Customize QR Code
  static String get nfcCardsListUrl => '$baseUrl/admin/nfc-cards';
  static String get orderNfcUrl => '$baseUrl/admin/order-nfc';
  static String get vcardListUrl => '$baseUrl/admin/vcard-list';
  static String get paymentTypesUrl => '$baseUrl/admin/payment-types';

  // Settings
  static String get settingsEditUrl => '$baseUrl/admin/settings-edit';
  static String get settingsUpdateUrl => '$baseUrl/admin/settings-update';
  static String get languageUpdateUrl => '$baseUrl/language-update';
  static String get paymentConfigUrl => '$baseUrl/admin/payment-config';

  // Custom Link
  static String get customLinksListUrl => '$baseUrl/admin/custom-links';
  static String get customLinkUrl => '$baseUrl/admin/custom-link';
  static String get updateCustomLinkUrl => '$baseUrl/admin/update-custom-link';

  //Customize QR Code
  static String get storageUrl => '$baseUrl/admin/storage';

  // Helper method to append ID to endpoints that require it
  static String appendId(String baseEndpoint, int id) {
    return '$baseEndpoint/$id';
  }

  // Helper method to create URL with query parameters
  static String withQueryParams(
    String baseEndpoint,
    Map<String, dynamic> params,
  ) {
    if (params.isEmpty) return baseEndpoint;

    final queryParams =
        Uri(
          queryParameters: params.map(
            (key, value) => MapEntry(key, value.toString()),
          ),
        ).query;

    return '$baseEndpoint?$queryParams';
  }
}
