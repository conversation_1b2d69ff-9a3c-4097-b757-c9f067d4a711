import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/blog/blog_detail_model.dart';
import 'package:v_card/app/data/model/blog/blog_model.dart';
import 'package:v_card/app/data/model/blog/create_blog_model.dart';
import 'package:v_card/app/data/model/blog/delete_Blog_model.dart';
import 'package:v_card/app/data/model/blog/update_blog_model.dart';

@injectable
class BlogRepository {
  final ApiClient apiClient;

  BlogRepository({required this.apiClient});

  Future<BlogModel> getadminBlogListRepository(String vCardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        '${ApiEndPoint.blogUrl}/$vCardId',
        withoutMessage: true,
      );
      return BlogModel.fromJson(response);
    } catch (error) {
      log("==========Blog Repo Error in Featch data - /vcard-blogs/$vCardId API==========$error");
      rethrow;
    }
  }

  Future<CreateBlogModel> createAdminBlogRepository({required Map<String, dynamic> data}) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.createBlogUrl,
        multipartData: data,
      );
      return CreateBlogModel.fromJson(response);
    } catch (error) {
      log("==========Create Service Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<BlogDetailModel> getBlogByIdRepository({required int id}) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.blogByIdUrl}/$id',
      );
      return BlogDetailModel.fromJson(response);
    } catch (error) {
      log("==========Blog Repo Error in Featch data - /admin/blogs/$id API==========$error");
      rethrow;
    }
  }

  Future<DeleteBlogModel> deleteBlogByIdRepository({required int id}) async {
    try {
      var response = await apiClient.buildRequest(RequestType.DELETE, '${ApiEndPoint.deleteBlogUrl}/$id');
      return DeleteBlogModel.fromJson(response);
    } catch (error) {
      log("==========Blog Repo Error Dlete - /admin/vcard-delete/$id API==========$error");
      rethrow;
    }
  }

  Future<UpdateBlogModel> updateBlogRepository({required int blogId, required Map<String, dynamic> data}) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        '${ApiEndPoint.createBlogUrl}/$blogId',
        multipartData: data,
      );
      return UpdateBlogModel.fromJson(response);
    } catch (error) {
      log(error.toString());
      rethrow;
    }
  }
}
