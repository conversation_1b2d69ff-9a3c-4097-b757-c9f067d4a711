import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/nfc/nfc_model.dart';

@injectable
class NfcRepository {
  final ApiClient apiClient;

  NfcRepository({required this.apiClient});

  Future<NfcCardListModel> getNfcCardsList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.nfcCardsListUrl,
        withoutMessage: true,
      );
      return NfcCardListModel.fromJson(response);
    } catch (error) {
      log(
        "==========NFC Repo Error in Fetch data - /admin/nfc-cards API==========$error",
      );
      rethrow;
    }
  }

  Future<NfcCardListModel> getNfcCardById(int id) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        '${ApiEndPoint.nfcCardsListUrl}/$id',
        withoutMessage: true,
      );
      return NfcCardListModel.fromJson(response);
    } catch (error) {
      log(
        "==========NFC Repo Error in Fetch data - /admin/nfc-cards/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<OrderNfcModel> getOrderNfcList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.orderNfcUrl,
        withoutMessage: true,
      );
      return OrderNfcModel.fromJson(response);
    } catch (error) {
      log(
        "==========NFC Repo Error in Fetch data - /admin/order-nfc API==========$error",
      );
      rethrow;
    }
  }

  Future<VcardListModel> getVcardList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.vcardListUrl,
        withoutMessage: true,
      );
      return VcardListModel.fromJson(response);
    } catch (error) {
      log(
        "==========NFC Repo Error in Fetch data - /admin/vcard-list API==========$error",
      );
      rethrow;
    }
  }

  Future<PaymentTypesModel> getPaymentTypes() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.paymentTypesUrl,
        withoutMessage: true,
      );
      return PaymentTypesModel.fromJson(response);
    } catch (error) {
      log(
        "==========NFC Repo Error in Fetch data - /admin/payment-types API==========$error",
      );
      rethrow;
    }
  }

  Future<CreateOrderNfcResponse> orderNfcCard({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.orderNfcUrl,
        multipartData: data,
      );

      return CreateOrderNfcResponse.fromJson(response);
    } catch (error) {
      log(
        "==========NFC Repo Error in Order NFC - /admin/order-nfc API==========$error",
      );
      rethrow;
    }
  }
}
