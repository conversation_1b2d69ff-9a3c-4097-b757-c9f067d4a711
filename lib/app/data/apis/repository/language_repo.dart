import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/language/language_model.dart';

@injectable
class LanguageRepository {
  final ApiClient apiClient;

  LanguageRepository({required this.apiClient});

  Future<LanguageModel> updatelanguageRepository(
    Map<String, dynamic> data,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.languageUpdateUrl,
        data: data,
      );

      return LanguageModel.fromJson(response);
    } catch (error) {
      log(
        "==========Language Repo Error in Featch data - /admin/language-update API==========$error",
      );
      rethrow;
    }
  }
}
