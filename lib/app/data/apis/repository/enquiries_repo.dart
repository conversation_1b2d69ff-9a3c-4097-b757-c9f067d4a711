import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/enquiries/enquiries_model.dart';

@injectable
class EnquiriesRepository {
  final ApiClient apiClient;

  EnquiriesRepository({required this.apiClient});

  Future<EnquiriesModel> getEnquiriesListRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,        withoutMessage: true,

        ApiEndPoint.enquiryUrl,
      );

      return EnquiriesModel.fromJson(response);
    } catch (error) {
      log(
        "==========Enquiries Repo Error in Featch data - /admin/enquiries API==========$error",
      );
      rethrow;
    }
  }

  Future<EnquiriesModel> getEnquiriesByIdRepository({
    required String id,
    required String authToken,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,        withoutMessage: true,

        '${ApiEndPoint.singleEnquiryUrl}/$id',
        data: {'auth_token': authToken},
      );

      return EnquiriesModel.fromJson(response);
    } catch (error) {
      log(
        "==========Enquiries Repo Error in Featch data - /admin/enquiries/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<DeleteEnquiriesModel> deleteEnquiriesByIdRepository({
    required int id,
    required String authToken,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteEnquiryUrl}/$id',
        data: {'auth_token': authToken},
      );

      return DeleteEnquiriesModel.fromJson(response);
    } catch (error) {
      log(
        "==========Enquiries Repo Error Dlete - /admin/enquiries-delete/$id API==========$error",
      );
      rethrow;
    }
  }
}
