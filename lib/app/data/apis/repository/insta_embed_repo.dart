import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/insta_embed/insta_embed_detail_model.dart';
import 'package:v_card/app/data/model/insta_embed/insta_embed_model.dart';

@injectable
class InstaEmbedRepository {
  final ApiClient apiClient;

  InstaEmbedRepository({required this.apiClient});

  // Get Instagram embeds by vCard ID
  Future<InstaEmbedModel> getInstaEmbeds(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.getInstaEmbed}/$vcardId',
      );
      log('==============Instagram Embed Data: ${response['data']}');

      return InstaEmbedModel.fromJson(response);
    } catch (error) {
      log(
        "==========Instagram Embed Repo Error in Fetch data - /admin/get-insta-embed/$vcardId API==========$error",
      );
      rethrow;
    }
  }

  Future<InstaEmbedDetailModel> getInstaEmbedsById(String embedId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.getInstaEmbedById}/$embedId',
      );
      log('==============  Embed Data: ${response['data']}');

      return InstaEmbedDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Instagram Embed Repo Error in Fetch data - /admin/insta-embed/$embedId API==========$error",
      );
      rethrow;
    }
  }

  // Create or Update Instagram embed
  Future<InstaEmbedCreateResponse> createInstaEmbed({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.storeInstaEmbed,
        data: data,
      );

      log('==============Create Instagram Embed Response: $response');
      return InstaEmbedCreateResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Instagram Embed Repo Error in Create - /admin/store-insta-embed API==========$error",
      );
      rethrow;
    }
  }

  // Update or Update Instagram embed
  Future<InstaEmbedCreateResponse> updateInstaEmbed({
    required Map<String, dynamic> data,
    required String id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.updateInstaEmbed}/$id',
        data: data,
      );

      log('==============Update Instagram Embed Response: $response');
      return InstaEmbedCreateResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Instagram Embed Repo Error in Update - /admin/update-insta-embed API==========$error",
      );
      rethrow;
    }
  }

  // Delete Instagram embed
  Future<InstaEmbedCreateResponse> deleteInstaEmbed(String embedId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteInstaEmbed}/$embedId',
      );

      log('==============Delete Instagram Embed Response: $response');
      return InstaEmbedCreateResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Instagram Embed Repo Error in Delete - /admin/delete-insta-embed/$embedId API==========$error",
      );
      rethrow;
    }
  }
}
