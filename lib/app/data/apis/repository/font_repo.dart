import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/font/font_model.dart';

@injectable
class FontRepository {
  final ApiClient apiClient;

  FontRepository({required this.apiClient});

  Future<FontListModel> getFontList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.fontUrl,
      );
      log('==============Font List Data: ${response['data']}');

      return FontListModel.fromJson(response);
    } catch (error) {
      log(
        "==========Font Repo Error in Fetch data - /admin/fonts API==========$error",
      );
      rethrow;
    }
  }

  Future<FontModel> getFontById(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.fontUrl}/$vcardId',
      );
      log('==============Font by ID Data: ${response['data']}');

      return FontModel.fromJson(response);
    } catch (error) {
      log(
        "==========Font Repo Error in Fetch data - /admin/fonts/$vcardId API==========$error",
      );
      rethrow;
    }
  }

  Future<FontUpdateModel> updateFont({
    required String fontFamily,
    required String fontSize,
    required String vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.fontUrl,
        data: {
          "font_family": fontFamily,
          "font_size": fontSize,
          "vcard_id": vcardId.toString(),
        },
      );

      log('==============Update Font Response: $response');
      return FontUpdateModel.fromJson(response);
    } catch (error) {
      log(
        "==========Font Repo Error in Update - /admin/fonts API==========$error",
      );
      rethrow;
    }
  }
}
