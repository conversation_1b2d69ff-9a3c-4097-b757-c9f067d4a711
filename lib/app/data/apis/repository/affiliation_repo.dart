// lib/app/data/apis/repository/affiliation_repo.dart
import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/affiliation/afiiliation_detail_model.dart';

@injectable
class AffiliationRepository {
  final ApiClient apiClient;

  AffiliationRepository({required this.apiClient});

  Future<AffiliationModel> getAffiliationUrl() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.getAffiliationUrl,
      );
      return AffiliationModel.fromJson(response);
    } catch (error) {
      log("Error in getAffiliationUrl: $error");
      rethrow;
    }
  }

  Future<AffiliationListModel> getAffiliationList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.getAffiliatioListUrl,
      );
      return AffiliationListModel.fromJson(response);
    } catch (error) {
      log("Error in getAffiliationList: $error");
      rethrow;
    }
  }

  Future<WithdrawalModel> getWithdrawalList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.withdrawalListUrl,
      );
      return WithdrawalModel.fromJson(response);
    } catch (error) {
      log("Error in getWithdrawalList: $error");
      rethrow;
    }
  }

  Future<CreateWithdrawalResponseModel> createWithdrawalRequest(
    Map<String, dynamic> data,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.withdrawaRequestUrl,
        data: data,
      );
      return CreateWithdrawalResponseModel.fromJson(response);
    } catch (error) {
      log("Error in createWithdrawalRequest: $error");
      rethrow;
    }
  }

  Future<WithdrawalDetailModel> showWithdrawalRequest(
    String withdrawalId,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.showWithdrawaRequestUrl}/$withdrawalId',
      );
      return WithdrawalDetailModel.fromJson(response);
    } catch (error) {
      log("Error in showWithdrawalRequest: $error");
      rethrow;
    }
  }
}
