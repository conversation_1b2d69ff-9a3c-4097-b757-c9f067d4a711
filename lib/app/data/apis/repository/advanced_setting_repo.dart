import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/advanced_setting/advannced_setting_detail_model.dart';
import 'package:v_card/app/data/model/advanced_setting/advannced_setting_model.dart';

@injectable
class AdvancedSettingsRepository {
  final ApiClient apiClient;

  AdvancedSettingsRepository({required this.apiClient});

  Future<CreateAdvancedSettingModel> createAdvanceSettingsRepository({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.advancedSettingsUrl,
        data: data,
      );

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return CreateAdvancedSettingModel.fromJson(response);
    } catch (error) {
      log("==========Create Testimonials Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<AdvancedSettingDetailModel> getAdvanceSettingsByIdRepository({
    required String id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.advancedSettingsUrl}/$id',
      );

      return AdvancedSettingDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Testimonials Repo Error in Featch data - /admin/testimonials/$id API==========$error",
      );
      rethrow;
    }
  }
}
