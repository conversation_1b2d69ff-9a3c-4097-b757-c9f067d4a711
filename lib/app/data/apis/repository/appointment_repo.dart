import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/appointment/appointment_model.dart';

@injectable
class AppointmentRepository {
  final ApiClient apiClient;

  AppointmentRepository({required this.apiClient});

  Future<AppointmentModel> getAppointmentListRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.appointmentUrl,
      );

      return AppointmentModel.fromJson(response);
    } catch (error) {
      log(
        "==========Appointment Repo Error in Featch data - /admin/appointment API==========$error",
      );
      rethrow;
    }
  }

  Future<AppointmentModel> getAppointmentByIdRepository({
    required String id,
    required String authToken,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.singleAppointmentUrl}/$id',
        data: {'auth_token': authToken},
      );

      return AppointmentModel.fromJson(response);
    } catch (error) {
      log(
        "==========Appointment Repo Error in Featch data - /admin/appointment/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<DeleteAppointmentResponse> deleteVcardByIdRepository({
    required int id,
    required String authToken,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteAppointmentUrl}/$id',
        data: {'auth_token': authToken},
      );

      return DeleteAppointmentResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Appointment Repo Error Dlete - /admin/appointment-delete/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<UpdateAppointmentResponse> updateAdminAppointmentRepository({
    required String status,
    required String appointmentId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        withoutMessage: true,
        '${ApiEndPoint.appointmentCompletedUrl}/$appointmentId',
        data: {'status': status},
      );

      return UpdateAppointmentResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - admin/appointment-completed API==========$error",
      );
      rethrow;
    }
  }
}
