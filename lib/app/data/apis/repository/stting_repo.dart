import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/setting/general_setting_model.dart';
import 'package:v_card/app/data/model/setting/payment_config_model.dart';

@injectable
class SettingsRepository {
  final ApiClient apiClient;

  SettingsRepository({required this.apiClient});

  Future<GeneralSettingModel> getGeneralSettings() async {
    try {
      final response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.settingsEditUrl,
        withoutMessage: true,
      );
      return GeneralSettingModel.fromJson(response);
    } catch (e) {
      log('Error fetching general settings: $e');
      rethrow;
    }
  }

  Future<UpdateGeneralSettingModel> updateGeneralSettings(
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        
        ApiEndPoint.settingsUpdateUrl,

        multipartData: data,
      );
      return UpdateGeneralSettingModel.fromJson(response);
    } catch (e) {
      log('Error updating general settings: $e');
      rethrow;
    }
  }

  Future<PaymentConfigModel> getPaymentConfig() async {
    try {
      final response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.paymentConfigUrl,
        withoutMessage: true,
      );
      return PaymentConfigModel.fromJson(response);
    } catch (e) {
      log('Error fetching payment config: $e');
      rethrow;
    }
  }

  Future<UpdatePaymentConfigModel> updatePaymentConfig(
    Map<String, dynamic> data,
  ) async {
    try {
      final response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.paymentConfigUrl,
        data: data,
      );
      return UpdatePaymentConfigModel.fromJson(response);
    } catch (e) {
      log('Error updating payment config: $e');
      rethrow;
    }
  }
}
