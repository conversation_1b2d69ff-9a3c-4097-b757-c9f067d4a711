// manage_section_repository.dart
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/manage_section/manage_section_model.dart';

@injectable
class ManageSectionRepository {
  final ApiClient apiClient;

  ManageSectionRepository({required this.apiClient});

  Future<ManageSectionModel> getManageSection(String vcardId) async {
    try {
      final response = await apiClient.buildRequest(
        RequestType.GET,
        '${ApiEndPoint.manageSectionUrl}/$vcardId',
        withoutMessage: true,
      );
      return ManageSectionModel.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }

  Future<UpdateManageSectionResponse> updateManageSection({
    required Map<String, dynamic> data,
  }) async {
    try {
      final response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.updateManageSectionUrl,
        data: data,
      );
      return UpdateManageSectionResponse.fromJson(response);
    } catch (e) {
      rethrow;
    }
  }
}
