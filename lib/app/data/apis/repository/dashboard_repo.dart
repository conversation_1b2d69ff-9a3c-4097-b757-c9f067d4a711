import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/dashboard/admin/admin_dashboard_model.dart';
import 'package:v_card/app/data/model/dashboard/admin/admin_today_appointment_model.dart';
import 'package:v_card/app/data/model/dashboard/admin/dashboard_chart_data_model.dart';
import 'package:v_card/app/data/model/dashboard/super_admin/super_Admin_chart_data_model.dart';
import 'package:v_card/app/data/model/dashboard/super_admin/super_admin_dashboard_model.dart';

@injectable
class DashboardRepository {
  final ApiClient apiClient;

  DashboardRepository({required this.apiClient});

  Future<AdminDashboard> getAdminDashboardRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.adminDashboardUrl,
      );

      return AdminDashboard.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - /admin/dashboard API==========$error",
      );
      rethrow;
    }
  }

  Future<SuperAdminDashboard> getSuperAdminDashboardRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.superDashboardUrl,
      );

      return SuperAdminDashboard.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - /dashboard API==========$error",
      );
      rethrow;
    }
  }

  Future<DashboardChartDataModel> getAdminDashboarIncomeChartRepository({
    String? startDate,
    String? endDate,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.adminIncomeChartUrl,
      );

      return DashboardChartDataModel.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - /admin/income-chart API==========$error",
      );
      rethrow;
    }
  }

  Future<DashboardChartDataModel> getAdminDashboarIncomeChartByDateRepository({
    String? startDate,
    String? endDate,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.adminIncomeChartUrl}/?start_date=$startDate&end_date=$endDate',
      );

      return DashboardChartDataModel.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - /admin/income-chart API==========$error",
      );
      rethrow;
    }
  }

  Future<SuperAdminChartDataModel>
  getSuperAdminDashboarIncomeChartRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.superAdminIncomeChartUrl,
      );

      return SuperAdminChartDataModel.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - /admin/income-chart API==========$error",
      );
      rethrow;
    }
  }

  Future<SuperAdminChartDataModel>
  getSuperAdminDashboarIncomeChartByDateRepository(
    String authToken, {
    String? startDate,
    String? endDate,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.superAdminIncomeChartUrl}/?start_date=$startDate&end_date=$endDate',
      );

      return SuperAdminChartDataModel.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - /admin/income-chart API==========$error",
      );
      rethrow;
    }
  }

  Future<AdminTodayAppointmentModel>
  getDashboardAdminAppointmentRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.todayAppointmentUrl,
      );

      return AdminTodayAppointmentModel.fromJson(response);
    } catch (error) {
      log(
        "==========Dashboard Repo Error in Featch data - admin/today-appointment API==========$error",
      );
      rethrow;
    }
  }
}
