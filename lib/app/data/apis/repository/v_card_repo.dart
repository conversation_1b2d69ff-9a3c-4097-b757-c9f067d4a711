import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/v_card/create_vcard_model.dart';
import 'package:v_card/app/data/model/v_card/single_vcard_model.dart';
import 'package:v_card/app/data/model/v_card/update_template_model.dart';
import 'package:v_card/app/data/model/v_card/v_card_model.dart';
import 'package:v_card/app/data/model/v_card/v_card_only_list_model.dart';
import 'package:v_card/app/data/model/v_card/v_card_qr_code_model.dart';
import 'package:v_card/app/data/model/v_card/vcard_basic_detail_moel.dart';
import 'package:v_card/app/data/model/v_card/vcard_delete_model.dart';
import 'package:v_card/app/data/model/v_card/vcard_tepmlate_model.dart';
import 'package:v_card/app/data/model/v_card/vcard_update_tepmlate_model.dart';

@injectable
class VcardRepository {
  final ApiClient apiClient;

  VcardRepository({required this.apiClient});

  Future<VCardModel> getsuperAdminVcardListRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        ApiEndPoint.superAdminVcardUrl,
      );

      return VCardModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error in Featch data - /vcard(superAdminVcardUrl) API==========$error",
      );
      rethrow;
    }
  }

  Future<VCardModel> getadminVcardListRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        ApiEndPoint.adminVcardUrl,
      );

      return VCardModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error in Featch data - /admin/vcard API==========$error",
      );
      rethrow;
    }
  }

  Future<SingleVCardModel> getVcardByIdRepository({
    required String id,
    // required String authToken,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.singleVcardUrl}/$id',
        // data: {'auth_token': authToken},
      );

      return SingleVCardModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error in Featch data - /admin/vcard/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<VCardQrCodeModel> getVcardQrCodeRepository({required String id}) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.superAdminVcardQrCodeUrl}/$id',
      );

      return VCardQrCodeModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error in Featch data - /vcard-qrcode$id API==========$error",
      );
      rethrow;
    }
  }

  Future<VCardDeleteModel> deleteVcardByIdRepository({
    required int id,
    required String authToken,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteVcardUrl}/$id',
        data: {'auth_token': authToken},
      );

      return VCardDeleteModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error Dlete - /admin/vcard-delete/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<CreateVCardModel> createAdminVcardRepository({
    required Map<String, dynamic> data,
    required Map<String, List<String>> fileFields,
  }) async {
    try {
      var response = await apiClient.buildMultipartWithMultipleFilesRequest(
        path: ApiEndPoint.adminCreateVcardUrl,
        fields: data,
        fileFields: fileFields,
        method: RequestType.MULTIPART_POST,
        
      );

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return CreateVCardModel.fromJson(response);
    } catch (error) {
      log("==========Create vCard Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<UpdateVCardTemplateModel> updateVCardBasicdetailrepository({
    required String vcardId,
    required Map<String, dynamic> data,
    required Map<String, List<String>> fileFields,
  }) async {
    try {
      var response = await apiClient.buildMultipartWithMultipleFilesRequest(
        path: '${ApiEndPoint.updateVCardBasicDetail}/$vcardId',
        fields: data,
        fileFields: fileFields,
        method: RequestType.MULTIPART_POST,
      );
      return UpdateVCardTemplateModel.fromJson(response);
    } catch (error) {
      log("Error updating vcard template: $error");
      rethrow;
    }
  }

  Future<VCardBasicDetailsModel> getVCardBasicDetails({
    required String vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.getVCardBasicDetails}/$vcardId',
      );
      return VCardBasicDetailsModel.fromJson(response);
    } catch (error) {
      log("Error getting vcard basic details: $error");
      rethrow;
    }
  }

  Future<VCardTemplatesModel> getVCardTemplates({
    required String vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.getvcardTemplates}/$vcardId',
      );
      return VCardTemplatesModel.fromJson(response);
    } catch (error) {
      log("Error getting vcard templates: $error");
      rethrow;
    }
  }

  Future<VCardUpdateTemplatesModel> updateVCardTemplate({
    required String vcardId,
    required String templateId,
    required String isActive,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.updateVCardTemplateModel}/$vcardId',
        data: {"template_id": templateId, "status": isActive},
      );
      return VCardUpdateTemplatesModel.fromJson(response);
    } catch (error) {
      log("Error updating vcard template: $error");
      rethrow;
    }
  }

  Future<VcardOnlyListModel> getOnlyVcardList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.vcardListUrl,
        withoutMessage: true,
      );
      return VcardOnlyListModel.fromJson(response);
    } catch (error) {
      log(
        "==========VCard Repo Error in Fetch data - /admin/vcard-list API==========$error",
      );
      rethrow;
    }
  }

}
