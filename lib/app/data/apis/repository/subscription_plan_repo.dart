import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/payment_check_model.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/payment_request_status_model.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/subscription_history_model.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/subscription_plan_model.dart';

@injectable
class SubscriptionPlanRepository {
  final ApiClient apiClient;

  SubscriptionPlanRepository({required this.apiClient});

  Future<SubscriptionHistoryModel> getSubscriptionHistoryRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.subscriptionHistoryUrl,
      );

      return SubscriptionHistoryModel.fromJson(response);
    } catch (error) {
      log(
        "==========Subscription Repo Error in Featch data - /admin/manage-subscription API==========$error",
      );
      rethrow;
    }
  }

  Future<SubscriptionPlanModel> getSubscriptionPlanRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.subscriptionPlanUrl,
      );

      return SubscriptionPlanModel.fromJson(response);
    } catch (error) {
      log(
        "==========Subscription Repo Error in Featch data - /admin/subscription-plan API==========$error",
      );
      rethrow;
    }
  }

  Future<SubscriptionPlanBuyDataModel> subscriptionPlanBuyRepository(
    String id,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.subscriptionPlanBuyUrl}/$id',
      );

      return SubscriptionPlanBuyDataModel.fromJson(response);
    } catch (error) {
      log(
        "==========Subscription Repo Error in Featch data - /admin/plans-buy API==========$error",
      );
      rethrow;
    }
  }

  Future<PaymentRequestStatusModel>
  getPaymentRequestStatusModelRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.paymentRequestStatusUrl,
        withoutMessage: true,
      );

      return PaymentRequestStatusModel.fromJson(response);
    } catch (error) {
      log(
        "==========Subscription Repo Error in Featch data - /admin/payment-is-pending API==========$error",
      );
      rethrow;
    }
  }
}
