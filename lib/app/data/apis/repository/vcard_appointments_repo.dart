import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/vcard_appointments/vcard_appointments_model.dart';

@injectable
class AppointmentsVcardrepository {
  final ApiClient apiClient;

  AppointmentsVcardrepository({required this.apiClient});

  // Add these methods to the VcardRepository class
  Future<AppointmentsResponse> getVcardAppointments({
    required String vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.adminVcardAppointmentsUrl}/$vcardId',
      );

      return AppointmentsResponse.fromJson(response);
    } catch (error) {
      log("Error getting vcard appointments: $error");
      rethrow;
    }
  }

  Future<AppointmentsResponse> saveVcardAppointments({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.adminVcardAppointmentsUrl,
        data: data,
      );

      return AppointmentsResponse.fromJson(response);
    } catch (error) {
      log("Error saving vcard appointments: $error");
      rethrow;
    }
  }
}
