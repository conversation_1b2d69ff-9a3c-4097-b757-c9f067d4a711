import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/business/business_card_model.dart';
import 'package:v_card/app/data/model/business/create_business_model.dart';
import 'package:v_card/app/data/model/business/create_group_model.dart';
import 'package:v_card/app/data/model/business/delete_group_model.dart';
import 'package:v_card/app/data/model/business/group_model.dart';

@injectable
class BusinesscardRepository {
  final ApiClient apiClient;

  BusinesscardRepository({required this.apiClient});

  Future<CreateGrouopModel> createGroupRepository(String groupName) async {
    try {
      var groupData = {"name": groupName};
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.groupCreateUrl,
        data: groupData,
      );

      return CreateGrouopModel.fromJson(response);
    } catch (error) {
      log(
        "==========Business Card/Group Repo Error in Create Group - /groups-create API==========$error",
      );
      rethrow;
    }
  }

  Future<GrouopModel> getGroupListRepository(String authToken) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.adminGroupUrl,
        // '${ApiEndPoint.adminGroupUrl}?auth_token=$authToken',
      );

      return GrouopModel.fromJson(response);
    } catch (error) {
      log(
        "==========Business Card/Group Repo Error in Featch data - /groups API==========$error",
      );
      rethrow;
    }
  }

  Future<DeleteGroupModel> deletGroupApi({required String id}) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteGroupUrl}/$id',
      );

      return DeleteGroupModel.fromJson(response);
    } catch (error) {
      log(
        "==========Business Card Repo Error in Delete Group API==========$error",
      );
      rethrow;
    }
  }

  Future<CreateBusinessCardModel> createBusinessCardRepository(
    String urlAliasScannedData,
    String groupId,
    // String selectedVcardId,
  ) async {
    try {
      var businessBody = {
        "url_alias": urlAliasScannedData,
        "group_id": groupId,
        // "id": selectedVcardId,
      };
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.businessCardCreateUrl,
        data: businessBody,
      );

      return CreateBusinessCardModel.fromJson(response);
    } catch (error) {
      log(
        "==========Business/Business Card Repo Error in Create Business Card - /business-cards-create API==========$error",
      );
      rethrow;
    }
  }

  Future<BusinessCardModel> getBusinessCardListRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.businessCardUrl,
      );
      // log('==============Data: ${response['data']}');

      return BusinessCardModel.fromJson(response);
    } catch (error) {
      log(
        "==========Business Card Repo Error in Featch data - /business-cards API==========$error",
      );
      rethrow;
    }
  }

  Future<BusinessCardModel> getFilteredBusinessCardListRepository({
    required String filterId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.businessCardUrl}?filter[]=$filterId',
      );
      log('==============Filtered Data: ${response['data']}');
      return BusinessCardModel.fromJson(response);
    } catch (error) {
      log(
        "==========Business Card Repo Error in Fetch Data - /business-cards API with filters==========$error",
      );
      rethrow;
    }
  }
}
