import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/currency_model/currency_model.dart';
import 'package:v_card/app/data/model/product/create_product_model.dart';
import 'package:v_card/app/data/model/product/delete_product_model.dart';
import 'package:v_card/app/data/model/product/product_detail_model.dart';
import 'package:v_card/app/data/model/product/product_model.dart';
import 'package:v_card/app/data/model/product/update_product_model.dart';

@injectable
class ProductRepository {
  final ApiClient apiClient;

  ProductRepository({required this.apiClient});

  Future<ProductModel> getadminProductListRepository(String vCardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.productsUrl}/$vCardId',
      );

      return ProductModel.fromJson(response);
    } catch (error) {
      log(
        "==========Product Repo Error in Fetch data - /vcard-products/$vCardId API==========$error",
      );
      rethrow;
    }
  }

  Future<CreateProductModel> createAdminServiceRepository({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildProductRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.createPoductsUrl,
        data: data,
      );

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return CreateProductModel.fromJson(response);
    } catch (error) {
      log("==========Create Service Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<UpdateProductModel> updateProductRepository({
    required String productId,
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildProductRequest(
        RequestType.MULTIPART_POST,
        '${ApiEndPoint.createPoductsUrl}/$productId',
        data: data,
      );

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return UpdateProductModel.fromJson(response);
    } catch (error) {
      log("==========Update Product Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<ProductDetailModel> getProductByIdRepository({required int id}) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.productsByIdUrl}/$id',
      );

      return ProductDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Product Repo Error in Fetch data - /admin/product/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<DeleteProductModel> deleteVcardByIdRepository({
    required int id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deletePoductsUrl}/$id',
      );

      return DeleteProductModel.fromJson(response);
    } catch (error) {
      log(
        "==========Product Repo Error Delete - /admin/vcard-delete/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<CurrencyModel> getCurrencyList() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.currencyListUrl,
        withoutMessage: true,
      );
      return CurrencyModel.fromJson(response);
    } catch (error) {
      log("==========Currency Repo Error: ${error.toString()}");
      rethrow;
    }
  }
}
