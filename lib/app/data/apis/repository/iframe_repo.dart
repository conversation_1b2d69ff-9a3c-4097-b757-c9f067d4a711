import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/iframe/iframe_detail_model.dart';
import 'package:v_card/app/data/model/iframe/iframe_model.dart';

@injectable
class IframeRepository {
  final ApiClient apiClient;

  IframeRepository({required this.apiClient});

  // Get Instagram embeds by vCard ID
  Future<IframeModel> getIframeRepo(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.iframeListUrl}/$vcardId',
      );
      log('==============Iframe Data: ${response['data']}');

      return IframeModel.fromJson(response);
    } catch (error) {
      log(
        "==========Iframe Repo Error in Fetch data - /admin/iframe/$vcardId API==========$error",
      );
      rethrow;
    }
  }

  Future<IframeDetailModel> getIframeByIdRepo(String iframeId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.iframeUrl}/$iframeId',
      );
      log('==============Iframe Data: ${response['data']}');

      return IframeDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Iframe Repo Error in Fetch data - /admin/iframe/$iframeId API==========$error",
      );
      rethrow;
    }
  }

  // Create or Update Instagram embed
  Future<IframeCreateResponse> createIframeRepo({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.iframeUrl,
        data: data,
      );

      log('==============Create Iframe Response: $response');
      return IframeCreateResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Iframe Repo Error in Create - /admin/iframe API==========$error",
      );
      rethrow;
    }
  }

  // Update or Update Instagram embed
  Future<IframeCreateResponse> updateIframeRepo({
    required Map<String, dynamic> data,
    required String iframeId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.iframeUrl}/$iframeId',
        data: data,
      );

      log('==============Update Iframe Response: $response');
      return IframeCreateResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Iframe Repo Error in Update - /admin/iframe API==========$error",
      );
      rethrow;
    }
  }

  // Delete Instagram embed
  Future<IframeCreateResponse> deleteIframeRepo(String iframeId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.iframeUrl}/$iframeId',
      );

      log('==============Delete Iframe Response: $response');
      return IframeCreateResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Iframe Repo Error in Delete - /admin/iframe/$iframeId API==========$error",
      );
      rethrow;
    }
  }
}
