import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/product_order/product_order_model.dart';

@injectable
class ProductOrderRepository {
  final ApiClient apiClient;

  ProductOrderRepository({required this.apiClient});

  Future<ProductOrderModel> getProductOrderRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        ApiEndPoint.productOrderUrl,
      );

      return ProductOrderModel.fromJson(response);
    } catch (error) {
      log(
        "==========Enquiries Repo Error in Featch data - /admin/enquiries API==========$error",
      );
      rethrow;
    }
  }
}
