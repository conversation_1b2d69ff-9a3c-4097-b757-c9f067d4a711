import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/profile/profile_model.dart';

@injectable
class ProfileRepository {
  final ApiClient apiClient;

  ProfileRepository({required this.apiClient});

  Future<ProfileModel> getProfileDataRepository() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        ApiEndPoint.profileEditUrl,
      );

      return ProfileModel.fromJson(response);
    } catch (error) {
      log(
        "==========Profile Repo Error in Featch data - /admin/profile-edit API==========$error",
      );
      rethrow;
    }
  }

  Future<ProfileModel> updateProfileDataRepository(
    Map<String, dynamic> data,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.profileUpdateUrl,
        multipartData: data,
      );

      return ProfileModel.fromJson(response);
    } catch (error) {
      log(
        "==========Profile Repo Error in Featch data - /admin/profile-update API==========$error",
      );
      rethrow;
    }
  }
}
