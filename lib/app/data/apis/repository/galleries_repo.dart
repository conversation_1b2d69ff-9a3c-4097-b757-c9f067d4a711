import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/galleries/create_galleries_model.dart';
import 'package:v_card/app/data/model/galleries/delete_galleries_model.dart';
import 'package:v_card/app/data/model/galleries/galleries_detail_model.dart';
import 'package:v_card/app/data/model/galleries/galleries_model.dart';
import 'package:v_card/app/data/model/galleries/update_galleries_model.dart';

@injectable
class GalleriesRepository {
  final ApiClient apiClient;

  GalleriesRepository({required this.apiClient});

  Future<GalleriesModel> getGalleriesListRepository(String vCardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        '${ApiEndPoint.galleryListUrl}/$vCardId',
        withoutMessage: true,
      );
      return GalleriesModel.fromJson(response);
    } catch (error) {
      log(
        "==========Blog Repo Error in Featch data - /vcard-blogs/$vCardId API==========$error",
      );
      rethrow;
    }
  }

  Future<CreateGalleriesModel> createGalleriesRepository({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.galleryUrl,
        multipartData: data,
      );
      return CreateGalleriesModel.fromJson(response);
    } catch (error) {
      log("==========Create Service Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<GalleriesDetailModel> getGalleriesByIdRepository({
    required int id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.galleryUrl}/$id',
      );
      return GalleriesDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Blog Repo Error in Featch data - /admin/blogs/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<DeleteGalleriesModel> deleteGalleriesByIdRepository({
    required int id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.galleryUrl}/$id',
      );
      return DeleteGalleriesModel.fromJson(response);
    } catch (error) {
      log(
        "==========Blog Repo Error Dlete - /admin/vcard-delete/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<UpdateGalleriesModel> updateGalleriesRepository({
    required int galleriesId,
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        '${ApiEndPoint.galleryUrl}/$galleriesId',
        multipartData: data,
      );
      return UpdateGalleriesModel.fromJson(response);
    } catch (error) {
      log(error.toString());
      rethrow;
    }
  }
}
