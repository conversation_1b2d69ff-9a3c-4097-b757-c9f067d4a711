import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/seo/seo_model.dart';

@injectable
class SeoRepository {
  final ApiClient apiClient;

  SeoRepository({required this.apiClient});

  Future<SeoModel> getSeoListRepository(String vCardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        '${ApiEndPoint.seoUrl}/$vCardId',
        withoutMessage: true,
      );
      return SeoModel.fromJson(response);
    } catch (error) {
      log("Error fetching SEO data: $error");
      rethrow;
    }
  }

  Future<UpdateSeoModel> updateSeoRepository({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.updateSeoUrl,
        data: data,
      );
      return UpdateSeoModel.fromJson(response);
    } catch (error) {
      log("Error updating SEO: $error");
      rethrow;
    }
  }
}