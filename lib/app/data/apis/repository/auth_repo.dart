import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/auth/change_password.dart';
import 'package:v_card/app/data/model/auth/create_new_pass_model.dart';
import 'package:v_card/app/data/model/auth/forgot_password_model.dart';
import 'package:v_card/app/data/model/auth/login_model.dart';
import 'package:v_card/app/data/model/auth/logout_model.dart';
import 'package:v_card/app/data/model/auth/registration_model.dart';

@injectable
class AuthRepository {
  final ApiClient apiClient;

  AuthRepository({required this.apiClient});

  Future<RegistrationModel> registerApi({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    required String contact,
    required String regionCode,
    required String termPolicyCheck,
  }) async {
    try {
      final Map<String, dynamic> registerData = {
        "first_name": firstName,
        "last_name": lastName,
        "email": email,
        "password": password,
        "contact": contact,
        "region_code": regionCode,
        "term_policy_check": termPolicyCheck,
      };

      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.registerUrl,
        data: registerData,
      );

      return RegistrationModel.fromJson(response);
    } catch (error) {
      log("==========Auth Repo Error in Register API==========$error");

      rethrow;
    }
  }

  Future<LoginModel> loginApi({
    required String email,
    required String password,
  }) async {
    try {
      final Map<String, dynamic> postData = {
        'email': email,
        'password': password,
      };

      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.loginUrl,
        data: postData,
      );

      return LoginModel.fromJson(response);
    } catch (error) {
      log("==========Auth Repo Error in Login API==========$error");
      rethrow;
    }
  }

  Future<ForgotPasswordModel> forgotPasswordApi({
    required String email,
    required dynamic urlDomain,
  }) async {
    try {
      final Map<String, dynamic> postData = {
        "email": email,
        "url_domain": urlDomain,
      };

      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.forgotPasswordUrl,
        data: postData,
      );

      return ForgotPasswordModel.fromJson(response);
    } catch (error) {
      log("==========Auth Repo Error in Forgot Password API==========$error");
      rethrow;
    }
  }

  // Future<ResetPasswordModel> createNewPasswordApi({
  //   required String email,
  //   required String otp,
  //   required String newPassword,
  //   required String confirmPassword,
  // }) async {
  //   try {
  //     final Map<String, dynamic> postData = {
  //       'email': email,
  //       'otp': otp,
  //       'new_password': newPassword,
  //       'confirm_password': confirmPassword,
  //     };

  //     var response = await apiClient.buildRequest(
  //       RequestType.POST,
  //       ApiEndPoint.resetPasswordByMailUrl,
  //       data: postData,
  //     );

  //     return ResetPasswordModel.fromJson(response);
  //   } catch (error) {
  //     log(
  //       "==========Auth Repo Error in Create new password API==========$error",
  //     );
  //     rethrow;
  //   }
  // }

  Future<LogoutModel> logoutApi({
    required String authToken,
    required String email,
  }) async {
    try {
      final Map<String, dynamic> postData = {'email': email};

      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.logoutUrl,
        data: postData,
        withoutMessage: true,
      );
      return LogoutModel.fromJson(response);
    } catch (error) {
      log("==========Auth Repo Error in Logout API==========$error");
      rethrow;
    }
  }

  Future<ResetPasswordModel> resetPasswordApi({
    required String token,
    required String email,
    required String password,
    required String confirmPassword,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.resetPasswordByMailUrl}?token=$token&email=$email&password=$password&password_confirmation=$confirmPassword',
        data: {},
      );

      return ResetPasswordModel.fromJson(response);
    } catch (error) {
      log("==========Auth Repo Error in Reset Password API==========$error");
      rethrow;
    }
  }

  Future<ChangePasswordModel> changePasswordApi({
    required String email,
    required String oldPassword,
    required String newPassword,
    required String confirmPassword,
  }) async {
    try {
      final Map<String, dynamic> postData = {
        "email": email,
        "old_password": oldPassword,
        "password": newPassword,
        "password_conformation": confirmPassword,
      };

      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.resetPasswordUrl,
        data: postData,
      );

      return ChangePasswordModel.fromJson(response);
    } catch (error) {
      log("==========Auth Repo Error in Change Password API==========$error");
      rethrow;
    }
  }

  Future<void> deleteAccountApi({required String id}) async {
    try {
      await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteAccountUrl}/$id',
      );
    } catch (error) {
      log("==========Auth Repo Error in Delete Account API==========$error");
      rethrow;
    }
  }

  // Add this method to your AuthRepository class

  Future<LoginModel> googleLoginApi({
    required String email,
    required String name,
  }) async {
    try {
      // final Map<String, dynamic> postData = {'token': token};
      final Map<String, dynamic> postData = {'email': email, 'name': name};

      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.googleLoginUrl,
        data: postData,
      );

      return LoginModel.fromJson(response);
    } catch (error) {
      log("==========Auth Repo Error in Google Login API==========$error");
      rethrow;
    }
  }
}
