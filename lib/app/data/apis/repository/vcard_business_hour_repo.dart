import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/vcard_business_hour/vcard_business_hour_model.dart';

@injectable
class VcardBusinessHoursRepository {
  final ApiClient apiClient;

  VcardBusinessHoursRepository({required this.apiClient});

  Future<VcardBusinessHoursModel> getBusinessHours(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.businessHoursUrl}/$vcardId',
      );
      log('Business Hours Response: $response');

      return VcardBusinessHoursModel.fromJson(response);
    } catch (error) {
      log("Error fetching business hours: $error");
      rethrow;
    }
  }

  Future<BusinessHoursResponseModel> saveBusinessHours(
    Map<String, dynamic> data,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.businessHoursUrl,
        data: data,
      );
      log('Save Business Hours Response: $response');

      return BusinessHoursResponseModel.fromJson(response);
    } catch (error) {
      log("Error saving business hours: $error");
      rethrow;
    }
  }
}
