import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/service/create_service_model.dart';
import 'package:v_card/app/data/model/service/delete_service_model.dart';
import 'package:v_card/app/data/model/service/service_model.dart';
import 'package:v_card/app/data/model/service/service_detail_model.dart';
import 'package:v_card/app/data/model/service/service_slider_model.dart';
import 'package:v_card/app/data/model/service/update_service_model.dart';

@injectable
class ServiceRepository {
  final ApiClient apiClient;

  ServiceRepository({required this.apiClient});

  Future<ServiceModel> getadminServiceListRepository(String vCardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.servicesUrl}/$vCardId',
      );

      return ServiceModel.fromJson(response);
    } catch (error) {
      log(
        "==========Service Repo Error in Featch data - /vcard-service/$vCardId API==========$error",
      );
      rethrow;
    }
  }

  Future<CreateServiceModel> createAdminServiceRepository({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.createServicesUrl,
        multipartData: data,
        // withoutMessage: true,
      );

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return CreateServiceModel.fromJson(response);
    } catch (error) {
      log("==========Create Service Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<UpdateServiceModel> updateServiceRepository({
    required String serviceId,
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        '${ApiEndPoint.createServicesUrl}/$serviceId',
        multipartData: data,
        // withoutMessage: true,
      );

      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return UpdateServiceModel.fromJson(response);
    } catch (error) {
      log("==========Create Service Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<ServiceDetailModel> getServiceByIdRepository({required int id}) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.servicesByIdUrl}/$id',
      );

      return ServiceDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error in Featch data - /admin/service/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<DeleteServiceModel> deleteServiceByIdRepository({
    required int id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteServicesUrl}/$id',
      );

      return DeleteServiceModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error Dlete - /admin/vcard-delete/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<ServiceSliderModel> serviceSliderRepository({
    required String vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.serviceSlidersUrl}/$vcardId',
      );

      return ServiceSliderModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error Dlete - /admin/service-slider/$vcardId API==========$error",
      );
      rethrow;
    }
  }
}
