import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/banner/banner_model.dart';

@injectable
class BannerRepository {
  final ApiClient apiClient;

  BannerRepository({required this.apiClient});

  Future<BannerModel> getBannerListRepository(String vCardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        '${ApiEndPoint.bannerUrl}/$vCardId',
        withoutMessage: true,
      );
      return BannerModel.fromJson(response);
    } catch (error) {
      log(
        "==========Banner Repo Error in Fetch data - /vcard-banners/$vCardId API==========$error",
      );
      rethrow;
    }
  }

  Future<UpdateBannerModel> updateBannerRepository({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.updateBannerUrl,
        data: data,
      );
      return UpdateBannerModel.fromJson(response);
    } catch (error) {
      log(error.toString());
      rethrow;
    }
  }
}
