import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/privacy_policy/privacy_policy_model.dart';


@injectable
class PrivacyPolicyRepository {
  final ApiClient apiClient;

  PrivacyPolicyRepository({required this.apiClient});

  Future<PrivacyPolicyModel> getPrivacyPolicy(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,        withoutMessage: true,

        '${ApiEndPoint.privacyPolicyUrl}/$vcardId',
      );
      log('==============Privacy Policy Data: ${response['data']}');

      return PrivacyPolicyModel.fromJson(response);
    } catch (error) {
      log("==========Privacy Policy Repo Error in Fetch data - /admin/privacy-policies/$vcardId API==========$error");
      rethrow;
    }
  }

  Future<UpdatePrivacyPolicyModel> updatePrivacyPolicy({
    required String privacyPolicy,
    required String vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.privacyPolicyUrl,
        data: {
          "privacy_policy": privacyPolicy,
          "vcard_id": vcardId.toString(),
        },
      );

      log('==============Update Privacy Policy Response: $response');
      return UpdatePrivacyPolicyModel.fromJson(response);
    } catch (error) {
      log("==========Privacy Policy Repo Error in Update - /admin/privacy-policies API==========$error");
      rethrow;
    }
  }
}