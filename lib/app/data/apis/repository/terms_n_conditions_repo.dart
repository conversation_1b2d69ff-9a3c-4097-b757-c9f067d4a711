import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/terms_n_conditions/terms_n_conditions_model.dart';

@injectable
class TermsAndConditionsRepository {
  final ApiClient apiClient;

  TermsAndConditionsRepository({required this.apiClient});

  Future<TermsAndConditionsModel> getTermsAndConditions(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.termsNConditionPolicyUrl}/$vcardId',
      );
      log('==============Terms And Conditions Data: ${response['data']}');

      return TermsAndConditionsModel.fromJson(response);
    } catch (error) {
      log(
        "==========Terms And Conditions Repo Error in Fetch data - /admin/terms-conditions/$vcardId API==========$error",
      );
      rethrow;
    }
  }

  Future<bool> updateTermsAndConditions({
    required String termCondition,
    required String vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.termsNConditionPolicyUrl,
        data: {"term_condition": termCondition, "vcard_id": vcardId.toString()},
      );

      log('==============Update Terms And Conditions Response: $response');
      return response['success'] == true;
    } catch (error) {
      log(
        "==========Terms And Conditions Repo Error in Update - /admin/terms-conditions API==========$error",
      );
      rethrow;
    }
  }
}
