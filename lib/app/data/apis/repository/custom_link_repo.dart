// File: app/data/apis/repository/custom_link_repo.dart

import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/custom_link/custom_link_model.dart';

@injectable
class CustomLinkRepository {
  final ApiClient apiClient;

  CustomLinkRepository({required this.apiClient});

  // Get all custom links by vCard ID
  Future<CustomLinkModel> getCustomLinksRepo(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.customLinksListUrl}/$vcardId',
      );
      log('==============Custom Link Data: ${response['data']}');

      return CustomLinkModel.fromJson(response);
    } catch (error) {
      log(
        "==========Custom Link Repo Error in Fetch data - /admin/custom-links/$vcardId API==========$error",
      );
      rethrow;
    }
  }

  // Get custom link by ID
  Future<CustomLinkDetailModel> getCustomLinkByIdRepo(String linkId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.customLinkUrl}/$linkId',
      );
      log('==============Custom Link Data: ${response['data']}');

      return CustomLinkDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Custom Link Repo Error in Fetch data - /admin/custom-link/$linkId API==========$error",
      );
      rethrow;
    }
  }

  // Create custom link
  Future<CustomLinkResponse> createCustomLinkRepo({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        ApiEndPoint.customLinkUrl,
        data: data,
      );

      log('==============Create Custom Link Response: $response');
      return CustomLinkResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Custom Link Repo Error in Create - /admin/custom-link API==========$error",
      );
      rethrow;
    }
  }

  // Update custom link
  Future<CustomLinkResponse> updateCustomLinkRepo({
    required Map<String, dynamic> data,
    required String linkId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.updateCustomLinkUrl}/$linkId',
        data: data,
      );

      log('==============Update Custom Link Response: $response');
      return CustomLinkResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Custom Link Repo Error in Update - /admin/update-custom-link/$linkId API==========$error",
      );
      rethrow;
    }
  }

  // Update Show As button
  Future<CustomLinkResponse> updateShowAsbuttonRepo({
    required Map<String, dynamic> data,
    required String linkId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.customLinkUrl}/$linkId/toggle-show-as-button',
        withoutMessage: true,
        // data: data,
      );

      log('==============Update Custom Link Response: $response');
      return CustomLinkResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Custom Link Repo Error in Update - /admin/update-custom-link/$linkId/toggle-show-as-button API==========$error",
      );
      rethrow;
    }
  }

  // Update Open New tab
  Future<CustomLinkResponse> updateUpdateOpenNewTabRepo({
    required Map<String, dynamic> data,
    required String linkId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.customLinkUrl}/$linkId/toggle-open-new-tab',
        withoutMessage: true,
        // data: data,
      );

      log('==============Update Custom Link Response: $response');
      return CustomLinkResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Custom Link Repo Error in Update - /admin/update-custom-link/$linkId/toggle-open-new-tab API==========$error",
      );
      rethrow;
    }
  }

  // Delete custom link
  Future<CustomLinkResponse> deleteCustomLinkRepo(String linkId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.customLinkUrl}/$linkId',
      );

      log('==============Delete Custom Link Response: $response');
      return CustomLinkResponse.fromJson(response);
    } catch (error) {
      log(
        "==========Custom Link Repo Error in Delete - /admin/custom-link/$linkId API==========$error",
      );
      rethrow;
    }
  }
}
