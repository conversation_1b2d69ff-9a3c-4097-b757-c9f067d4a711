import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/storage/storage_model.dart';

@injectable
class StorageRepository {
  final ApiClient apiClient;

  StorageRepository({required this.apiClient});

  Future<StorageResponseModel> getStorageDataRepo() async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        ApiEndPoint.storageUrl,
        withoutMessage: true,
      );
      return StorageResponseModel.fromJson(response);
    } catch (error) {
      log(
        "==========Storage Repo Error in Fetch data - /admin/storage API==========$error",
      );
      rethrow;
    }
  }
}
