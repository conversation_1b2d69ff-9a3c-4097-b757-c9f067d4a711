import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/social_connect/social_connect_model.dart';

@injectable
class SocialConnectRepository {
  final ApiClient apiClient;

  SocialConnectRepository({required this.apiClient});

  Future<SocialConnectModel> getsosialConnectData(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.socialconnectUrl}/$vcardId',
      );
      log('Social Connect Response: $response');

      return SocialConnectModel.fromJson(response);
    } catch (error) {
      log("Error fetching Social Connect: $error");
      rethrow;
    }
  }

  Future<SocialConnectResponseModel> saveSocialConnectData(
    Map<String, dynamic> data,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.socialconnectUrl,
        multipartData: data,
      );
      log('Save Social Connect Hours Response: $response');

      return SocialConnectResponseModel.fromJson(response);
    } catch (error) {
      log("Error saving Social Connect hours: $error");
      rethrow;
    }
  }
}
