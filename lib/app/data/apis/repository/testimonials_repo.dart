import 'dart:developer';
import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/testimonials/create_testimonials_model.dart';
import 'package:v_card/app/data/model/testimonials/delete_testimonials_model.dart';
import 'package:v_card/app/data/model/testimonials/testimonials_detail_model.dart';
import 'package:v_card/app/data/model/testimonials/testimonials_model.dart';
import 'package:v_card/app/data/model/testimonials/update_testimonials_model.dart';

@injectable
class TestimonialsRepository {
  final ApiClient apiClient;

  TestimonialsRepository({required this.apiClient});

  Future<TestimonialsModel> getadminTestimonialsListRepository(
    String vCardId,
  ) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.testimonialssUrl}/$vCardId',
      );

      return TestimonialsModel.fromJson(response);
    } catch (error) {
      log(
        "==========Testimonials Repo Error in Featch data - /vcard-testimonials/$vCardId API==========$error",
      );
      rethrow;
    }
  }

  Future<CreateTestimonialsModel> createAdminTestimonialsRepository({
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        ApiEndPoint.createTestimonialsUrl,
        multipartData: data,
      );
      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return CreateTestimonialsModel.fromJson(response);
    } catch (error) {
      log("==========Create Testimonials Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<UpdateTestimonialsModel> updateTestimonialsRepository({
    required int testimonialsId,
    required Map<String, dynamic> data,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.MULTIPART_POST,
        '${ApiEndPoint.createTestimonialsUrl}/$testimonialsId',
        multipartData: data,
      );
      if (response['success'] != true) {
        throw Exception(response['message'] ?? 'Unknown error occurred');
      }

      return UpdateTestimonialsModel.fromJson(response);
    } catch (error) {
      log("==========Create Testimonials Error: ${error.toString()}");
      rethrow;
    }
  }

  Future<TestimonialsDetailModel> getTestimonialsByIdRepository({
    required int id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,

        '${ApiEndPoint.testimonialsByIdUrl}/$id',
      );

      return TestimonialsDetailModel.fromJson(response);
    } catch (error) {
      log(
        "==========Testimonials Repo Error in Featch data - /admin/testimonials/$id API==========$error",
      );
      rethrow;
    }
  }

  Future<DeleteTestimonialsModel> deleteTestimonialsByIdRepository({
    required int id,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.DELETE,
        '${ApiEndPoint.deleteTestimonialsUrl}/$id',
      );

      return DeleteTestimonialsModel.fromJson(response);
    } catch (error) {
      log(
        "==========Vcard Repo Error Dlete - /admin/vcard-delete/$id API==========$error",
      );
      rethrow;
    }
  }

  // Future<UpdateTestimonialsModel> updateTestimonialsRepository({
  //   required int testimonialsId,
  //   required Map<String, dynamic> data,
  // }) async {
  //   try {
  //     // Create a multipart request
  //     var uri = Uri.parse(
  //       '${ApiEndPoint.createTestimonialsUrl}/$testimonialsId',
  //     );
  //     var request = http.MultipartRequest('POST', uri);

  //     // Add authorization header
  //     String? authToken = getIt<SharedPreferences>().getToken;
  //     request.headers['Authorization'] = 'Bearer $authToken';

  //     // Add text fields
  //     data.forEach((key, value) {
  //       if (key != 'image') {
  //         request.fields[key] = value.toString();
  //       }
  //     });

  //     // Add profile image if available
  //     if (data['image'] != null && data['image'] is File) {
  //       var file = data['image'] as File;
  //       var stream = http.ByteStream(file.openRead());
  //       var length = await file.length();

  //       var multipartFile = http.MultipartFile(
  //         'image',
  //         stream,
  //         length,
  //         filename: file.path.split('/').last,
  //       );

  //       request.files.add(multipartFile);
  //     }

  //     // Send the request
  //     var response = await request.send();
  //     var responseBody = await response.stream.bytesToString();

  //     if (response.statusCode == 200) {
  //       return UpdateTestimonialsModel.fromJson(jsonDecode(responseBody));
  //     } else {
  //       throw Exception(
  //         'Failed to update testimonials: ${response.statusCode}',
  //       );
  //     }
  //   } catch (e) {
  //     throw Exception('Failed to update testimonials: $e');
  //   }
  // }
}
