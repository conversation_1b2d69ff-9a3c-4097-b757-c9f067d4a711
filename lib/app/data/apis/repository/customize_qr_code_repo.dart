import 'dart:developer';

import 'package:injectable/injectable.dart';
import 'package:v_card/app/data/apis/api_client/api_client.dart';
import 'package:v_card/app/data/apis/api_endpoints/api_end_point.dart';
import 'package:v_card/app/data/model/customize_qr_code/customize_qr_code_model.dart';

@injectable
class CustomizeQRCodeRepository {
  final ApiClient apiClient;

  CustomizeQRCodeRepository({required this.apiClient});

  Future<CustomizeQRCodeModel> getCustomizeQRCodeRepo(String vcardId) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.GET,
        withoutMessage: true,
        '${ApiEndPoint.customizeQrCodeUrl}/$vcardId',
      );
      log('==============Customize QR Code Data: ${response['data']}');

      return CustomizeQRCodeModel.fromJson(response);
    } catch (error) {
      log(
        "==========Customize QR Code Repo Error in Fetch data - /admin/privacy-policies/$vcardId API==========$error",
      );
      rethrow;
    }
  }

  Future<UpdateCustomizeQRCodeModel> updateCustomizeQRCodeRepo({
    required Map<String, dynamic> data,
    String? vcardId,
  }) async {
    try {
      var response = await apiClient.buildRequest(
        RequestType.POST,
        '${ApiEndPoint.customizeQrCodeUrl}/$vcardId',
        data: data,
      );

      log('==============Update Customize QR Code Response: $response');
      return UpdateCustomizeQRCodeModel.fromJson(response);
    } catch (error) {
      log(
        "==========Customize QR Code Repo Error in Update - /admin/privacy-policies API==========$error",
      );
      rethrow;
    }
  }
}
