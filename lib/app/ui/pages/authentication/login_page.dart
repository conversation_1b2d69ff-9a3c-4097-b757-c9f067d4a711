import 'package:get/get.dart';
import 'package:v_card/app/controllers/auth_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/themes/button_theme.dart';

class LoginPage extends GetItHook<AuthController> {
  LoginPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Obx(
        () => IgnorePointer(
          ignoring:
              controller.isLoadingLogin.value ||
              controller.isLoadingGoogleLogin.value,
          child: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 40.h),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildLogo(),
                      <PERSON>(60.h),
                      _buildTitle(),
                      Gap(8.h),
                      _buildSubtitle(),
                      Gap(70.h),
                      _buildLoginForm(),
                      Gap(8.h),
                      if (controller.errorText.value.isNotEmpty)
                        AppText(
                          controller.errorText.value,
                          style: Get.theme.textTheme.bodyLarge,
                        ),
                      _buildForgotPasswordButton(),
                      Gap(25.h),
                      _buildLoginButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return CustomImageView(
      imagePath: AssetConstants.pngAppLogoWhite,
      height: 65.h,
      alignment: Alignment.center,
    );
  }

  Widget _buildTitle() {
    return AppText(
      AppStrings.T.lbl_welcome_message,
      style: Get.theme.textTheme.bodyMedium?.copyWith(
        fontSize: 20.sp,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSubtitle() {
    return AppText(
      AppStrings.T.lbl_sign_in_to_continue,
      style: Get.theme.textTheme.bodyMedium?.copyWith(
        fontSize: 14.sp,
        fontWeight: FontWeight.w600,
        color: Get.theme.customColors.greyTextColor,
      ),
    );
  }

  Widget _buildLoginForm() {
    return Column(
      children: [
        TextInputField(
          type: InputType.email,
          hintLabel: AppStrings.T.emailLabel,
          controller: controller.emailController,
          keyboardType: TextInputType.emailAddress,
          autoFillHints: [AutofillHints.email],
          prefixIcon: SizedBox(
            width: 40.0.w,
            child: CustomImageView(
              imagePath: AssetConstants.svgEmail,
              margin: EdgeInsets.symmetric(vertical: 16.h),
            ),
          ),
          validator: AppValidations.emailValidation,
        ),
        Gap(20.h),
        Obx(
          () => TextInputField(
            type: InputType.password,
            hintLabel: AppStrings.T.passwordLabel,
            controller: controller.passwordController,
            obscureText: controller.passObscure,
            prefixIcon: SizedBox(
              width: 40.0.w,
              child: CustomImageView(
                imagePath: AssetConstants.svgPassword,
                margin: EdgeInsets.symmetric(vertical: 16.h),
              ),
            ),
            suffixIcon: IconButton(
              icon: CustomImageView(
                imagePath:
                    controller.passObscure.value
                        ? AssetConstants.svgCloseEye
                        : AssetConstants.svgEye,
                color: Get.theme.customColors.black,
              ),
              onPressed:
                  () =>
                      controller.passObscure.value =
                          !controller.passObscure.value,
            ),
            validator: AppValidations.passwordValidation,
          ),
        ),
      ],
    );
  }

  Widget _buildForgotPasswordButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap:
              () => NavigationService.navigateWithSlideAnimation(
                AppRoutes.forgotPasswordPage,
              ),
          child: AppText(
            AppStrings.T.forgotPassword,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: Get.theme.customColors.primaryColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLoginButtons() {
    return Column(
      children: [
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            isLoading: controller.isLoadingLogin.value,
            text: AppStrings.T.login,
            onPressed: () {
              if (controller.isLoadingLogin.value) {
                return;
              }
              if (_formKey.currentState?.validate() ?? false) {
                Get.focusScope!.unfocus();
                controller.signIn();
              }
            },
          ),
        ),
        Gap(20.h),
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            isLoading: controller.isLoadingGoogleLogin.value,
            text: AppStrings.T.lbl_continue_with_google,
            secondary: true,
            buttonStyle: ButtonThemeHelper.secondaryButtonStyle(Get.context!),
            leftIcon: CustomImageView(
              imagePath: AssetConstants.icGoogle,
              margin: EdgeInsets.all(16.0),
            ),

            onPressed: () {
              if (controller.isLoadingGoogleLogin.value) {
                return;
              }
              controller.signInWithGoogle();
            },
          ),
        ),

        Gap(32.h),
        Row(
          children: [
            Expanded(
              child: Divider(height: 1, color: Get.theme.customColors.black),
            ),
            Gap(10.w),
            AppText(
              AppStrings.T.lbl_or,
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                fontSize: 14.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            Gap(10.w),
            Expanded(
              child: Divider(height: 1, color: Get.theme.customColors.black),
            ),
          ],
        ),
        Gap(32.h),
        Row(
          children: [
            RichText(
              text: TextSpan(
                text: AppStrings.T.lbl_not_register,
                style: Get.theme.textTheme.bodyMedium?.copyWith(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Get.theme.customColors.greyTextColor,
                ),
                children: [
                  TextSpan(
                    recognizer:
                        TapGestureRecognizer()
                          ..onTap =
                              () =>
                                  NavigationService.navigateWithSlideAnimation(
                                    AppRoutes.register,
                                  ),
                    text: " ${AppStrings.T.lbl_create_account}",
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Get.theme.customColors.primaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  @override
  void onInit() {}

  @override
  void onDispose() {}

  @override
  bool get canDisposeController => false;
}
