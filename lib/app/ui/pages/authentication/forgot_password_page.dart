import 'package:get/get.dart';
import 'package:v_card/app/controllers/auth_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ForgotPasswordPage extends GetItHook<AuthController> {
  ForgotPasswordPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Obx(
        () => IgnorePointer(
          ignoring: controller.isLoadingForgotPassword.value,
          child: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 38.h),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildLogo(),
                      <PERSON>(60.h),
                      _buildTitle(),
                      Gap(8.h),
                      _buildSubtitle(),
                      Gap(60.h),
                      _buildForgotPasswordForm(),
                      Gap(80.h),
                      _buildButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return CustomImageView(
      imagePath: AssetConstants.pngAppLogoWhite,
      height: 65.h,
      alignment: Alignment.center,
    );
  }

  Widget _buildTitle() {
    return AppText(
      AppStrings.T.forgotPassword,
      style: Get.theme.textTheme.headlineLarge?.copyWith(
        fontSize: 24.sp,

        fontWeight: FontWeight.w700,
        color: Get.theme.customColors.black,
      ),
    );
  }

  Widget _buildSubtitle() {
    return AppText(
      AppStrings.T.lbl_forgot_password_subtitle,
      style: Get.theme.textTheme.bodyMedium?.copyWith(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: Get.theme.customColors.greyTextColor,
      ),
    );
  }

  Widget _buildForgotPasswordForm() {
    return Column(
      children: [
        TextInputField(
          type: InputType.email,
          hintLabel: AppStrings.T.emailLabel,
          controller: controller.forgotPasswordEmailController,
          keyboardType: TextInputType.emailAddress,
          autoFillHints: [AutofillHints.email],
          prefixIcon: CustomImageView(
            imagePath: AssetConstants.svgEmail,
            margin: EdgeInsets.symmetric(vertical: 16.h),
          ),
          validator: AppValidations.emailValidation,
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Column(
      children: [
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            isLoading: controller.isLoadingForgotPassword.value,
            text: AppStrings.T.sendResetLinkButton,
            onPressed: () {
              if (controller.isLoadingForgotPassword.value) {
                return;
              }
              if (_formKey.currentState?.validate() ?? false) {
                Get.focusScope!.unfocus();
                controller.forgotPassword(_formKey.currentState);
              }
            },
          ),
        ),
      ],
    );
  }

  @override
  void onInit() {}

  @override
  void onDispose() {
    controller.forgotPasswordEmailController.clear();
  }

  @override
  bool get canDisposeController => false;
}
