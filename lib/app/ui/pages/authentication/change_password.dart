import 'package:get/get.dart';
import 'package:v_card/app/controllers/auth_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ChangePasswordPage extends GetItHook<AuthController> {
  ChangePasswordPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.changePassword,
          style: Get.theme.textTheme.headlineLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
            color: Get.theme.customColors.black,
          ),
        ),
      ),
      body: Obx(
        () => IgnorePointer(
          ignoring: controller.changepasswordLoading.value,
          child: Safe<PERSON><PERSON>(
            child: ListView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 38.h),
              children: [
                Form(
                  key: _form<PERSON><PERSON>,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [_buildLoginForm()],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: _buildButtons(context),
    );
  }

  Widget _buildLoginForm() {
    return Column(
      children: [
        Obx(
          () => TextInputField(
            type: InputType.password,
            hintLabel: AppStrings.T.lbl_current_password,
            controller: controller.changeOldPasswordController,
            obscureText: controller.changeOldPassObscure,
            isRequiredField: true,
            label: AppStrings.T.lbl_current_password,
            prefixIcon: SizedBox(
              width: 40.0.w,
              child: CustomImageView(
                imagePath: AssetConstants.svgPassword,
                margin: EdgeInsets.symmetric(vertical: 16.h),
              ),
            ),
            suffixIcon: IconButton(
              icon: CustomImageView(
                imagePath:
                    controller.changeOldPassObscure.value
                        ? AssetConstants.svgCloseEye
                        : AssetConstants.svgEye,
                color: Get.theme.customColors.black,
              ),
              onPressed:
                  () =>
                      controller.changeOldPassObscure.value =
                          !controller.changeOldPassObscure.value,
            ),
            validator: AppValidations.passwordValidation,
          ),
        ),
        Gap(16.h),
        Obx(
          () => TextInputField(
            type: InputType.password,
            hintLabel: AppStrings.T.lbl_new_password,
            controller: controller.changeNewPasswordController,
            obscureText: controller.changeNewPassObscure,
            prefixIcon: SizedBox(
              width: 40.0.w,
              child: CustomImageView(
                imagePath: AssetConstants.svgPassword,
                margin: EdgeInsets.symmetric(vertical: 16.h),
              ),
            ),
            isRequiredField: true,
            label: AppStrings.T.lbl_new_password,
            suffixIcon: IconButton(
              icon: CustomImageView(
                imagePath:
                    controller.changeNewPassObscure.value
                        ? AssetConstants.svgCloseEye
                        : AssetConstants.svgEye,
                color: Get.theme.customColors.black,
              ),
              onPressed:
                  () =>
                      controller.changeNewPassObscure.value =
                          !controller.changeNewPassObscure.value,
            ),
            validator: AppValidations.passwordValidation,
          ),
        ),
        Gap(16.h),
        Obx(
          () => TextInputField(
            type: InputType.password,
            hintLabel: AppStrings.T.lbl_confirm_password,
            controller: controller.changeConfPasswordController,
            obscureText: controller.changeNewConfPassObscure,
            prefixIcon: SizedBox(
              width: 40.0.w,
              child: CustomImageView(
                imagePath: AssetConstants.svgPassword,
                margin: EdgeInsets.symmetric(vertical: 16.h),
              ),
            ),
            isRequiredField: true,
            label: AppStrings.T.lbl_confirm_password,
            suffixIcon: IconButton(
              icon: CustomImageView(
                imagePath:
                    controller.changeNewConfPassObscure.value
                        ? AssetConstants.svgCloseEye
                        : AssetConstants.svgEye,
                color: Get.theme.customColors.black,
              ),
              onPressed:
                  () =>
                      controller.changeNewConfPassObscure.value =
                          !controller.changeNewConfPassObscure.value,
            ),

            validator: AppValidations.passwordValidation,
          ),
        ),
      ],
    );
  }

  Widget _buildButtons(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20.0),
      margin: EdgeInsets.only(bottom: MediaQuery.viewInsetsOf(context).bottom),
      child: CustomElevatedButton(
        checkConnectivity: true,
        isLoading: controller.changepasswordLoading.value,
        text: AppStrings.T.lbl_save,
        onPressed: () {
          if (controller.changepasswordLoading.value) {
            return;
          }
          if (_formKey.currentState?.validate() ?? false) {
            Get.focusScope!.unfocus();
            controller.changePassword(_formKey.currentState);
          }
        },
      ),
    );
  }

  @override
  void onInit() {}

  @override
  void onDispose() {
    controller.changeOldPasswordController.clear();
    controller.changeNewPasswordController.clear();
    controller.changeConfPasswordController.clear();
    controller.changeOldPassObscure.value = true;
    controller.changeNewPassObscure.value = true;
    controller.changeNewConfPassObscure.value = true;
  }

  @override
  bool get canDisposeController => false;
}
