import 'package:get/get.dart';
import 'package:v_card/app/controllers/auth_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ResetPasswordPage extends GetItHook<AuthController> {
  ResetPasswordPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Obx(
        () => IgnorePointer(
          ignoring: controller.resetConfirmPassObscure.value,
          child: SafeArea(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 40.h),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildLogo(),
                      <PERSON>(60.h),
                      _buildTitle(),
                      Gap(8.h),
                      _buildSubtitle(),
                      Gap(80.h),
                      _buildResetPasswordForm(),
                      Gap(60.h),
                      _buildButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return CustomImageView(
      imagePath: AssetConstants.pngAppLogoWhite,
      height: 60.h,
      alignment: Alignment.center,
    );
  }

  Widget _buildTitle() {
    return AppText(
      AppStrings.T.resetPassword,
      style: Get.theme.textTheme.headlineLarge?.copyWith(
        fontSize: 24.sp,
        fontWeight: FontWeight.w700,
        color: Get.theme.customColors.black,
      ),
    );
  }

  Widget _buildSubtitle() {
    return AppText(
      AppStrings.T.lbl_reset_password_subtitle,
      style: Get.theme.textTheme.bodyMedium?.copyWith(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: Get.theme.customColors.greyTextColor,
      ),
    );
  }

  Widget _buildResetPasswordForm() {
    return Column(
      children: [
        Obx(
          () => TextInputField(
            type: InputType.password,
            hintLabel: AppStrings.T.newPasswordLabel,
            controller: controller.resetNewPasswordController,
            obscureText: controller.resetPassObscure,
            prefixIcon: CustomImageView(
              imagePath: AssetConstants.svgPassword,
              margin: EdgeInsets.symmetric(vertical: 16.h),
            ),
            suffixIcon: IconButton(
              icon: CustomImageView(
                imagePath:
                    controller.resetPassObscure.value
                        ? AssetConstants.svgCloseEye
                        : AssetConstants.svgEye,
                color: Get.theme.customColors.black,
              ),

              onPressed:
                  () =>
                      controller.resetPassObscure.value =
                          !controller.resetPassObscure.value,
            ),
            validator: AppValidations.passwordValidation,
          ),
        ),
        Gap(43.h),
        Obx(
          () => TextInputField(
            type: InputType.password,
            hintLabel: AppStrings.T.lbl_conf_password,
            controller: controller.resetConfirmPasswordController,
            obscureText: controller.resetConfirmPassObscure,
            prefixIcon: CustomImageView(
              imagePath: AssetConstants.svgPassword,
              margin: EdgeInsets.symmetric(vertical: 16.h),
            ),
            suffixIcon: IconButton(
              icon: CustomImageView(
                imagePath:
                    controller.resetConfirmPassObscure.value
                        ? AssetConstants.svgCloseEye
                        : AssetConstants.svgEye,
                color: Get.theme.customColors.black,
              ),
              onPressed:
                  () =>
                      controller.resetConfirmPassObscure.value =
                          !controller.resetConfirmPassObscure.value,
            ),
            validator: AppValidations.passwordValidation,
          ),
        ),
      ],
    );
  }

  Widget _buildButtons() {
    return Column(
      children: [
        CustomElevatedButton(
          checkConnectivity: true,
          isLoading: controller.resetConfirmPassObscure.value,
          text: AppStrings.T.lbl_save,
          onPressed: () {
            if (controller.resetConfirmPassObscure.value) {
              return;
            }
            if (_formKey.currentState?.validate() ?? false) {
              Get.focusScope!.unfocus();
              controller.resetPassword(_formKey.currentState);
            }
          },
        ),
      ],
    );
  }

  @override
  void onInit() {}

  @override
  void onDispose() {
    controller.resetNewPasswordController.clear();
    controller.resetConfirmPasswordController.clear();
    controller.resetPassObscure.value = true;
    controller.resetConfirmPassObscure.value = true;
  }

  @override
  bool get canDisposeController => false;
}
