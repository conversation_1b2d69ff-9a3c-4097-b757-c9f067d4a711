import 'package:country_picker/country_picker.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/auth_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/themes/button_theme.dart';

class RegisterPage extends GetItHook<AuthController> {
   RegisterPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Obx(
        () => IgnorePointer(
          ignoring: controller.registerLoading.value,
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(
                    horizontal: 20.w,
                    vertical: 40.h,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildLogo(),
                      Gap(60.h),
                      _buildTitle(),
                      Gap(8.h),
                      _buildSubtitle(),
                      Gap(30.h),
                      _buildRegisterForm(context),
                      Gap(30.h),
                      _buildRegisterButtons(),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogo() {
    return CustomImageView(
      imagePath: AssetConstants.pngAppLogoWhite,
      height: 60.h,
      alignment: Alignment.center,
    );
  }

  Widget _buildTitle() {
    return AppText(
      AppStrings.T.lbl_register_account,
      style: Get.theme.textTheme.headlineLarge?.copyWith(
        fontSize: 24.sp,
        fontWeight: FontWeight.w700,
        color: Get.theme.customColors.black,
      ),
    );
  }

  Widget _buildSubtitle() {
    return AppText(
      AppStrings.T.lbl_register_subtitle,
      style: Get.theme.textTheme.bodyMedium?.copyWith(
        fontSize: 16.sp,
        fontWeight: FontWeight.w400,
        color: Get.theme.customColors.greyTextColor,
      ),
    );
  }

  Widget _buildRegisterForm(BuildContext context) {
    return Column(
      children: [
        _buildTextField(
          hint: AppStrings.T.lbl_first_name,
          isCapitalized: true,
          controller: controller.registerfirstNameController,
          prefixIcon: AssetConstants.icPerson,
          validator:
              (v) => AppValidations.validateRequired(
                v,
                fieldName: AppStrings.T.lbl_first_name,
              ),
        ),
        Gap(16.h),
        _buildTextField(
          hint: AppStrings.T.lbl_last_name,
          isCapitalized: true,
          controller: controller.registerlastNameController,
          prefixIcon: AssetConstants.icPerson,

          validator:
              (v) => AppValidations.validateRequired(
                v,
                fieldName: AppStrings.T.lbl_last_name,
              ),
        ),
        Gap(16.h),
        _buildTextField(
          hint: AppStrings.T.lbl_email_id,
          type: InputType.email,
          controller: controller.registeremailController,
          prefixIcon: AssetConstants.svgEmail,
          keyboardType: TextInputType.emailAddress,
          autoFillHints: [AutofillHints.email],
          validator: AppValidations.emailValidation,
        ),
        Gap(16.h),
        buildPhoneNumberField(context),
        Gap(16.h),
        _buildPasswordInput(
          controller: controller.registerpasswordController,
          obscure: controller.registerpassObscure,
          hint: AppStrings.T.newPasswordLabel,
          validator: AppValidations.passwordValidation,
        ),
        Gap(16.h),
        _buildPasswordInput(
          controller: controller.registerConfirmPasswordController,
          obscure: controller.registerconfirmPassObscure,
          hint: AppStrings.T.confirmPasswordLabel,
          validator:
              (v) => AppValidations.confirmPasswordValidation(
                v,
                controller.registerpasswordController.text,
              ),
        ),
      ],
    );
  }

  Widget buildPhoneNumberField(BuildContext context) {
    return TextInputField(
      type: InputType.phoneNumber,
      hintLabel: AppStrings.T.lbl_contact,
      controller: controller.registerContactController,
      keyboardType: TextInputType.phone,
      validator: (value) => AppValidations.phoneNumberValidation(value),
      boxConstraints: BoxConstraints(),
      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
      prefixIcon: GestureDetector(
        onTap: () {
          showCountryPicker(
            context: context,
            showPhoneCode: true,
            countryListTheme: CountryListThemeData(
              searchTextStyle: Get.theme.textTheme.labelLarge,
              textStyle: Get.theme.textTheme.labelLarge,
              inputDecoration: InputDecoration(
                hintText: AppStrings.T.lbl_search,
                hintStyle: Get.theme.textTheme.bodySmall?.copyWith(
                  fontSize: 14.sp,
                  color: Get.theme.customColors.greyTextColor,
                ),
                border: InputBorder.none,
                prefixIcon: Icon(
                  Icons.search,
                  color: Get.theme.customColors.greyTextColor,
                ),
              ),
            ),
            onSelect: (country) {
              controller.registerCountryCode.value = country.phoneCode;
              controller.registerCountryflag.value = country.flagEmoji;
              controller.update();
            },
          );
        },
        child: Obx(
          () => Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  controller.registerCountryflag.value.isNotEmpty
                      ? controller.registerCountryflag.value
                      : '🇮🇳',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(4.w),
                Text(
                  "+${controller.registerCountryCode.value.isNotEmpty ? controller.registerCountryCode.value : '91'}",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(16.w),
                Text(
                  "|",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.greyTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTextField({
    required String hint,
    required TextEditingController controller,
    InputType type = InputType.text,
    TextInputType? keyboardType,
    String? prefixIcon,
    List<String>? autoFillHints,
    String? Function(String?)? validator,
    bool? isCapitalized,
  }) {
    return TextInputField(
      type: type,
      hintLabel: hint,
      isCapitalized: isCapitalized ?? false,
      controller: controller,
      prefixIcon: CustomImageView(
        imagePath: prefixIcon,
        margin: EdgeInsets.symmetric(vertical: 16.h),
      ),
      keyboardType: keyboardType,
      autoFillHints: autoFillHints,
      validator: validator,
    );
  }

  Widget _buildPasswordInput({
    required TextEditingController controller,
    required RxBool obscure,
    required String hint,
    String? Function(String?)? validator,
  }) {
    return Obx(
      () => TextInputField(
        type: InputType.password,
        hintLabel: hint,
        controller: controller,
        obscureText: obscure.value.obs,
        prefixIcon: CustomImageView(
          imagePath: AssetConstants.svgPassword,
          margin: EdgeInsets.symmetric(vertical: 16.h),
        ),
        suffixIcon: IconButton(
          icon: CustomImageView(
            imagePath:
                obscure.value
                    ? AssetConstants.svgCloseEye
                    : AssetConstants.svgEye,
            color: Get.theme.customColors.black,
          ),
          onPressed: () => obscure.value = !obscure.value,
        ),
        validator: validator,
      ),
    );
  }

  Widget _buildRegisterButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            text: AppStrings.T.register,
            isLoading: controller.registerLoading.value,
            onPressed: () {
              if (controller.registerLoading.value) {
                return;
              }
              if (_formKey.currentState?.validate() ??
                  false) {
                Get.focusScope!.unfocus();
                controller.signUp();
              }
            },
          ),
        ),
        Gap(14.h),
        AppText(
          AppStrings.T.loginRedirect,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontSize: 13.sp,
            fontWeight: FontWeight.w600,
            color: Get.theme.customColors.greyTextColor,
          ),
        ),
        Gap(30.h),
        CustomElevatedButton(
          text: AppStrings.T.login,
          secondary: true,
          buttonStyle: ButtonThemeHelper.secondaryButtonStyle(Get.context!),
          onPressed:
              () =>
                  NavigationService.navigateWithSlideAnimation(AppRoutes.login),
        ),
      ],
    );
  }

  @override
  void onInit() {}

  @override
  void onDispose() {
    controller.registerfirstNameController.clear();
    controller.registerlastNameController.clear();
    controller.registeremailController.clear();
    controller.registerContactController.clear();
    controller.registerpasswordController.clear();
    controller.registerConfirmPasswordController.clear();
    controller.registerpassObscure.value = true;
    controller.registerconfirmPassObscure.value = true;
    controller.registerCountryCode.value = '91';
    controller.registerCountryflag.value = '🇮🇳';
  }

  @override
  bool get canDisposeController => false;
}
