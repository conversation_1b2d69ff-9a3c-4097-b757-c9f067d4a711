import 'package:v_card/app/utils/helpers/exporter.dart';

class AppointmentsShimmer extends StatelessWidget {
  final bool controller;

  const AppointmentsShimmer(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: EdgeInsets.symmetric(
        vertical: controller ? 78.h : 8.h,
        horizontal: 16.w,
      ),
      itemCount: 6, // number of shimmer cards
      separatorBuilder: (_, __) => Gap(8.h),
      itemBuilder: (context, index) {
        return Card(
          child: Container(
            padding: EdgeInsets.all(16.r),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    ShimmerBox(width: 50.w, height: 50.h),
                    Gap(12.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          ShimmerBox(width: 100.w, height: 16.h),
                          Gap(8.h),
                          Shimmer<PERSON>ox(width: 60.w, height: 14.h),
                        ],
                      ),
                    ),
                    ShimmerBox(width: 60.w, height: 20.h),
                  ],
                ),
                Gap(16.h),
                ShimmerBox(width: 200.w, height: 12.h),
              ],
            ),
          ),
        );
      },
    );
  }
}

class ShimmerEnquiryDetailBottomSheet extends StatelessWidget {
  const ShimmerEnquiryDetailBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            ShimmerBox(width: 60.w, height: 60.h),

            Gap(16.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShimmerBox(height: 16.h, width: 120.w),
                  Gap(8.h),
                  ShimmerBox(height: 14.h, width: 100.w),
                ],
              ),
            ),
            ShimmerBox(height: 24.h, width: 60.w),
          ],
        ),
        Gap(24.h),
        for (int i = 0; i < 4; i++) ...[
          ShimmerBox(height: 12.h, width: double.infinity),
          Gap(8.h),
          ShimmerBox(height: 16.h, width: double.infinity),
          Gap(16.h),
        ],
        Gap(24),
        ShimmerBox(height: 45.h, width: double.infinity),
      ],
    );
  }
}
