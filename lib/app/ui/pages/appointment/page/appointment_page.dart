import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:v_card/app/controllers/appointment_controller.dart';
import 'package:v_card/app/data/model/appointment/appointment_model.dart';
import 'package:v_card/app/ui/pages/appointment/widgets/appointment_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class AppointmentPage extends GetItHook<AppointmentController> {
  const AppointmentPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Stack(
        children: [_buildAppointmentListSection(), _buildSearchOverlay()],
      ),
    );
  }

  Widget _buildAppointmentListSection() {
    return Obx(() {
      final hasOriginalData =
          controller.appointmentList.value?.data.isNotEmpty ?? false;

      // Show shimmer only during initial load/refresh with no existing data
      if ((!controller.hasInitialData.value || controller.isLoading.value) &&
          !hasOriginalData) {
        return AppointmentsShimmer(controller.isSearchActive.value);
      }

      // If we have original data, check filtered results
      if (hasOriginalData) {
        return _buildAppointmentList();
      }

      // Show proper empty state based on connection
      return RefreshIndicator(
        onRefresh: () => controller.checkNetworkAndLoad(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: Get.height * 0.7,
            child: Center(
              child:
                  controller.isConnected.value
                      ? NoDataWidget(
                        message: AppStrings.T.lbl_no_appointments_found,
                        padding: EdgeInsets.zero,
                      )
                      : NoInternetWidget(
                        message: AppStrings.T.no_internet_connection,
                      ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildSearchOverlay() {
    return Obx(
      () =>
          controller.isSearchActive.value
              ? Positioned(
                top: 10,
                left: 0,
                right: 0,
                child: Container(
                  color: Get.theme.scaffoldBackgroundColor,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: TextInputField(
                    controller: controller.searchController,
                    focusNode: controller.searchFocusNode,
                    label: AppStrings.T.lbl_search_appointments,
                    onChanged: (value) => controller.searchText.value = value,
                    type: InputType.text,
                    textInputAction: TextInputAction.done,
                    suffixIcon:
                        controller.searchText.value.isNotEmpty
                            ? IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Get.theme.customColors.greyTextColor,
                              ),
                              onPressed: _clearSearch,
                            )
                            : null,
                  ),
                ),
              )
              : const SizedBox.shrink(),
    );
  }

  void _clearSearch() {
    controller.searchController.clear();
    controller.searchText.value = '';
    controller.toggleSearchMode();
  }

  Widget _buildAppointmentList() {
    return RefreshIndicator(
      onRefresh: () async {
        if (controller.isConnected.value) {
          controller.appointmentList.value = null;
          await controller.getAppointmentList();
          controller.applyFilters(
            type: controller.selectedType.value,
            status: controller.selectedStatus.value,
          );
        } else {
          // Get.snackbar(
          //   AppStrings.T.apiError,
          //   AppStrings.T.no_internet_connection,
          //   snackPosition: SnackPosition.BOTTOM,
          // );
        }
      },
      child: Obx(() {
        final filteredAppointments = _getFilteredAppointments();

        return Padding(
          padding: EdgeInsets.only(
            top: controller.isSearchActive.value ? 78.h : 8.h,
          ),
          child:
              filteredAppointments.isEmpty
                  ? _buildEmptyResultsWidget()
                  : ListView.separated(
                    padding: EdgeInsets.only(
                      left: 16.w,
                      right: 16.h,
                      bottom: 16.h,
                    ),
                    itemCount: filteredAppointments.length,
                    separatorBuilder: (_, index) => Gap(8.h),
                    itemBuilder: (context, index) {
                      final appointment = filteredAppointments[index];
                      return _buildAppointmentCard(appointment);
                    },
                  ),
        );
      }),
    );
  }

  Widget _buildEmptyResultsWidget() {
    final args = Get.arguments;
    final isVCardFiltered = args != null && args['vcardName'] != null;
    final vcardName = isVCardFiltered ? args['vcardName'].toString() : '';

    String message;
    if (controller.isSearchActive.value &&
        controller.searchText.value.isNotEmpty) {
      message =
          "${AppStrings.T.lbl_no_results_found_for} '${controller.searchText.value}'";
    } else if (isVCardFiltered) {
      message = "No appointments found for '$vcardName'";
    } else {
      message = AppStrings.T.lbl_no_appointments_found;
    }

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: SizedBox(
        height: Get.height * 0.7,
        child: Center(
          child: NoDataWidget(message: message, padding: EdgeInsets.zero),
        ),
      ),
    );
  }

  List<AppointmentData> _getFilteredAppointments() {
    final allAppointments = controller.appointmentList.value?.data ?? [];
    final reversedAppointments = allAppointments.reversed.toList();

    // First filter by vCard name if provided in arguments
    var filteredByVCard = reversedAppointments;
    final args = Get.arguments;
    if (args != null && args['vcardName'] != null) {
      final vcardName = args['vcardName'].toString();
      filteredByVCard =
          reversedAppointments
              .where((appointment) => appointment.vcardName == vcardName)
              .toList();
    }

    // Then apply search filter if active
    if (!controller.isSearchActive.value) return filteredByVCard;

    final searchLower = controller.searchText.value.trim().toLowerCase();
    return filteredByVCard
        .where(
          (appointment) =>
              (appointment.name?.toLowerCase().contains(searchLower) ??
                  false) ||
              (appointment.vcardName?.toLowerCase().contains(searchLower) ??
                  false) ||
              (appointment.date?.toLowerCase().contains(searchLower) ?? false),
        )
        .toList();
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return CustomAppbar(
      title: AppText(
        AppStrings.T.lbl_appointments,
        style: Get.theme.textTheme.bodyLarge?.copyWith(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      hasLeadingIcon: false,
      actions: [
        IconButton(
          padding: EdgeInsets.zero,
          constraints: BoxConstraints(),
          highlightColor: Colors.transparent,
          icon: Container(
            margin: EdgeInsets.only(left: 8.w),
            height: 40.h,
            width: 40.w,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Get.theme.customColors.textfieldFillColor!,
              ),
            ),
            child: CustomImageView(
              imagePath: AssetConstants.icSearch,
              margin: const EdgeInsets.all(8.0),
            ),
          ),
          onPressed: () {
            controller.toggleSearchMode();
          },
        ),
        IconButton(
          padding: EdgeInsets.zero,
          constraints: BoxConstraints(),
          highlightColor: Colors.transparent,
          icon: Container(
            margin: EdgeInsets.only(left: 8.w),
            height: 40.h,
            width: 40.w,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Get.theme.customColors.textfieldFillColor!,
              ),
            ),
            child: CustomImageView(
              imagePath: AssetConstants.icFilter,
              margin: const EdgeInsets.all(8.0),
            ),
          ),
          onPressed: () {
            showFilterBottomSheet(context, controller);
          },
        ),
      ],
    );
  }

  Widget _buildAppointmentCard(AppointmentData appointment) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.r),
          ),
          color: Get.theme.customColors.white,
          margin: EdgeInsets.symmetric(vertical: 3.h),
          child: InkWell(
            borderRadius: BorderRadius.circular(12.r),
            onTap: () => _showAppointmentDetailBottomSheet(appointment.id ?? 0),
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 50.w,
                        height: 50.h,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Get.theme.customColors.greenColor?.withValues(
                            alpha: 0.1,
                          ),
                        ),
                        child: CustomImageView(
                          imagePath: AssetConstants.icAppointment,
                          margin: EdgeInsets.all(16.0),
                        ),
                      ),
                      Gap(12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              appointment.name ?? AppStrings.T.lbl_no_name,
                              style: Get.textTheme.bodyLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              '${AppStrings.T.lbl_vcard_name}: ${appointment.vcardName}',
                              style: Get.textTheme.labelMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      _buildStatusIndicator(
                        appointment.status ?? 0,
                        appointment.id,
                      ),
                    ],
                  ),
                  Gap(12.h),
                  _buildAppointmentTime(
                    date: appointment.date ?? AppStrings.T.lbl_no_data,
                    fromTime: appointment.fromTime ?? AppStrings.T.lbl_no_time,
                    toTime: appointment.toTime ?? AppStrings.T.lbl_no_time,
                  ),
                  // Add Confirm button for pending appointments
                  if ((appointment.status ?? 0) == 0)
                    Obx(() {
                      final isUpdating =
                          controller.updatingAppointments[appointment.id] ==
                          true;
                      return Padding(
                        padding: EdgeInsets.only(top: 12.h),
                        child: CustomElevatedButton(
                          checkConnectivity: true,
                          text: isUpdating ? '' : AppStrings.T.lbl_completed,
                          onPressed:
                              isUpdating
                                  ? null
                                  : () => _confirmAppointment(appointment),
                          height: 40.h,
                          isLoading:
                              controller.updatingAppointments[appointment.id] ??
                              false,
                        ),
                      );
                    }),
                ],
              ),
            ),
          ),
        ),
        Positioned(
          right: 31.5,
          top: -2,
          child: Container(
            decoration: BoxDecoration(
              color: Get.theme.customColors.primaryColor,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(8.r),
                bottomRight: Radius.circular(8.r),
              ),
            ),
            child: Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 2.h),
                child: Text(
                  (appointment.paidAmount?.isEmpty ?? true)
                      ? AppStrings.T.lbl_free
                      : AppStrings.T.lbl_paid,
                  style: Get.theme.textTheme.labelLarge!.copyWith(
                    color: Get.theme.customColors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void showFilterBottomSheet(
    BuildContext context,
    AppointmentController controller,
  ) {
    // Temporary variables to store selected values
    RxString tempType = controller.selectedType.value.obs;
    RxString tempStatus = controller.selectedStatus.value.obs;

    Get.bottomSheet(
      Container(
        padding: EdgeInsets.symmetric(vertical: 12.h),
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
        ),
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Center(
              child: Container(
                height: 5.h,
                width: 50.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  color: Get.theme.customColors.primaryColor!.withValues(
                    alpha: 0.3,
                  ),
                ),
              ),
            ),
            Gap(10.0.h),
            // Title
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0.w),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppStrings.T.lbl_filter,
                      // 'Filter',
                      style: Get.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        tempType.value = 'All';
                        tempStatus.value = 'All';
                        controller.resetFilters();
                        NavigationService.navigateBack();
                      },
                      child: Text(
                        AppStrings.T.lbl_clear_filter,
                        style: Get.textTheme.labelLarge?.copyWith(
                          color: Get.theme.customColors.primaryColor,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Gap(10.0.h),
            Divider(height: 2.h),

            Gap(10.0.h),

            // Type filter section
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0.w),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  AppStrings.T.lbl_select_type,
                  style: Get.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            Gap(10.0.h),

            // Type Chips
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0.w),
              child: Obx(
                () => Row(
                  children: [
                    _buildFilterChip(
                      AppStrings.T.lbl_all,
                      tempType.value == 'All',
                      () {
                        tempType.value = 'All';
                        controller.applyFilters(
                          type: tempType.value,
                          status: tempStatus.value,
                        );
                        NavigationService.navigateBack();
                      },
                    ),
                    Gap(10.0.w),
                    _buildFilterChip(
                      AppStrings.T.lbl_free,
                      tempType.value == 'Free',
                      () {
                        tempType.value = 'Free';
                        controller.applyFilters(
                          type: tempType.value,
                          status: tempStatus.value,
                        );
                        NavigationService.navigateBack();
                      },
                    ),
                    Gap(10.0.w),
                    _buildFilterChip(
                      AppStrings.T.lbl_paid,
                      tempType.value == 'Paid',
                      () {
                        tempType.value = 'Paid';
                        controller.applyFilters(
                          type: tempType.value,
                          status: tempStatus.value,
                        );
                        NavigationService.navigateBack();
                      },
                    ),
                  ],
                ),
              ),
            ),

            Gap(20.0.h),

            // Status filter section
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0.w),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  AppStrings.T.lbl_status,
                  style: Get.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),

            Gap(10.0.h),

            // Status Chips
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.0.w),
              child: Obx(
                () => Row(
                  children: [
                    _buildFilterChip(
                      AppStrings.T.lbl_all,
                      tempStatus.value == 'All',
                      () {
                        tempStatus.value = 'All';
                        controller.applyFilters(
                          type: tempType.value,
                          status: tempStatus.value,
                        );
                        NavigationService.navigateBack();
                      },
                    ),
                    Gap(10.0.w),
                    _buildFilterChip(
                      AppStrings.T.lbl_pending,
                      tempStatus.value == 'Pending',
                      () {
                        tempStatus.value = 'Pending';
                        controller.applyFilters(
                          type: tempType.value,
                          status: tempStatus.value,
                        );
                        NavigationService.navigateBack();
                      },
                    ),
                    Gap(10.0.w),
                    _buildFilterChip(
                      AppStrings.T.lbl_completed,
                      tempStatus.value == 'Completed',
                      () {
                        tempStatus.value = 'Completed';
                        controller.applyFilters(
                          type: tempType.value,
                          status: tempStatus.value,
                        );
                        NavigationService.navigateBack();
                      },
                    ),
                  ],
                ),
              ),
            ),

            Gap(30.h),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 6.h),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? Get.theme.customColors.primaryColor!.withValues(alpha: 0.2)
                  : Get.theme.customColors.white,
          borderRadius: BorderRadius.circular(20.r),
          border: Border.all(
            color:
                isSelected
                    ? (Get.theme.customColors.primaryColor!)
                    : (Get.theme.customColors.greyTextColor!.withValues(
                      alpha: 0.3,
                    )),
            width: 1,
          ),
        ),
        child: Text(
          label,
          style: Get.textTheme.bodyMedium?.copyWith(
            color:
                isSelected
                    ? Get.theme.customColors.primaryColor
                    : Get.theme.customColors.greyTextColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  void _confirmAppointment(AppointmentData appointment) {
    if (!controller.isConnected.value) {
      // Get.snackbar(
      //   AppStrings.T.apiError,
      //   AppStrings.T.no_internet_connection,
      //   snackPosition: SnackPosition.BOTTOM,
      // );
      return;
    }

    Get.dialog(
      LoadingConfirmationDialog(
        title: AppStrings.T.lbl_confirm_appointment,
        message: AppStrings.T.lbl_confirm_appointment_message,
        onConfirm: () async {
          NavigationService.navigateBack();
          await controller.updateAppointmentById(
            appointmentId: (appointment.id ?? 0).toString(),
          );
        },
        onCancel: () => NavigationService.navigateBack(),
        isLoading: controller.isLoadingAppointmentUpdate,
      ),
    );
  }

  Widget _buildStatusIndicator(int status, int? appointmentId) {
    return Obx(() {
      if (controller.updatingAppointments[appointmentId] == true) {
        return ShimmerBox(width: 90.w, height: 24.h);
      }

      return Container(
        width: 90.w,
        height: 24.h,
        decoration: BoxDecoration(
          color:
              status == 1
                  ? Get.theme.customColors.greenColor?.withValues(alpha: 0.2)
                  : Get.theme.customColors.primaryColor?.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Center(
          child: Text(
            status == 1 ? AppStrings.T.lbl_completed : AppStrings.T.lbl_pending,
            style: Get.textTheme.labelSmall?.copyWith(
              color:
                  status == 1
                      ? Get.theme.customColors.greenColor
                      : Get.theme.customColors.primaryColor,
            ),
          ),
        ),
      );
    });
  }

  Widget _buildAppointmentTime({
    required String date,
    required String fromTime,
    required String toTime,
  }) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 16,
          color: Get.theme.customColors.greyTextColor,
        ),
        Gap(8.w),
        Text(
          '${_formatDate(date)} • ${_formatTime(fromTime)} - ${_formatTime(toTime)}',
          style: Get.textTheme.bodySmall?.copyWith(
            color: Get.theme.customColors.greyTextColor,
          ),
        ),
      ],
    );
  }

  String _formatDate(String date) {
    try {
      final parsedDate = DateTime.parse(date);
      return DateFormat('dd MMM, y').format(parsedDate);
    } catch (e) {
      return date;
    }
  }

  String _formatTime(String time) {
    try {
      final timeFormat = DateFormat('h:mm a');
      if (time.toLowerCase().contains('am') ||
          time.toLowerCase().contains('pm')) {
        return time; // Already formatted
      }
      final timeParts = time.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);
      final dateTime = DateTime(0, 0, 0, hour, minute);
      return timeFormat.format(dateTime);
    } catch (e) {
      return time;
    }
  }

  void _showAppointmentDetailBottomSheet(int id) {
    Get.bottomSheet(
      Obx(() => _buildBottomSheetObserver(id)),
      isScrollControlled: true,
    );

    controller.getAppointmentById(id: id);
  }

  Widget _buildBottomSheetObserver(int id) {
    if (controller.isLoadingAppointmentById.value) {
      return _buildBottomSheetContainer(
        child: ShimmerEnquiryDetailBottomSheet(),
      );
    }

    final appointment = controller.appointmentIdData.value?.data.firstOrNull;
    if (appointment == null) {
      return _buildBottomSheetContainer(
        child: Center(child: Text(AppStrings.T.lbl_no_details_available)),
      );
    }

    return _buildBottomSheetContainer(
      child: Column(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 60.w,
                    height: 60.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Get.theme.customColors.primaryColor?.withValues(
                        alpha: 0.1,
                      ),
                    ),
                    child: Icon(
                      Icons.calendar_today,
                      size: 30,
                      color: Get.theme.customColors.primaryColor,
                    ),
                  ),
                  Gap(16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          appointment.name ?? AppStrings.T.lbl_no_name,
                          style: Get.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Gap(4.h),
                        Text(
                          '${AppStrings.T.lbl_vcard_name}: ${appointment.vcardName}',
                          style: Get.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                  _buildStatusIndicator(appointment.status ?? 0, id),
                ],
              ),
              Gap(24.h),
              _buildDetailItem(
                AppStrings.T.lbl_date,
                _formatDate(appointment.date ?? AppStrings.T.lbl_no_date),
              ),
              _buildDetailItem(
                AppStrings.T.lbl_time,
                '${_formatTime(appointment.fromTime ?? AppStrings.T.lbl_no_time)} - ${_formatTime(appointment.toTime ?? 'No time')}',
              ),
              if (appointment.email != null)
                _buildDetailItem(AppStrings.T.lbl_email, appointment.email!),
              if (appointment.phone != null)
                _buildDetailItem(
                  AppStrings.T.phoneNumberLabel,
                  appointment.phone!,
                ),

              if (appointment.paidAmount!.isNotEmpty)
                _buildDetailItem('Paid Amount', appointment.paidAmount!),
              Gap(20.h),
              CustomElevatedButton(
                text: AppStrings.T.lbl_delete,
                onPressed:
                    controller.isConnected.value
                        ? () {
                          NavigationService.navigateBack();
                          _showDeleteConfirmation(id);
                        }
                        : null,
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(int id) {
    Get.dialog(
      LoadingConfirmationDialog(
        title: "Delete Appointment",
        message: "Are you sure you want to delete this appointment?",
        onCancel: Get.back,
        onConfirm: () => controller.deleteAppointmentById(id: id),
        isLoading: controller.isLoadingAppointmentDelete,
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Get.textTheme.labelSmall?.copyWith(
              color: Get.theme.customColors.greyTextColor,
            ),
          ),
          Gap(4.h),
          Text(value, style: Get.textTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildBottomSheetContainer({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                margin: EdgeInsets.only(
                  left: 20.0.w,
                  right: 20.0.w,
                  top: 8.0.h,
                ),
                height: 5.0,
                width: 50.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(100.r),
                  color: Get.theme.customColors.primaryColor?.withValues(
                    alpha: 0.6,
                  ),
                ),
              ),
              Padding(padding: const EdgeInsets.all(20.0), child: child),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void onInit() {
    controller.checkNetworkAndLoad();
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.searchController.clear();
    controller.isSearchActive.value = false;
    controller.searchText.value = '';
    controller.searchFocusNode.unfocus();
  }
}
