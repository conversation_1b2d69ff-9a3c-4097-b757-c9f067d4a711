import 'dart:io';

import 'package:country_picker/country_picker.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/profile_controller.dart';
import 'package:v_card/app/ui/pages/profile/widgets/account_setting_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class AccountSettingPage extends GetItHook<ProfileController> {
  AccountSettingPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Get.theme.customColors.white,
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_account_settings,
          style: Get.theme.textTheme.headlineLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
            color: Get.theme.customColors.black,
          ),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const AccountSettingsShimmer();
        }

        final hasProfileData =
            controller.profileData.value != null &&
            controller.profileData.value!.data.isNotEmpty;

        if (!hasProfileData) {
          return Center(child: Text(AppStrings.T.lbl_no_profile_data));
        }

        final profile = controller.profileData.value?.data.first;

        if (profile == null) {
          return Center(child: Text(AppStrings.T.lbl_no_profile_data));
        }

        return IgnorePointer(
          ignoring: controller.isLoadingUpdateProfile.value,
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [_buildProfileFields(context)],
              ),
            ),
          ),
        );
      }),
      bottomNavigationBar: _buildUpdateButton(context),
    );
  }

  Widget _buildProfileFields(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Obx(() {
          return Stack(
            children: [
              GestureDetector(
                onTap: () async {
                  if (controller.isProcessingPWAIcon.value) {
                    Logger.log("Processing is already in progress.");
                    return;
                  } else {
                    Future.delayed(Duration(seconds: 1), () {
                      controller.isProcessingPWAIcon.value = true;
                    });

                    try {
                      final File? image = await PWAIconHandler.createPWAIcon(
                        context: context,
                        source: ImageSource.gallery,
                        size: 512,
                        maxKB: 50,
                        format: 'png',
                      );

                      if (image != null) {
                        controller.profileImageFile.value = File(image.path);
                      }
                    } catch (e) {
                      print('Error creating PWA icon: $e');
                    } finally {
                      controller.isProcessingPWAIcon.value = false;
                    }
                  }
                },
                child: Container(
                  height: 130.h,
                  width: 130.h,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Get.theme.customColors.primaryColor!,
                    ),
                    borderRadius: BorderRadius.circular(100.r),
                  ),
                  alignment: Alignment.center,
                  child: Container(
                    height: 126.h,
                    width: 126.h,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Get.theme.customColors.primaryColor!,
                      ),
                      borderRadius: BorderRadius.circular(100.r),
                    ),
                    child:
                        controller.isProcessingPWAIcon.value
                            ? ShimmerBox(
                              width: 126.h,
                              height: 126.h,
                              borderRadius: BorderRadius.circular(100.r),
                            )
                            : CustomImageView(
                              margin: EdgeInsets.all(2.0),
                              radius: BorderRadius.circular(100.r),
                              fit: BoxFit.cover,
                              imagePath:
                                  controller.profileImageFile.value?.path ??
                                  controller
                                      .profileData
                                      .value
                                      ?.data
                                      .first
                                      .profileImage,
                            ),
                  ),
                ),
              ),
              Positioned(
                right: 0,
                bottom: 0,
                child: GestureDetector(
                  onTap: () async {
                    if (controller.isProcessingPWAIcon.value) {
                      Logger.log("Processing is already in progress.");
                      return;
                    } else {
                      Future.delayed(Duration(seconds: 1), () {
                        controller.isProcessingPWAIcon.value = true;
                      });

                      try {
                        final File? image = await PWAIconHandler.createPWAIcon(
                          context: context,
                          source: ImageSource.gallery,
                          size: 512,
                          maxKB: 50,
                          format: 'png',
                        );

                        if (image != null) {
                          controller.profileImageFile.value = File(image.path);
                        }
                      } catch (e) {
                        print('Error creating PWA icon: $e');
                      } finally {
                        controller.isProcessingPWAIcon.value = false;
                      }
                    }
                  },
                  child: CircleAvatar(
                    radius: 20.r,
                    backgroundColor: Get.theme.customColors.primaryColor,
                    child: CustomImageView(imagePath: AssetConstants.icCamera),
                  ),
                ),
              ),
            ],
          );
        }),
        Gap(40.w),
        TextInputField(
          isCapitalized: true,
          type: InputType.text,
          hintLabel: AppStrings.T.lbl_first_name,
          controller: controller.firstNameController,
          validator:
              (value) =>
                  value!.isEmpty
                      ? AppStrings.T.lbl_enter_your_first_name
                      : null,
          prefixIcon: SizedBox(
            width: 40.0.w,
            child: CustomImageView(
              imagePath: AssetConstants.icPerson,
              margin: EdgeInsets.symmetric(vertical: 16.h),
              height: 18.0,
            ),
          ),
        ),
        Gap(16.h),
        TextInputField(
          isCapitalized: true,
          type: InputType.text,
          hintLabel: AppStrings.T.lbl_last_name,
          controller: controller.lastNameController,
          validator:
              (value) =>
                  value!.isEmpty ? AppStrings.T.lbl_enter_your_last_name : null,
          prefixIcon: SizedBox(
            width: 40.0.w,
            child: CustomImageView(
              imagePath: AssetConstants.icPerson,
              margin: EdgeInsets.symmetric(vertical: 16.h),

              height: 18.0,
            ),
          ),
        ),
        Gap(16.h),
        TextInputField(
          type: InputType.email,
          hintLabel: AppStrings.T.lbl_email,
          controller: controller.emailController,
          keyboardType: TextInputType.emailAddress,
          autoFillHints: [AutofillHints.email],
          validator: AppValidations.emailValidation,
          prefixIcon: SizedBox(
            width: 40.0.w,
            child: CustomImageView(
              imagePath: AssetConstants.svgEmail,
              margin: EdgeInsets.symmetric(vertical: 16.h),
              height: 18.0,
            ),
          ),
        ),
        Gap(16.h),
        buildPhoneNumberField(context),
      ],
    );
  }

  Widget buildPhoneNumberField(BuildContext context) {
    return TextInputField(
      type: InputType.phoneNumber,
      hintLabel: AppStrings.T.lbl_contact,
      controller: controller.contactController,
      keyboardType: TextInputType.phone,
      validator: (value) => AppValidations.phoneNumberValidation(value),
      boxConstraints: BoxConstraints(),
      prefixIcon: GestureDetector(
        onTap: () {
          showCountryPicker(
            context: context,
            showPhoneCode: true,
            countryListTheme: CountryListThemeData(
              searchTextStyle: Get.theme.textTheme.labelLarge,
              textStyle: Get.theme.textTheme.labelLarge,
              inputDecoration: InputDecoration(
                hintText: AppStrings.T.lbl_search,
                hintStyle: Get.theme.textTheme.bodySmall?.copyWith(
                  fontSize: 14.sp,
                  color: Get.theme.customColors.greyTextColor,
                ),
                border: InputBorder.none,
                prefixIcon: Icon(
                  Icons.search,
                  color: Get.theme.customColors.greyTextColor,
                ),
              ),
            ),
            onSelect: (country) {
              controller.registerCountryCode.value = country.phoneCode;
              controller.registerCountryflag.value = country.flagEmoji;
              controller.update();
            },
          );
        },
        child: Obx(
          () => Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "+${controller.registerCountryCode.value.isNotEmpty ? controller.registerCountryCode.value : '91'}",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(16.w),
                Text(
                  "|",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.greyTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUpdateButton(BuildContext context) {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(20.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.viewInsetsOf(context).bottom,
        ),
        child: CustomElevatedButton(
          checkConnectivity: true,
          isDisabled: controller.isLoadingUpdateProfile.value,
          isLoading: controller.isLoadingUpdateProfile.value,
          text: AppStrings.T.lbl_save,
          onPressed: () async {
            if (controller.isLoadingUpdateProfile.value ||
                controller.isShowingToast.value) {
              return;
            }

            if (_formKey.currentState?.validate() ?? false) {
              // Check if image is processing
              if (controller.isProcessingPWAIcon.value) {
                _showImageValidationToast(
                  AppStrings.T.lbl_image_processing_message,
                  false,
                );
                return;
              }

              // Check if original profile image is empty AND no new image selected
              final originalImageEmpty =
                  controller
                      .profileData
                      .value
                      ?.data
                      .first
                      .profileImage
                      ?.isEmpty ??
                  true;
              if (originalImageEmpty &&
                  controller.profileImageFile.value == null) {
                _showImageValidationToast(
                  AppStrings.T.lbl_please_select_image,
                  true,
                );
                return;
              }

              controller.isLoadingUpdateProfile.value = true;
              Get.focusScope!.unfocus();

              String? imagePath;
              if (controller.profileImageFile.value?.path != null) {
                // Use newly selected image
                imagePath = controller.profileImageFile.value!.path;
              } else {
                // Use existing image
                final file = await urlToFile(
                  controller.profileData.value?.data.first.profileImage ?? "",
                );
                imagePath = file.path;
              }

              await controller.updateProfile(
                imagePath,
                controller.firstNameController.text.trim(),
                controller.lastNameController.text.trim(),
                controller.emailController.text.trim(),
                controller.contactController.text.trim(),
                controller.registerCountryCode.trim(),
              );
            }
          },
        ),
      ),
    );
  }

  // Widget _buildUpdateButton(BuildContext context) {
  //   return Obx(
  //     () => Container(
  //       padding: const EdgeInsets.all(20.0),
  //       margin: EdgeInsets.only(
  //         bottom: MediaQuery.viewInsetsOf(context).bottom,
  //       ),
  //       child: CustomElevatedButton(
  //         checkConnectivity: true,
  //         isDisabled: controller.isLoadingUpdateProfile.value,
  //         isLoading: controller.isLoadingUpdateProfile.value,
  //         text: AppStrings.T.lbl_save,
  //         onPressed: () async {
  //           if (controller.isLoadingUpdateProfile.value) {
  //             return;
  //           }
  //           if (controller.isShowingToast.value) {
  //             return;
  //           }

  //           if (controller
  //               .profileData
  //               .value!
  //               .data
  //               .first
  //               .profileImage!
  //               .isEmpty) {
  //             if (_formKey.currentState?.validate() ?? false) {
  //               if (controller.isProcessingPWAIcon.value) {
  //                 _showImageValidationToast(
  //                   AppStrings.T.lbl_image_processing_message,
  //                   false,
  //                 );
  //                 return;
  //               } else if (controller.profileImageFile.value == null) {
  //                 _showImageValidationToast(
  //                   AppStrings.T.lbl_please_select_image,
  //                   true,
  //                 );
  //                 return;
  //               }
  //               controller.isLoadingUpdateProfile.value = true;
  //               Get.focusScope!.unfocus();
  //               String? imagePath;
  //               if (controller.profileImageFile.value?.path != null) {
  //                 imagePath = controller.profileImageFile.value!.path;
  //               } else {
  //                 final file = await urlToFile(
  //                   controller.profileData.value?.data.first.profileImage ?? "",
  //                 );
  //                 imagePath = file.path;
  //               }
  //               await controller.updateProfile(
  //                 imagePath,
  //                 controller.firstNameController.text.trim(),
  //                 controller.lastNameController.text.trim(),
  //                 controller.emailController.text.trim(),
  //                 controller.contactController.text.trim(),
  //                 controller.registerCountryCode.trim(),
  //               );
  //             }
  //           }
  //         },
  //       ),
  //     ),
  //   );
  // }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.firstNameController.clear();
    controller.lastNameController.clear();
    controller.emailController.clear();
    controller.contactController.clear();
    controller.registerCountryCode.value = '';
    controller.profileImageFile.value = null;
  }

  @override
  void onInit() {
    controller.getProfileData();
    controller.isProcessingPWAIcon = false.obs;
  }
}
