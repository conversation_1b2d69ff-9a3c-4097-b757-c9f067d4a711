import 'package:get/get.dart';
import 'package:v_card/app/controllers/auth_controller.dart';
import 'package:v_card/app/controllers/profile_controller.dart';
import 'package:v_card/app/ui/pages/profile/widgets/profile_tile.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/helpers/generated/assets.gen.dart';
import 'package:v_card/app/utils/themes/button_theme.dart';

class EditProfilePage extends GetItHook<ProfileController> {
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Get.theme.customColors.white,
      appBar: CustomAppbar(
        appbarBgColor: Get.theme.customColors.white,
        title: AppText(
          AppStrings.T.editProfile,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: ListView(
        physics: BouncingScrollPhysics(),
        padding: const EdgeInsets.all(20.0),
        children: [
          EditProfileTile(
            icon: AssetConstants.icProfile,
            title: AppStrings.T.lbl_account_settings,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.accountSettingPage,
                ),
          ),
          EditProfileTile(
            icon: Assets.images.png.icons.icMessage.path,
            title: AppStrings.T.lbl_manage_subscription,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.manageSubscriptionPage,
                ),
          ),
          EditProfileTile(
            icon: AssetConstants.svglock,
            title: AppStrings.T.changePassword,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.changePasswordPage,
                ),
          ),
          EditProfileTile(
            icon: AssetConstants.icLanguage3,
            title: AppStrings.T.lbl_change_language,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.languagePage,
                ),
          ),
          EditProfileTile(
            icon: AssetConstants.icSetting2,
            title: AppStrings.T.lbl_general_setting,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.generalSettingPage,
                ),
          ),
          EditProfileTile(
            icon: Assets.images.icon.icPayment.path,
            title: AppStrings.T.lbl_payment_configuration,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.paymentConfigPage,
                ),
          ),
          EditProfileTile(
            icon: AssetConstants.icProducts,
            title: AppStrings.T.lbl_product_order,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.productOrderPage,
                ),
          ),
          EditProfileTile(
            icon: Assets.images.icon.icNfcCard.path,
            title: AppStrings.T.lbl_my_nfc_cards,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.nfcCardsPage,
                ),
          ),
          EditProfileTile(
            icon: Assets.images.icon.icAffiliations.path,
            title: AppStrings.T.lbl_affiliations,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.affiliationPage,
                ),
            // isSelected: true,
          ),
          EditProfileTile(
            icon: AssetConstants.icStorage,
            title: AppStrings.T.lbl_storage,
            onTap:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.storagePage,
                ),
          ),
          Gap(16.0),
          CustomElevatedButton(
            text: AppStrings.T.lbl_delete_account,
            secondary: true,
            buttonStyle: ButtonThemeHelper.secondaryButtonStyle(Get.context!),
            onPressed: () {
              final authController = getIt<AuthController>();
              Get.dialog(
                barrierDismissible: false,
                LoadingConfirmationDialog(
                  title: AppStrings.T.lbl_delete_account,
                  message: AppStrings.T.lbl_delete_account_confirmation,
                  onCancel: () => NavigationService.navigateBack(),
                  onConfirm: () => authController.deleteAccount(),
                  isLoading: authController.isLoadingDeleteAcount,
                ),
              );
            },
          ),
          Gap(16.0),
          CustomElevatedButton(
            checkConnectivity: true,
            text: AppStrings.T.logout,
            onPressed: () {
              final authController = getIt<AuthController>();

              Get.dialog(
                barrierDismissible: false,
                LoadingConfirmationDialog(
                  title: AppStrings.T.lbl_are_you_sure,
                  message: AppStrings.T.lbl_logout_confirmation,
                  onCancel: () => NavigationService.navigateBack(),
                  onConfirm: () => authController.logout(),
                  isLoading: authController.isLoadingLogout,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {}
}
