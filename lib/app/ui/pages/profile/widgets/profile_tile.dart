import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class EditProfileTile extends StatelessWidget {
  const EditProfileTile({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
  });

  final String icon;
  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(bottom: 18.h),
        child: Container(
          height: 72.h,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: Get.theme.customColors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: Get.theme.customColors.darkBlueColor
                      ?.withValues(alpha: 0.05),
                  child: Center(
                    child: CustomImageView(
                      imagePath: icon,
                      height: 20.0,
                      width: 20.0,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                Gap(8.w),
                Expanded(
                  child: AppText(
                    title,
                    style: Get.theme.textTheme.bodyLarge!.copyWith(
                      color: Get.theme.customColors.darkGreyTextColor,
                      fontWeight: FontWeight.w500,
                      fontSize: 16.0.sp,
                    ),
                  ),
                ),
                Gap(10.0),
                CustomImageView(imagePath: AssetConstants.icRightArrow),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
