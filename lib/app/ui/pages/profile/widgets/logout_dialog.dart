// // Customized version for logout confirmation
// import 'package:get/get.dart';
// import 'package:v_card/app/controllers/auth_controller.dart';
// import 'package:v_card/app/ui/widgets/custom_button.dart';
// import 'package:v_card/app/utils/helpers/exporter.dart';
// import 'package:v_card/app/utils/themes/button_theme.dart';

// class LogoutDialog extends StatelessWidget {
//   final VoidCallback onConfirm;
//   final VoidCallback onCancel;

//   const LogoutDialog({
//     super.key,
//     required this.onConfirm,
//     required this.onCancel,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final authController = Get.find<AuthController>();

//     return Dialog(
//       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
//       elevation: 0,
//       backgroundColor: Colors.white,
//       child: Container(
//         padding: EdgeInsets.all(20),
//         child: Column(
//           mainAxisSize: MainAxisSize.min,
//           children: [
//             Text(
//               'Are You Sure?',
//               style: Get.textTheme.headlineSmall?.copyWith(
//                 fontWeight: FontWeight.w600,
//                 color: Colors.black,
//               ),
//             ),
//             Gap(12.h),
//             Text(
//               'Are you sure you want to\nlogout from your account?',
//               textAlign: TextAlign.center,
//               style: Get.textTheme.bodyMedium?.copyWith(color: Colors.black54),
//             ),
//             Gap(24.h),
//             Row(
//               children: [
//                 Expanded(
//                   child: CustomElevatedButton(
//                     height: 45.h,
//                     onPressed: onCancel,
//                     secondary: true,
//                     buttonStyle: ButtonThemeHelper.secondaryButtonStyle(
//                       Get.context!,
//                     ),
//                     text: AppStrings.T.lbl_no,
//                   ),
//                 ),
//                 Gap(12.w),
//                 Expanded(
//                   child: Obx(
//                     () => CustomElevatedButton(
//                       height: 45.h,
//                       isLoading: authController.isLoadingLogout.value,
//                       onPressed: onConfirm,
//                       text: AppStrings.T.lbl_yes,
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
