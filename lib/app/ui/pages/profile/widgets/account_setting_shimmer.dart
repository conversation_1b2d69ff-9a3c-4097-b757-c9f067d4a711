import 'package:v_card/app/utils/helpers/exporter.dart';

class AccountSettingsShimmer extends StatelessWidget {
  const AccountSettingsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerBox(
            height: 130.h,
            width: 130.h,
            borderRadius: BorderRadius.circular(100.r),
          ),
          Gap(40.h),
          ...List.generate(4, (_) => _buildShimmerField()),
        ],
      ),
    );
  }

  Widget _buildShimmerField() {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: ShimmerBox(
        height: 50.h,
        width: double.infinity,
        borderRadius: BorderRadius.circular(12.r),
      ),
    );
  }
}
