import 'package:v_card/app/utils/helpers/exporter.dart';

class LanguagePageShimmer extends StatelessWidget {
  const LanguagePageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Column(
        children: [
          Gap(20.h),
          _shimmerBox(height: 50.h, width: double.infinity), // Dropdown
          Spacer(),
          _shimmerButton(),
          Gap(10.h),
          _shimmerButton(),
          Gap(20.h),
        ],
      ),
    );
  }

  Widget _shimmerBox({required double height, required double width}) {
    return ShimmerBox(height: height, width: width);
  }

  Widget _shimmerButton() => _shimmerBox(height: 48.h, width: double.infinity);
}
