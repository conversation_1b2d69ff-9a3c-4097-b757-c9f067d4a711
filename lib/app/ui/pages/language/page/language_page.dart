import 'package:get/get.dart';
import 'package:v_card/app/controllers/language_controller.dart';
import 'package:v_card/app/controllers/profile_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/helpers/generated/assets.gen.dart';

class LanguagePage extends GetItHook<LanguageController> {
  LanguagePage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final Map<String, Map<String, String>> _languageData = {
    'ar': {'name': AppStrings.T.lbl_arabic, 'flag': AssetConstants.icArabic},
    'zh': {'name': AppStrings.T.lbl_chinese, 'flag': AssetConstants.icChinese},
    'en': {'name': AppStrings.T.lbl_english, 'flag': AssetConstants.icEnglish},
    'fr': {'name': AppStrings.T.lbl_french, 'flag': AssetConstants.icFrench},
    'de': {'name': AppStrings.T.lbl_german, 'flag': AssetConstants.icGerman},
    'pt': {
      'name': AppStrings.T.lbl_portuguese,
      'flag': AssetConstants.icPortuguese,
    },
    'ru': {'name': AppStrings.T.lbl_russian, 'flag': AssetConstants.icRussian},
    'es': {'name': AppStrings.T.lbl_spanish, 'flag': AssetConstants.icSpanish},
    'tr': {'name': AppStrings.T.lbl_turkish, 'flag': AssetConstants.icTurkish},
  };

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
        bottomNavigationBar: _buildSaveButton(),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppbar(
      title: AppText(
        AppStrings.T.language,
        style: Get.theme.textTheme.bodyLarge?.copyWith(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildBody() {
    return ListView(
      padding: EdgeInsets.all(20.w),
      children: [_buildLanguageDropdown()],
    );
  }

  Widget _buildLanguageDropdown() {
    return Obx(() {
      final isProfileLoading = getIt<ProfileController>().isLoading.value;
      final selectedCode = controller.selectedLanguageCode.value;
      final languageName =
          selectedCode != null ? (_languageData[selectedCode]?['name']) : null;

      return GestureDetector(
        onTap: () => _showLanguageBottomSheet(),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: Get.theme.customColors.textblcolor!),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (isProfileLoading)
                ShimmerBox(width: 120, height: 18)
              else
                Text(
                  languageName ??
                      getIt<ProfileController>()
                          .profileData
                          .value
                          ?.data
                          .first
                          .language ??
                      AppStrings.T.lbl_select_language,
                  style: Get.textTheme.bodyLarge,
                ),
              CustomImageView(imagePath: Assets.images.icon.icDownArrow.path),
            ],
          ),
        ),
      );
    });
  }

  void _showLanguageBottomSheet() {
    Get.bottomSheet(
      Container(
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
              height: 5.0,
              width: 50.0,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100.r),
                color: Get.theme.customColors.primaryColor?.withValues(
                  alpha: 0.6,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    AppStrings.T.lbl_select_language,
                    style: Get.textTheme.bodyLarge,
                  ),
                  Gap(8.h),
                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _languageData.length,
                    itemBuilder: (context, index) {
                      final languageCode = _languageData.keys.elementAt(index);
                      final language = _languageData[languageCode]!;
                      return ListTile(
                        leading: ClipRRect(
                          borderRadius: BorderRadius.circular(4),
                          child: CustomImageView(
                            imagePath: language['flag'],
                            width: 32.w,
                            height: 24.h,
                            fit: BoxFit.cover,
                          ),
                        ),
                        title: Text(
                          language['name']!,
                          style: Get.theme.textTheme.bodyLarge!.copyWith(
                            color:
                                controller.selectedLanguageCode.value ==
                                        languageCode
                                    ? Get.theme.customColors.primaryColor
                                    : Get.theme.customColors.darkGreyTextColor,
                            fontWeight:
                                controller.selectedLanguageCode.value ==
                                        languageCode
                                    ? FontWeight.bold
                                    : FontWeight.w500,
                          ),
                        ),
                        onTap: () {
                          controller.selectedLanguageCode.value = languageCode;
                          NavigationService.navigateBack();
                        },
                      );
                    },
                    separatorBuilder:
                        (context, index) => Divider(
                          color: Get.theme.customColors.textblcolor!.withValues(
                            alpha: 0.5,
                          ),
                          height: 1.h,
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  Widget _buildSaveButton() {
    return Obx(
      () => Padding(
        padding: const EdgeInsets.all(20.0),
        child: CustomElevatedButton(
          checkConnectivity: true,
          isLoading: controller.isLoading.value,
          text: AppStrings.T.lbl_save,
          onPressed: controller.isLoading.value ? null : _saveLanguageSelection,
        ),
      ),
    );
  }

  void _saveLanguageSelection() async {
    final selectedCode = controller.selectedLanguageCode.value;
    if (selectedCode == null) {
      toastification.show(
        type: ToastificationType.error,
        description: Text(AppStrings.T.lbl_please_select_a_language),
      );
      return;
    }

    try {
      await controller.updateLanguage(selectedCode);
      NavigationService.navigateBack();
    } catch (error) {
      toastification.show(
        type: ToastificationType.error,
        description: Text(AppStrings.T.lbl_failed_to_update_language),
      );
    }
  }

  @override
  void onInit() {
    final currentLanguage = Get.locale?.languageCode.toLowerCase() ?? 'en';
    if (_languageData.containsKey(currentLanguage)) {
      controller.selectedLanguageCode.value = currentLanguage;
    }
    getIt<ProfileController>().getProfileData();
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}
}
