import 'package:v_card/app/utils/helpers/exporter.dart';

class ProductOrderShimmer extends StatelessWidget {
  const ProductOrderShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        itemCount: 5,
        itemBuilder: (context, index) {
          return _buildShimmerOrderItem();
        },
      ),
    );
  }

  Widget _buildShimmerOrderItem() {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with Order ID and Status
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    ShimmerBox(width: 20, height: 20),
                    Gap(8.w),
                    ShimmerBox(width: 100, height: 18),
                  ],
                ),
                ShimmerBox(width: 80, height: 24),
              ],
            ),
          ),

          // Order Details
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product and Customer
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(child: _buildShimmerInfoSection()),
                    Gap(16.w),
                    Expanded(child: _buildShimmerInfoSection()),
                  ],
                ),
                Gap(16.h),
                // Order Date and Amount
                Row(
                  children: [
                    Expanded(child: _buildShimmerInfoSection()),
                    Gap(16.w),
                    Expanded(child: _buildShimmerInfoSection()),
                  ],
                ),
                Gap(16.h),

                // Contact Details Section
                _buildShimmerSelectionContainer(),
                Gap(16.h),

                // Payment Type
                _buildShimmerInfoSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ShimmerBox(
              width: 16,
              height: 16,
              borderRadius: BorderRadius.circular(100),
            ),
            Gap(8.w),
            ShimmerBox(width: 70, height: 12),
          ],
        ),
        Gap(4.h),
        Padding(
          padding: EdgeInsets.only(left: 24),
          child: ShimmerBox(width: double.infinity, height: 16),
        ),
      ],
    );
  }

  Widget _buildShimmerSelectionContainer() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  ShimmerBox(
                    width: 20,
                    height: 20,
                    borderRadius: BorderRadius.circular(100),
                  ),
                  Gap(8.w),
                  ShimmerBox(width: 120, height: 16),
                ],
              ),
              ShimmerBox(
                width: 20,
                height: 20,
                borderRadius: BorderRadius.circular(100),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
