import 'package:get/get.dart';
import 'package:v_card/app/controllers/product_order_controller.dart';
import 'package:v_card/app/data/model/product_order/product_order_model.dart';
import 'package:v_card/app/ui/pages/product_order/widgets/product_order_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ProductOrderPage extends GetItHook<ProductOrderController> {
  const ProductOrderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_product_order,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          await controller.getProductOrderList();
        },
        child: Obx(() {
          if (controller.isLoading.value) {
            return ProductOrderShimmer();
          }

          final orderData = controller.enquiriesList.value?.data ?? [];

          if (orderData.isEmpty) {
            return _buildEmptyState();
          }

          return ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            itemCount: orderData.length,
            itemBuilder: (context, index) {
              final order = orderData[index];
              return _buildOrderCard(order, index);
            },
          );
        }),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_bag_outlined,
            size: 80,
            color: Get.theme.customColors.darkGreyTextColor,
          ),
          Gap(20.h),
          Text(
            "No Orders Found",
            style: Get.theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Get.theme.customColors.darkGreyTextColor,
            ),
          ),
          Gap(12.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              "You don't have any product orders yet.",
              textAlign: TextAlign.center,
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(ProductOrder order, int index) {
    return Container(
      clipBehavior: Clip.hardEdge,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Get.theme.customColors.black!.withValues(alpha: 0.2),
            blurRadius: 5,
            offset: Offset(0, 0),
          ),
        ],
      ),
      margin: EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with Order ID and Status
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Get.theme.colorScheme.primaryContainer.withValues(
                alpha: 0.3,
              ),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.shopping_cart,
                      size: 20,
                      color: Get.theme.customColors.darkGreyTextColor,
                    ),
                    Gap(8.w),
                    Text(
                      "Order #${order.id}",
                      style: Get.theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                _buildStatusChip(order.status ?? 0),
              ],
            ),
          ),

          // Order Details
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product and Customer
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: _buildInfoSection(
                        title: "Product",
                        content: order.productName ?? "Not Available",
                        icon: Icons.inventory_2_outlined,
                      ),
                    ),
                    Gap(16.w),
                    Expanded(
                      child: _buildInfoSection(
                        title: "Customer",
                        content: order.name ?? "Not Available",
                        icon: Icons.person_outline,
                      ),
                    ),
                  ],
                ),
                Gap(16.h),
                // Order Date and Amount
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoSection(
                        title: "Order Date",
                        content: order.orderAt ?? "Not Available",
                        icon: Icons.calendar_today_rounded,
                      ),
                    ),
                    Gap(16.w),
                    Expanded(
                      child: _buildInfoSection(
                        title: "Amount",
                        content: order.amount ?? "Not Available",
                        icon: Icons.attach_money_rounded,
                      ),
                    ),
                  ],
                ),
                Gap(16.h),

                // Contact Details Section
                _buildSelectionContainer(
                  "Contact Details",
                  Icons.contact_phone_outlined,
                  _buildContactDetails(order),
                  index,
                ),
                Gap(16.h),

                // Payment Type
                _buildInfoSection(
                  title: "Payment Method",
                  content: _getPaymentTypeString(order.paymentType ?? 0),
                  icon: Icons.payment_rounded,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionContainer(
    String title,
    IconData icon,
    Widget options,
    int index,
  ) {
    return Obx(() {
      final isExpanded = controller.isExpanded(index);
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main selection container
          GestureDetector(
            onTap: () => controller.toggleExpansion(index),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(
                  color: Get.theme.colorScheme.primary.withValues(alpha: 0.3),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        icon,
                        size: 20,
                        color: Get.theme.customColors.darkGreyTextColor,
                      ),
                      Gap(8.w),
                      Text(
                        title,
                        style: Get.theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: Get.theme.customColors.darkGreyTextColor,
                  ),
                ],
              ),
            ),
          ),

          // Dropdown options
          if (isExpanded) ...[
            Gap(8.h),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Get.theme.customColors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: options,
            ),
          ],
        ],
      );
    });
  }

  Widget _buildStatusChip(int status) {
    String label;
    Color color;
    IconData icon;

    switch (status) {
      case 0:
        label = "Approved";
        color = Colors.green;
        icon = Icons.check_circle_outline;
        break;
      case 1:
        label = "Pending";
        color = Colors.orange;
        icon = Icons.pending_outlined;
        break;
      case 2:
        label = "Reject";
        color = Colors.red;
        icon = Icons.cancel_outlined;
        break;
      default:
        label = "Pending";
        color = Colors.blue;
        icon = Icons.sync_outlined;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.15),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          Gap(4.w),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactDetails(ProductOrder order) {
    return Column(
      children: [
        _buildContactDetail(
          title: "Email",
          content: order.email ?? "Not Available",
          icon: Icons.email_outlined,
        ),
        Gap(8.h),
        _buildContactDetail(
          title: "Phone",
          content: order.phone ?? "Not Available",
          icon: Icons.phone_outlined,
        ),
        Gap(8.h),
        _buildContactDetail(
          title: "Address",
          content: order.address ?? "Not Available",
          icon: Icons.location_on_outlined,
        ),
      ],
    );
  }

  Widget _buildInfoSection({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: Get.theme.customColors.darkGreyTextColor,
            ),
            Gap(8.w),
            Text(
              title,
              style: Get.theme.textTheme.bodySmall?.copyWith(
                color: Get.theme.customColors.darkGreyTextColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        Gap(4.h),
        Padding(
          padding: EdgeInsets.only(left: 24),
          child: Text(
            content,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: Get.theme.customColors.darkGreyTextColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildContactDetail({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Padding(
      padding: EdgeInsets.only(left: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: Get.theme.customColors.darkGreyTextColor),
          Gap(12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Get.theme.textTheme.bodySmall?.copyWith(
                    color: Get.theme.customColors.darkGreyTextColor,
                  ),
                ),
                Text(
                  content,
                  style: Get.theme.textTheme.bodyMedium,
                  overflow: TextOverflow.ellipsis,
                  maxLines: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getPaymentTypeString(int paymentType) {
    switch (paymentType) {
      case 1:
        return "Stripe";
      case 2:
        return "PayPal";
      case 3:
        return "Manual Payment";
      case 4:
        return "PhonePe";
      case 5:
        return "Paystack";
      case 6:
        return "Razorpay";
      case 7:
        return "Flutterwave";
      case 9:
        return "MercadoPago";
      default:
        return "Other";
    }
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getProductOrderList();
  }
}
