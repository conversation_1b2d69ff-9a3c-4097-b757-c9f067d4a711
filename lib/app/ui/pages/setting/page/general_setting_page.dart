import 'dart:io';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/product_controller.dart';
import 'package:v_card/app/controllers/setting_controller.dart';
import 'package:v_card/app/ui/pages/setting/widget/update_setting_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class GeneralSettingPage extends GetItHook<SettingsController> {
  const GeneralSettingPage({super.key});

  @override
  void onInit() {
    getIt<ProductController>().getCurrencyList();
    controller.fetchGeneralSettings();
    controller.isProcessingPWAIcon = false.obs;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_general_setting,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Obx(() {
        if (controller.isLoadingGeneralSettings.value) {
          return const Center(child: UpdateSettingFormShimmer());
        }

        return IgnorePointer(
          ignoring: controller.isUpdatingGeneralSettings.value,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Paypal Email
                TextInputField(
                  controller: controller.paypalEmailController,
                  label: AppStrings.T.lbl_paypal_payout_email,
                  // label: 'Paypal Payout Email:',
                  validator: (value) => AppValidations.emailValidation(value),
                  type: InputType.text,
                ),
                Gap(16.h),

                // Currency Dropdown
                TextInputField(
                  type: InputType.text,
                  readOnly: true,
                  isRequiredField: true,
                  controller: controller.currencyIdController,
                  label: AppStrings.T.lbl_currency,
                  suffixIcon: CustomImageView(
                    imagePath: AssetConstants.icDownArrow2,
                    margin: EdgeInsets.all(8.0),
                  ),
                  onTap: () {
                    final searchText = ''.obs;
                    final searchController = TextEditingController();
                    final currencyMap =
                        getIt<ProductController>().currencyList.value?.data ??
                        {};

                    Get.bottomSheet(
                      Container(
                        height: Get.height * 0.45,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(20),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Container(
                              margin: EdgeInsets.only(
                                left: 20.0.w,
                                right: 20.0.w,
                                top: 8.0.h,
                                bottom: 8.0.h,
                              ),
                              height: 5.0,
                              width: 50.0,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(100.r),
                                color: Get.theme.customColors.primaryColor
                                    ?.withValues(alpha: 0.6),
                              ),
                            ),
                            Obx(
                              () => Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16.0,
                                ),
                                child: TextInputField(
                                  onChanged:
                                      (value) => searchText.value = value,
                                  hintLabel: AppStrings.T.hint_search_currency,
                                  controller: searchController,
                                  prefixIcon: CustomImageView(
                                    imagePath: AssetConstants.icSearch,
                                  ),
                                  type: InputType.text,
                                  suffixIcon:
                                      searchText.value.isNotEmpty
                                          ? IconButton(
                                            icon: Icon(
                                              Icons.close,
                                              color:
                                                  Get
                                                      .theme
                                                      .customColors
                                                      .greyTextColor,
                                            ),
                                            onPressed: () {
                                              searchController.clear();
                                              searchText.value = '';
                                            },
                                          )
                                          : null,
                                ),
                              ),
                            ),
                            Gap(8.h),
                            // Currency List
                            Expanded(
                              child: Obx(() {
                                final query = searchText.value.toLowerCase();
                                final filteredList =
                                    currencyMap.entries
                                        .where(
                                          (e) => e.value.toLowerCase().contains(
                                            query,
                                          ),
                                        )
                                        .toList();

                                return ListView.separated(
                                  itemCount: filteredList.length,
                                  separatorBuilder: (_, __) => Divider(),
                                  itemBuilder: (_, index) {
                                    final entry = filteredList[index];
                                    return ListTile(
                                      title: Text(
                                        entry.value,
                                        style: Get.theme.textTheme.bodySmall,
                                      ),
                                      onTap: () {
                                        controller.selectedCurrencyId.value =
                                            entry.key;
                                        controller.selectedCurrencyName.value =
                                            entry.value;
                                        controller.currencyIdController.text =
                                            entry.value;
                                        NavigationService.navigateBack();
                                      },
                                    );
                                  },
                                );
                              }),
                            ),
                          ],
                        ),
                      ),
                      isScrollControlled: true,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(20),
                        ),
                      ),
                    );
                  },
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_currency,
                      ),
                ),
                Gap(16.h),

                // Newsletter Modal Time
                TextInputField(
                  controller: controller.subscriptionModalTimeController,
                  label: AppStrings.T.lbl_newsletter_modal_time,
                  // 'Newsletter Modal will open in X seconds after page load:',
                  keyboardType: TextInputType.number,
                  type: InputType.text,
                ),
                Gap(16.h),

                // Time Format
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppStrings.T.lbl_time_format,
                      style: Get.theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Gap(10.h),
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              controller.timeFormat24Hour.value = false;
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                color:
                                    !controller.timeFormat24Hour.value
                                        ? Get.theme.customColors.primaryColor
                                        : Get.theme.customColors.white,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                AppStrings.T.lbl_12_hour,
                                style: Get.theme.textTheme.bodyMedium?.copyWith(
                                  color:
                                      !controller.timeFormat24Hour.value
                                          ? Get.theme.customColors.white
                                          : Get.theme.customColors.primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ),
                        Gap(16.h),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              controller.timeFormat24Hour.value = true;
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(vertical: 12),
                              decoration: BoxDecoration(
                                color:
                                    controller.timeFormat24Hour.value
                                        ? Get.theme.customColors.primaryColor
                                        : Get.theme.customColors.white,
                                borderRadius: BorderRadius.circular(8.r),
                                border: Border.all(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                AppStrings.T.lbl_24_hour,
                                style: Get.theme.textTheme.bodyMedium?.copyWith(
                                  color:
                                      controller.timeFormat24Hour.value
                                          ? Get.theme.customColors.white
                                          : Get.theme.customColors.primaryColor,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Gap(24.h),

                // Enable PWA Toggle
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppStrings.T.lbl_enable_pwa,
                      style: Get.theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Obx(
                      () => Transform.scale(
                        scale: 0.8,
                        alignment: Alignment.centerRight,
                        child: Switch(
                          value: controller.enablePWA.value,
                          activeColor: Get.theme.customColors.white,
                          inactiveThumbColor: Get.theme.customColors.darkColor,
                          inactiveTrackColor: Get.theme.customColors.white,
                          activeTrackColor: Get.theme.customColors.primaryColor,
                          onChanged: (value) {
                            controller.enablePWA.value = value;
                          },
                        ),
                      ),
                    ),
                  ],
                ),

                // PWA Icon
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Text(
                          AppStrings.T.lbl_pwa_icon,
                          style: Get.theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    Obx(() {
                      return GestureDetector(
                        onTap:
                            controller.isProcessingPWAIcon.value
                                ? null
                                : () async {
                                  Future.delayed(Duration(seconds: 1), () {
                                    controller.isProcessingPWAIcon.value = true;
                                  });

                                  try {
                                    final File?
                                    iconFile = await PWAIconHandler.createPWAIcon(
                                      context: context,
                                      source: ImageSource.gallery,
                                      size: 512, // Match your example file size
                                      maxKB: 50,
                                      format:
                                          'png', // Use PNG since your example was PNG
                                    );

                                    if (iconFile != null) {
                                      controller.profileImageFile.value =
                                          iconFile;
                                      // Now the icon is ready for upload
                                    }
                                  } catch (e) {
                                    print('Error creating PWA icon: $e');
                                    // Show error toast or message here if needed
                                  } finally {
                                    // Set processing flag to false when done
                                    controller.isProcessingPWAIcon.value =
                                        false;
                                  }
                                },
                        child: Container(
                          height: 100.h,
                          width: 100.w,
                          decoration: BoxDecoration(
                            color: Get.theme.customColors.white,
                            borderRadius: BorderRadius.circular(10.r),
                            border: Border.all(
                              color: Get.theme.customColors.primaryColor!,
                            ),
                          ),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              if (controller.isProcessingPWAIcon.value)
                                // Show loading indicator
                                ShimmerBox(height: 90.h, width: 90.w)
                              else
                                // Show image when not loading
                                CustomImageView(
                                  height: 90.h,
                                  width: 90.w,
                                  fit: BoxFit.cover,
                                  radius: BorderRadius.circular(8.r),
                                  imagePath:
                                      controller.profileImageFile.value?.path ??
                                      controller
                                          .generalSettings
                                          .value
                                          ?.data
                                          .first
                                          .pwaIcon,
                                ),

                              // Edit icon - only show when not processing
                              if (!controller.isProcessingPWAIcon.value)
                                Positioned(
                                  top: 5,
                                  right: 5,
                                  child: Container(
                                    padding: EdgeInsets.all(4),
                                    decoration: BoxDecoration(
                                      color: Get.theme.customColors.white,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .greyTextColor!,
                                      ),
                                    ),
                                    child: Icon(
                                      Icons.edit,
                                      size: 12,
                                      color:
                                          Get.theme.customColors.primaryColor,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      );
                    }),
                  ],
                ),
                Gap(24.h),

                // Enable Attachment for Inquiry Toggle
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      AppStrings.T.lbl_enable_attachment_inquiry,
                      maxLines: 2,
                      style: Get.theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Obx(
                      () => Transform.scale(
                        scale: 0.8,
                        alignment: Alignment.centerRight,
                        child: Switch(
                          value: controller.enableAttachmentForInquiry.value,
                          activeColor: Get.theme.customColors.white,
                          inactiveThumbColor: Get.theme.customColors.darkColor,
                          inactiveTrackColor: Get.theme.customColors.white,
                          activeTrackColor: Get.theme.customColors.primaryColor,
                          onChanged: (value) {
                            controller.enableAttachmentForInquiry.value = value;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                Gap(16.h),

                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        AppStrings.T.lbl_ask_details_download,
                        maxLines: 2,
                        style: Get.theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Obx(
                      () => Transform.scale(
                        scale: 0.8,
                        alignment: Alignment.centerRight,
                        child: Switch(
                          value:
                              controller
                                  .askDetailsBeforeDownloadingContact
                                  .value,
                          activeColor: Get.theme.customColors.white,
                          inactiveThumbColor: Get.theme.customColors.darkColor,
                          inactiveTrackColor: Get.theme.customColors.white,
                          activeTrackColor: Get.theme.customColors.primaryColor,
                          onChanged: (value) {
                            controller
                                .askDetailsBeforeDownloadingContact
                                .value = value;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                Gap(32.h),

                // Save Button - Disable when processing PWA icon
                CustomElevatedButton(
                  checkConnectivity: true,
                  isLoading: controller.isUpdatingGeneralSettings.value,
                  text: AppStrings.T.lbl_save,
                  onPressed:
                      controller.isUpdatingGeneralSettings.value
                          ? null
                          : () async {
                            if (controller.isUpdatingGeneralSettings.value) {
                              return;
                            }

                            controller.isUpdatingGeneralSettings.value = true;

                            try {
                              if (controller
                                  .generalSettings
                                  .value!
                                  .data
                                  .first
                                  .pwaIcon
                                  .isEmpty) {
                                if (controller.isShowingToast.value) {
                                  return;
                                }
                                if (controller.isProcessingPWAIcon.value) {
                                  _showImageValidationToast(
                                    AppStrings.T.lbl_image_processing_message,
                                    false,
                                  );
                                  return;
                                } else if (controller.profileImageFile.value ==
                                    null) {
                                  _showImageValidationToast(
                                    AppStrings.T.lbl_please_select_image,
                                    true,
                                  );
                                  return;
                                }
                                await controller.updateGeneralSettings();
                              } else {
                                if (controller.isShowingToast.value) {
                                  return;
                                }
                                String? imagePath;
                                if (controller.profileImageFile.value?.path !=
                                    null) {
                                  imagePath =
                                      controller.profileImageFile.value!.path;
                                } else {
                                  final file = await urlToFile(
                                    controller
                                        .generalSettings
                                        .value!
                                        .data
                                        .first
                                        .pwaIcon,
                                  );
                                  imagePath = file.path;
                                }
                                await controller.updateGeneralSettings(
                                  pwaIcon: imagePath,
                                );
                              }
                            } finally {
                              controller.isUpdatingGeneralSettings.value =
                                  false;
                            }
                          },
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.paypalEmailController.clear();
    controller.currencyIdController.clear();
    controller.subscriptionModalTimeController.clear();
    controller.selectedCurrencyId.value = '0';
    controller.selectedCurrencyName.value = '';
    controller.profileImageFile.value = null;
    controller.isProcessingPWAIcon.value = false;
    controller.enableAttachmentForInquiry.value = false;
    controller.askDetailsBeforeDownloadingContact.value = false;
  }
}
