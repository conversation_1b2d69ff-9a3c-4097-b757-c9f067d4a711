import 'package:get/get.dart';
import 'package:v_card/app/controllers/setting_controller.dart';
import 'package:v_card/app/ui/pages/setting/widget/payment_config_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class PaymentConfigPage extends GetItHook<SettingsController> {
  PaymentConfigPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    controller.fetchPaymentConfig();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_payment_configuration,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Obx(() {
        if (controller.isLoadingPaymentConfig.value) {
          return PaymentConfigShimmer();
        }

        return IgnorePointer(
          ignoring: controller.isUpdatingPaymentConfig.value,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppStrings.T.lbl_payment_method,
                    style: Get.theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Gap(24.h),
                  // Stripe
                  _buildPaymentMethodToggle(
                    title: AppStrings.T.lbl_stripe,
                    isEnabled: controller.stripeEnabled.value,
                    onChanged: (value) {
                      controller.stripeEnabled.value = value;
                    },
                  ),
                  if (controller.stripeEnabled.value) ...[
                    Gap(8.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.stripeKeyController,
                      label: AppStrings.T.lbl_stripe_key,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_stripe_key,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.stripeSecretController,
                      label: AppStrings.T.lbl_stripe_secret,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_stripe_secret,
                          ),
                    ),
                  ],

                  // Paystack
                  Gap(16.h),
                  _buildPaymentMethodToggle(
                    title: AppStrings.T.lbl_paystack,
                    isEnabled: controller.paystackEnabled.value,
                    onChanged: (value) {
                      controller.paystackEnabled.value = value;
                    },
                  ),
                  if (controller.paystackEnabled.value) ...[
                    Gap(8.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.paystackKeyController,
                      label: AppStrings.T.lbl_paystack_key,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_paystack_key,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.paystackSecretController,
                      label: AppStrings.T.lbl_paystack_secret,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_paystack_secret,
                          ),
                    ),
                  ],

                  // Flutterwave
                  Gap(16.h),
                  _buildPaymentMethodToggle(
                    title: AppStrings.T.lbl_flutterwave,
                    isEnabled: controller.flutterwaveEnabled.value,
                    onChanged: (value) {
                      controller.flutterwaveEnabled.value = value;
                    },
                  ),
                  if (controller.flutterwaveEnabled.value) ...[
                    Gap(8.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.flutterwaveKeyController,
                      label: AppStrings.T.lbl_flutterwave_key,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_flutterwave_key,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.flutterwaveSecretController,
                      label: AppStrings.T.lbl_flutterwave_secret,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_flutterwave_secret,
                          ),
                    ),
                  ],

                  // Razorpay
                  Gap(16.h),
                  _buildPaymentMethodToggle(
                    title: AppStrings.T.lbl_razorpay,
                    isEnabled: controller.razorpayEnabled.value,
                    onChanged: (value) {
                      controller.razorpayEnabled.value = value;
                    },
                  ),
                  if (controller.razorpayEnabled.value) ...[
                    Gap(8.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.razorpayKeyController,
                      label: AppStrings.T.lbl_flutterwave_key,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_flutterwave_key,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.razorpaySecretController,
                      label: AppStrings.T.lbl_flutterwave_secret,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_flutterwave_secret,
                          ),
                    ),
                  ],

                  // PhonePe
                  Gap(16.h),
                  _buildPaymentMethodToggle(
                    title: AppStrings.T.lbl_phonepe,
                    isEnabled: controller.phonePeEnabled.value,
                    onChanged: (value) {
                      controller.phonePeEnabled.value = value;
                    },
                  ),
                  if (controller.phonePeEnabled.value) ...[
                    Gap(8.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.phonePeMerchantIdController,
                      label: AppStrings.T.lbl_phonepe_merchant_id,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_phonepe_merchant_id,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.phonePeMerchantUserIdController,
                      label: AppStrings.T.lbl_phonepe_merchant_user_id,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName:
                                AppStrings.T.lbl_phonepe_merchant_user_id,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.phonePeEnvController,
                      label: AppStrings.T.lbl_phonepe_env,
                      // label: 'Phonepe Env',
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_phonepe_env,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.phonePeSaltKeyController,
                      label: AppStrings.T.lbl_phonepe_salt_key,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_phonepe_salt_key,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.phonePeSaltIndexController,
                      label: AppStrings.T.lbl_phonepe_salt_index,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_phonepe_salt_index,
                          ),
                    ),
                  ],

                  // Paypal
                  Gap(16.h),
                  _buildPaymentMethodToggle(
                    title: AppStrings.T.lbl_paypal,
                    isEnabled: controller.paypalEnabled.value,
                    onChanged: (value) {
                      controller.paypalEnabled.value = value;
                    },
                  ),
                  if (controller.paypalEnabled.value) ...[
                    Gap(8.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.paypalClientIdController,
                      label: AppStrings.T.lbl_paypal_client_id,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_paypal_client_id,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.paypalSecretController,
                      label: AppStrings.T.lbl_paypal_secret,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_paypal_secret,
                          ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.paypalModeController,
                      label: AppStrings.T.lbl_paypal_mode,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_paypal_mode,
                          ),
                    ),
                  ],

                  // Manually
                  Gap(16.h),
                  _buildPaymentMethodToggle(
                    title: AppStrings.T.lbl_manually,
                    isEnabled: controller.manuallyEnabled.value,
                    onChanged: (value) {
                      controller.manuallyEnabled.value = value;
                    },
                  ),
                  if (controller.manuallyEnabled.value) ...[
                    Gap(8.h),
                    TextInputField(
                      type: InputType.text,
                      isRequiredField: true,
                      controller: controller.manualPaymentGuideController,
                      label: AppStrings.T.lbl_manual_payment_guide,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_manual_payment_guide,
                          ),
                    ),
                  ],

                  // Save Button
                  Gap(32.h),
                  CustomElevatedButton(
                    checkConnectivity: true,
                    isLoading: controller.isUpdatingPaymentConfig.value,
                    text: AppStrings.T.lbl_save,
                    onPressed: () {
                      if (controller.isUpdatingPaymentConfig.value) {
                        return;
                      }
                      if (_formKey.currentState?.validate() ?? false) {
                        final data = _buildPaymentConfigData();
                        controller.updatePaymentConfig(data);
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildPaymentMethodToggle({
    required String title,
    required bool isEnabled,
    required Function(bool) onChanged,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Transform.scale(
          scale: 0.8,
          alignment: Alignment.centerRight,
          child: Switch(
            value: isEnabled,
            activeColor: Get.theme.customColors.white,
            inactiveThumbColor: Get.theme.customColors.darkColor,
            inactiveTrackColor: Get.theme.customColors.white,
            activeTrackColor: Get.theme.customColors.primaryColor,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Map<String, dynamic> _buildPaymentConfigData() {
    return {
      'stripe_enable': controller.stripeEnabled.value ? '1' : '0',
      'stripe_key':
          controller.stripeEnabled.value
              ? controller.stripeKeyController.text
              : '',
      'stripe_secret':
          controller.stripeEnabled.value
              ? controller.stripeSecretController.text
              : '',
      'paytack_enable': controller.paystackEnabled.value ? '1' : '0',
      'paystack_key':
          controller.paystackEnabled.value
              ? controller.paystackKeyController.text
              : '',
      'paystack_secret':
          controller.paystackEnabled.value
              ? controller.paystackSecretController.text
              : '',
      'flutterwave_enable': controller.flutterwaveEnabled.value ? '1' : '0',
      'flutterwave_key':
          controller.flutterwaveEnabled.value
              ? controller.flutterwaveKeyController.text
              : '',
      'flutterwave_secret':
          controller.flutterwaveEnabled.value
              ? controller.flutterwaveSecretController.text
              : '',
      'rozorpay_enable': controller.razorpayEnabled.value ? '1' : '0',
      'razorpay_key':
          controller.razorpayEnabled.value
              ? controller.razorpayKeyController.text
              : '',
      'razorpay_secret':
          controller.razorpayEnabled.value
              ? controller.razorpaySecretController.text
              : '',
      'phonepe_enable': controller.phonePeEnabled.value ? '1' : '0',
      'phonepe_merchant_id':
          controller.phonePeEnabled.value
              ? controller.phonePeMerchantIdController.text
              : '',
      'phonepe_merchant_user_id':
          controller.phonePeEnabled.value
              ? controller.phonePeMerchantUserIdController.text
              : '',
      'phonepe_env':
          controller.phonePeEnabled.value
              ? controller.phonePeEnvController.text
              : '',
      'phonepe_salt_key':
          controller.phonePeEnabled.value
              ? controller.phonePeSaltKeyController.text
              : '',
      'phonepe_salt_index':
          controller.phonePeEnabled.value
              ? controller.phonePeSaltIndexController.text
              : '',
      'paypal_enable': controller.paypalEnabled.value ? '1' : '0',
      'paypal_client_id':
          controller.paypalEnabled.value
              ? controller.paypalClientIdController.text
              : '',
      'paypal_secret':
          controller.paypalEnabled.value
              ? controller.paypalSecretController.text
              : '',
      'paypal_mode':
          controller.paypalEnabled.value
              ? controller.paypalModeController.text
              : '',
      'manually_enable': controller.manuallyEnabled.value ? '1' : '0',
      'manual_payment_guide':
          controller.manuallyEnabled.value
              ? controller.manualPaymentGuideController.text
              : '',
    };
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.stripeKeyController.clear();
    controller.stripeSecretController.clear();
    controller.paystackKeyController.clear();
    controller.paystackSecretController.clear();
    controller.razorpayKeyController.clear();
    controller.razorpaySecretController.clear();
    controller.flutterwaveKeyController.clear();
    controller.flutterwaveSecretController.clear();
    controller.phonePeMerchantIdController.clear();
    controller.phonePeMerchantUserIdController.clear();
    controller.phonePeEnvController.clear();
    controller.phonePeSaltKeyController.clear();
    controller.phonePeSaltIndexController.clear();
    controller.paypalClientIdController.clear();
    controller.paypalSecretController.clear();
    controller.paypalModeController.clear();
    controller.manualPaymentGuideController.clear();
  }
}
