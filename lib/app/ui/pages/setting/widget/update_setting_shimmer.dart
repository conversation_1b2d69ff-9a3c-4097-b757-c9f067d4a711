import 'package:v_card/app/utils/helpers/exporter.dart';

class UpdateSettingFormShimmer extends StatelessWidget {
  const UpdateSettingFormShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Paypal Email Field
          _buildShimmerLabel(width: 150),
          Gap(8.h),
          _buildShimmerTextField(),
          Gap(16.h),

          // Currency Dropdown
          _buildShimmerLabel(width: 120),
          Gap(8.h),
          _buildShimmerTextField(),
          Gap(16.h),

          // Newsletter Modal Time
          _buildShimmerLabel(width: 280),
          Gap(8.h),
          _buildShimmerTextField(),
          Gap(16.h),

          // Time Format
          _buildShimmerLabel(width: 120),
          Gap(10.h),
          Row(
            children: [
              Expanded(child: _buildShimmerButton()),
              Gap(16.w),
              Expanded(child: _buildShimmerButton()),
            ],
          ),
          Gap(24.h),

          // Enable PWA Toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [_buildShimmerLabel(width: 100), _buildShimmerSwitch()],
          ),
          Gap(16.h),

          // PWA Icon
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: _buildShimmerLabel(width: 80),
              ),
              _buildShimmerImageContainer(),
            ],
          ),
          Gap(24.h),

          // Enable Attachment for Inquiry Toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [_buildShimmerLabel(width: 180), _buildShimmerSwitch()],
          ),
          Gap(16.h),

          // Ask Details before downloading Contact Toggle
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [_buildShimmerLabel(width: 220), _buildShimmerSwitch()],
          ),
          Gap(32.h),

          // Save Button
          _buildShimmerButton(height: 50),
        ],
      ),
    );
  }

  Widget _buildShimmerLabel({required double width}) {
    return ShimmerBox(width: width, height: 18);
  }

  Widget _buildShimmerTextField({double height = 50}) {
    return ShimmerBox(width: double.infinity, height: height);
  }

  Widget _buildShimmerButton({double height = 45}) {
    return ShimmerBox(width: double.infinity, height: height);
  }

  Widget _buildShimmerSwitch() {
    return ShimmerBox(width: 40, height: 24);
  }

  Widget _buildShimmerImageContainer() {
    return ShimmerBox(height: 100, width: 100);
  }
}
