import 'package:v_card/app/utils/helpers/exporter.dart';

class PaymentConfigShimmer extends StatelessWidget {
  const PaymentConfigShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Payment Method Heading
          _buildShimmerLabel(width: 150.w),
          Gap(24.h),

          // Payment Method Toggles
          ..._buildToggleRows(),
          Gap(16.h),

          // Save Button
          _buildShimmerButton(),
        ],
      ),
    );
  }

  List<Widget> _buildToggleRows() {
    return [
      _buildToggleRow(labelWidth: 80.w), // Stripe
      Gap(16.h),
      _buildToggleRow(labelWidth: 100.w), // Paystack
      Gap(16.h),
      _buildToggleRow(labelWidth: 120.w), // Flutterwave
      Gap(16.h),
      _buildToggleRow(labelWidth: 100.w), // Razorpay
      Gap(16.h),
      _buildToggleRow(labelWidth: 100.w), // PhonePe
      Gap(16.h),
      _buildToggleRow(labelWidth: 80.w), // Paypal
      Gap(16.h),
      _buildToggleRow(labelWidth: 100.w), // Manually
    ];
  }

  Widget _buildToggleRow({required double labelWidth}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildShimmerLabel(width: labelWidth),
        ShimmerBox(width: 40.w, height: 24.h),
      ],
    );
  }

  Widget _buildShimmerLabel({required double width}) {
    return ShimmerBox(width: width, height: 18.h);
  }

  Widget _buildShimmerButton() {
    return ShimmerBox(width: double.infinity, height: 50.h);
  }
}
