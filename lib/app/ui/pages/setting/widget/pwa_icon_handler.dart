// ignore_for_file: use_build_context_synchronously

import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class PWAIconHandler {
  /// Creates PWA icon with the exact required dimensions
  /// Supports multiple standard PWA icon sizes
  static Future<File?> createPWAIcon({
    required BuildContext context,
    required ImageSource source,
    required int size, // Target size (e.g., 192, 512)
    int maxKB = 50, // Maximum file size in KB
    String format = 'png', // Use 'png' for better quality
  }) async {
    try {
      // Pick image from gallery or camera
      final ImagePicker picker = ImagePicker();
      final XFile? pickedImage = await picker.pickImage(
        source: source,
        maxWidth: 2048, // Higher resolution for better quality
        maxHeight: 2048,
        imageQuality: 80, // Set to 80% to match compression quality
      );

      if (pickedImage == null) return null;

      // Read image bytes
      final File originalFile = File(pickedImage.path);
      final Uint8List imageBytes = await originalFile.readAsBytes();

      // Get image dimensions
      final ui.Image decodedImage = await decodeImageFromList(imageBytes);

      // Calculate crop parameters to get a square from the center
      final int minSide =
          decodedImage.width < decodedImage.height
              ? decodedImage.width
              : decodedImage.height;

      final int offsetX = (decodedImage.width - minSide) ~/ 2;
      final int offsetY = (decodedImage.height - minSide) ~/ 2;

      // Crop to perfect square
      final Uint8List croppedBytes = await _cropImage(
        imageBytes,
        offsetX,
        offsetY,
        minSide,
        minSide,
      );

      // Create PWA icon with exact dimensions
      File? pwaIcon = await _createExactSizeIcon(
        croppedBytes,
        size,
        maxKB,
        format,
      );

      return pwaIcon;
    } catch (e) {
      print('❌ Error creating PWA icon: $e');
      // Show error dialog
      await showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text('Error Creating Icon'),
              content: Text(
                'Failed to process the image. Please try another image.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('OK'),
                ),
              ],
            ),
      );
      return null;
    }
  }

  /// Selects multiple images from gallery and processes them for PWA icons
  static Future<List<File>> selectMultipleImagesFromGallery({
    required BuildContext context,
    int targetSize = 512,
    int maxKB = 100,
    String format = 'png',
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final List<XFile> pickedImages = await picker.pickMultiImage(
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 80, // Set to 80% consistently
      );

      if (pickedImages.isEmpty) return [];

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShimmerBox(width: 120, height: 18),
                  Gap(16.h),
                  Text('Processing ${pickedImages.length} images...'),
                ],
              ),
            ),
      );

      final List<File> processedImages = [];

      for (int i = 0; i < pickedImages.length; i++) {
        try {
          final File originalFile = File(pickedImages[i].path);
          final Uint8List imageBytes = await originalFile.readAsBytes();

          // Get image dimensions
          final ui.Image decodedImage = await decodeImageFromList(imageBytes);

          // Calculate crop parameters to get a square from the center
          final int minSide =
              decodedImage.width < decodedImage.height
                  ? decodedImage.width
                  : decodedImage.height;

          final int offsetX = (decodedImage.width - minSide) ~/ 2;
          final int offsetY = (decodedImage.height - minSide) ~/ 2;

          // Crop to perfect square
          final Uint8List croppedBytes = await _cropImage(
            imageBytes,
            offsetX,
            offsetY,
            minSide,
            minSide,
          );

          // Create PWA icon with exact dimensions
          File? processedImage = await _createExactSizeIcon(
            croppedBytes,
            targetSize,
            maxKB, // Use the passed maxKB instead of hardcoded 100
            format,
            suffix: '_${i + 1}',
          );

          if (processedImage != null) {
            processedImages.add(processedImage);
          }
        } catch (e) {
          print('❌ Error processing image ${i + 1}: $e');
        }
      }

      // Close loading dialog
      Navigator.of(context).pop();

      if (processedImages.length != pickedImages.length) {
        // Show warning if some images failed to process
        await showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: Text('Partial Success'),
                content: Text(
                  'Successfully processed ${processedImages.length} out of ${pickedImages.length} images. '
                  'Some images may have failed due to format or size issues.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text('OK'),
                  ),
                ],
              ),
        );
      }

      return processedImages;
    } catch (e) {
      // Close loading dialog if open
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      print('❌ Error selecting multiple images: $e');
      await showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: Text('Error Selecting Images'),
              content: Text(
                'Failed to process the selected images. Please try again.',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text('OK'),
                ),
              ],
            ),
      );
      return [];
    }
  }

  /// Creates an icon with exact dimensions - MAINTAINING 80% QUALITY
  static Future<File?> _createExactSizeIcon(
    Uint8List sourceBytes,
    int targetSize,
    int maxKB,
    String format, {
    String suffix = '',
  }) async {
    // Get temporary directory for saving the file
    final tempDir = await getTemporaryDirectory();
    final String finalPath =
        format.toLowerCase() == 'png'
            ? '${tempDir.path}/pwa_icon_${targetSize}x${targetSize}_${DateTime.now().millisecondsSinceEpoch}$suffix.png'
            : '${tempDir.path}/pwa_icon_${targetSize}x${targetSize}_${DateTime.now().millisecondsSinceEpoch}$suffix.jpg';

    try {
      // Choose format based on requirements
      CompressFormat compressFormat =
          format.toLowerCase() == 'png'
              ? CompressFormat.png
              : CompressFormat.jpeg;

      // MAINTAIN 80% QUALITY - Fixed quality setting
      const int fixedQuality = 80;

      // Resize to exact dimensions with 80% quality
      Uint8List compressedBytes = await FlutterImageCompress.compressWithList(
        sourceBytes,
        format: compressFormat,
        minWidth: targetSize,
        minHeight: targetSize,
        quality: fixedQuality,
      );

      double currentSize = compressedBytes.length / 1024;

      print(
        '🔄 Created with 80% quality: ${currentSize.toStringAsFixed(1)}KB, Target: ${maxKB}KB',
      );

      // If the file is larger than maxKB but quality is maintained at 80%,
      // we can optionally try JPEG format for PNG files to reduce size
      // while keeping quality at 80%
      if (currentSize > maxKB && format.toLowerCase() == 'png') {
        print('🔄 Trying JPEG format at 80% quality for smaller size...');
        compressedBytes = await FlutterImageCompress.compressWithList(
          sourceBytes,
          format: CompressFormat.jpeg,
          minWidth: targetSize,
          minHeight: targetSize,
          quality: fixedQuality, // Still maintain 80% quality
        );
        currentSize = compressedBytes.length / 1024;
        print('🔄 JPEG at 80% quality: ${currentSize.toStringAsFixed(1)}KB');
      }

      // Save final file
      final File outputFile = File(finalPath);
      await outputFile.writeAsBytes(compressedBytes);

      final double finalSizeKB = compressedBytes.length / 1024;
      final String sizeStatus =
          finalSizeKB <= maxKB ? '✅' : '⚠️ (Quality prioritized)';

      print(
        '$sizeStatus PWA icon created: ${targetSize}x$targetSize, ${finalSizeKB.toStringAsFixed(1)}KB (Quality: 80% maintained)',
      );

      return outputFile;
    } catch (e) {
      print('❌ Error during image compression: $e');
      return null;
    }
  }

  /// Crops image to exact dimensions
  static Future<Uint8List> _cropImage(
    Uint8List imageBytes,
    int x,
    int y,
    int width,
    int height,
  ) async {
    // Decode the image
    final ui.Image image = await decodeImageFromList(imageBytes);

    // Create a PictureRecorder to draw the cropped image
    final ui.PictureRecorder recorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(recorder);

    // Draw only the required portion
    canvas.drawImageRect(
      image,
      Rect.fromLTWH(
        x.toDouble(),
        y.toDouble(),
        width.toDouble(),
        height.toDouble(),
      ),
      Rect.fromLTWH(0, 0, width.toDouble(), height.toDouble()),
      Paint(),
    );

    // Convert the Picture to an Image
    final ui.Picture picture = recorder.endRecording();
    final ui.Image croppedImage = await picture.toImage(width, height);

    // Convert the image to byte data
    final ByteData? byteData = await croppedImage.toByteData(
      format: ui.ImageByteFormat.png,
    );

    return byteData!.buffer.asUint8List();
  }

  /// Generate all standard PWA icon sizes with consistent 80% quality
  static Future<Map<int, File?>> generateAllPWAIcons(
    BuildContext context,
  ) async {
    // Standard PWA icon sizes with generous file size limits to accommodate 80% quality
    final Map<int, int> sizeToMaxKB = {
      72: 50, // More generous limits to maintain quality
      96: 60,
      128: 80,
      144: 90,
      152: 100,
      192: 120, // Standard PWA icon
      384: 200, // Larger icons get more space
      512: 300, // Largest icon gets most space
    };

    final Map<int, File?> icons = {};

    // Pick once and generate all sizes
    final ImagePicker picker = ImagePicker();
    final XFile? pickedImage = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 2048, // Higher resolution for better quality
      maxHeight: 2048,
      imageQuality: 80, // Consistent 80% quality
    );

    if (pickedImage == null) return {};

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ShimmerBox(width: 120, height: 18),
                  Gap(16.h),
                  Text('Generating PWA icons...'),
                ],
              ),
            ),
      );

      // Read image bytes
      final File originalFile = File(pickedImage.path);
      final Uint8List imageBytes = await originalFile.readAsBytes();

      // Get image dimensions
      final ui.Image decodedImage = await decodeImageFromList(imageBytes);

      // Calculate crop parameters to get a square from the center
      final int minSide =
          decodedImage.width < decodedImage.height
              ? decodedImage.width
              : decodedImage.height;

      final int offsetX = (decodedImage.width - minSide) ~/ 2;
      final int offsetY = (decodedImage.height - minSide) ~/ 2;

      // Crop to perfect square
      final Uint8List croppedBytes = await _cropImage(
        imageBytes,
        offsetX,
        offsetY,
        minSide,
        minSide,
      );

      // Generate all sizes with appropriate quality settings
      for (MapEntry<int, int> entry in sizeToMaxKB.entries) {
        int size = entry.key;
        int maxKB = entry.value;

        icons[size] = await _createExactSizeIcon(
          croppedBytes,
          size,
          maxKB,
          'png', // Always use PNG for app icons for transparency support
          suffix: '_${size}x$size',
        );
      }

      // Close loading dialog
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      return icons;
    } catch (e) {
      // Close loading dialog if open
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }

      print('❌ Error generating PWA icons: $e');
      return {};
    }
  }

  /// Helper method to get recommended file size limits for different icon sizes
  static int getRecommendedMaxKB(int iconSize) {
    if (iconSize <= 96) return 30;
    if (iconSize <= 152) return 50;
    if (iconSize <= 192) return 70;
    if (iconSize <= 384) return 120;
    return 150; // For 512px and larger
  }
}
