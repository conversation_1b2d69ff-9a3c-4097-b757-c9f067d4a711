import 'package:get/get.dart';
import 'package:v_card/app/controllers/navigation_menu_controller.dart';
import 'package:v_card/app/controllers/subscription_plan_controller.dart';
import 'package:v_card/app/ui/pages/dashboard/helper_fuction/dashboard_helper_fuction.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class NavigationMenu extends GetItHook<NavigationMenuController> {
  const NavigationMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: PersistentTabView(
        context,
        controller: controller.persistentTabController,
        screens: controller.pages,
        items: _navBarsItems(),
        bottomScreenMargin: 70,
        confineToSafeArea: true,
        backgroundColor: Get.theme.customColors.white!,
        handleAndroidBackButtonPress: true,
        resizeToAvoidBottomInset: true,
        stateManagement: false,
        decoration: NavBarDecoration(
          colorBehindNavBar: Get.theme.customColors.transparent!,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              offset: const Offset(0, -2),
              blurRadius: 5,
              spreadRadius: 1,
            ),
          ],
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        navBarStyle: NavBarStyle.style1,
        animationSettings: NavBarAnimationSettings(
          navBarItemAnimation: ItemAnimationSettings(duration: Duration.zero),
        ),
        navBarHeight: 70.h,
        onItemSelected: (index) {
          controller.changePage(index);
        },
      ),
    );
  }

  List<PersistentBottomNavBarItem> _navBarsItems() {
    return [
      _buildNavBarItem(
        // icon: AssetConstants.icDashboard2,
        icon: AssetConstants.icDash,
        title: AppStrings.T.lbl_dashboard,
        index: 0,
      ),
      _buildNavBarItem(
        icon: AssetConstants.icDashVcard,
        // icon: AssetConstants.icVCard2,
        title: "VCard",
        index: 1,
      ),
      _buildNavBarItem(
        icon: AssetConstants.icDashBusinessCard,
        // icon: AssetConstants.icAppbaTraillingIconSvg,
        title: AppStrings.T.lbl_business_card,
        index: 2,
      ),
      _buildNavBarItem(
        icon: AssetConstants.icTotalEnquires,
        // icon: AssetConstants.icDashEnquiries,
        title: AppStrings.T.lbl_enquiries,
        index: 3,
      ),
      _buildNavBarItem(
        icon: AssetConstants.icDashAppointment,
        // icon: AssetConstants.icTotalAppointment,
        title: AppStrings.T.lbl_appointments,
        index: 4,
      ),
    ];
  }

  PersistentBottomNavBarItem _buildNavBarItem({
    required String icon,
    required String title,
    required int index,
  }) {
    return PersistentBottomNavBarItem(
      icon: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomImageView(
              imagePath: icon,
              height: 24.h,
              width: 24.w,
              color: Get.theme.customColors.primaryColor,
            ),
            Gap(4.w),
            Flexible(
              child: Text(
                title,
                textAlign: TextAlign.start,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: Get.theme.textTheme.bodySmall!.copyWith(
                  fontSize: 8.sp,
                  fontWeight: FontWeight.w600,
                  color: Get.theme.customColors.greyTextColor,
                ),
              ),
            ),
          ],
        ),
      ),
      inactiveIcon: CustomImageView(
        imagePath: icon,
        height: 24.h,
        width: 24.w,
        color: Get.theme.customColors.greyTextColor,
      ),
      activeColorPrimary: Get.theme.customColors.primaryColor!,
      inactiveColorPrimary: Colors.transparent,
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  Future<void> onInit() async {
    final prefs = getIt<SharedPreferences>();
    final role = prefs.getRole.toString().toLowerCase();

    if (role == 'admin') {
      await handleAdminSubscriptionFlow();
    }
  }

  Future<void> handleAdminSubscriptionFlow() async {
    final networkHelper = NetworkHelper();

    // Check network before making API calls
    final hasNetwork = await networkHelper.hasNetworkConnection();

    if (!hasNetwork) {
      // Navigate to no network page
      Get.toNamed(AppRoutes.noInternetPage);
      return;
    }

    final subscriptionController = getIt<SubscriptionPlanController>();
    await subscriptionController.getPaymentRequestStatusData();

    final paymentStatus =
        subscriptionController.paymentRequestStatusData.value?.data;

    if (paymentStatus == 'Approved') {
      await subscriptionController.getSubscriptionPlanData();

      final plans =
          subscriptionController.subscriptionPlanData.value?.data ?? [];
      if (Functions.isSubscriptionExpire(plans)) {
        Get.offAllNamed(AppRoutes.manageSubscriptionPage);
      }
    } else {
      Get.toNamed(AppRoutes.paymentApprovalPage);
    }
  }
}
