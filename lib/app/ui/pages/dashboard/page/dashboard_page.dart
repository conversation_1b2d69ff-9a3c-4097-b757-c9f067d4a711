import 'package:get/get.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import 'package:v_card/app/controllers/dashboard_controller.dart';
import 'package:v_card/app/data/model/dashboard/admin/admin_dashboard_model.dart';
import 'package:v_card/app/data/model/dashboard/admin/admin_today_appointment_model.dart';
import 'package:v_card/app/data/model/dashboard/admin/dashboard_chart_data_model.dart';
import 'package:v_card/app/data/model/dashboard/super_admin/super_Admin_chart_data_model.dart';
import 'package:v_card/app/data/model/dashboard/super_admin/super_admin_dashboard_model.dart';
import 'package:v_card/app/ui/pages/dashboard/widgets/dashboard_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class DashboardPage extends GetItHook<DashboardController> {
  DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
    return FocusScope(
      child: Form(
        key: formKey,
        child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) async {
            if (!didPop) {
              final shouldExit = await Get.dialog<bool>(
                ExitAppConfirmationDialog(
                  title: AppStrings.T.lbl_exit_app,
                  message: AppStrings.T.lbl_exit_subtitle,
                  onCancel: () => Get.back(result: false),
                  onConfirm: () => Get.back(result: true),
                ),
              );
              if (shouldExit == true) {
                SystemNavigator.pop();
              }
            }
          },
          child: Scaffold(
            key: scaffoldKey,
            appBar: CustomAppbar(
              title: AppText(
                AppStrings.T.lbl_dashboard,
                style: Get.theme.textTheme.bodyLarge?.copyWith(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onLeadingTap: () => scaffoldKey.currentState?.openDrawer(),
              hasLeadingIcon: false,
              // leadingIcon: CustomImageView(
              //   imagePath: AssetConstants.icAppbarLeadingIcon,
              //   onTap: () => _scaffoldKey.currentState?.openDrawer(),
              // ),
              actions: [
                CustomImageView(
                  imagePath: AssetConstants.icSetting2,
                  onTap:
                      () => NavigationService.navigateWithSlideAnimation(
                        AppRoutes.editProfilegPage,
                      ),
                ),
              ],
            ),
            // drawer: _buildCollapsibleDrawer(context),
            body: Builder(
              builder: (context) {
                final role = getIt<SharedPreferences>().getRole;
                if (role != null && role.toString().toLowerCase() == 'admin') {
                  return Obx(() {
                    if (controller.isLoadingAdmin.value ||
                        controller.isLoadingAdmiChart.value ||
                        controller.isLoadingAdminTodayAppointment.value) {
                      return const Center(child: DashboardShimmerEffect());
                    }

                    final dashboardData = controller.adminDashboard.value?.data;
                    final chartData =
                        controller.adminDashboardChart.value?.data;
                    final appointmentData =
                        controller.adminTodatAppointment.value?.data;

                    if (dashboardData == null ||
                        chartData == null ||
                        appointmentData == null) {
                      return _buildEmptyState();
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        final hasConnection =
                            await controller.checkConnectivity();
                        if (hasConnection) {
                          controller.selectedStartDate.value = DateTime.now()
                              .subtract(Duration(days: 7));
                          await controller.fetchDashboardData();
                        }
                        // else {
                        //   toastification.show(
                        //     type: ToastificationType.error,
                        //     style: ToastificationStyle.flatColored,
                        //     alignment: Alignment.topCenter,
                        //     description: Text(
                        //       AppStrings.T.no_internet_connection,
                        //     ),
                        //     autoCloseDuration: const Duration(seconds: 3),
                        //     showProgressBar: false,
                        //   );
                        // }
                      },
                      child: ListView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        children: [
                          Gap(20.h),
                          Padding(
                            padding: EdgeInsets.symmetric(horizontal: 16.w),
                            child: _buildDashboardGrid(dashboardData),
                          ),
                          Gap(20.h),
                          _buildAdminChartSection(dashboardData, chartData),
                          Gap(20.h),
                          if (appointmentData.isNotEmpty)
                            Padding(
                              padding: EdgeInsets.only(
                                left: 16.w,
                                right: 16.w,
                                top: 0.h,
                              ),
                              child: Text(
                                AppStrings.T.lbl_today_appointment,
                                style: Get.theme.textTheme.bodyLarge!.copyWith(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ),
                          Padding(
                            padding: EdgeInsets.only(
                              bottom: 16.h,
                              left: 16.w,
                              right: 16.w,
                              top: 8.h,
                            ),
                            child: _buildAppointmentList(appointmentData),
                          ),
                          Gap(20.h),
                        ],
                      ),
                    );
                  });
                } else {
                  return Obx(() {
                    if (controller.isLoadingSuperAdminData.value ||
                        controller.isLoadingSuperAdminChart.value) {
                      return const Center(child: DashboardShimmerEffect());
                    }
                    // final dashboardData = controller.adminDashboard.value?.data;

                    final superAdminDashboardData =
                        controller.superAdminDashboard.value?.data;
                    final superAdminchartData =
                        controller.superAdminDashboardChart.value?.data;
                    if (superAdminDashboardData == null ||
                        superAdminchartData == null) {
                      return _buildSuperEmptyState();
                    }

                    return RefreshIndicator(
                      onRefresh: () async {
                        final hasConnection =
                            await controller.checkConnectivity();
                        if (hasConnection) {
                          controller.selectedStartDate.value = DateTime.now()
                              .subtract(Duration(days: 7));
                          await controller.fetchDashboardData();
                        }
                        //  else {
                        //   toastification.show(
                        //     type: ToastificationType.error,
                        //     style: ToastificationStyle.flatColored,
                        //     alignment: Alignment.topCenter,
                        //     description: Text(
                        //       AppStrings.T.no_internet_connection,
                        //     ),
                        //     autoCloseDuration: const Duration(seconds: 3),
                        //     showProgressBar: false,
                        //   );
                        // }
                      },
                      child: ListView(
                        physics: const AlwaysScrollableScrollPhysics(),
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 20.h,
                            ),
                            child: _buildDashboardSuperAdmin(
                              superAdminDashboardData,
                            ),
                          ),
                          _buildSuperAdminChartSection(superAdminchartData),
                          Gap(16.h),
                        ],
                      ),
                    );
                  });
                }
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return RefreshIndicator(
      onRefresh: () async {
        final hasConnection = await controller.checkConnectivity();
        if (hasConnection) {
          controller.selectedStartDate.value = DateTime.now().subtract(
            Duration(days: 7),
          );
          await controller.fetchDashboardData();
        }
        // else {
        //   toastification.show(
        //     type: ToastificationType.error,
        //     style: ToastificationStyle.flatColored,
        //     alignment: Alignment.topCenter,
        //     description: Text(AppStrings.T.no_internet_connection),
        //     autoCloseDuration: const Duration(seconds: 3),
        //     showProgressBar: false,
        //   );
        // }
      },
      child: ListView(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: _buildDashboardGrid(DashboardData.empty()),
          ),
          _buildAdminChartSection(DashboardData.empty(), ChartData.empty()),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: _buildAppointmentList([]),
          ),
        ],
      ),
    );
  }

  Widget _buildSuperEmptyState() {
    return RefreshIndicator(
      onRefresh: () async {
        final hasConnection = await controller.checkConnectivity();
        if (hasConnection) {
          controller.selectedStartDate.value = DateTime.now().subtract(
            Duration(days: 7),
          );
          await controller.fetchDashboardData();
        }
        // else {
        //   toastification.show(
        //     type: ToastificationType.error,
        //     style: ToastificationStyle.flatColored,
        //     alignment: Alignment.topCenter,
        //     description: Text(AppStrings.T.no_internet_connection),
        //     autoCloseDuration: const Duration(seconds: 3),
        //     showProgressBar: false,
        //   );
        // }
      },
      child: ListView(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.h),
            child: _buildDashboardSuperAdmin(SuperAdminDashboardData.empty()),
          ),
          _buildSuperAdminChartSection(
            SuperAdminChartData(labels: [], breakDown: []),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardGrid(DashboardData dashboardData) {
    final activeVcards = dashboardData.activeVcard ?? 0;
    final deActiveVcards = dashboardData.deActiveVcard ?? 0;
    final enquiries = dashboardData.enquiry ?? 0;
    final appointments = dashboardData.appointment ?? 0;

    return GridView(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 15.0,
        mainAxisSpacing: 15.0,
        childAspectRatio: 1.5,
      ),
      children: [
        _buildDashboardCard(
          AppStrings.T.lbl_total_active_vcards,
          activeVcards,
          Get.theme.customColors.blueColor!,
          AssetConstants.icTotalActiveVcard,
        ),
        _buildDashboardCard(
          AppStrings.T.lbl_total_deactivated_vcards,
          deActiveVcards,
          Get.theme.customColors.redColor!,
          AssetConstants.icTotalDeactiveVcard,
        ),
        _buildDashboardCard(
          AppStrings.T.lbl_total_enquiries,
          enquiries,
          Get.theme.customColors.darkColor!,
          AssetConstants.icTotalEnquires,
        ),
        _buildDashboardCard(
          AppStrings.T.lbl_total_appointments,
          appointments,
          Get.theme.customColors.greenColor!,
          AssetConstants.icTotalAppointment,
        ),
      ],
    );
  }

  Widget _buildDashboardSuperAdmin(SuperAdminDashboardData dashboardData) {
    final activeVcards = dashboardData.activeVcard ?? 0;
    final deActiveVcards = dashboardData.deActiveVcard ?? 0;
    final enquiries = dashboardData.enquiry ?? 0;
    final appointments = dashboardData.appointment ?? 0;

    return GridView(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 15.0,
        mainAxisSpacing: 15.0,
        childAspectRatio: 1.5,
      ),
      children: [
        _buildDashboardCard(
          AppStrings.T.lbl_total_active_vcards,
          activeVcards,
          Get.theme.customColors.blueColor!,
          AssetConstants.icTotalActiveVcard,
        ),
        _buildDashboardCard(
          AppStrings.T.lbl_total_deactivated_vcards,
          deActiveVcards,
          Get.theme.customColors.redColor!,
          AssetConstants.icTotalDeactiveVcard,
        ),
        _buildDashboardCard(
          AppStrings.T.lbl_total_enquiries,
          enquiries,
          Get.theme.customColors.darkColor!,
          AssetConstants.icTotalEnquires,
        ),
        _buildDashboardCard(
          AppStrings.T.lbl_total_appointments,
          appointments,
          Get.theme.customColors.greenColor!,
          AssetConstants.icTotalAppointment,
        ),
      ],
    );
  }

  // Add this variable to your DashboardController class:
  final RxMap<int, bool> expandedAppointments = <int, bool>{}.obs;

  // Replace your existing _buildAppointmentList method with this:

  Widget _buildAppointmentList(List<AppointmentData> appointmentData) {
    return Column(
      children: List.generate(appointmentData.length, (index) {
        final appointment = appointmentData[index];
        final isExpanded = controller.expandedAppointments[index] ?? false;

        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: GestureDetector(
            onTap: () {
              controller.expandedAppointments[index] = !isExpanded;
            },
            child: Container(
              clipBehavior: Clip.hardEdge,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Get.theme.customColors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Get.theme.customColors.black!.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min, // Added this
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: 16.w,
                      right: 16.w,
                      bottom: 12.h,
                      top: 16.h,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 6,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min, // Added this
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Flexible(
                                    // Changed from Row to Flexible
                                    child: Row(
                                      mainAxisSize:
                                          MainAxisSize.min, // Added this
                                      children: [
                                        Flexible(
                                          // Wrap Text with Flexible
                                          child: Text(
                                            appointment.vcardName,
                                            style: Get.theme.textTheme.bodyLarge
                                                ?.copyWith(
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.w500,
                                                ),
                                            overflow:
                                                TextOverflow
                                                    .ellipsis, // Added overflow handling
                                          ),
                                        ),
                                        Gap(8.w),
                                        Container(
                                          height: 24.h,
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(
                                              3.r,
                                            ),
                                            color:
                                                appointment.status == 0
                                                    ? Get
                                                        .theme
                                                        .customColors
                                                        .primaryColor!
                                                        .withValues(alpha: 0.2)
                                                    : Get
                                                        .theme
                                                        .customColors
                                                        .greenColor!
                                                        .withValues(alpha: 0.2),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8.0,
                                            ),
                                            child: Center(
                                              child: Text(
                                                appointment.status == 0
                                                    ? AppStrings.T.lbl_pending
                                                    : AppStrings
                                                        .T
                                                        .lbl_completed,
                                                style: Get
                                                    .theme
                                                    .textTheme
                                                    .labelSmall!
                                                    .copyWith(
                                                      fontWeight:
                                                          FontWeight.w600,
                                                      fontSize: 12.0.sp,
                                                      color:
                                                          appointment.status ==
                                                                  0
                                                              ? Get
                                                                  .theme
                                                                  .customColors
                                                                  .primaryColor
                                                              : Get
                                                                  .theme
                                                                  .customColors
                                                                  .greenColor,
                                                    ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              Gap(8.h),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Text(
                                      appointment.name,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: Get.theme.textTheme.bodyMedium,
                                    ),
                                  ),
                                ],
                              ),
                              Gap(8.h),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  CustomImageView(
                                    imagePath: AssetConstants.icClock,
                                  ),
                                  Gap(8.w),
                                  Expanded(
                                    child: RichText(
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                      text: TextSpan(
                                        style: Get.theme.textTheme.labelMedium!
                                            .copyWith(
                                              color:
                                                  Get
                                                      .theme
                                                      .customColors
                                                      .greyTextColor,
                                              fontWeight: FontWeight.w400,
                                            ),
                                        children: [
                                          TextSpan(
                                            text: _formatDate(
                                              appointment.date
                                                  .toString()
                                                  .split(' ')
                                                  .first,
                                            ),
                                          ),
                                          TextSpan(
                                            text: ' • ',
                                            style: Get
                                                .theme
                                                .textTheme
                                                .labelMedium
                                                ?.copyWith(
                                                  color:
                                                      Get
                                                          .theme
                                                          .customColors
                                                          .primaryColor,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                          ),
                                          TextSpan(
                                            text:
                                                '${appointment.fromTime.toString().split(' ').first} - ${appointment.toTime}',
                                            style: Get
                                                .theme
                                                .textTheme
                                                .labelMedium!
                                                .copyWith(
                                                  color:
                                                      Get
                                                          .theme
                                                          .customColors
                                                          .greyTextColor,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            mainAxisSize: MainAxisSize.min, // Added this
                            children: [
                              Flexible(
                                // Wrap Container with Flexible
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(12),
                                    color: Colors.transparent,
                                    border: Border.all(
                                      color:
                                          Get.theme.customColors.primaryColor!,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 8.w,
                                      vertical: 4.h,
                                    ),
                                    child: Center(
                                      child: Text(
                                        appointment.paidAmount == ''
                                            ? AppStrings.T.lbl_free
                                            : appointment.paidAmount,
                                        overflow:
                                            TextOverflow
                                                .ellipsis, // Added overflow handling
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                              Gap(4.w), // Reduced gap
                              AnimatedRotation(
                                turns: isExpanded ? 0.5 : 0,
                                duration: Duration(milliseconds: 200),
                                child: CustomImageView(
                                  imagePath: AssetConstants.icDownArrow2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Fixed AnimatedSize implementation
                  ClipRect(
                    // Added ClipRect to prevent overflow
                    child: AnimatedContainer(
                      duration: Duration(milliseconds: 200),
                      curve: Curves.easeInOut,
                      height: isExpanded ? null : 0,
                      child:
                          isExpanded
                              ? Container(
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Get.theme.customColors.white,
                                  borderRadius: BorderRadius.circular(10.0.r),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Get.theme.customColors.black!
                                          .withValues(alpha: 0.1),
                                      blurRadius: 10,
                                      offset: Offset(0, 5),
                                    ),
                                  ],
                                ),
                                padding: EdgeInsets.only(
                                  left: 16.w,
                                  right: 16.w,
                                  bottom: 16.h,
                                  top: 16.h,
                                ),
                                child: IntrinsicHeight(
                                  // Added IntrinsicHeight
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize:
                                        MainAxisSize.min, // Added this
                                    children: [
                                      Row(
                                        children: [
                                          CustomImageView(
                                            imagePath: AssetConstants.icPerson,
                                            height: 16.h,
                                            width: 16.w,
                                          ),
                                          Gap(8.w),
                                          Expanded(
                                            child: Text(
                                              "${AppStrings.T.lbl_email} : ${appointment.email}",
                                              style:
                                                  Get
                                                      .theme
                                                      .textTheme
                                                      .bodyMedium,
                                              overflow:
                                                  TextOverflow
                                                      .ellipsis, // Added overflow handling
                                            ),
                                          ),
                                        ],
                                      ),
                                      Gap(8.h),
                                      Row(
                                        children: [
                                          CustomImageView(
                                            imagePath: AssetConstants.icCall,
                                            height: 16.h,
                                            width: 16.w,
                                          ),
                                          Gap(8.w),
                                          Expanded(
                                            child: Text(
                                              "${AppStrings.T.lbl_phone} : ${appointment.phone}",
                                              style:
                                                  Get
                                                      .theme
                                                      .textTheme
                                                      .bodyMedium,
                                              overflow:
                                                  TextOverflow
                                                      .ellipsis, // Added overflow handling
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              )
                              : SizedBox.shrink(), // Use SizedBox.shrink() instead of null height
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildAdminChartSection(
    DashboardData dashboardData,
    ChartData chartData,
  ) {
    final activeVcards = dashboardData.activeVcard ?? 0;
    final deActiveVcards = dashboardData.deActiveVcard ?? 0;
    final enquiries = dashboardData.enquiry ?? 0;
    final appointments = dashboardData.appointment ?? 0;

    final total = activeVcards + deActiveVcards + enquiries + appointments;
    final isTotalZero = total == 0;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          border: Border.all(color: Color(0xFFEFF0F6)),
          borderRadius: BorderRadius.circular(15.r),
          boxShadow: [
            BoxShadow(
              color: Get.theme.customColors.black!.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, 5),
            ),
          ],
        ),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.T.lbl_vcard_analytics,
              style: Get.theme.textTheme.bodyLarge!.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w800,
              ),
            ),
            Gap(12.h),
            LayoutBuilder(
              builder: (context, constraints) {
                final chartSize =
                    constraints.maxWidth < 300
                        ? constraints.maxWidth * 0.4
                        : constraints.maxWidth * 0.5;
                final radius = constraints.maxWidth < 300 ? 30.0 : 35.0;
                final centerRadius = constraints.maxWidth < 300 ? 35.0 : 40.0;
                final titleFontSize = constraints.maxWidth < 300 ? 6.sp : 8.sp;

                return SizedBox(
                  height: chartSize,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: PieChart(
                          PieChartData(
                            sectionsSpace: 2,
                            centerSpaceRadius: centerRadius,
                            sections:
                                isTotalZero
                                    ? [
                                      PieChartSectionData(
                                        value: 1,
                                        title: '0%',
                                        color: Colors.grey.shade300,
                                        radius: radius,
                                        titleStyle: Get
                                            .theme
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              fontSize: titleFontSize,
                                              color:
                                                  Get.theme.customColors.white,
                                            ),
                                      ),
                                    ]
                                    : [
                                      PieChartSectionData(
                                        value: activeVcards.toDouble(),
                                        title:
                                            '${(activeVcards / total * 100).toStringAsFixed(1)}%',
                                        color:
                                            Get.theme.customColors.blueColor!,
                                        radius: radius,
                                        titleStyle: Get
                                            .theme
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              fontSize: titleFontSize,
                                              color:
                                                  Get.theme.customColors.white,
                                            ),
                                      ),
                                      PieChartSectionData(
                                        value: deActiveVcards.toDouble(),
                                        title:
                                            '${(deActiveVcards / total * 100).toStringAsFixed(1)}%',
                                        color: Get.theme.customColors.redColor!,
                                        radius: radius,
                                        titleStyle: Get
                                            .theme
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              fontSize: titleFontSize,
                                              color:
                                                  Get.theme.customColors.white,
                                            ),
                                      ),
                                      PieChartSectionData(
                                        value: enquiries.toDouble(),
                                        title:
                                            '${(enquiries / total * 100).toStringAsFixed(1)}%',
                                        color:
                                            Get.theme.customColors.darkColor!,
                                        radius: radius,
                                        titleStyle: Get
                                            .theme
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              fontSize: titleFontSize,
                                              color:
                                                  Get.theme.customColors.white,
                                            ),
                                      ),
                                      PieChartSectionData(
                                        value: appointments.toDouble(),
                                        title:
                                            '${(appointments / total * 100).toStringAsFixed(1)}%',
                                        color:
                                            Get.theme.customColors.greenColor!,
                                        radius: radius,
                                        titleStyle: Get
                                            .theme
                                            .textTheme
                                            .bodyLarge!
                                            .copyWith(
                                              fontSize: titleFontSize,
                                              color:
                                                  Get.theme.customColors.white,
                                            ),
                                      ),
                                    ],
                          ),
                        ),
                      ),
                      Gap(12.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            _buildIconLegendItem(
                              "${AppStrings.T.lbl_active_vcards} ($activeVcards)",
                              Get.theme.customColors.blueColor!.withValues(
                                alpha: 0.2,
                              ),
                              AssetConstants.icTotalActiveVcard,
                            ),
                            Gap(8.h),
                            _buildIconLegendItem(
                              "${AppStrings.T.lbl_deactivated_vcards} ($deActiveVcards)",
                              Get.theme.customColors.redColor!.withValues(
                                alpha: 0.2,
                              ),
                              AssetConstants.icTotalDeactiveVcard,
                            ),
                            Gap(8.h),
                            _buildIconLegendItem(
                              "${AppStrings.T.lbl_enquiries} ($enquiries)",
                              Get.theme.customColors.darkColor!.withValues(
                                alpha: 0.2,
                              ),
                              AssetConstants.icTotalEnquires,
                            ),
                            Gap(8.h),
                            _buildIconLegendItem(
                              "${AppStrings.T.lbl_appointments} ($appointments)",
                              Get.theme.customColors.greenColor!.withValues(
                                alpha: 0.2,
                              ),
                              AssetConstants.icTotalAppointment,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            Gap(16.h),
            Divider(),
            Gap(12.h),
            Text(
              AppStrings.T.lbl_weekly_activity,
              style: Get.theme.textTheme.bodyLarge!.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            Gap(10.h),
            Obx(() {
              if (controller.isLoadingAdmiChartByDate.value) {
                return Column(
                  children: [
                    ShimmerBox(
                      width: double.infinity,
                      height: 140.h,
                      borderRadius: BorderRadius.circular(16.r),
                    ),
                  ],
                );
              }
              final chartData = controller.adminDashboardChart.value?.data;

              if (chartData == null ||
                  chartData.weeklyLabels.isEmpty ||
                  chartData.data.isEmpty ||
                  chartData.data.every((series) => series.data.isEmpty)) {
                return _buildNoDataWidget();
              }

              return Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    height: 140.h,
                    child: LineChart(_buildChart(chartData)),
                  ),
                ],
              );
            }),
            Gap(16.h),
            Divider(),
            Gap(12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getDisplayDateRange(),
                  style: Get.theme.textTheme.bodyLarge!.copyWith(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(6.w),
                PopupMenuButton<DateRangeOption>(
                  offset: Offset(0, 40.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                    side: BorderSide(color: Get.theme.dividerColor),
                  ),
                  elevation: 4,
                  onSelected: (value) async {
                    final hasConnection = await controller.checkConnectivity();
                    if (!hasConnection) {
                      // toastification.show(
                      //   type: ToastificationType.error,
                      //   style: ToastificationStyle.flatColored,
                      //   alignment: Alignment.topCenter,
                      //   description: Text(AppStrings.T.no_internet_connection),
                      //   autoCloseDuration: const Duration(seconds: 3),
                      //   showProgressBar: false,
                      // );

                      return;
                    }

                    final now = DateTime.now();
                    final today = DateTime(now.year, now.month, now.day);

                    if (value == DateRangeOption.thisWeek) {
                      final start = controller.getSunday(today);
                      controller.selectedStartDate.value = start;
                      controller.selectedEndDate.value = start.add(
                        const Duration(days: 6),
                      );
                    } else if (value == DateRangeOption.lastWeek) {
                      final start = controller
                          .getSunday(today)
                          .subtract(const Duration(days: 7));
                      controller.selectedStartDate.value = start;
                      controller.selectedEndDate.value = start.add(
                        const Duration(days: 6),
                      );
                    } else {
                      await _selectDateRange();
                    }

                    controller.selectedDateRangeOption.value = value;
                    controller.getAdminDashboardIncomeChartByDate();
                  },
                  itemBuilder:
                      (context) => [
                        // This Week
                        PopupMenuItem<DateRangeOption>(
                          height: 40.h,
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          value: DateRangeOption.thisWeek,
                          child: SizedBox(
                            width: 180.w,
                            child: Text(
                              AppStrings.T.lbl_this_week,
                              style: Get.theme.textTheme.bodyMedium?.copyWith(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const PopupMenuDivider(height: 1),

                        // Last Week
                        PopupMenuItem<DateRangeOption>(
                          height: 40.h,
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          value: DateRangeOption.lastWeek,
                          child: SizedBox(
                            width: 180.w,
                            child: Text(
                              AppStrings.T.lbl_last_week,
                              style: Get.theme.textTheme.bodyMedium?.copyWith(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const PopupMenuDivider(height: 1),

                        // Custom
                        PopupMenuItem<DateRangeOption>(
                          height: 40.h,
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          value: DateRangeOption.custom,
                          child: SizedBox(
                            width: 180.w,
                            child: Text(
                              AppStrings.T.lbl_custom,
                              style: Get.theme.textTheme.bodyMedium?.copyWith(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ],
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: Get.theme.dividerColor),
                    ),
                    padding: EdgeInsets.all(6.w),
                    child: CustomImageView(
                      imagePath: AssetConstants.icFilter,
                      width: 24.w,
                      height: 24.h,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuperAdminChartSection(SuperAdminChartData chartData) {
    if (chartData.breakDown.isEmpty) {
      // Show a "No Data" widget or similar fallback
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Get.theme.customColors.white,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: Get.theme.customColors.black!.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: Offset(0, 5),
              ),
            ],
          ),
          padding: EdgeInsets.all(16.w),
          child: Center(
            child: Text(
              AppStrings.T.lbl_no_data,
              style: Get.theme.textTheme.bodyMedium,
            ),
          ),
        ),
      );
    }
    final monthlyData = chartData.breakDown.first.data;

    final total = monthlyData.reduce((a, b) => a + b);

    final pieData = <PieChartSectionData>[];
    final nonZeroMonths = <int>[];

    for (int i = 0; i < monthlyData.length; i++) {
      if (monthlyData[i] > 0) {
        String rgbaString = chartData.breakDown.first.backgroundColor[i];
        final match = RegExp(
          r'rgb\((\d+),\s*(\d+),\s*(\d+)\)',
        ).firstMatch(rgbaString);

        Color parsedColor =
            match != null
                ? Color.fromRGBO(
                  int.parse(match.group(1)!),
                  int.parse(match.group(2)!),
                  int.parse(match.group(3)!),
                  1.0,
                )
                : Colors.grey;

        nonZeroMonths.add(i);
        pieData.add(
          PieChartSectionData(
            value: monthlyData[i].toDouble(),
            title: '${(monthlyData[i] / total * 100).toStringAsFixed(1)}%',
            color: parsedColor,
            radius: 40,
            titleStyle: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: Get.theme.customColors.white,
            ),
          ),
        );
      }
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Get.theme.customColors.black!.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: Offset(0, 5),
            ),
          ],
        ),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.T.lbl_monthly_revenue_distribution,
              style: Get.theme.textTheme.bodyLarge!.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w800,
              ),
            ),
            Gap(10.h),
            LayoutBuilder(
              builder: (context, constraints) {
                final chartSize =
                    constraints.maxWidth < 300
                        ? constraints.maxWidth * 0.4
                        : constraints.maxWidth * 0.5;
                final radius = constraints.maxWidth < 300 ? 30.0 : 35.0;
                final centerRadius = constraints.maxWidth < 300 ? 35.0 : 40.0;
                final titleFontSize = constraints.maxWidth < 300 ? 6.sp : 8.sp;
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: chartSize.w,
                      height: chartSize.h,
                      child:
                          pieData.isEmpty
                              ? Center(child: Text(AppStrings.T.lbl_no_data))
                              : PieChart(
                                PieChartData(
                                  sectionsSpace: 2,
                                  centerSpaceRadius: centerRadius,
                                  sections:
                                      pieData
                                          .map(
                                            (section) => PieChartSectionData(
                                              value: section.value,
                                              title: section.title,
                                              color: section.color,
                                              radius: radius,
                                              titleStyle: TextStyle(
                                                fontSize: titleFontSize,
                                                fontWeight: FontWeight.bold,
                                                color:
                                                    Get
                                                        .theme
                                                        .customColors
                                                        .white,
                                              ),
                                            ),
                                          )
                                          .toList(),
                                ),
                              ),
                    ),
                    Gap(16.w),
                    if (nonZeroMonths.isNotEmpty)
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            for (int i in nonZeroMonths)
                              Padding(
                                padding: EdgeInsets.only(bottom: 8.h),
                                child: _buildLegendItem(
                                  _monthName(i),
                                  pieData[nonZeroMonths.indexOf(i)].color,
                                  AssetConstants.icPerson,
                                ),
                              ),
                          ],
                        ),
                      ),
                  ],
                );
              },
            ),
            Gap(16.h),
            Divider(),
            Gap(16.h),
            _buildSuperAdminChartWidget(chartData),
            Gap(16.h),
            Divider(),
            Gap(12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _getDisplayDateRange(),
                  style: Get.theme.textTheme.bodyLarge!.copyWith(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(6.w),
                CustomImageView(
                  imagePath: AssetConstants.icFilter,
                  onTap: _selectSuperAdminDateRange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardCard(
    String title,
    int value,
    Color color,
    String icon,
  ) {
    return Stack(
      children: [
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16.r),
            color: Get.theme.customColors.white,
            boxShadow: [
              BoxShadow(
                color: Get.theme.customColors.black!.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: Offset(0, 5),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.only(left: 16.w, bottom: 8.h, top: 8.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    value.toString(),
                    style: Get.theme.textTheme.bodyLarge!.copyWith(
                      fontSize: 26.sp,
                      fontWeight: FontWeight.w800,
                      color: color,
                    ),
                  ),
                ),
                Gap(6.h),
                Expanded(
                  child: Text(
                    title,
                    style: Get.theme.textTheme.bodyLarge!.copyWith(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          right: 0,
          top: 0,
          child: Container(
            height: 45.h,
            width: 52.w,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(16.r),
                bottomLeft: Radius.circular(40.r),
              ),
            ),
          ),
        ),
        Positioned(
          right: 10.w,
          top: 10.h,
          child: CustomImageView(imagePath: icon),
        ),
      ],
    );
  }

  LineChartData _buildSuperAdminChart(SuperAdminChartData chartData) {
    String rgbaString = chartData.breakDown.first.borderColor.first;
    final match = RegExp(
      r'rgb\((\d+),\s*(\d+),\s*(\d+),\s*(\d*\.?\d+)\)',
    ).firstMatch(rgbaString);

    Color parsedColor =
        match != null
            ? Color.fromRGBO(
              int.parse(match.group(1)!),
              int.parse(match.group(2)!),
              int.parse(match.group(3)!),
              double.parse(match.group(4)!),
            )
            : Colors.grey;
    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        drawHorizontalLine: true,
        getDrawingHorizontalLine:
            (value) => FlLine(
              color: Colors.grey.withValues(alpha: 0.2),
              strokeWidth: 1,
            ),
        getDrawingVerticalLine:
            (value) => FlLine(
              color: Colors.grey.withValues(alpha: 0.2),
              strokeWidth: 1,
            ),
      ),

      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 3,
            getTitlesWidget: (value, meta) {
              final index = value.toInt();
              if (index >= 0 && index < chartData.labels.length) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    chartData.labels[index].substring(0, 3),
                    style: Get.theme.textTheme.labelSmall,
                  ),
                );
              }
              return const Text('');
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: 1000,
            getTitlesWidget: (value, meta) {
              String formatNumber(int value) {
                if (value >= 1000 && value < 1000000) {
                  double result = value / 1000;
                  return '${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}K';
                }
                return value.toString();
              }

              return Text(
                formatNumber(value.toInt()),
                style: Get.theme.textTheme.labelSmall,
              );
            },
          ),
        ),
        rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      borderData: FlBorderData(
        show: true,
        border: const Border(
          left: BorderSide(color: Colors.grey, width: 1),
          right: BorderSide(color: Colors.grey, width: 1),
          top: BorderSide(color: Colors.transparent, width: 0),
          bottom: BorderSide(color: Colors.transparent, width: 0),
        ),
      ),
      minX: 0,
      maxX: (chartData.labels.length - 1).toDouble(),
      minY: 0,
      maxY: _calculateSuperAdminMaxY(chartData),

      lineBarsData:
          chartData.breakDown.map((item) {
            return LineChartBarData(
              spots:
                  item.data
                      .asMap()
                      .entries
                      .map(
                        (entry) => FlSpot(
                          entry.key.toDouble(), // X-axis (month index)
                          entry.value.toDouble(), // Y-axis (amount)
                        ),
                      )
                      .toList(),
              isCurved: false,
              curveSmoothness: item.lineTension,
              color: parsedColor,
              barWidth: 2,
              isStrokeCapRound: true,
              preventCurveOverShooting: true,
              belowBarData: BarAreaData(show: false),
              dotData: const FlDotData(
                show: true,
              ), // Show dots for each data point
            );
          }).toList(),
    );
  }

  double _calculateSuperAdminMaxY(SuperAdminChartData chartData) =>
      chartData.breakDown
          .expand((item) => item.data)
          .reduce((a, b) => a > b ? a : b) *
      1;

  Widget _buildSuperAdminChartWidget(SuperAdminChartData chartData) {
    return Padding(
      padding: const EdgeInsets.all(0.0),
      child: SizedBox(
        height: 140.h,
        width: double.infinity,
        child: LineChart(_buildSuperAdminChart(chartData)),
      ),
    );
  }

  Widget _buildIconLegendItem(String label, Color color, String imagePath) {
    return Row(
      children: [
        Container(
          width: 28.w,
          height: 28.h,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
          child: CustomImageView(
            imagePath: imagePath,
            margin: EdgeInsets.all(9.0),
          ),
        ),
        Gap(4.w),
        Expanded(
          child: Text(
            label,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: Get.theme.textTheme.bodyMedium!.copyWith(fontSize: 11.sp),
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color, String imagePath) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 14.w,
          height: 14.h,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        Gap(4.w),
        Expanded(
          child: AppText(
            label,
            maxLines: 2,
            style: Get.theme.textTheme.bodyMedium!.copyWith(fontSize: 12.sp),
          ),
        ),
      ],
    );
  }

  LineChartData _buildChart(ChartData chartData) {
    // Calculate grouping based on data length
    final dataLength = chartData.weeklyLabels.length;
    final shouldGroup = dataLength > 15;
    int groupSize = 1;
    List<String> displayLabels = chartData.weeklyLabels;
    List<List<double>> groupedData = [];

    if (shouldGroup) {
      // Determine group size based on data length
      if (dataLength <= 50) {
        groupSize = (dataLength / 5).ceil(); // Group into ~5 parts
      } else {
        groupSize = (dataLength / 10).ceil(); // Group into ~10 parts
      }

      // Create grouped labels (day names only, no month/year)
      displayLabels = [];
      for (int i = 0; i < dataLength; i += groupSize) {
        final endIndex = (i + groupSize - 1).clamp(0, dataLength - 1);
        if (i == endIndex) {
          displayLabels.add(chartData.weeklyLabels[i].substring(0, 2));
        } else {
          displayLabels.add(
            '${chartData.weeklyLabels[i].substring(0, 2)}-${chartData.weeklyLabels[endIndex].substring(0, 2)}',
          );
        }
      }

      // Group data points by averaging values in each group
      for (var series in chartData.data) {
        List<double> seriesGroupedData = [];
        for (int i = 0; i < dataLength; i += groupSize) {
          final endIndex = (i + groupSize).clamp(0, dataLength);
          final groupValues = series.data.sublist(i, endIndex);
          final average =
              groupValues.isNotEmpty
                  ? groupValues.reduce((a, b) => a + b) / groupValues.length
                  : 0.0;
          seriesGroupedData.add(average);
        }
        groupedData.add(seriesGroupedData);
      }
    } else {
      // Use original data when not grouping
      groupedData =
          chartData.data
              .map((series) => series.data.map((e) => e.toDouble()).toList())
              .toList();
    }

    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        drawHorizontalLine: false,
        getDrawingHorizontalLine: (_) => FlLine(strokeWidth: 0),
        getDrawingVerticalLine:
            (_) => FlLine(
              strokeWidth: 1,
              color: Colors.grey.withValues(alpha: 0.3),
            ),
        checkToShowVerticalLine: (value) => value.toInt() % 1 == 0,
      ),

      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            interval: 1,
            getTitlesWidget: (value, meta) {
              final index = value.toInt();
              if (index >= 0 && index < displayLabels.length) {
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    displayLabels[index].substring(0, 2),
                    style: Get.theme.textTheme.labelSmall,
                    textAlign: TextAlign.center,
                  ),
                );
              }
              return const Text('');
            },
          ),
        ),
        leftTitles: AxisTitles(
          sideTitles: SideTitles(
            showTitles: true,
            reservedSize: 30,
            interval: _calculateYInterval(chartData),
            getTitlesWidget: (value, meta) {
              String formatNumber(int value) {
                if (value >= 1000 && value < 1000000) {
                  double result = value / 1000;
                  return '${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}K';
                }
                return value.toString();
              }

              return Text(
                formatNumber(value.toInt()),
                style: Get.theme.textTheme.labelSmall,
              );
            },
          ),
        ),
        rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      borderData: FlBorderData(
        show: true,
        border: const Border(
          left: BorderSide(color: Colors.grey, width: 1),
          right: BorderSide(color: Colors.grey, width: 1),
          top: BorderSide(color: Colors.transparent, width: 0),
          bottom: BorderSide(color: Colors.transparent, width: 0),
        ),
      ),
      minX: 0,
      maxX: (displayLabels.length - 1).toDouble(),
      minY: 0,
      maxY:
          shouldGroup
              ? _calculateMaxYGrouped(groupedData)
              : _calculateMaxY(chartData),
      lineTouchData: LineTouchData(
        touchTooltipData: LineTouchTooltipData(
          fitInsideHorizontally: true,
          tooltipBgColor: Colors.blueGrey.shade50,
          getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
            return touchedBarSpots.map((barSpot) {
              final flSpot = barSpot;
              final seriesIndex = barSpot.barIndex;
              final pointIndex = barSpot.x.toInt();

              String label;
              double value;

              if (shouldGroup) {
                label = displayLabels[pointIndex];
                value = groupedData[seriesIndex][pointIndex];
              } else {
                final data = chartData.data[seriesIndex];
                label = data.label;
                value = flSpot.y;
              }

              return (flSpot.y != 0)
                  ? LineTooltipItem(
                    '$label: ${shouldGroup ? value.toStringAsFixed(1) : value.toInt()}',
                    TextStyle(
                      color: chartData.data[seriesIndex].backgroundColor,
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                    ),
                  )
                  : null;
            }).toList();
          },
        ),
      ),

      lineBarsData:
          chartData.data.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final dataToUse =
                shouldGroup
                    ? groupedData[index]
                    : item.data.map((e) => e.toDouble()).toList();

            return LineChartBarData(
              spots:
                  dataToUse
                      .asMap()
                      .entries
                      .map((entry) => FlSpot(entry.key.toDouble(), entry.value))
                      .toList(),
              isCurved: false,
              curveSmoothness: item.lineTension,
              preventCurveOverShooting: true,
              color: item.backgroundColor,
              barWidth: 2,
              isStrokeCapRound: true,
              belowBarData: BarAreaData(show: false),
              dotData: const FlDotData(show: true),
            );
          }).toList(),
    );
  }

  double _calculateYInterval(ChartData chartData) {
    final allValues = chartData.data.expand((series) => series.data);
    final maxVal =
        allValues.isNotEmpty ? allValues.reduce((a, b) => a > b ? a : b) : 1;

    if (maxVal <= 10) return 1;
    if (maxVal <= 50) return 5;
    if (maxVal <= 100) return 10;
    if (maxVal <= 1000) return 100;
    return 500;
  }

  double _calculateMaxY(ChartData chartData) {
    final maxVal = chartData.data
        .expand((item) => item.data)
        .fold<double>(0, (prev, el) => el > prev ? el.toDouble() : prev);

    return _getRoundedMax(maxVal);
  }

  double _calculateMaxYGrouped(List<List<double>> groupedData) {
    final maxVal = groupedData
        .expand((series) => series)
        .fold<double>(0, (prev, el) => el > prev ? el : prev);

    return _getRoundedMax(maxVal);
  }

  double _getRoundedMax(double maxVal) {
    if (maxVal <= 10) return (maxVal / 5).ceil() * 5;
    if (maxVal <= 50) return (maxVal / 10).ceil() * 10;
    if (maxVal <= 100) return (maxVal / 20).ceil() * 20;
    if (maxVal <= 1000) return (maxVal / 100).ceil() * 100;
    return (maxVal / 500).ceil() * 500;
  }

  // LineChartData _buildChart(ChartData chartData) {
  //   return LineChartData(
  //     gridData: FlGridData(
  //       show: true,
  //       drawVerticalLine: true,
  //       drawHorizontalLine: false,
  //       getDrawingHorizontalLine: (_) => FlLine(strokeWidth: 0),
  //       getDrawingVerticalLine:
  //           (_) => FlLine(
  //             strokeWidth: 1,
  //             color: Colors.grey.withValues(alpha: 0.3),
  //           ),
  //       checkToShowVerticalLine: (value) => value.toInt() % 1 == 0,
  //     ),

  //     titlesData: FlTitlesData(
  //       bottomTitles: AxisTitles(
  //         sideTitles: SideTitles(
  //           showTitles: true,
  //           interval: 1,
  //           getTitlesWidget: (value, meta) {
  //             final index = value.toInt();
  //             if (index >= 0 && index < chartData.weeklyLabels.length) {
  //               return Padding(
  //                 padding: const EdgeInsets.only(top: 8.0),
  //                 child: Text(
  //                   chartData.weeklyLabels[index].substring(0, 2),
  //                   style: Get.theme.textTheme.labelSmall,
  //                 ),
  //               );
  //             }
  //             return const Text('');
  //           },
  //         ),
  //       ),
  //       leftTitles: AxisTitles(
  //         sideTitles: SideTitles(
  //           showTitles: true,
  //           reservedSize: 30,
  //           interval: _calculateYInterval(chartData),
  //           getTitlesWidget: (value, meta) {
  //             String formatNumber(int value) {
  //               if (value >= 1000 && value < 1000000) {
  //                 double result = value / 1000;
  //                 return '${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 1)}K';
  //               }
  //               return value.toString();
  //             }

  //             return Text(
  //               formatNumber(value.toInt()),
  //               style: Get.theme.textTheme.labelSmall,
  //             );
  //           },
  //         ),
  //       ),
  //       rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
  //       topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
  //     ),
  //     borderData: FlBorderData(
  //       show: true,
  //       border: const Border(
  //         left: BorderSide(color: Colors.grey, width: 1),
  //         right: BorderSide(color: Colors.grey, width: 1),
  //         top: BorderSide(color: Colors.transparent, width: 0),
  //         bottom: BorderSide(color: Colors.transparent, width: 0),
  //       ),
  //     ),
  //     minX: 0,
  //     maxX: (chartData.weeklyLabels.length - 1).toDouble(),
  //     minY: 0,
  //     maxY: _calculateMaxY(chartData),
  //     lineTouchData: LineTouchData(
  //       touchTooltipData: LineTouchTooltipData(
  //         fitInsideHorizontally: true,
  //         tooltipBgColor: Colors.blueGrey.shade50,
  //         getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
  //           return touchedBarSpots.map((barSpot) {
  //             final flSpot = barSpot;

  //             final data = chartData.data[barSpot.barIndex];
  //             final label = data.label;

  //             return (flSpot.y != 0)
  //                 ? LineTooltipItem(
  //                   '$label: ${flSpot.y.toInt()}',
  //                   TextStyle(
  //                     color: data.backgroundColor,
  //                     fontSize: 10,
  //                     fontWeight: FontWeight.w500,
  //                   ),
  //                 )
  //                 : null;
  //           }).toList();
  //         },
  //       ),
  //     ),

  //     lineBarsData:
  //         chartData.data.map((item) {
  //           return LineChartBarData(
  //             spots:
  //                 item.data
  //                     .asMap()
  //                     .entries
  //                     .map(
  //                       (entry) => FlSpot(
  //                         entry.key.toDouble(),
  //                         entry.value.toDouble(),
  //                       ),
  //                     )
  //                     .toList(),
  //             isCurved: false,
  //             curveSmoothness: item.lineTension,
  //             preventCurveOverShooting: true,
  //             color: item.backgroundColor,
  //             barWidth: 2,
  //             isStrokeCapRound: true,
  //             belowBarData: BarAreaData(show: false),
  //             dotData: const FlDotData(show: true),
  //           );
  //         }).toList(),
  //   );
  // }

  // double _calculateYInterval(ChartData chartData) {
  //   final allValues = chartData.data.expand((series) => series.data);
  //   final maxVal =
  //       allValues.isNotEmpty ? allValues.reduce((a, b) => a > b ? a : b) : 1;

  //   if (maxVal <= 10) return 1;
  //   if (maxVal <= 50) return 5;
  //   if (maxVal <= 100) return 10;
  //   if (maxVal <= 1000) return 100;
  //   return 500;
  // }

  // double _calculateMaxY(ChartData chartData) {
  //   final maxVal = chartData.data
  //       .expand((item) => item.data)
  //       .fold<double>(0, (prev, el) => el > prev ? el.toDouble() : prev);

  //   return _getRoundedMax(maxVal);
  // }

  // double _getRoundedMax(double maxVal) {
  //   if (maxVal <= 10) return (maxVal / 5).ceil() * 5;
  //   if (maxVal <= 50) return (maxVal / 10).ceil() * 10;
  //   if (maxVal <= 100) return (maxVal / 20).ceil() * 20;
  //   if (maxVal <= 1000) return (maxVal / 100).ceil() * 100;
  //   return (maxVal / 500).ceil() * 500;
  // }

  String _monthName(int index) {
    final months = [
      AppStrings.T.lbl_january,
      AppStrings.T.lbl_february,
      AppStrings.T.lbl_march,
      AppStrings.T.lbl_april,
      AppStrings.T.lbl_may,
      AppStrings.T.lbl_june,
      AppStrings.T.lbl_july,
      AppStrings.T.lbl_august,
      AppStrings.T.lbl_september,
      AppStrings.T.lbl_october,
      AppStrings.T.lbl_november,
      AppStrings.T.lbl_december,
    ];
    return months[index];
  }

  // Widget _buildCollapsibleDrawer(BuildContext context) {
  //   final double collapsedWidth = 80.0;
  //   final double expandedWidth = 250.0;

  //   return Obx(
  //     () => Stack(
  //       clipBehavior: Clip.none,
  //       children: [
  //         GestureDetector(
  //           onTap: () {},
  //           child: AnimatedContainer(
  //             duration: const Duration(milliseconds: 0),
  //             width:
  //                 controller.isDrawerExpanded.value
  //                     ? expandedWidth.w
  //                     : collapsedWidth.w,
  //             child: Drawer(
  //               clipBehavior: Clip.none,
  //               shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
  //               child: Container(
  //                 decoration: BoxDecoration(
  //                   color: Get.theme.customColors.white,
  //                   borderRadius: BorderRadius.zero,
  //                 ),
  //                 child: Column(
  //                   children: [
  //                     Container(
  //                       height: 120.h,
  //                       padding: const EdgeInsets.all(8.0),
  //                       decoration: BoxDecoration(
  //                         color: Get.theme.customColors.primaryColor,
  //                         borderRadius: BorderRadius.only(
  //                           bottomLeft: Radius.circular(12.r),
  //                           bottomRight: Radius.circular(12.r),
  //                         ),
  //                       ),
  //                       alignment: Alignment.center,
  //                       child: Row(
  //                         mainAxisAlignment:
  //                             controller.isDrawerExpanded.value
  //                                 ? MainAxisAlignment.start
  //                                 : MainAxisAlignment.center,
  //                         children: [
  //                           CustomImageView(
  //                             onTap: () {
  //                               // controller.isDrawerExpanded.value =
  //                               //     !controller.isDrawerExpanded.value;
  //                               // getIt<SharedPreferences>().isDrawerLarge =
  //                               //     !controller.isDrawerExpanded.value;
  //                             },
  //                             imagePath: AssetConstants.icAppLogoPng,
  //                             height: 45.h,
  //                             width: 45.w,
  //                           ),
  //                           if (controller.isDrawerExpanded.value) ...[
  //                             Gap(12.w),
  //                             Text(
  //                               "InfyVCards",
  //                               style: Get.theme.textTheme.bodyLarge!.copyWith(
  //                                 color: Get.theme.customColors.white,
  //                                 fontSize: 20.sp,
  //                                 fontWeight: FontWeight.bold,
  //                               ),
  //                             ),
  //                           ],
  //                         ],
  //                       ),
  //                     ),
  //                     // Menu Items
  //                     Gap(20.h),
  //                     Expanded(
  //                       child: ListView(
  //                         padding: EdgeInsets.zero,
  //                         children: [
  //                           _buildDrawerItem(
  //                             icon: AssetConstants.icProducts,
  //                             title: AppStrings.T.lbl_product_order,
  //                             onTap:
  //                                 () =>
  //                                     NavigationService.navigateWithSlideAnimation(
  //                                       AppRoutes.productOrderPage,
  //                                     ),
  //                             // isSelected: true,
  //                           ),
  //                           _buildDrawerItem(
  //                             icon: AssetConstants.icAppLogoPng,
  //                             title: AppStrings.T.lbl_affiliations,
  //                             onTap:
  //                                 () =>
  //                                     NavigationService.navigateWithSlideAnimation(
  //                                       AppRoutes.affiliationPage,
  //                                     ),
  //                             // isSelected: true,
  //                           ),
  //                           _buildDrawerItem(
  //                             icon: AssetConstants.icAppLogoPng,
  //                             title: AppStrings.T.lbl_my_nfc_cards,
  //                             onTap:
  //                                 () =>
  //                                     NavigationService.navigateWithSlideAnimation(
  //                                       AppRoutes.nfcCardsPage,
  //                                     ),
  //                             // isSelected: true,
  //                           ),
  //                           _buildDrawerItem(
  //                             icon: AssetConstants.icAppLogoPng,
  //                             title: AppStrings.T.lbl_storage,
  //                             onTap:
  //                                 () =>
  //                                     NavigationService.navigateWithSlideAnimation(
  //                                       AppRoutes.storagePage,
  //                                     ),
  //                             // isSelected: true,
  //                           ),
  //                         ],
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  // Widget _buildDrawerItem({
  //   required String icon,
  //   required String title,
  //   required VoidCallback onTap,
  //   bool isSelected = false,
  // }) {
  //   final Color bgColor =
  //       isSelected
  //           ? Get.theme.customColors.primaryColor!.withValues(alpha: 0.2)
  //           : Colors.transparent;

  //   final Color borderColor =
  //       isSelected ? Get.theme.customColors.primaryColor! : Colors.transparent;

  //   return Obx(
  //     () => InkWell(
  //       onTap: onTap,
  //       child: Padding(
  //         padding: const EdgeInsets.all(8.0),
  //         child: Container(
  //           height: 50.h,
  //           decoration: BoxDecoration(
  //             color: bgColor,
  //             border: Border.all(color: borderColor),
  //           ),
  //           padding: EdgeInsets.symmetric(
  //             horizontal: controller.isDrawerExpanded.value ? 16.0.w : 0,
  //           ),
  //           alignment:
  //               controller.isDrawerExpanded.value
  //                   ? Alignment.centerLeft
  //                   : Alignment.center,
  //           child: Row(
  //             mainAxisAlignment:
  //                 controller.isDrawerExpanded.value
  //                     ? MainAxisAlignment.start
  //                     : MainAxisAlignment.center,
  //             children: [
  //               CustomImageView(imagePath: icon, height: 20.h, width: 20.w),
  //               if (controller.isDrawerExpanded.value) ...[
  //                 Gap(16.w),
  //                 Text(title, style: Get.theme.textTheme.bodyLarge),
  //               ],
  //             ],
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }

  String _formatDate(String date) {
    try {
      final parsedDate = DateTime.parse(date);
      return DateFormat('dd MMM, y').format(parsedDate);
    } catch (e) {
      return date;
    }
  }

  Future<void> _selectDateRange() async {
    final hasConnection = await controller.checkConnectivity();
    if (!hasConnection) {
      // toastification.show(
      //   type: ToastificationType.error,
      //   style: ToastificationStyle.flatColored,
      //   alignment: Alignment.topCenter,
      //   description: Text(AppStrings.T.no_internet_connection),
      //   autoCloseDuration: const Duration(seconds: 3),
      //   showProgressBar: false,
      // );

      return;
    }

    final DateTimeRange? picked = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: controller.selectedStartDate.value,
        end: controller.selectedEndDate.value,
      ),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Get.theme.customColors.primaryColor!,
              onPrimary: Colors.white,
              onSurface: Colors.black,
              secondary: Get.theme.customColors.primaryColor!.withValues(
                alpha: 0.3,
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Get.theme.customColors.primaryColor!,
              ),
            ),
          ),
          child: MediaQuery(
            data: MediaQuery.of(context).copyWith(
              viewPadding: EdgeInsets.zero,
              viewInsets: EdgeInsets.zero,
              padding: EdgeInsets.zero,
            ),
            child: Align(
              alignment: Alignment.bottomCenter,
              child: ClipRRect(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                child: Container(
                  height: MediaQuery.of(context).size.height * 0.65,
                  color: Colors.white,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                          left: 20.0.w,
                          right: 20.0.w,
                          top: 8.0.h,
                        ),
                        height: 5.0,
                        width: 50.0,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100.r),
                          color: Get.theme.customColors.primaryColor
                              ?.withValues(alpha: 0.6),
                        ),
                      ),
                      Expanded(child: child ?? SizedBox()),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );

    if (picked != null) {
      controller.selectedStartDate.value = picked.start;
      controller.selectedEndDate.value = picked.end;
      controller.selectedDateRangeOption.value = DateRangeOption.custom;
      controller.getAdminDashboardIncomeChartByDate();
    }
  }

  Future<void> _selectSuperAdminDateRange() async {
    final hasConnection = await controller.checkConnectivity();
    if (!hasConnection) {
      // toastification.show(
      //   type: ToastificationType.error,
      //   style: ToastificationStyle.flatColored,
      //   alignment: Alignment.topCenter,
      //   description: Text(AppStrings.T.no_internet_connection),
      //   autoCloseDuration: const Duration(seconds: 3),
      //   showProgressBar: false,
      // );

      return;
    }

    final DateTimeRange? picked = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(
        start: controller.selectedStartDate.value,
        end: controller.selectedEndDate.value,
      ),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Get.theme.customColors.primaryColor!,
              onPrimary: Colors.white,
              onSurface: Colors.black,
              secondary: Get.theme.customColors.primaryColor!.withValues(
                alpha: 0.3,
              ),
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Get.theme.customColors.primaryColor!,
              ),
            ),
          ),
          child: Align(
            alignment: Alignment.bottomCenter,
            child: ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
              child: Container(
                height: MediaQuery.of(context).size.height * 0.65,
                color: Colors.white,
                child: child,
              ),
            ),
          ),
        );
      },
    );

    if (picked != null) {
      controller.selectedStartDate.value = picked.start;
      controller.selectedEndDate.value = picked.end;
      controller.getSuperAdminDashboardIncomeChartByDate().then((_) {
        controller.update();
      });
    }
  }

  String _getDisplayDateRange() {
    final start = controller.selectedStartDate.value;
    final end = controller.selectedEndDate.value;

    final startFormat = DateFormat('dd MMM');
    final endFormat = DateFormat('dd MMM, y');

    return '${startFormat.format(start)} - ${endFormat.format(end)}';
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.adminDashboard.value = null;
    controller.superAdminDashboard.value = null;
    controller.adminDashboardChart.value = null;
    controller.superAdminDashboardChart.value = null;
    controller.adminTodatAppointment.value = null;
  }

  @override
  void onInit() async {
    final now = DateTime.now();
    ever(
      controller.selectedStartDate,
      (_) => controller.updateDateRangeOption(),
    );
    ever(controller.selectedEndDate, (_) => controller.updateDateRangeOption());
    controller.setInitialDates();
    controller.selectedEndDate.value = now;
    await controller.checkNetworkAndLoad();
  }

  Widget _buildNoDataWidget() {
    return Container(
      height: 140.h,
      alignment: Alignment.center,
      child: Text(
        AppStrings.T.lbl_no_data,
        style: Get.theme.textTheme.bodyMedium,
      ),
    );
  }
}
