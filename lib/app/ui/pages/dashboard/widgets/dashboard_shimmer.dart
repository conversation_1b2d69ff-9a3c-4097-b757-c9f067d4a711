import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class DashboardShimmerEffect extends StatelessWidget {
  const DashboardShimmerEffect({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 20),
            child: _buildDashboardCardShimmer(),
          ),
          _buildChartSectionShimmer(),
          Gap(20),
          Padding(
            padding: EdgeInsets.only(left: 16, right: 16, bottom: 8),
            child: _buildShimmerLine(width: 180, height: 24),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: _buildAppointmentListShimmer(),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerContainer({
    required double width,
    required double height,
    BorderRadius? borderRadius,
  }) {
    return ShimmerBox(
      width: width,
      height: height,
      borderRadius: borderRadius ?? BorderRadius.circular(8),
    );
  }

  Widget _buildShimmerLine({
    required double width,
    required double height,
    BorderRadius? borderRadius,
  }) {
    return ShimmerBox(
      width: width,
      height: height,
      borderRadius: borderRadius ?? BorderRadius.circular(4),
    );
  }

  Widget _buildDashboardCardShimmer() {
    return GridView.count(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 15.0,
      mainAxisSpacing: 15.0,
      childAspectRatio: 1.5,
      children: [
        _buildDashboardCardShimmerItem(),
        _buildDashboardCardShimmerItem(),
        _buildDashboardCardShimmerItem(),
        _buildDashboardCardShimmerItem(),
      ],
    );
  }

  Widget _buildDashboardCardShimmerItem() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: Get.theme.customColors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildShimmerLine(width: 60, height: 24),
          Gap(10),
          _buildShimmerLine(width: 100, height: 16),
        ],
      ),
    );
  }

  Widget _buildChartSectionShimmer() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Container(
        height: 480,
        width: double.infinity,
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildShimmerLine(width: 140, height: 24),
            Gap(20),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Pie chart placeholder
                Stack(
                  children: [
                    _buildShimmerContainer(
                      width: Get.width * 0.45,
                      height: Get.width * 0.45,
                      borderRadius: BorderRadius.circular(Get.width * 0.45 / 2),
                    ),
                    Positioned.fill(
                      child: Center(
                        child: CircleAvatar(
                          radius: 46,
                          backgroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                Gap(8),
                // Legend column placeholder
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildLegendItemShimmer(),
                      Gap(10),
                      _buildLegendItemShimmer(),
                      Gap(10),
                      _buildLegendItemShimmer(),
                      Gap(10),
                      _buildLegendItemShimmer(),
                    ],
                  ),
                ),
              ],
            ),
            Gap(20),
            const Divider(),
            Gap(20),
            _buildShimmerLine(width: 90, height: 24),
            Gap(20),
            _buildShimmerContainer(
              width: double.infinity,
              height: 100,
              borderRadius: BorderRadius.circular(12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItemShimmer() {
    return Row(
      children: [
        _buildShimmerContainer(
          width: 12,
          height: 12,
          borderRadius: BorderRadius.circular(6),
        ),
        Gap(8),
        Expanded(child: _buildShimmerLine(width: 90, height: 16)),
      ],
    );
  }

  Widget _buildAppointmentListShimmer() {
    return Column(
      children: List.generate(5, (index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: Container(
            height: 70,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 5,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                children: [
                  _buildShimmerContainer(
                    width: 40,
                    height: 40,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  Gap(16),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildShimmerLine(width: 140, height: 16),
                      Gap(8),
                      _buildShimmerLine(width: 100, height: 14),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }
}
