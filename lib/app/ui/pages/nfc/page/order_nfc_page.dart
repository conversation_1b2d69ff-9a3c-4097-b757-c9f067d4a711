import 'package:get/get.dart';
import 'package:v_card/app/controllers/nfc_controller.dart';
import 'package:v_card/app/data/model/nfc/nfc_model.dart';
import 'package:v_card/app/ui/pages/nfc/widgets/nfc_page_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class OrderNfcPage extends GetItHook<NfcController> {
  const OrderNfcPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_order_nfc_cards,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          IconButton(
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            highlightColor: Colors.transparent,
            icon: Container(
              margin: EdgeInsets.only(left: 8.w),
              height: 40.h,
              width: 40.w,
              decoration: BoxDecoration(
                color: Get.theme.customColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Get.theme.customColors.textfieldFillColor!,
                ),
              ),
              child: CustomImageView(
                imagePath: AssetConstants.icSearch,
                margin: const EdgeInsets.all(8.0),
              ),
            ),
            onPressed: controller.toggleNfcSearchMode,
          ),
        ],
      ),
      body: Stack(
        children: [
          Obx(() {
            if (controller.isLoadingOrderNfc.value) {
              return NfcShimmerEffect(
                isGrid: true,
                controller: controller.isNfcSearchActive.value,
              );
            }

            final nfcCards = controller.orderNfcList.value?.data ?? [];

            if (nfcCards.isEmpty) {
              return Center(
                child: Text(
                  AppStrings.T.lbl_no_nfc_cards_design_available,
                  style: Get.theme.textTheme.titleMedium,
                ),
              );
            }
            // Filter based on search
            final filteredNfcCard =
                controller.isNfcSearchActive.value
                    ? nfcCards.where(
                      (nfcCard) =>
                          (nfcCard.name?.toLowerCase().contains(
                                controller.searchNfcText.value
                                    .trim()
                                    .toLowerCase(),
                              ) ??
                              false) ||
                          (nfcCard.price?.toLowerCase().contains(
                                controller.searchNfcText.value
                                    .trim()
                                    .toLowerCase(),
                              ) ??
                              false),
                    )
                    : nfcCards;
            if (filteredNfcCard.isEmpty) {
              return Center(
                child: Text(
                  controller.isNfcSearchActive.value &&
                          controller.searchNfcText.value.isNotEmpty
                      ? "${AppStrings.T.lbl_no_results_found_for} '${controller.searchNfcText.value}'"
                      : AppStrings.T.lbl_no_data,
                  style: Get.theme.textTheme.bodyMedium?.copyWith(
                    color: Get.theme.customColors.greyTextColor,
                  ),
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () => controller.getOrderNfcList(),
              child: GridView.builder(
                padding: EdgeInsets.only(
                  left: 16.w,
                  right: 16.w,
                  top: controller.isNfcSearchActive.value ? 78.h : 8.h,
                  bottom: 20.0.h,
                ),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15.w,
                  mainAxisSpacing: 15.h,
                  childAspectRatio: 0.8,
                ),
                itemCount: filteredNfcCard.length,

                itemBuilder: (context, index) {
                  final card = filteredNfcCard.elementAt(index);
                  return _buildNfcCardItem(card);
                },
              ),
            );
          }),
          // Search Bar Overlay
          Obx(
            () =>
                controller.isNfcSearchActive.value
                    ? Positioned(
                      top: 10,
                      left: 0,
                      right: 0,
                      child: Container(
                        color: Get.theme.scaffoldBackgroundColor,
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: TextInputField(
                          controller: controller.searchNfcController,
                          focusNode: controller.searchNfcFocusNode,
                          label: AppStrings.T.lbl_search_nfc_cards,
                          onChanged:
                              (value) => controller.searchNfcText.value = value,
                          type: InputType.text,
                          textInputAction: TextInputAction.done,
                          suffixIcon:
                              controller.searchNfcText.value.isNotEmpty
                                  ? IconButton(
                                    icon: Icon(
                                      Icons.close,
                                      color:
                                          Get.theme.customColors.greyTextColor,
                                    ),
                                    onPressed: () {
                                      controller.searchNfcController.clear();
                                      controller.searchNfcText.value = '';
                                      controller.toggleNfcSearchMode();
                                    },
                                  )
                                  : null,
                        ),
                      ),
                    )
                    : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildNfcCardItem(OrderNfcData card) {
    return Card(
      elevation: 2,
      color: Get.theme.customColors.white,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: CustomImageView(
              imagePath: card.nfcImage,
              radius: BorderRadius.only(
                topLeft: Radius.circular(12.r),
                topRight: Radius.circular(12.r),
              ),
              fit: BoxFit.cover,
            ),
          ),
          Padding(
            padding: EdgeInsets.all(8.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  card.name ?? AppStrings.T.lbl_no_name,
                  style: Get.theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Gap(4.h),
                Text(
                  card.price ?? '\$0',
                  style: Get.theme.textTheme.labelMedium?.copyWith(
                    color: Get.theme.colorScheme.primary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                Gap(4.h),
                CustomElevatedButton(
                  height: 28.h,
                  text: AppStrings.T.lbl_buy_now,
                  buttonTextStyle: Get.theme.textTheme.bodySmall!.copyWith(
                    color: Get.theme.customColors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  borderRadius: 8.r,
                  onPressed: () {
                    NavigationService.navigateWithSlideAnimation(
                      AppRoutes.orderNfcFormPage,
                      arguments: {'nfcId': card.id.toString()},
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.searchNfcController.clear();
    controller.isNfcSearchActive.value = false;
    controller.searchNfcText.value = '';
  }

  @override
  void onInit() {
    controller.getOrderNfcList();
  }
}
