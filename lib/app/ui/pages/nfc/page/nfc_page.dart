import 'package:get/get.dart';
import 'package:v_card/app/controllers/nfc_controller.dart';
import 'package:v_card/app/data/model/nfc/nfc_model.dart';
import 'package:v_card/app/ui/pages/nfc/widgets/nfc_page_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class NfcCardsPage extends GetItHook<NfcController> {
  const NfcCardsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_nfc,
          style: Get.theme.textTheme.bodyLarge?.copyWith(fontSize: 18.sp, fontWeight: FontWeight.w500),
        ),
        actions: [
          IconButton(
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            highlightColor: Colors.transparent,
            icon: Container(
              margin: EdgeInsets.only(left: 8.w),
              height: 40.h,
              width: 40.w,
              decoration: BoxDecoration(
                color: Get.theme.customColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Get.theme.customColors.textfieldFillColor!),
              ),
              child: CustomImageView(imagePath: AssetConstants.icSearch, margin: const EdgeInsets.all(8.0)),
            ),
            onPressed: controller.toggleSearchMode,
          ),
          IconButton(
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            highlightColor: Colors.transparent,
            icon: Container(
              margin: EdgeInsets.only(left: 8.w),
              height: 40.h,
              width: 40.w,
              decoration: BoxDecoration(
                color: Get.theme.customColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Get.theme.customColors.textfieldFillColor!),
              ),
              child: Icon(Icons.nfc, size: 24.w),
            ),
            onPressed: controller.navigateToOrderNfcPage,
          ),
        ],
      ),
      body: Stack(
        children: [
          Obx(() {
            if (controller.isLoadingNfcList.value) {
              return NfcShimmerEffect(controller: controller.isSearchActive.value);
            }

            final nfcCards = controller.nfcCardsList.value?.data ?? [];

            if (nfcCards.isEmpty) {
              return Center(
                child: Text(AppStrings.T.lbl_no_nfc_cardS_available, style: Get.theme.textTheme.titleMedium),
              );
            }

            // Filter based on search
            final filteredNfcCard =
                controller.isSearchActive.value
                    ? nfcCards.where(
                      (nfcCard) =>
                          (nfcCard.name?.toLowerCase().contains(controller.searchText.value.trim().toLowerCase()) ??
                              false) ||
                          (nfcCard.cardType?.toLowerCase().contains(controller.searchText.value.trim().toLowerCase()) ??
                              false) ||
                          (nfcCard.vcard?.toString().toLowerCase().contains(
                                controller.searchText.value.trim().toLowerCase(),
                              ) ??
                              false) ||
                          (nfcCard.address?.toString().toLowerCase().contains(
                                controller.searchText.value.trim().toLowerCase(),
                              ) ??
                              false),
                    )
                    : nfcCards;

            if (filteredNfcCard.isEmpty) {
              return Center(
                child: Text(
                  controller.isSearchActive.value && controller.searchText.value.isNotEmpty
                      ? "${AppStrings.T.lbl_no_results_found_for} '${controller.searchText.value}'"
                      : AppStrings.T.lbl_no_data,
                  style: Get.theme.textTheme.bodyMedium?.copyWith(color: Get.theme.customColors.greyTextColor),
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () => controller.getNfcCardsList(),
              child: Padding(
                padding: EdgeInsets.only(top: controller.isSearchActive.value ? 78.h : 8.h),
                child: ListView.separated(
                  itemCount: filteredNfcCard.length,
                  itemBuilder: (context, index) {
                    final card = filteredNfcCard.elementAt(index);
                    return _buildCustomNfcCard(card);
                  },
                  separatorBuilder: (_, index) => Gap(8.h),
                ),
              ),
            );
          }),

          // Search Bar Overlay
          Obx(
            () =>
                controller.isSearchActive.value
                    ? Positioned(
                      top: 10,
                      left: 0,
                      right: 0,
                      child: Container(
                        color: Get.theme.scaffoldBackgroundColor,
                        padding: EdgeInsets.symmetric(horizontal: 16.w),
                        child: TextInputField(
                          controller: controller.searchController,
                          focusNode: controller.searchFocusNode,
                          label: AppStrings.T.lbl_search_nfc_cards,
                          onChanged: (value) => controller.searchText.value = value,
                          type: InputType.text,
                          textInputAction: TextInputAction.done,
                          suffixIcon:
                              controller.searchText.value.isNotEmpty
                                  ? IconButton(
                                    icon: Icon(Icons.close, color: Get.theme.customColors.greyTextColor),
                                    onPressed: () {
                                      controller.searchController.clear();
                                      controller.searchText.value = '';
                                      controller.toggleSearchMode();
                                    },
                                  )
                                  : null,
                        ),
                      ),
                    )
                    : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomNfcCard(NfcCardData card) {
    return GestureDetector(
      onTap: () => _showNfcCardDetails(card),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Get.theme.customColors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.08), blurRadius: 6.r, offset: Offset(0, 2))],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Left side - Profile image
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: Get.theme.customColors.primaryColor!.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: CustomImageView(
                  imagePath: card.logo,
                  height: 48.h,
                  width: 48.w,
                  radius: BorderRadius.circular(100.r),
                  fit: BoxFit.cover,
                ),
              ),

              Gap(12.w),

              // Middle - Card information
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      card.name ?? 'No Name',
                      style: Get.theme.textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.w600),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Gap(4.h),
                    Text(
                      card.address ?? 'No Address',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: Get.theme.textTheme.bodySmall?.copyWith(color: Get.theme.customColors.greyTextColor),
                    ),
                    Gap(4.h),
                    Row(
                      children: [
                        Icon(Icons.calendar_today_outlined, size: 12.w, color: Get.theme.customColors.greyTextColor),
                        Gap(4.w),
                        Text(
                          card.createdAt ?? '',
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            color: Get.theme.customColors.greyTextColor,
                            fontSize: 10.sp,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              Gap(8.w),
              Column(mainAxisAlignment: MainAxisAlignment.center, children: [_buildStatusChip(card.orderStatus ?? '')]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color chipColor;
    switch (status.toLowerCase()) {
      case 'delivered':
        chipColor = Colors.green;
        break;
      case 'pending':
        chipColor = Colors.orange;
        break;
      case 'ready to ship':
        chipColor = Colors.blue;
        break;
      case 'cancelled':
        chipColor = Colors.red;
        break;
      default:
        chipColor = Colors.orange;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(color: chipColor, borderRadius: BorderRadius.circular(12.r)),
      child: Text(
        status.isNotEmpty ? status : 'N/A',
        style: Get.theme.textTheme.bodySmall?.copyWith(
          color: Colors.white,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  void _showNfcCardDetails(NfcCardData card) {
    Get.bottomSheet(
      isScrollControlled: true,
      Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40.w,
                height: 4.h,
                decoration: BoxDecoration(
                  color: Get.theme.customColors.primaryColor!.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
            ),
            Gap(16.h),
            Row(
              children: [
                CustomImageView(
                  imagePath: card.logo,
                  height: 60.h,
                  width: 60.w,
                  fit: BoxFit.cover,
                  radius: BorderRadius.circular(100.r),
                ),
                Gap(16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        card.name ?? 'No Name',
                        style: Get.theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(card.companyName ?? 'No Company', style: Get.theme.textTheme.bodyMedium),
                    ],
                  ),
                ),
              ],
            ),
            Gap(12.h),
            _buildDetailRow('Card Type', card.cardType ?? 'N/A'),
            _buildDetailRow('VCard', card.vcard ?? 'N/A'),
            _buildDetailRow('Address', card.address ?? 'N/A'),
            _buildDetailRow('Order Status', card.orderStatus ?? 'N/A'),
            _buildDetailRow('Created At', card.createdAt ?? 'N/A'),
            _buildDetailRow('Payment Status', card.paymentStatus ?? 'N/A'),
            _buildDetailRow(
              'Payment Type',
              card.paymentType!.isEmpty ? 'Failed' : card.paymentType?.capitalizeFirst ?? 'Failed',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    if (label == 'Order Status') {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 100.w,
              child: Text(
                label,
                style: Get.theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Get.theme.customColors.darkGreyTextColor,
                ),
              ),
            ),
            Gap(4.w),
            Text(':'),
            Gap(4.w),
            _buildStatusChip(value),
          ],
        ),
      );
    }

    if (label == 'Payment Status') {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 100.w,
              child: Text(
                label,
                style: Get.theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Get.theme.customColors.darkGreyTextColor,
                ),
              ),
            ),
            Gap(4.w),
            Text(':'),
            Gap(4.w),
            _buildPaymentStatusChip(value),
          ],
        ),
      );
    }

    // Normal row for other details
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Get.theme.customColors.darkGreyTextColor,
              ),
            ),
          ),
          Gap(4.w),
          Text(':'),
          Gap(4.w),
          Expanded(child: Text(value, style: Get.theme.textTheme.bodyMedium)),
        ],
      ),
    );
  }

  Widget _buildPaymentStatusChip(String status) {
    final normalizedStatus = (status.isEmpty ? 'failed' : status.toLowerCase());

    Color chipColor;
    switch (normalizedStatus) {
      case 'paid':
        chipColor = Colors.green;
        break;
      case 'pending':
        chipColor = Colors.orange;
        break;
      case 'failed':
        chipColor = Colors.red;
        break;
      default:
        chipColor = Colors.red;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(color: chipColor, borderRadius: BorderRadius.circular(12.r)),
      child: Text(
        normalizedStatus.capitalizeFirst ?? 'Failed',
        style: Get.theme.textTheme.bodySmall?.copyWith(
          color: Colors.white,
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.searchController.clear();
    controller.isSearchActive.value = false;
    controller.searchText.value = '';
  }

  @override
  void onInit() {
    controller.getNfcCardsList();
    controller.getVcardList();
    controller.getPaymentTypes();
  }
}
