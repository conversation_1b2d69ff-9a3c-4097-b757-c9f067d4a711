import 'dart:io';
import 'package:country_picker/country_picker.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/nfc_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class OrderNfcFormPage extends GetItHook<NfcController> {
  OrderNfcFormPage({super.key});

  final String nfcId = Get.arguments['nfcId'] ?? '0';
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    controller.isProcessingPWAIcon = false.obs;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_order_nfc_cards,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Form(
        key: _form<PERSON><PERSON>,
        child: IgnorePointer(
          ignoring: controller.isLoadingcreateNfcOrder.value,
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text.rich(
                        TextSpan(
                          text: AppStrings.T.lbl_logo,
                          style: Get.theme.textTheme.bodyMedium,
                          children: [
                            TextSpan(
                              text: '*',
                              style: Get.theme.textTheme.bodyMedium?.copyWith(
                                color: Get.theme.customColors.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Gap(8.h),
                      Obx(() {
                        return GestureDetector(
                          onTap: () async {
                            if (controller.isProcessingPWAIcon.value) {
                              Logger.log("Processing is already in progress.");
                              return; // Exit early if processing is ongoing
                            } else {
                              controller.isProcessingPWAIcon.value = true;

                              try {
                                final File? image =
                                    await PWAIconHandler.createPWAIcon(
                                      context: Get.context!,
                                      source: ImageSource.gallery,
                                      size: 512,
                                      maxKB: 50,
                                      format: 'png',
                                    );

                                if (image != null) {
                                  controller.selectedLogo.value = File(
                                    image.path,
                                  );
                                  // The icon is now ready for upload
                                }
                              } catch (e) {
                                Logger.log('Error creating PWA icon: $e');
                                // Display an error message or toast if needed
                              } finally {
                                // Reset the processing flag
                                controller.isProcessingPWAIcon.value = false;
                              }
                            }
                          },
                          child: Stack(
                            children: [
                              Container(
                                height: 140.h,
                                width: 150.w,
                                decoration: BoxDecoration(
                                  color: Get.theme.customColors.white,
                                  borderRadius: BorderRadius.circular(16.r),
                                  border: Border.all(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Get.theme.customColors.black!
                                          .withValues(alpha: 0.1),
                                      blurRadius: 25,
                                      spreadRadius: 5,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: CustomImageView(
                                    height: 145.h,
                                    width: 145.w,
                                    fit: BoxFit.cover,
                                    radius: BorderRadius.circular(12.r),

                                    imagePath:
                                        controller.selectedLogo.value?.path,
                                  ),
                                ),
                              ),
                              Positioned(
                                right: 10.w,
                                bottom: 10.h,
                                child: CircleAvatar(
                                  radius: 22.r,
                                  backgroundColor: Get.theme.customColors.white,
                                  child: CustomImageView(
                                    imagePath: AssetConstants.icCamera,
                                    color: Get.theme.customColors.primaryColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
                Gap(20.0.h),
                TextInputField(
                  controller: controller.quantityController,
                  label: AppStrings.T.lbl_quantity,
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_quantity,
                      ),
                  type: InputType.phoneNumber,
                  isRequiredField: true,
                ),
                Gap(16.h),
                TextInputField(
                  controller: controller.vcardController,
                  label: AppStrings.T.lbl_select_vcard,
                  readOnly: true,
                  type: InputType.text,
                  onTap: () {
                    controller.showVcardBottomSheet(context);
                  },
                  suffixIcon: CustomImageView(
                    imagePath: AssetConstants.icDownArrow2,
                    margin: EdgeInsets.all(8.0),
                  ),
                ),
                Gap(16.h),
                TextInputField(
                  controller: controller.companyNameController,
                  label: AppStrings.T.lbl_company_name,
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_company_name,
                      ),
                  type: InputType.text,
                  isRequiredField: true,
                ),
                Gap(16.h),

                TextInputField(
                  controller: controller.nameController,
                  label: AppStrings.T.lbl_name,
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_name,
                      ),
                  type: InputType.text,
                  isRequiredField: true,
                ),
                Gap(16.h),

                TextInputField(
                  controller: controller.emailController,
                  label: AppStrings.T.lbl_email,
                  validator: AppValidations.emailValidation,
                  type: InputType.email,
                  isRequiredField: true,
                ),
                Gap(16.h),
                buildPhoneNumberField(context),

                Gap(16.h),

                TextInputField(
                  controller: controller.designationController,
                  label: AppStrings.T.lbl_designation,

                  isRequiredField: true,
                  type: InputType.text,
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_designation,
                      ),
                ),
                Gap(16.h),

                TextInputField(
                  controller: controller.addressController,
                  label: AppStrings.T.lbl_shipping_address,
                  isRequiredField: true,
                  type: InputType.text,
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_shipping_address,
                      ),
                  maxLines: 3,
                ),
                Gap(16.h),

                TextInputField(
                  controller: controller.paymentMethodController,
                  label: AppStrings.T.lbl_payment_method,
                  // validator:
                  //     (value) => AppValidations.validateRequired(
                  //       value,
                  //       fieldName: AppStrings.T.lbl_payment_method,
                  //     ),
                  readOnly: true,
                  type: InputType.text,
                  onTap: () => controller.showPaymentMethodBottomSheet(context),
                  suffixIcon: CustomImageView(
                    imagePath: AssetConstants.icDownArrow2,
                    margin: EdgeInsets.all(8.0),
                  ),
                ),
                Gap(30.h),

                // Order Button
                Obx(() {
                  return CustomElevatedButton(
                    checkConnectivity: true,
                    text: AppStrings.T.lbl_place_order,
                    isLoading: controller.isLoadingcreateNfcOrder.value,
                    onPressed: () {
                      if (controller.isLoadingcreateNfcOrder.value) {
                        return;
                      }
                      if (controller.isShowingToast.value) {
                        return;
                      }

                      if (_formKey.currentState!.validate()) {
                        if (controller.isProcessingPWAIcon.value) {
                          _showImageValidationToast(
                            AppStrings.T.lbl_image_processing_message,
                            false,
                          );
                          return;
                        } else if (controller.selectedLogo.value == null) {
                          _showImageValidationToast(
                            AppStrings.T.lbl_please_select_image,
                            true,
                          );
                          return;
                        }
                        if (controller.vcardController.text.isEmpty) {
                          toastification.show(
                            type: ToastificationType.warning,
                            style: ToastificationStyle.flatColored,
                            alignment: Alignment.topCenter,
                            showProgressBar: false,
                            description: Text(
                              AppStrings.T.lbl_place_select_vcard,
                            ),
                            autoCloseDuration: const Duration(seconds: 3),
                          );
                          return;
                        }

                        if (controller.paymentMethodController.text.isEmpty) {
                          toastification.show(
                            type: ToastificationType.warning,
                            style: ToastificationStyle.flatColored,
                            alignment: Alignment.topCenter,
                            showProgressBar: false,
                            description: Text(
                              AppStrings.T.lbl_place_select_payment_method,
                            ),
                            autoCloseDuration: const Duration(seconds: 3),
                          );
                          return;
                        }
                        controller.createNfcOrder(nfcId);
                      }
                    },
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  Widget buildPhoneNumberField(BuildContext context) {
    return TextInputField(
      type: InputType.phoneNumber,
      hintLabel: AppStrings.T.lbl_contact,
      isRequiredField: true,
      label: AppStrings.T.lbl_contact,
      controller: controller.phoneController,
      keyboardType: TextInputType.phone,
      validator: (value) => AppValidations.phoneNumberValidation(value),
      boxConstraints: BoxConstraints(),
      prefixIcon: GestureDetector(
        onTap: () {
          showCountryPicker(
            context: context,
            showPhoneCode: true,
            countryListTheme: CountryListThemeData(
              searchTextStyle: Get.theme.textTheme.labelLarge,
              textStyle: Get.theme.textTheme.labelLarge,
              inputDecoration: InputDecoration(
                hintText: AppStrings.T.lbl_search,
                hintStyle: Get.theme.textTheme.bodySmall?.copyWith(
                  fontSize: 14.sp,
                  color: Get.theme.customColors.greyTextColor,
                ),
                border: InputBorder.none,
                prefixIcon: Icon(
                  Icons.search,
                  color: Get.theme.customColors.greyTextColor,
                ),
              ),
            ),
            onSelect: (country) {
              controller.registerCountryCode.value = country.phoneCode;
              controller.registerCountryflag.value = country.flagEmoji;
              controller.update();
            },
          );
        },
        child: Obx(
          () => Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  controller.registerCountryflag.value.isNotEmpty
                      ? controller.registerCountryflag.value
                      : '🇮🇳',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(4.w),
                Text(
                  "+${controller.registerCountryCode.value.isNotEmpty ? controller.registerCountryCode.value : '91'}",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(16.w),
                Text(
                  "|",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.greyTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.selectedLogo.value = null;
    controller.quantityController.clear();
    controller.vcardController.clear();
    controller.selectedVcardId.value = -1;
    controller.companyNameController.clear();
    controller.nameController.clear();
    controller.emailController.clear();
    controller.phoneController.clear();
    controller.registerCountryCode.value = '91';
    controller.registerCountryflag.value = '🇮🇳';
    controller.designationController.clear();
    controller.addressController.clear();
    controller.paymentMethodController.clear();
    controller.selectedPaymentMethod.value = -1;
    controller.searchVcadrController.clear();
    controller.vcardSearchText.value = '';
  }
}
