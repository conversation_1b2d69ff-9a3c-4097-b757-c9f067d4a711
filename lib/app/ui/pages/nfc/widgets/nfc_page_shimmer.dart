import 'package:v_card/app/utils/helpers/exporter.dart';

class NfcShimmerEffect extends StatelessWidget {
  final bool isGrid;
  final bool controller;

  const NfcShimmerEffect({
    super.key,
    this.isGrid = false,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return isGrid ? _buildGridShimmer() : _buildListShimmer();
  }

  Widget _buildGridShimmer() {
    return GridView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 4.w,
        mainAxisSpacing: 4.h,
        childAspectRatio: 0.8,
      ),
      itemCount: 6,
      itemBuilder: (context, index) {
        return Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: ShimmerBox(
                  width: double.infinity,
                  height: double.infinity,
                  borderRadius: BorderRadius.circular(12.r),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(8.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShimmerBox(width: double.infinity, height: 16.h),
                    Gap(4.h),
                    ShimmerBox(width: 60.w, height: 14.h),
                    Gap(4.h),
                    ShimmerBox(width: double.infinity, height: 28.h),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildListShimmer() {
    return ListView.separated(
      padding: EdgeInsets.symmetric(
        vertical: controller ? 78.h : 8.h,
        horizontal: 16.w,
      ),
      itemCount: 12,
      separatorBuilder: (_, index) => Gap(8.h),
      itemBuilder: (context, index) {
        return Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12.r),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 6.r,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Left side - Profile image
              ShimmerBox(height: 48.h, width: 48.w),
              Gap(12.w),
              // Middle - Card information
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShimmerBox(width: 120.w, height: 16.h),
                    Gap(4.h),
                    ShimmerBox(width: 160.w, height: 14.h),
                    Gap(4.h),
                    Row(
                      children: [
                        ShimmerBox(width: 12.w, height: 12.h),
                        Gap(4.w),
                        ShimmerBox(
                          width: 80.w,
                          height: 12.h,
                          borderRadius: BorderRadius.circular(100.r),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Gap(8.w),
              ShimmerBox(width: 60.w, height: 20.h),
            ],
          ),
        );
      },
    );
  }
}
