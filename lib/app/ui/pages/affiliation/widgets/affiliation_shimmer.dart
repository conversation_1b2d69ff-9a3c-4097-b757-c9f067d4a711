// affiliation_shimmers.dart
import 'package:v_card/app/utils/helpers/exporter.dart';

class AffiliationShimmer extends StatelessWidget {
  const AffiliationShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.all(16),
      children: [
        _AffiliationInfoCard(hasTrailingIcon: true),
        Gap(16.h),
        _AffiliationInfoCard(),
        Gap(16.h),
        _AffiliationInfoCard(),
        Gap(16.h),
        _AffiliationInfoCard(hasLongContent: true),
        Gap(16.h),
        ShimmerBox(
          height: 50,
          width: double.infinity,
          borderRadius: BorderRadius.circular(12),
        ),
      ],
    );
  }
}

class AffiliationListShimmer extends StatelessWidget {
  const AffiliationListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.all(16),
      children: List.generate(
        5,
        (index) => Padding(
          padding: EdgeInsets.only(bottom: 8),
          child: _AffiliationListCard(),
        ),
      ),
    );
  }
}

class WithdrawalListShimmer extends StatelessWidget {
  const WithdrawalListShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: EdgeInsets.symmetric(horizontal: 16),
      children: [
        ...List.generate(
          5,
          (index) => Padding(
            padding: EdgeInsets.only(bottom: 8),
            child: _WithdrawalCard(),
          ),
        ),
      ],
    );
  }
}

class _AffiliationInfoCard extends StatelessWidget {
  final bool hasTrailingIcon;
  final bool hasLongContent;

  const _AffiliationInfoCard({
    this.hasTrailingIcon = false,
    this.hasLongContent = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    ShimmerBox(
                      width: 20,
                      height: 20,
                      borderRadius: BorderRadius.circular(100),
                    ),
                    Gap(8.w),
                    ShimmerBox(
                      width: 120,
                      height: 20,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
                if (hasTrailingIcon)
                  ShimmerBox(
                    width: 28,
                    height: 28,
                    borderRadius: BorderRadius.circular(4),
                  ),
              ],
            ),
            Gap(8.h),
            ShimmerBox(
              width: hasLongContent ? double.infinity : 150,
              height: hasLongContent ? 40 : 20,
              borderRadius: BorderRadius.circular(4),
            ),
            if (hasLongContent) ...[
              Gap(4.h),
              ShimmerBox(
                width: 200,
                height: 20,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _AffiliationListCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    ShimmerBox(
                      width: 40,
                      height: 40,
                      borderRadius: BorderRadius.circular(100),
                    ),
                    Gap(12.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ShimmerBox(
                          width: 120,
                          height: 16,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        Gap(4.h),
                        ShimmerBox(
                          width: 150,
                          height: 14,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ],
                    ),
                  ],
                ),
                ShimmerBox(
                  width: 70,
                  height: 26,
                  borderRadius: BorderRadius.circular(16),
                ),
              ],
            ),
            Gap(12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [_buildInfoItemShimmer(), _buildInfoItemShimmer()],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItemShimmer() {
    return Row(
      children: [
        ShimmerBox(
          width: 16,
          height: 16,
          borderRadius: BorderRadius.circular(100),
        ),
        Gap(8.w),
        ShimmerBox(
          width: 80,
          height: 16,
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }
}

class _WithdrawalCard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ShimmerBox(
                  width: 180,
                  height: 18,
                  borderRadius: BorderRadius.circular(4),
                ),
                ShimmerBox(
                  width: 80,
                  height: 26,
                  borderRadius: BorderRadius.circular(16),
                ),
              ],
            ),
            Gap(8.h),
            _buildWithdrawalInfoItemShimmer(),
            Gap(8.h),
            _buildWithdrawalInfoItemShimmer(),
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalInfoItemShimmer() {
    return Row(
      children: [
        ShimmerBox(
          width: 16,
          height: 16,
          borderRadius: BorderRadius.circular(100),
        ),
        Gap(8.w),
        Row(
          children: [
            ShimmerBox(
              width: 60,
              height: 16,
              borderRadius: BorderRadius.circular(4),
            ),
            Gap(4.w),
            ShimmerBox(
              width: 100,
              height: 16,
              borderRadius: BorderRadius.circular(4),
            ),
          ],
        ),
      ],
    );
  }
}

class ShimmerWithdrawalDetail extends StatelessWidget {
  const ShimmerWithdrawalDetail({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              ShimmerBox(
                width: 60,
                height: 60,
                borderRadius: BorderRadius.circular(100),
              ),
              Gap(16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShimmerBox(
                      height: 20,
                      width: 150,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    Gap(4),
                    ShimmerBox(
                      height: 16,
                      width: 100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ),
              ShimmerBox(
                width: 80,
                height: 30,
                borderRadius: BorderRadius.circular(16),
              ),
            ],
          ),
          Gap(24),
          ...List.generate(6, (index) => _buildDetailItemShimmer()),
          Gap(24),
          ShimmerBox(
            height: 50,
            width: double.infinity,
            borderRadius: BorderRadius.circular(12),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItemShimmer() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ShimmerBox(
            height: 14,
            width: 80,
            borderRadius: BorderRadius.circular(2),
          ),
          Gap(4.h),
          ShimmerBox(
            height: 18,
            width: double.infinity,
            borderRadius: BorderRadius.circular(4),
          ),
          Divider(height: 24),
        ],
      ),
    );
  }
}
