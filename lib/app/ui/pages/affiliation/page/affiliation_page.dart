import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:v_card/app/controllers/affiliation_controller.dart';
import 'package:v_card/app/data/model/affiliation/afiiliation_detail_model.dart';
import 'package:v_card/app/ui/pages/affiliation/widgets/affiliation_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class AffiliationPage extends GetItHook<AffiliationController> {
  const AffiliationPage({super.key});

  @override
  Widget build(BuildContext context) {
    final RxInt currentTabIndex = 0.obs;

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_affiliations,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      bottomNavigationBar: Obx(() {
        if (currentTabIndex.value == 2) {
          return Padding(
            padding: EdgeInsets.all(16),
            child: CustomElevatedButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.withdrawalRequestPage,
                  ),
              text: AppStrings.T.btn_withdraw_amount,
            ),
          );
        } else {
          return SizedBox.shrink();
        }
      }),
      body: Column(
        children: [
          // Tab selector
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 8.h),
              decoration: BoxDecoration(
                color: Get.theme.customColors.textfieldFillColor,
                borderRadius: BorderRadius.circular(14.r),
                border: Border.all(color: Get.theme.primaryColor, width: 1),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildTabButton(
                    context,
                    index: 0,
                    currentIndex: currentTabIndex,
                    label: AppStrings.T.lbl_affiliations,
                  ),
                  _buildTabButton(
                    context,
                    index: 1,
                    currentIndex: currentTabIndex,
                    label: AppStrings.T.lbl_affiliation_list,
                  ),
                  _buildTabButton(
                    context,
                    index: 2,
                    currentIndex: currentTabIndex,
                    label: AppStrings.T.lbl_withdrawals,
                  ),
                ],
              ),
            ),
          ),
          // Tab content
          Expanded(
            child: Obx(() {
              switch (currentTabIndex.value) {
                case 0:
                  return _buildAffiliationTab();
                case 1:
                  return _buildAffiliationListTab();
                case 2:
                  return _buildWithdrawalsTab();
                default:
                  return _buildAffiliationTab();
              }
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTabButton(
    BuildContext context, {
    required int index,
    required RxInt currentIndex,
    required String label,
  }) {
    return Obx(() {
      final isSelected = currentIndex.value == index;
      return GestureDetector(
        onTap: () => currentIndex.value = index,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(100.r),
            color:
                isSelected
                    ? Get.theme.customColors.primaryColor?.withValues(
                      alpha: 0.1,
                    )
                    : Get.theme.customColors.transparent,
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
            child: Text(
              label,
              style: Get.theme.textTheme.labelMedium?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildAffiliationTab() {
    return RefreshIndicator(
      onRefresh: () async => controller.refreshData(),
      // onRefresh: () async => await controller.getAffiliationUrl(),
      child: Obx(() {
        if (controller.isgetAffiliationLoading.value) {
          return AffiliationShimmer();
        }

        final data = controller.affiliationData.value?.data.firstOrNull;
        if (data == null) {
          return _buildEmptyState(
            message: AppStrings.T.msg_no_affiliation_records,
            // "No affiliation data found",
            onRefresh: () async => controller.refreshData(),
            // onRefresh: () => controller.getAffiliationUrl(),
          );
        }

        return SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildCopyCard(
                onLongPress: () {
                  Clipboard.setData(ClipboardData(text: data.affiliationUrl));
                  toastification.show(
                    style: ToastificationStyle.flatColored,
                    type: ToastificationType.success,
                    showProgressBar: false,
                    alignment: Alignment.topCenter,
                    description: Text(
                      AppStrings.T.msg_url_copied,
                      // 'Affiliation URL copied to clipboard',
                      style: GoogleFonts.koHo(
                        fontSize: 16.0.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    autoCloseDuration: const Duration(seconds: 3),
                  );
                },
                title: AppStrings.T.lbl_affiliation_url,
                content: data.affiliationUrl,
                icon: Icons.link,
                trailingIcon: GestureDetector(
                  onTap: () {
                    Clipboard.setData(ClipboardData(text: data.affiliationUrl));
                    toastification.show(
                      style: ToastificationStyle.flatColored,
                      type: ToastificationType.success,
                      showProgressBar: false,
                      alignment: Alignment.topCenter,
                      description: Text(
                        AppStrings.T.msg_url_copied,
                        style: GoogleFonts.koHo(
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      autoCloseDuration: const Duration(seconds: 3),
                    );
                  },
                  child: Container(
                    width: 36.w,
                    height: 36.h,
                    decoration: BoxDecoration(
                      color: Get.theme.customColors.blueColor?.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: CustomImageView(
                      margin: EdgeInsets.all(6),
                      imagePath: AssetConstants.icCopy,
                    ),
                  ),
                ),
              ),
              Gap(16),
              _buildInfoCard(
                title: AppStrings.T.lbl_total_amount,
                content: "\$${data.totalAmount}",
                icon: Icons.attach_money,
              ),
              Gap(16),
              _buildInfoCard(
                title: AppStrings.T.lbl_current_amount,
                content: "\$${data.currentAmount.toString()}",
                icon: Icons.account_balance_wallet,
              ),
              Gap(16.h),
              ...[
                _buildInfoCard(
                  title: AppStrings.T.lbl_note,
                  content: data.note,
                  icon: Icons.note,
                ),
                Gap(16.h),
              ],
              if (data.howItWorks != null) ...[
                _buildInfoCard(
                  title: AppStrings.T.lbl_how_it_works,
                  content: data.howItWorks.toString(),
                  icon: Icons.help_outline,
                ),
                Gap(16.h),
              ],
              CustomElevatedButton(
                onPressed: () {
                  Share.share(data.affiliationUrl);
                },
                text: AppStrings.T.btn_share_affiliation_link,
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildAffiliationListTab() {
    return RefreshIndicator(
      onRefresh: () async => controller.refreshData(),
      // onRefresh: () async => await controller.getAffiliationList(),
      child: Obx(() {
        if (controller.isAffiliationListLoading.value) {
          return AffiliationListShimmer();
        }

        final list = controller.affiliationListData.value?.data ?? [];
        if (list.isEmpty) {
          return _buildEmptyState(
            message: AppStrings.T.msg_no_affiliation_records,
            onRefresh: () async => controller.refreshData(),
            // onRefresh: () => controller.getAffiliationList(),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.all(16),
          itemCount: list.length,
          itemBuilder: (context, index) {
            final item = list[index];
            return _buildAffiliationListItem(item, index);
          },
        );
      }),
    );
  }

  // In lib/app/ui/pages/affiliation/affiliation_page.dart
  Widget _buildWithdrawalsTab() {
    return RefreshIndicator(
      onRefresh: () async => controller.refreshData(),
      // onRefresh: () => controller.getWithdrawalList(),
      child: Padding(
        padding: const EdgeInsets.only(top: 16.0),
        child: Column(
          children: [
            Expanded(
              child: Obx(() {
                if (controller.isWithdrawalListLoading.value) {
                  return WithdrawalListShimmer();
                }

                final withdrawals =
                    controller.withdrawalListData.value?.data ?? [];
                if (withdrawals.isEmpty) {
                  return _buildWithdrawalEmptyState(
                    message: AppStrings.T.msg_no_withdrawal_records,
                  );
                }

                return ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  itemCount: withdrawals.length,
                  itemBuilder: (context, index) {
                    final withdrawal = withdrawals[index];
                    return _buildWithdrawalCard(withdrawal, index);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAffiliationListItem(AffiliationListData item, int index) {
    return Card(
      color: Get.theme.customColors.white,
      margin: EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildUserInfo(item.user, ""),
                _buildStatusChip(item.isVerified == 1),
              ],
            ),
            Gap(12.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildInfoItem(
                  AppStrings.T.lbl_amount,
                  "\$${item.amount}",
                  Icons.attach_money,
                ),
                _buildInfoItem(
                  AppStrings.T.lbl_date,
                  item.createdAt.split("T").first,
                  Icons.calendar_today,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWithdrawalCard(WithdrawalData withdrawal, int index) {
    return GestureDetector(
      onTap: () => _showWithdrawalDetails(withdrawal.id),
      child: Card(
        color: Get.theme.customColors.white,
        margin: EdgeInsets.only(bottom: 16),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${withdrawal.user?.fullName}",
                    style: Get.theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  _buildWithdrawalStatusChip(withdrawal.isApproved),
                ],
              ),
              Gap(8.h),
              _buildInfoItem(
                AppStrings.T.lbl_amount,
                "\$${withdrawal.amount}",
                Icons.attach_money,
              ),
              Gap(8.h),
              _buildInfoItem(
                AppStrings.T.lbl_date,
                withdrawal.createdAt.split("T").first,
                Icons.calendar_today,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showWithdrawalDetails(int withdrawalId) {
    Get.bottomSheet(
      Obx(() {
        if (controller.isshowWithdrawalLoading.value) {
          return _buildBottomSheetContainer(
            child: Center(child: ShimmerWithdrawalDetail()),
          );
        }

        final withdrawal = controller.withdrawalRequestData.value?.data;
        if (withdrawal == null) {
          return _buildBottomSheetContainer(
            child: Center(child: Text(AppStrings.T.msg_no_withdrawal_records)),
          );
        }

        return _buildBottomSheetContainer(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 60.w,
                    height: 60.h,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Get.theme.colorScheme.primary.withValues(
                        alpha: 0.1,
                      ),
                    ),
                    child: Icon(
                      Icons.account_balance_wallet,
                      size: 30,
                      color: Get.theme.colorScheme.primary,
                    ),
                  ),
                  Gap(16.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.T.lbl_withdrawal_details,
                          style: Get.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Gap(4.h),
                        Text(
                          "#${withdrawal.id}",
                          style: Get.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                  _buildWithdrawalStatusChip(withdrawal.isApproved),
                ],
              ),
              Gap(24.h),
              _buildDetailItem(
                AppStrings.T.lbl_amount,
                "\$${withdrawal.amount}",
              ),
              _buildDetailItem(
                AppStrings.T.lbl_status,
                _getWithdrawalStatus(withdrawal.isApproved),
              ),
              _buildDetailItem(
                AppStrings.T.lbl_request_date,
                withdrawal.createdAt.split("T").first,
              ),
              _buildDetailItem(
                AppStrings.T.lbl_processed_date,
                withdrawal.updatedAt.split("T").first,
              ),
              _buildDetailItem(AppStrings.T.lbl_email, withdrawal.email),
              _buildDetailItem(
                AppStrings.T.lbl_bank_details,
                withdrawal.bankDetails,
              ),
              if (withdrawal.rejectionNote != null)
                _buildDetailItem(
                  AppStrings.T.lbl_rejection_reason,
                  withdrawal.rejectionNote!,
                ),
              Gap(24.h),
              if (withdrawal.isApproved == 0) // If pending
                CustomElevatedButton(
                  text: AppStrings.T.btn_cancel_withdrawal,
                  onPressed: () {
                    NavigationService.navigateBack();
                    toastification.show(
                      type: ToastificationType.error,
                      style: ToastificationStyle.flatColored,
                      alignment: Alignment.topCenter,
                      description: Text(AppStrings.T.msg_cancel_pending),
                      autoCloseDuration: const Duration(seconds: 3),
                    );
                  },
                ),
            ],
          ),
        );
      }),
      isScrollControlled: true,
    );

    controller.showWithdrawalRequest(withdrawalId.toString());
  }

  Widget _buildBottomSheetContainer({required Widget child}) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Get.theme.colorScheme.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: SingleChildScrollView(child: child),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Get.theme.textTheme.bodySmall?.copyWith(
              color: Get.theme.customColors.primaryColor,
            ),
          ),
          Gap(4.h),
          Text(
            value,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Divider(height: 24),
        ],
      ),
    );
  }

  Widget _buildUserInfo(AffiliatedUser? user, String title) {
    if (user == null) return SizedBox();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            CircleAvatar(
              backgroundImage: NetworkImage(user.profileImage),
              radius: 20,
            ),
            Gap(12.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.fullName,
                  style: Get.theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(user.email, style: Get.theme.textTheme.bodySmall),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCopyCard({
    required String title,
    required String content,
    required IconData icon,
    Widget? trailingIcon,
    GestureLongPressCallback? onLongPress,
  }) {
    return GestureDetector(
      onLongPress: onLongPress,
      child: Card(
        color: Get.theme.customColors.white,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(icon, size: 20),
                        Gap(8.w),
                        Text(
                          title,
                          style: Get.theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    Gap(8.w),
                    Text(
                      content,
                      style: Get.theme.textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 36.w,
                height: 36.h,
                child: trailingIcon ?? Gap(0),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required String content,
    required IconData icon,
    Widget? trailingIcon,
    GestureLongPressCallback? onLongPress,
  }) {
    return GestureDetector(
      onLongPress: onLongPress,
      child: Card(
        color: Get.theme.customColors.white,
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(icon, size: 20),
                      Gap(8.w),
                      Text(
                        title,
                        style: Get.theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    width: 36.w,
                    height: 36.h,
                    child: trailingIcon ?? Gap(0),
                  ),
                ],
              ),
              Gap(8.h),
              Text(content, style: Get.theme.textTheme.bodyMedium),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Get.theme.customColors.darkGreyTextColor),
        Gap(8.w),
        Text(
          "$label: ",
          style: Get.theme.textTheme.bodySmall?.copyWith(
            color: Get.theme.customColors.darkGreyTextColor,
          ),
        ),
        Text(
          value,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusChip(bool isVerified) {
    return Chip(
      label: Text(
        isVerified ? AppStrings.T.lbl_verified : AppStrings.T.lbl_pending,
        style: Get.theme.textTheme.labelMedium!.copyWith(
          color: isVerified ? Colors.green : Colors.orange,
        ),
      ),
      backgroundColor:
          isVerified
              ? Colors.green.withValues(alpha: 0.2)
              : Colors.orange.withValues(alpha: 0.2),
      labelStyle: TextStyle(color: isVerified ? Colors.green : Colors.orange),
      shape: StadiumBorder(
        side: BorderSide(color: isVerified ? Colors.green : Colors.orange),
      ),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      visualDensity: VisualDensity.compact,
    );
  }

  Widget _buildWithdrawalStatusChip(int status) {
    String label;
    Color color;

    switch (status) {
      case 0:
        label = "Pending";
        color = Colors.orange;
        break;
      case 1:
        label = "Approved";
        color = Colors.green;
        break;
      case 2:
        label = "Reject";
        color = Colors.red;
        break;
      default:
        label = "Pending";
        color = Colors.orange;
    }

    return Chip(
      label: Text(label),
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 0),
      visualDensity: VisualDensity.compact,
      backgroundColor: color.withValues(alpha: 0.2),
      labelStyle: TextStyle(color: color),
      shape: StadiumBorder(side: BorderSide(color: color)),
    );
  }

  String _getWithdrawalStatus(int status) {
    switch (status) {
      case 1:
        return "Approved";
      case 2:
        return "Rejected";
      default:
        return "Pending";
    }
  }

  Widget _buildEmptyState({
    required String message,
    RefreshCallback? onRefresh,
  }) {
    return RefreshIndicator(
      onRefresh: onRefresh!,
      child: ListView(
        physics: const AlwaysScrollableScrollPhysics(),
        children: [
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Gap(Get.height * 0.35),
                Icon(Icons.info_outline, size: 48, color: Colors.grey),
                Gap(16),
                Text(message, style: Get.theme.textTheme.bodyLarge),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWithdrawalEmptyState({required String message}) {
    return ListView(
      physics: const AlwaysScrollableScrollPhysics(),
      children: [
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Gap(Get.height * 0.30),
              Icon(Icons.info_outline, size: 48, color: Colors.grey),
              Gap(16),
              Text(message, style: Get.theme.textTheme.bodyLarge),
            ],
          ),
        ),
      ],
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.refreshData();
    // controller.getAffiliationUrl();
    // controller.getAffiliationList();
    // controller.getWithdrawalList();
  }
}
