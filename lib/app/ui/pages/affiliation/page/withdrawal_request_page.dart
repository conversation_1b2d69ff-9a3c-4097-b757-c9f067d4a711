import 'package:get/get.dart';
import 'package:v_card/app/controllers/affiliation_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class WithdrawalRequestPage extends GetItHook<AffiliationController> {
  WithdrawalRequestPage({super.key});
  
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: Text(
          AppStrings.T.lbl_withdraw_funds,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: IgnorePointer(
        ignoring: controller.iscreateWithdrawalLoading.value,
        child: SingleChildScrollView(
          padding: EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Obx(
                  () => Text(
                    "${AppStrings.T.lbl_available_balance} \$${controller.affiliationData.value?.data.firstOrNull?.currentAmount ?? 0}",
                    style: Get.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Gap(24.h),
                TextInputField(
                  controller: controller.amountController,
                  keyboardType: TextInputType.number,
                  label: AppStrings.T.lbl_amount,
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_amount,
                      ),
                  type: InputType.phoneNumber,
                ),
                Gap(16.h),
                TextInputField(
                  controller: controller.emailController,
                  keyboardType: TextInputType.emailAddress,
                  label: AppStrings.T.lbl_email,
                  validator: AppValidations.emailValidation,
                  type: InputType.phoneNumber,
                ),
                Gap(16.h),
                TextInputField(
                  controller: controller.bankDetailsController,
                  isCapitalized: true,
                  maxLines: 3,
                  label: AppStrings.T.lbl_bank_details,
                  validator:
                      (value) => AppValidations.validateRequired(
                        value,
                        fieldName: AppStrings.T.lbl_bank_details,
                      ),
                  type: InputType.text,
                  textInputAction: TextInputAction.done,
                ),
                Gap(32.h),
                Obx(
                  () => CustomElevatedButton(
                    checkConnectivity: true,
                    isLoading: controller.iscreateWithdrawalLoading.value,
                    onPressed: () {
                      if (controller.iscreateWithdrawalLoading.value) {
                        return;
                      }

                      if (_formKey.currentState?.validate() ?? false) {
                        controller.createWithdrawalRequest();
                      }
                    },
                    text: AppStrings.T.btn_submit_withdrawal_request,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.amountController.clear();
    controller.emailController.clear();
    controller.bankDetailsController.clear();
  }

  @override
  void onInit() {}
}
