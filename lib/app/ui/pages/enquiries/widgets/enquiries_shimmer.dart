import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class EnquiriesShimmer extends StatelessWidget {
  final bool controller;

  const EnquiriesShimmer(this.controller, {super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.separated(
        itemCount: 10,
        padding: EdgeInsets.symmetric(
          vertical: controller ? 78.h : 8.h,
          horizontal: 16.w,
        ),

        itemBuilder: (context, index) {
          return ShimmerEnquiryItem();
        },
        separatorBuilder: (_, index) => Gap(8.h),
      ),
    );
  }
}

class ShimmerEnquiryItem extends StatelessWidget {
  const ShimmerEnquiryItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 3,
      color: Get.theme.customColors.white,
      margin: const EdgeInsets.symmetric(vertical: 6),
      child: Container(
        height: 80,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Profile image placeholder
            Row(
              children: [
                ShimmerBox(width: 50.w, height: 50.h),

                Gap(16),
                // Text content placeholders
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ShimmerBox(height: 16, width: 150),
                    Gap(8),
                    ShimmerBox(height: 12, width: 100),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ShimmerEnquiryDetailBottomSheet extends StatelessWidget {
  const ShimmerEnquiryDetailBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20.r),
          topRight: Radius.circular(20.r),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              // Profile image placeholder
              ShimmerBox(width: 50, height: 50),
              Gap(10),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShimmerBox(height: 16, width: 120),
                  Gap(8),
                  ShimmerBox(height: 12, width: 100),
                ],
              ),
            ],
          ),
          Gap(16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShimmerBox(height: 18, width: 120),
              Gap(4),
              ShimmerBox(height: 18, width: double.infinity),
            ],
          ),
          Gap(16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShimmerBox(height: 18, width: 120),
              Gap(4),
              ShimmerBox(height: 18, width: double.infinity),
            ],
          ),
          Gap(16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ShimmerBox(height: 18, width: 120),
              Gap(4),
              ShimmerBox(height: 18, width: double.infinity),
              Gap(4),
              ShimmerBox(height: 18, width: double.infinity),
            ],
          ),
          Gap(24),
          ShimmerBox(
            height: 45.h,
            width: double.infinity,
            borderRadius: BorderRadius.circular(12.r),
          ),
        ],
      ),
    );
  }
}
