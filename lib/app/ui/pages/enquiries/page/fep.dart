import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:v_card/app/controllers/enquiries_controller.dart';
import 'package:v_card/app/data/model/enquiries/enquiries_model.dart';
import 'package:v_card/app/ui/pages/enquiries/widgets/enquiries_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class FilterdEnquiriesPage extends GetItHook<EnquiriesController> {
  const FilterdEnquiriesPage({super.key});

  String? get vcardName => Get.arguments?['vcardName'] as String?;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Stack(
        children: [_buildEnquiriesListSection(), _buildSearchOverlay()],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppbar(
      title: AppText(
        AppStrings.T.lbl_enquiries,
        style: Get.theme.textTheme.bodyLarge?.copyWith(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      hasLeadingIcon: true,
      actions: [
        IconButton(
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          highlightColor: Colors.transparent,
          icon: Container(
            margin: EdgeInsets.only(left: 8.w),
            height: 40.h,
            width: 40.w,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Get.theme.customColors.textfieldFillColor!,
              ),
            ),
            child: CustomImageView(
              imagePath: AssetConstants.icSearch,
              margin: const EdgeInsets.all(8.0),
            ),
          ),
          onPressed: controller.toggleFepSearchMode,
        ),
      ],
    );
  }

  Widget _buildSearchOverlay() {
    return Obx(
      () =>
          controller.isFepSearchActive.value
              ? Positioned(
                top: 10,
                left: 0,
                right: 0,
                child: Container(
                  color: Get.theme.scaffoldBackgroundColor,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: TextInputField(
                    controller: controller.fepSearchController,
                    focusNode: controller.fepSearchFocusNode,
                    label: AppStrings.T.lbl_search_enquiries,
                    onChanged:
                        (value) => controller.fepSearchText.value = value,
                    type: InputType.text,
                    textInputAction: TextInputAction.done,
                    suffixIcon:
                        controller.fepSearchText.value.isNotEmpty
                            ? IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Get.theme.customColors.greyTextColor,
                              ),
                              onPressed: () {
                                controller.fepSearchController.clear();
                                controller.fepSearchText.value = '';
                                controller.toggleFepSearchMode();
                              },
                            )
                            : null,
                  ),
                ),
              )
              : const SizedBox.shrink(),
    );
  }

  Widget _buildEnquiriesListSection() {
    return Obx(() {
      final hasData =
          controller.filteredEnquiriesList.value?.data.isNotEmpty ?? false;

      // Show shimmer during initial load/refresh with no existing data
      if ((!controller.hasFilteredInitialData.value ||
              controller.isFilteredLoading.value) &&
          !hasData) {
        return EnquiriesShimmer(controller.isFepSearchActive.value);
      }

      // If we have data, show the list
      if (hasData) {
        return _buildEnquiriesList();
      }

      // Show proper empty state based on connection
      return RefreshIndicator(
        onRefresh: () {
          if (controller.isConnected.value) {
            controller.filteredEnquiriesList.value = null;
            return controller.getFilteredEnquiriesList();
          }
          return Future.value();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: Get.height * 0.8,
            child: Center(
              child:
                  controller.isConnected.value
                      ? NoDataWidget(
                        message:
                            vcardName != null
                                ? "${AppStrings.T.lbl_no_enquiries_found} for $vcardName"
                                : AppStrings.T.lbl_no_enquiries_found,
                        padding: EdgeInsets.zero,
                      )
                      : NoInternetWidget(
                        message: AppStrings.T.no_internet_connection,
                      ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildEnquiriesList() {
    return Obx(() {
      final allEnquiries = controller.fullFilteredEnquiriesList;
      final filteredEnquiries = _getFilteredEnquiries(allEnquiries);

      if (filteredEnquiries.isEmpty) {
        return RefreshIndicator(
          onRefresh: () {
            if (controller.isConnected.value) {
              controller.filteredEnquiriesList.value = null;
              return controller.getFilteredEnquiriesList();
            }
            return Future.value();
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: SizedBox(
              height: Get.height * 0.8,
              child: Center(
                child: NoDataWidget(
                  message:
                      controller.isFepSearchActive.value &&
                              controller.fepSearchText.value.isNotEmpty
                          ? "${AppStrings.T.lbl_no_results_found_for} '${controller.fepSearchText.value}'"
                          : vcardName != null
                          ? "${AppStrings.T.lbl_no_enquiries_found} for $vcardName"
                          : AppStrings.T.lbl_no_enquiries_found,
                  padding: EdgeInsets.zero,
                ),
              ),
            ),
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: () {
          if (controller.isConnected.value) {
            controller.filteredEnquiriesList.value = null;
            return controller.getFilteredEnquiriesList();
          }
          return Future.value();
        },
        child: ListView.separated(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: EdgeInsets.only(
            top: controller.isFepSearchActive.value ? 78.h : 8.h,
            left: 16.w,
            right: 16.w,
            bottom: 8.h,
          ),
          itemCount: filteredEnquiries.length,
          itemBuilder: (context, index) {
            final enquiry = filteredEnquiries[index];
            return _buildEnquiryTile(enquiry);
          },
          separatorBuilder: (_, __) => Gap(8.h),
        ),
      );
    });
  }

  List<EnquiriesData> _getFilteredEnquiries(List<EnquiriesData> allEnquiries) {
    // First filter by vCard name if provided
    var filteredByVCard =
        vcardName != null
            ? allEnquiries
                .where(
                  (enquiry) =>
                      enquiry.vcardName?.toLowerCase() ==
                      vcardName?.toLowerCase(),
                )
                .toList()
            : allEnquiries;

    // Then apply search filter if active
    if (!controller.isFepSearchActive.value) return filteredByVCard;

    final searchLower = controller.fepSearchText.value.toLowerCase().trim();
    return filteredByVCard.where((enquiry) {
      return (enquiry.name?.toLowerCase().contains(searchLower) ?? false) ||
          (enquiry.email?.toLowerCase().contains(searchLower) ?? false) ||
          (enquiry.phone?.toString().toLowerCase().contains(searchLower) ??
              false) ||
          (enquiry.message?.toLowerCase().contains(searchLower) ?? false);
    }).toList();
  }

  Widget _buildEnquiryTile(EnquiriesData enquiry) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0.w),
      child: Card(
        elevation: 3,
        color: Get.theme.customColors.white,
        margin: EdgeInsets.symmetric(vertical: 3.h),
        child: ListTile(
          leading: Container(
            width: 50.w,
            height: 50.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Get.theme.customColors.primaryColor?.withValues(
                alpha: 0.1,
              ),
            ),
            child: CustomImageView(
              imagePath: AssetConstants.icPerson,
              margin: const EdgeInsets.all(16.0),
              color: Get.theme.customColors.primaryColor,
            ),
          ),
          title: Text(
            enquiry.name ?? AppStrings.T.lbl_no_name,
            style: Get.textTheme.bodyLarge,
          ),
          subtitle: Text(
            '${enquiry.vcardName} | ${_formatDate(enquiry.createdAt.toString())}',
            style: Get.textTheme.labelMedium,
          ),
          onTap: () => _openEnquiryDetailBottomSheet(enquiry.id!),
        ),
      ),
    );
  }

  Future<void> _openEnquiryDetailBottomSheet(int id) async {
    if (!controller.isConnected.value) {
      return;
    }

    // Immediately open bottom sheet
    Get.bottomSheet(
      Obx(() => _buildBottomSheetObserver()),
      isScrollControlled: true,
    );

    // Then trigger API call after bottom sheet is shown
    controller.getEnquiriesById(id: id);
  }

  Widget _buildBottomSheetObserver() {
    final detail = controller.enquiriesIdData.value;
    final isLoading = controller.isLoadingEnquiriesById.value;

    if (isLoading) return _shimmerBottomSheet();
    if (detail == null) return _noDetailBottomSheet();

    final enquiry = detail.data.first;
    return _buildBottomSheetContent(enquiry);
  }

  Widget _shimmerBottomSheet() =>
      _bottomSheetContainer(child: ShimmerEnquiryDetailBottomSheet());

  Widget _noDetailBottomSheet() => _bottomSheetContainer(
    child: Center(child: Text(AppStrings.T.lbl_no_details_available)),
  );

  Widget _bottomSheetContainer({required Widget child}) => Container(
    decoration: BoxDecoration(
      color: Get.theme.customColors.white,
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    // padding: const EdgeInsets.all(16.0),
    child: SingleChildScrollView(
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
            height: 5.0,
            width: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.r),
              color: Get.theme.customColors.primaryColor?.withValues(
                alpha: 0.6,
              ),
            ),
          ),
          Padding(padding: const EdgeInsets.all(20.0), child: child),
        ],
      ),
    ),
  );
  Widget _buildBottomSheetContent(EnquiriesData enquiry) =>
      _bottomSheetContainer(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50.w,
                  height: 50.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Get.theme.customColors.primaryColor?.withValues(
                      alpha: 0.1,
                    ),
                  ),
                  child: CustomImageView(
                    imagePath: AssetConstants.icPerson,
                    margin: const EdgeInsets.all(16.0),
                    color: Get.theme.customColors.primaryColor,
                  ),
                ),
                Gap(10.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      enquiry.name ?? AppStrings.T.lbl_no_name,
                      style: Get.textTheme.bodyLarge,
                    ),
                    Text(
                      '${AppStrings.T.lbl_vcard_name} : ${enquiry.vcardName}',
                      style: Get.textTheme.labelMedium,
                    ),
                  ],
                ),
              ],
            ),
            Gap(20.h),
            _buildDetailRow(AppStrings.T.lbl_email, enquiry.email ?? ''),
            _buildDetailRow(
              AppStrings.T.phoneNumberLabel,
              enquiry.phone?.toString() ?? '',
            ),
            _buildDetailRow(AppStrings.T.messageLabel, enquiry.message ?? ''),
            _buildDetailRow(
              AppStrings.T.lbl_created_at,
              _formatDate(enquiry.createdAt.toString()),
            ),
            Gap(24.h),
            CustomElevatedButton(
              text: AppStrings.T.lbl_delete,
              onPressed: () {
                NavigationService.navigateBack();
                _showDeleteConfirmation(enquiry.id ?? 0);
              },
            ),
          ],
        ),
      );

  void _showDeleteConfirmation(int id) {
    Get.dialog(
      LoadingConfirmationDialog(
        title: AppStrings.T.lbl_delete_enquiry,
        message: AppStrings.T.lbl_delete_enquiry_subtitle,
        onCancel: Get.back,
        onConfirm: () {
          controller.deleteEnquiriesById(id: id);
        },
        isLoading: controller.isLoadingEnquiriesDelete,
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Get.textTheme.labelSmall?.copyWith(
              color: Get.theme.customColors.greyTextColor,
            ),
          ),
          Gap(4.h),
          Text(value, style: Get.textTheme.bodyMedium),
        ],
      ),
    );
  }

  String _formatDate(String? rawDate) {
    if (rawDate == null) return '';
    try {
      final dateTime = DateTime.parse(rawDate);
      return DateFormat('dd MMM, y').format(dateTime);
    } catch (_) {
      return rawDate;
    }
  }

  @override
  bool get canDisposeController => false;

  @override
  void onInit() {
    controller.checkFilteredNetworkAndLoad();
  }

  @override
  void onDispose() {
    controller.closeFepSearch();
  }
}
