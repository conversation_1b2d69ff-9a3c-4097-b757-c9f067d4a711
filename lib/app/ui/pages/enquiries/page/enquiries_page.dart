import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:v_card/app/controllers/enquiries_controller.dart';
import 'package:v_card/app/data/model/enquiries/enquiries_model.dart';
import 'package:v_card/app/ui/pages/enquiries/widgets/enquiries_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class EnquiriesPage extends GetItHook<EnquiriesController> {
  const EnquiriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_enquiries,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        hasLeadingIcon: false,
        actions: [
          IconButton(
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
            highlightColor: Colors.transparent,
            icon: Container(
              margin: EdgeInsets.only(left: 8.w),
              height: 40.h,
              width: 40.w,
              decoration: BoxDecoration(
                color: Get.theme.customColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Get.theme.customColors.textfieldFillColor!,
                ),
              ),
              child: CustomImageView(
                imagePath: AssetConstants.icSearch,
                margin: const EdgeInsets.all(8.0),
              ),
            ),
            onPressed: controller.toggleSearchMode,
          ),
        ],
      ),
      body: Stack(
        children: [_buildEnquiriesListSection(), _buildSearchOverlay()],
      ),
    );
  }

  Widget _buildEnquiriesListSection() {
    return Obx(() {
      final hasOriginalData =
          controller.enquiriesList.value?.data.isNotEmpty ?? false;

      // Show shimmer only during initial load/refresh with no existing data
      if ((!controller.hasInitialData.value || controller.isLoading.value) &&
          !hasOriginalData) {
        return EnquiriesShimmer(controller.isSearchActive.value);
      }

      // If we have original data, check filtered results
      if (hasOriginalData) {
        return _buildEnquiriesList();
      }

      // Show proper empty state based on connection
      return RefreshIndicator(
        onRefresh: () => controller.checkNetworkAndLoad(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: Get.height * 0.7,
            child: Center(
              child:
                  controller.isConnected.value
                      ? NoDataWidget(
                        message: AppStrings.T.lbl_no_enquiries_found,
                        padding: EdgeInsets.zero,
                      )
                      : NoInternetWidget(
                        message: AppStrings.T.no_internet_connection,
                      ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildSearchOverlay() {
    return Obx(
      () =>
          controller.isSearchActive.value
              ? Positioned(
                top: 10,
                left: 0,
                right: 0,
                child: Container(
                  color: Get.theme.scaffoldBackgroundColor,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: TextInputField(
                    controller: controller.searchController,
                    focusNode: controller.searchFocusNode,
                    label: AppStrings.T.lbl_search_enquiries,
                    onChanged: (value) => controller.searchText.value = value,
                    type: InputType.text,
                    textInputAction: TextInputAction.done,
                    suffixIcon:
                        controller.searchText.value.isNotEmpty
                            ? IconButton(
                              icon: Icon(
                                Icons.close,
                                color: Get.theme.customColors.greyTextColor,
                              ),
                              onPressed: _clearSearch,
                            )
                            : null,
                  ),
                ),
              )
              : const SizedBox.shrink(),
    );
  }

  void _clearSearch() {
    controller.searchController.clear();
    controller.searchText.value = '';
    controller.toggleSearchMode();
  }

  Widget _buildEnquiriesList() {
    return RefreshIndicator(
      onRefresh: () {
        if (controller.isConnected.value) {
          controller.enquiriesList.value = null;
          return controller.getEnquiriesList();
        } else {
          return Future.value();
        }
      },
      child: Obx(() {
        final filteredEnquiries = _getFilteredEnquiries();

        return Padding(
          padding: EdgeInsets.only(
            top: controller.isSearchActive.value ? 78.h : 8.h,
          ),
          child:
              filteredEnquiries.isEmpty
                  ? _buildEmptyResultsWidget()
                  : ListView.separated(
                    itemCount: filteredEnquiries.length,
                    itemBuilder: (context, index) {
                      final enquiry = filteredEnquiries[index];
                      return _buildEnquiryTile(context, enquiry);
                    },
                    separatorBuilder: (_, __) => Gap(8.h),
                  ),
        );
      }),
    );
  }

  Widget _buildEmptyResultsWidget() {
    final args = Get.arguments;
    final isVCardFiltered = args != null && args['vcardName'] != null;
    final vcardName = isVCardFiltered ? args['vcardName'].toString() : '';

    String message;
    if (controller.isSearchActive.value &&
        controller.searchText.value.isNotEmpty) {
      message =
          "${AppStrings.T.lbl_no_results_found_for} '${controller.searchText.value}'";
    } else if (isVCardFiltered) {
      message = "No enquiries found for '$vcardName'";
    } else {
      message = AppStrings.T.lbl_no_enquiries_found;
    }

    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: SizedBox(
        height: Get.height * 0.7,
        child: Center(
          child: NoDataWidget(message: message, padding: EdgeInsets.zero),
        ),
      ),
    );
  }

  List<EnquiriesData> _getFilteredEnquiries() {
    final allEnquiries = controller.fullEnquiriesList;

    // First filter by vCard name if provided in arguments
    var filteredByVCard = allEnquiries;

    // Then apply search filter if active
    if (!controller.isSearchActive.value) return filteredByVCard;

    final searchLower = controller.searchText.value.toLowerCase().trim();
    return filteredByVCard.where((enquiry) {
      return (enquiry.name?.toLowerCase().contains(searchLower) ?? false) ||
          (enquiry.email?.toLowerCase().contains(searchLower) ?? false) ||
          (enquiry.phone?.toString().toLowerCase().contains(searchLower) ??
              false) ||
          (enquiry.message?.toLowerCase().contains(searchLower) ?? false);
    }).toList();
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.searchController.clear();
    controller.isSearchActive.value = false;
    controller.searchText.value = '';
    controller.searchFocusNode.unfocus();
    controller.getEnquiriesList();
  }

  @override
  void onInit() {
    controller.checkNetworkAndLoad();
  }

  Widget _buildEnquiryTile(BuildContext context, EnquiriesData enquiry) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Card(
        elevation: 3,
        color: Get.theme.customColors.white,
        margin: EdgeInsets.symmetric(vertical: 3.h),
        child: ListTile(
          leading: Container(
            width: 50.w,
            height: 50.h,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Get.theme.customColors.primaryColor?.withValues(
                alpha: 0.1,
              ),
            ),
            child: CustomImageView(
              imagePath: AssetConstants.icPerson,
              margin: const EdgeInsets.all(16.0),
              color: Get.theme.customColors.primaryColor,
            ),
          ),
          title: Text(
            enquiry.name ?? AppStrings.T.lbl_no_name,
            style: Get.textTheme.bodyLarge,
          ),
          subtitle: Text(
            '${enquiry.vcardName} | ${_formatDate(enquiry.createdAt.toString())}',
            style: Get.textTheme.labelMedium,
          ),
          onTap: () => _openEnquiryDetailBottomSheet(enquiry.id!),
        ),
      ),
    );
  }

  Future<void> _openEnquiryDetailBottomSheet(int id) async {
    if (!controller.isConnected.value) {
      return;
    }

    // Immediately open bottom sheet
    Get.bottomSheet(
      Obx(() => _buildBottomSheetObserver()),
      isScrollControlled: true,
    );

    // Then trigger API call after bottom sheet is shown
    controller.getEnquiriesById(id: id);
  }

  Widget _buildBottomSheetObserver() {
    final detail = controller.enquiriesIdData.value;
    final isLoading = controller.isLoadingEnquiriesById.value;

    if (isLoading) return _shimmerBottomSheet();
    if (detail == null) return _noDetailBottomSheet();

    final enquiry = detail.data.first;
    return _buildBottomSheetContent(enquiry);
  }

  Widget _shimmerBottomSheet() =>
      _bottomSheetContainer(child: ShimmerEnquiryDetailBottomSheet());

  Widget _noDetailBottomSheet() => _bottomSheetContainer(
    child: Center(child: Text(AppStrings.T.lbl_no_details_available)),
  );

  Widget _bottomSheetContainer({required Widget child}) => Container(
    decoration: BoxDecoration(
      color: Get.theme.customColors.white,
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    // padding: const EdgeInsets.all(16.0),
    child: SingleChildScrollView(
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
            height: 5.0,
            width: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.r),
              color: Get.theme.customColors.primaryColor?.withValues(
                alpha: 0.6,
              ),
            ),
          ),
          Padding(padding: const EdgeInsets.all(20.0), child: child),
        ],
      ),
    ),
  );

  Widget _buildBottomSheetContent(
    EnquiriesData enquiry,
  ) => _bottomSheetContainer(
    child: Column(
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50.w,
                  height: 50.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Get.theme.customColors.primaryColor?.withValues(
                      alpha: 0.1,
                    ),
                  ),
                  child: CustomImageView(
                    imagePath: AssetConstants.icPerson,
                    margin: const EdgeInsets.all(16.0),
                    color: Get.theme.customColors.primaryColor,
                  ),
                ),
                Gap(10.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      enquiry.name ?? AppStrings.T.lbl_no_name,
                      style: Get.textTheme.bodyLarge,
                    ),
                    Text(
                      '${AppStrings.T.lbl_vcard_name} : ${enquiry.vcardName}',
                      style: Get.textTheme.labelMedium,
                    ),
                  ],
                ),
              ],
            ),
            Gap(20.h),
            _buildDetailRow(AppStrings.T.lbl_email, enquiry.email ?? ''),
            _buildDetailRow(
              AppStrings.T.phoneNumberLabel,
              enquiry.phone?.toString() ?? '',
            ),
            _buildDetailRow(AppStrings.T.messageLabel, enquiry.message ?? ''),
            _buildDetailRow(
              AppStrings.T.lbl_created_at,
              _formatDate(enquiry.createdAt.toString()),
            ),
            Gap(24.h),
            CustomElevatedButton(
              text: AppStrings.T.lbl_delete,
              onPressed: () {
                NavigationService.navigateBack();
                _showDeleteConfirmation(enquiry.id ?? 0);
              },
            ),
          ],
        ),
      ],
    ),
  );

  void _showDeleteConfirmation(int id) {
    Get.dialog(
      LoadingConfirmationDialog(
        title: AppStrings.T.lbl_delete_enquiry,
        message: AppStrings.T.lbl_delete_enquiry_subtitle,
        onCancel: Get.back,
        onConfirm: () {
          controller.deleteEnquiriesById(id: id);
        },
        isLoading: controller.isLoadingEnquiriesDelete,
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Get.textTheme.labelSmall?.copyWith(
              color: Get.theme.customColors.greyTextColor,
            ),
          ),
          Gap(4.h),
          Text(value, style: Get.textTheme.bodyMedium),
        ],
      ),
    );
  }

  String _formatDate(String? rawDate) {
    if (rawDate == null) return '';
    try {
      final dateTime = DateTime.parse(rawDate);
      return DateFormat('dd MMM, y').format(dateTime);
    } catch (_) {
      return rawDate;
    }
  }
}
