import 'package:get/get.dart';
import 'package:v_card/app/controllers/business_card_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class GroupFilterBottomSheet extends StatelessWidget {
  final BusinesscardController controller;

  const GroupFilterBottomSheet({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      constraints: BoxConstraints(maxHeight: Get.height * 0.5),
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBottomSheetHandle(),
          Gap(12.h),
          _buildHeader(),
          Gap(16.h),
          _buildFilterLabel(),
          Gap(12.h),
          _buildGroupFilterChips(),
        ],
      ),
    );
  }

  Widget _buildBottomSheetHandle() {
    return Center(
      child: Container(
        height: 5.h,
        width: 50.h,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(100.r),
          color: Get.theme.customColors.primaryColor!.withValues(alpha: 0.3),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          AppStrings.T.lbl_select_group,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            fontSize: 16.sp,
            color: Get.theme.customColors.darkGreyTextColor,
            fontWeight: FontWeight.w600,
          ),
        ),
        TextButton(
          onPressed: () {
            controller.businessCardList.value = null;
            controller.selectedGroupId.value = -1;
            controller.getBusinessCardList();
            NavigationService.navigateBack();
          },
          child: Text(
            AppStrings.T.lbl_clear_filter,
            style: Get.textTheme.labelLarge?.copyWith(
              color: Get.theme.customColors.primaryColor,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterLabel() {
    return Text(
      AppStrings.T.lbl_filter_by_group,
      // "Filter by group:",
      style: Get.theme.textTheme.bodyMedium!.copyWith(
        fontSize: 14.sp,
        color: Get.theme.customColors.greyTextColor,
      ),
    );
  }

  Widget _buildGroupFilterChips() {
    return Obx(() {
      if (controller.isLoadingGroupList.value) {
        return const ShimmerChips();
      }
      final groups = controller.groupList.value?.data ?? [];

      return Flexible(
        child: SingleChildScrollView(
          child: Wrap(
            spacing: 6.w,
            runSpacing: 6.h,
            children: [
              // "All" chip
              // _buildFilterChip(
              //   isSelected: controller.selectedGroupId.value == -1,
              //   label: AppStrings.T.lbl_all,
              //   onSelected: (selected) {
              //     if (selected) {
              //       controller.selectedGroupId.value = -1;
              //       controller.getBusinessCardList();
              //       NavigationService.navigateBack(); // Close bottom sheet
              //     }
              //   },
              // ),

              // Group chips
              ...groups.map(
                (group) => _buildFilterChip(
                  isSelected: controller.selectedGroupId.value == group.id,
                  label: group.name ?? AppStrings.T.lbl_unknown,
                  onSelected: (selected) {
                    if (selected) {
                      controller.selectedGroupId.value = group.id!;
                      controller.getFilteredBusinessCardList();
                      NavigationService.navigateBack();
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildFilterChip({
    required bool isSelected,
    required String label,
    required Function(bool) onSelected,
  }) {
    return FilterChip(
      showCheckmark: false,
      selected: isSelected,
      label: Text(
        label,
        style: TextStyle(
          color:
              isSelected
                  ? Get.theme.customColors.white
                  : Get.theme.customColors.greyTextColor,
          fontWeight: FontWeight.w500,
        ),
      ),
      selectedColor: Get.theme.customColors.primaryColor ?? Colors.blue,
      backgroundColor: Get.theme.customColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Get.theme.customColors.greyTextColor!.withValues(alpha: 0.3),
        ),
      ),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
      onSelected: onSelected,
    );
  }
}
