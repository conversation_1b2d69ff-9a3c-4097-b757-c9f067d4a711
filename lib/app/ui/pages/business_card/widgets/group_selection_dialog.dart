import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:v_card/app/controllers/business_card_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class GroupSelectionDialog extends StatefulWidget {
  final String scannedData;
  final BusinesscardController controller;

  const GroupSelectionDialog({
    super.key,
    required this.scannedData,
    required this.controller,
  });

  @override
  State<GroupSelectionDialog> createState() => _GroupSelectionDialogState();
}

class _GroupSelectionDialogState extends State<GroupSelectionDialog> {
  int? selectedGroupId;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  void _showAddGroupDialog() {
    // final TextEditingController groupNameController = TextEditingController();
    // final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    Get.dialog(
      barrierDismissible: false,
      AlertDialog(
        title: Text(
          AppStrings.T.lbl_add_group,
          style: Get.textTheme.titleLarge!.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextInputField(
                controller: widget.controller.groupNameController,
                label: AppStrings.T.lbl_group_name,
                type: InputType.text,
                validator:
                    (value) => AppValidations.validateRequired(
                      value,
                      fieldName: AppStrings.T.lbl_group_name,
                    ),
              ),
              // Gap(16.h),
              // TextInputField(
              //   controller: widget.controller.vcardController,
              //   label: AppStrings.T.lbl_select_vcard,
              //   readOnly: true,
              //   type: InputType.text,
              //   onTap: () {
              //     widget.controller.showVcardBottomSheet(context);
              //   },
              //   suffixIcon: CustomImageView(
              //     imagePath: AssetConstants.icDownArrow2,
              //     margin: EdgeInsets.all(8.0),
              //   ),
              // ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed:
                widget.controller.isLoadingcreateGroup.value
                    ? null
                    : () => NavigationService.navigateBack(),
            child: Text(AppStrings.T.lbl_cancel),
          ),
          Obx(
            () => CustomElevatedButton(
              checkConnectivity: true,
              isLoading: widget.controller.isLoadingcreateGroup.value,
              width: 90.w,
              height: 32.h,
              text: AppStrings.T.lbl_add,
              onPressed:
                  widget.controller.isLoadingcreateGroup.value
                      ? null
                      : () async {
                        if (_formKey.currentState!
                            .validate()) {
                          // widget.controller.groupNameController.text =
                          //     groupNameController.text.trim();
                          await widget.controller.createGroup(
                            widget.controller.groupNameController.text.trim(),
                          );
                        }
                      },
            ),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteGroup(int groupId) {
    Get.dialog(
      barrierDismissible: false,
      AlertDialog(
        title: Text(AppStrings.T.lbl_delete_group),
        content: Text(AppStrings.T.lbl_confirm_delete_group),
        actions: [
          TextButton(
            onPressed: () => NavigationService.navigateBack(),
            child: Text(AppStrings.T.lbl_cancel),
          ),
          Obx(
            () => CustomElevatedButton(
              checkConnectivity: true,
              isLoading: widget.controller.isLoadingDeleteGroup.value,
              width: 90.w,
              height: 32.h,
              text: AppStrings.T.lbl_delete,
              onPressed: () async {
                widget.controller.selectedGroupId.value = groupId;
                await widget.controller.deleteGroup();
                widget.controller.selectedGroupId.value = -1;
              },
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Column(
        children: [
          Text(
            AppStrings.T.lbl_create_business_card,
            style: Get.textTheme.bodyLarge!.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          // Gap(8.h),
          // TextInputField(
          //   controller: widget.controller.vcardController,
          //   label: AppStrings.T.lbl_select_vcard,
          //   readOnly: true,
          //   type: InputType.text,
          //   onTap: () {
          //     widget.controller.showVcardBottomSheet(context);
          //   },
          //   suffixIcon: CustomImageView(
          //     imagePath: AssetConstants.icDownArrow2,
          //     margin: EdgeInsets.all(8.0),
          //   ),
          // ),
          Gap(8.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppStrings.T.lbl_select_group,
                style: Get.textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.w400,
                ),
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: _showAddGroupDialog,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100.r),
                        border: Border.all(
                          color: Get.theme.customColors.primaryColor!,
                        ),
                      ),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 4.h,
                        ),
                        child: Center(
                          child: Text(
                            AppStrings.T.lbl_add_group,
                            style: Get.theme.textTheme.labelMedium!.copyWith(
                              color: Get.theme.customColors.primaryColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
      content: Obx(() {
        if (widget.controller.isLoadingGroupList.value) {
          return SizedBox(
            width: double.maxFinite,
            height: Get.size.height * 0.25,
            child: ListView.builder(
              itemCount: 5,
              itemBuilder:
                  (_, __) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Shimmer.fromColors(
                      baseColor: Colors.grey.shade300,
                      highlightColor: Colors.grey.shade100,
                      child: Container(
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.grey.shade300,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
            ),
          );
        }

        final groups = widget.controller.groupList.value?.data ?? [];
        if (groups.isEmpty) {
          return SizedBox(
            width: double.maxFinite,
            height: Get.size.height * 0.25,
            child: Center(
              child: Text(
                AppStrings.T.lbl_no_groups_available,
                style: Get.textTheme.bodyMedium,
              ),
            ),
          );
        }

        return SizedBox(
          width: double.maxFinite,
          height: Get.size.height * 0.25,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: groups.length,
            itemBuilder: (context, index) {
              final group = groups[index];
              final isSelected = selectedGroupId == group.id;

              return GestureDetector(
                onLongPress: () => _confirmDeleteGroup(group.id!),
                onTap: () {
                  setState(() {
                    selectedGroupId = group.id!;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.only(left: 12, bottom: 2, top: 2),
                  decoration: BoxDecoration(
                    color:
                        isSelected
                            ? Get.theme.customColors.primaryColor!.withValues(
                              alpha: 0.1,
                            )
                            : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    // border: Border.all(
                    //   color:
                    //       isSelected
                    //           ? Get.theme.customColors.primaryColor!
                    //           : Colors.grey.shade300,
                    // ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Custom Radio Icon and Label
                      Expanded(
                        child: Row(
                          children: [
                            Icon(
                              size: 16,
                              isSelected
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_unchecked,
                              color:
                                  isSelected
                                      ? Get.theme.customColors.primaryColor
                                      : Colors.grey,
                            ),
                            Gap(8.w),
                            Expanded(
                              child: Text(
                                group.name ?? AppStrings.T.lbl_unnamed_group,
                                style: Get.textTheme.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Delete Icon
                      GestureDetector(
                        onTap: () => _confirmDeleteGroup(group.id!),
                        child: Container(
                          height: 40,
                          width: 40,
                          color: Colors.transparent,
                          child: CustomImageView(
                            margin: EdgeInsets.all(12.0),
                            imagePath: AssetConstants.icDelete2,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      }),
      actions: [
        TextButton(
          onPressed: () => NavigationService.navigateBack(),
          child: Text(AppStrings.T.lbl_cancel),
        ),
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            width: 90.w,
            height: 32.h,
            isLoading: widget.controller.isLoadingCreateBusiness.value,
            onPressed:
                widget.controller.isLoadingCreateBusiness.value
                    ? null
                    : () {
                      if (selectedGroupId == null) {
                        toastification.show(
                          type: ToastificationType.error,
                          style: ToastificationStyle.flatColored,
                          alignment: Alignment.topCenter,
                          description: Text(
                            AppStrings.T.lbl_please_select_group,
                          ),
                          autoCloseDuration: const Duration(seconds: 3),
                          showProgressBar: false,
                        );
                        return;
                      }
                      widget.controller.createBusinessCard(
                        widget.scannedData,
                        selectedGroupId.toString(),
                      );
                    },
            text: AppStrings.T.lbl_save,
          ),
        ),
      ],
    );
  }
}
