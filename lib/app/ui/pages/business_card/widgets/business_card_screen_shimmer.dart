import 'package:v_card/app/utils/helpers/exporter.dart';

class BusinessCardShimmer extends StatelessWidget {
  final bool controller;

  const BusinessCardShimmer(this.controller, {super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ListView.separated(
        padding: EdgeInsets.symmetric(vertical: controller ? 78.h : 8.h),

        itemCount: 5,
        itemBuilder: (context, index) {
          return ShimmerBusinessCardItem();
        },
        separatorBuilder: (_, index) => Gap(8.h),
      ),
    );
  }
}

class ShimmerBusinessCardItem extends StatelessWidget {
  const ShimmerBusinessCardItem({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                ShimmerBox(width: 36.w, height: 36.h),
                Gap(12.w),
                Expanded(
                  child: ShimmerBox(height: 20.h, width: double.infinity),
                ),
              ],
            ),
          ),
          Divider(color: Colors.grey[300]),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ShimmerBox(height: 14.h, width: 200.w),
                Gap(8.h),
                ShimmerBox(height: 50.h, width: double.infinity),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class ShimmerChips extends StatelessWidget {
  const ShimmerChips({super.key});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      spacing: 6.w,
      runSpacing: 6.h,
      children: List.generate(
        10, // Number of placeholder chips
        (index) => ShimmerBox(
          width: 80.w,
          height: 30.h,
          borderRadius: BorderRadius.circular(16),
        ),
      ),
    );
  }
}
