import 'package:get/get.dart';
import 'package:v_card/app/data/model/business/business_card_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class BusinessCardGroup extends StatelessWidget {
  final String groupName;
  final List<BusinessCardData> cards;
  final bool isLastGroup;

  const BusinessCardGroup({
    super.key,
    required this.groupName,
    required this.cards,
    this.isLastGroup = false,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Group header
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          child: Text(
            groupName,
            style: Get.theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Get.theme.customColors.darkGreyTextColor,
            ),
          ),
        ),

        // Business cards in this group
        ...cards.map((card) => BusinessCardItem(vCard: card)),

        // Add some space between groups
        if (!isLastGroup) Gap(16.h),
      ],
    );
  }
}

class BusinessCardItem extends StatelessWidget {
  final BusinessCardData vCard;

  const BusinessCardItem({super.key, required this.vCard});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: GestureDetector(
        // onTap: () => _showCardDetailsBottomSheet(vCard),
        child: Card(
          elevation: 3,
          color: Get.theme.customColors.white,
          margin: EdgeInsets.symmetric(vertical: 3.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [_buildCardHeader(vCard), _buildCardActions()],
          ),
        ),
      ),
    );
  }

  Widget _buildCardHeader(BusinessCardData vCard) {
    return Container(
      padding: EdgeInsets.all(16.w),
      child: Row(
        children: [
          vCard.vcardId != null
              ? CustomImageView(
                width: 50.w,
                height: 50.h,
                imagePath: vCard.vcardImage,
                fit: BoxFit.cover,
                radius: BorderRadius.circular(8.0),
              )
              : SizedBox.shrink(),
          Gap(vCard.id != null ? 12.w : 0),
          Expanded(
            child: Text(
              vCard.url ?? AppStrings.T.lbl_no_name,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Get.theme.textTheme.bodyLarge!.copyWith(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardActions() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0, left: 16.0, right: 16.0),
      child: SizedBox(
        height: 50.h,
        child: Row(
          children: [
            _buildActionButton(
              onTap: _shareViaWhatsApp,
              icon: AssetConstants.icWp2,
              isFirst: true,
            ),
            _buildActionButton(onTap: _openUrl, icon: AssetConstants.icView),
            _buildActionButton(
              onTap: _shareCard,
              icon: AssetConstants.icShare,
              isLast: true,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _shareViaWhatsApp() async {
    if (vCard.url != null && vCard.url!.isNotEmpty) {
      final message =
          vCard.vcardImage != null
              ? '${vCard.url}\n\n${vCard.vcardImage}'
              : vCard.url!;
      final encodedMessage = Uri.encodeComponent(message);
      final whatsappUrl = "https://wa.me/?text=$encodedMessage";

      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(Uri.parse(whatsappUrl));
      }
    }
  }

  Future<void> _openUrl() async {
    final url = Uri.parse(vCard.url.toString());
    await launchUrl(url, mode: LaunchMode.externalApplication);
  }

  Future<void> _shareCard() async {
    if (vCard.url != null && vCard.url!.isNotEmpty) {
      await Share.share(vCard.url!);
    }
  }

  // void _showCardDetailsBottomSheet(BusinessCardData vCard) {
  //   Get.bottomSheet(
  //     Container(
  //       padding: EdgeInsets.only(
  //         top: 4.h,
  //         bottom: 16.h,
  //         right: 16.w,
  //         left: 16.w,
  //       ),
  //       decoration: BoxDecoration(
  //         color: Get.theme.customColors.white,
  //         borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
  //       ),
  //       child: SingleChildScrollView(
  //         child: Column(
  //           crossAxisAlignment: CrossAxisAlignment.start,
  //           mainAxisSize: MainAxisSize.min,
  //           children: [
  //             Center(
  //               child: Container(
  //                 width: 50.w,
  //                 height: 5.h,
  //                 margin: EdgeInsets.only(bottom: 16.h),
  //                 decoration: BoxDecoration(
  //                   color: Get.theme.customColors.primaryColor?.withValues(
  //                     alpha: 0.6,
  //                   ),
  //                   borderRadius: BorderRadius.circular(2.r),
  //                 ),
  //               ),
  //             ),
  //             if (vCard.image != null) ...[
  //               Center(
  //                 child: CustomImageView(
  //                   width: 100.w,
  //                   height: 100.h,
  //                   imagePath: vCard.image,
  //                   fit: BoxFit.cover,
  //                   radius: BorderRadius.circular(12.0),
  //                 ),
  //               ),
  //               SizedBox(height: 16.h),
  //             ],
  //             _buildDetailItem(AppStrings.T.lbl_vcard_name, vCard.name),
  //             _buildDetailItem(AppStrings.T.lbl_occupation, vCard.occupation),
  //             _buildDetailItem(AppStrings.T.lbl_phone, vCard.phone),
  //           ],
  //         ),
  //       ),
  //     ),
  //     isScrollControlled: true,
  //   );
  // }

  // Widget _buildDetailItem(String label, String? value) {
  //   if (value == null || value.isEmpty) return SizedBox();

  //   return Padding(
  //     padding: EdgeInsets.symmetric(vertical: 8.h),
  //     child: Column(
  //       crossAxisAlignment: CrossAxisAlignment.start,
  //       children: [
  //         Text(
  //           label,
  //           style: Get.theme.textTheme.bodySmall!.copyWith(
  //             color: Get.theme.customColors.darkGreyTextColor,
  //           ),
  //         ),
  //         SizedBox(height: 4.h),
  //         Text(value, style: Get.theme.textTheme.bodyLarge),
  //       ],
  //     ),
  //   );
  // }

  Expanded _buildActionButton({
    required VoidCallback onTap,
    required String icon,
    bool isFirst = false,
    bool isLast = false,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 50.h,
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(
                color: Get.theme.customColors.lightGreyBackgroundColor!,
              ),
              bottom: BorderSide(
                color: Get.theme.customColors.lightGreyBackgroundColor!,
              ),
              left: BorderSide(
                color: Get.theme.customColors.lightGreyBackgroundColor!,
                width: isFirst ? 1 : 0.5,
              ),
              right: BorderSide(
                color: Get.theme.customColors.lightGreyBackgroundColor!,
                width: isLast ? 1 : 0.5,
              ),
            ),
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(isFirst ? 8 : 0),
              topLeft: Radius.circular(isFirst ? 8 : 0),
              bottomRight: Radius.circular(isLast ? 8 : 0),
              topRight: Radius.circular(isLast ? 8 : 0),
            ),
          ),
          child: CustomImageView(
            imagePath: icon,
            margin: const EdgeInsets.all(15.0),
            height: 24.0,
          ),
        ),
      ),
    );
  }
}
