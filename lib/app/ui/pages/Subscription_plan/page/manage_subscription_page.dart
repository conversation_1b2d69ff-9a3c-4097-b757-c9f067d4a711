import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:v_card/app/controllers/subscription_plan_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ManageSubscriptionPage extends GetItHook<SubscriptionPlanController> {
  const ManageSubscriptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppbar(
        title: Text(
          AppStrings.T.lbl_manage_subscription,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
        actions: [
          IconButton(
            padding: EdgeInsets.zero,
            constraints: BoxConstraints(),
            highlightColor: Colors.transparent,
            icon: Container(
              margin: EdgeInsets.only(left: 8.w),
              height: 40.h,
              width: 40.w,
              decoration: BoxDecoration(
                color: Get.theme.customColors.white,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Get.theme.customColors.textfieldFillColor!,
                ),
              ),
              child: CustomImageView(
                imagePath: AssetConstants.icClock,
                color: Get.theme.customColors.greyTextColor,
                margin: const EdgeInsets.all(8.0),
              ),
            ),
            onPressed: () {
              NavigationService.navigateWithSlideAnimation(
                AppRoutes.subscriptionHistoryPage,
              );
            },
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoadingsubscriptionPlanData.value) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: ManageSubscriptionsimmerLoading(),
          );
        }

        final plans = controller.subscriptionPlanData.value?.data ?? [];

        if (plans.isEmpty) {
          return Center(child: Text(AppStrings.T.lbl_no_data));
        }

        if (controller.expandedTiles.length != plans.length) {
          controller.expandedTiles.value = List.generate(
            plans.length,
            (_) => false,
          );
        }

        final activePlan = plans.firstWhereOrNull(
          (plan) => plan.currentActiveSubscription == true,
        );
        final double? activePlanPrice = activePlan?.price.toDouble();

        return RefreshIndicator(
          onRefresh: () => controller.getSubscriptionPlanData(),
          child: ListView.separated(
            separatorBuilder: (context, index) {
              return Column(
                children: [
                  Gap(plans[index].currentActiveSubscription ? 12.h : 24.h),
                  Gap(!controller.expandedTiles[index] ? 0.h : 30.h),
                ],
              );
            },
            padding: EdgeInsets.only(
              left: 16.w,
              right: 16.w,
              top: 24.h,
              bottom: 56.h,
            ),
            itemCount: plans.length,
            itemBuilder: (context, index) {
              final plan = plans[index];

              return Obx(() {
                final isExpanded = controller.expandedTiles[index];
                return Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Column(
                      children: [
                        GestureDetector(
                          onTap:
                              () =>
                                  controller.expandedTiles[index] =
                                      !controller.expandedTiles[index],
                          child: Container(
                            decoration: BoxDecoration(
                              color:
                                  isExpanded
                                      ? Get.theme.customColors.primaryColor
                                      : Get.theme.customColors.white,
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: Get.theme.customColors.dividercolor!,
                                width: 1,
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 12,
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            plan.name,
                                            style: Get
                                                .theme
                                                .textTheme
                                                .bodyLarge!
                                                .copyWith(
                                                  color:
                                                      isExpanded
                                                          ? Get
                                                              .theme
                                                              .customColors
                                                              .white
                                                          : Get
                                                              .theme
                                                              .customColors
                                                              .darkGreyTextColor,
                                                ),
                                          ),
                                          Text(
                                            '${AppStrings.T.lbl_no_of_vcards} ${plan.noOfVcards}',
                                            style: Get
                                                .theme
                                                .textTheme
                                                .labelMedium!
                                                .copyWith(
                                                  color:
                                                      isExpanded
                                                          ? Get
                                                              .theme
                                                              .customColors
                                                              .white
                                                          : Get
                                                              .theme
                                                              .customColors
                                                              .greyTextColor,
                                                ),
                                          ),
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          RichText(
                                            text: TextSpan(
                                              children: [
                                                TextSpan(
                                                  text: '\$ ${plan.price}',
                                                  style: Get
                                                      .theme
                                                      .textTheme
                                                      .titleLarge!
                                                      .copyWith(
                                                        color:
                                                            isExpanded
                                                                ? Get
                                                                    .theme
                                                                    .customColors
                                                                    .white
                                                                : Get
                                                                    .theme
                                                                    .customColors
                                                                    .darkGreyTextColor,
                                                      ),
                                                ),
                                                TextSpan(
                                                  text:
                                                      '/${AppStrings.T.lbl_yearly}',
                                                  style: Get
                                                      .theme
                                                      .textTheme
                                                      .titleLarge!
                                                      .copyWith(
                                                        color:
                                                            isExpanded
                                                                ? Get
                                                                    .theme
                                                                    .customColors
                                                                    .white
                                                                : Get
                                                                    .theme
                                                                    .customColors
                                                                    .darkGreyTextColor,
                                                      ),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Gap(16.w),
                                          CustomImageView(
                                            imagePath:
                                                isExpanded
                                                    ? AssetConstants
                                                        .icDownArrow2
                                                    : AssetConstants
                                                        .icRightArrow2,
                                            color:
                                                isExpanded
                                                    ? Get
                                                        .theme
                                                        .customColors
                                                        .white
                                                    : Get
                                                        .theme
                                                        .customColors
                                                        .darkGreyTextColor,
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                if (isExpanded)
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 0,
                                    ),
                                    child: Container(
                                      // margin: const EdgeInsets.only(bottom: 12),
                                      decoration: BoxDecoration(
                                        color: Get.theme.customColors.white,
                                        borderRadius: BorderRadius.only(
                                          bottomLeft: Radius.circular(20),
                                          bottomRight: Radius.circular(20),
                                        ),
                                        // border: Border.all(
                                        //   color: Colors.orange.withValues(
                                        //     alpha: 0.5,
                                        //   ),
                                        //   width: 1,
                                        // ),
                                      ),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          vertical: 8.0,
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            if (plan.currentActiveSubscription)
                                              Padding(
                                                padding: const EdgeInsets.all(
                                                  16.0,
                                                ),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            '${AppStrings.T.lbl_subscribed_date}:',
                                                            style: Get
                                                                .theme
                                                                .textTheme
                                                                .labelMedium!
                                                                .copyWith(
                                                                  color:
                                                                      Get
                                                                          .theme
                                                                          .customColors
                                                                          .dividercolor,
                                                                ),
                                                          ),
                                                          Text(
                                                            _formatDate(
                                                              plan.startDate
                                                                  .toString(),
                                                            ),
                                                            style: Get
                                                                .theme
                                                                .textTheme
                                                                .bodyLarge!
                                                                .copyWith(
                                                                  color:
                                                                      Get
                                                                          .theme
                                                                          .customColors
                                                                          .darkGreyTextColor,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    Padding(
                                                      padding:
                                                          const EdgeInsets.symmetric(
                                                            horizontal: 16.0,
                                                          ),
                                                      child: Container(
                                                        width: 1,
                                                        height: 40.h,
                                                        color:
                                                            Get
                                                                .theme
                                                                .customColors
                                                                .dividercolor,
                                                      ),
                                                    ),
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                            AppStrings
                                                                .T
                                                                .lbl_active_till,
                                                            // 'Active Till:',
                                                            style: Get
                                                                .theme
                                                                .textTheme
                                                                .labelMedium!
                                                                .copyWith(
                                                                  color:
                                                                      Get
                                                                          .theme
                                                                          .customColors
                                                                          .dividercolor,
                                                                ),
                                                          ),
                                                          Text(
                                                            _formatDate(
                                                              plan.startDate
                                                                  .toString(),
                                                            ),
                                                            style: Get
                                                                .theme
                                                                .textTheme
                                                                .bodyLarge!
                                                                .copyWith(
                                                                  color:
                                                                      Get
                                                                          .theme
                                                                          .customColors
                                                                          .darkGreyTextColor,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            Column(
                                              children: List.generate(
                                                plan.features
                                                    .toJson()
                                                    .entries
                                                    .length,
                                                (i) {
                                                  final entry = plan.features
                                                      .toJson()
                                                      .entries
                                                      .elementAt(i);

                                                  final isLast =
                                                      i ==
                                                      plan.features
                                                              .toJson()
                                                              .entries
                                                              .length -
                                                          1;

                                                  return Column(
                                                    children: [
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets.symmetric(
                                                              horizontal: 16.0,
                                                            ),
                                                        child: Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceBetween,
                                                          crossAxisAlignment:
                                                              CrossAxisAlignment
                                                                  .start,
                                                          children: [
                                                            Expanded(
                                                              child: Text(
                                                                entry.key
                                                                    .replaceAll(
                                                                      '_',
                                                                      ' ',
                                                                    )
                                                                    .capitalizeFirst!,
                                                                style: const TextStyle(
                                                                  fontSize: 14,
                                                                  color:
                                                                      Colors
                                                                          .black87,
                                                                ),
                                                              ),
                                                            ),
                                                            Container(
                                                              padding:
                                                                  const EdgeInsets.all(
                                                                    2,
                                                                  ),
                                                              decoration: BoxDecoration(
                                                                color:
                                                                    entry.value ==
                                                                            1
                                                                        ? Colors.lightGreen.withValues(
                                                                          alpha:
                                                                              0.1,
                                                                        )
                                                                        : Colors.red.withValues(
                                                                          alpha:
                                                                              0.1,
                                                                        ),
                                                                borderRadius:
                                                                    BorderRadius.circular(
                                                                      12,
                                                                    ),
                                                              ),
                                                              child: Container(
                                                                padding:
                                                                    const EdgeInsets.all(
                                                                      2,
                                                                    ),
                                                                decoration: BoxDecoration(
                                                                  color:
                                                                      entry.value ==
                                                                              1
                                                                          ? Colors
                                                                              .lightGreen
                                                                          : Colors
                                                                              .red,
                                                                  borderRadius:
                                                                      BorderRadius.circular(
                                                                        12,
                                                                      ),
                                                                ),
                                                                child: Icon(
                                                                  entry.value ==
                                                                          1
                                                                      ? Icons
                                                                          .check
                                                                      : Icons
                                                                          .close,
                                                                  color:
                                                                      Colors
                                                                          .white,
                                                                  size: 14,
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      if (!isLast)
                                                        Padding(
                                                          padding:
                                                              const EdgeInsets.symmetric(
                                                                vertical: 8,
                                                              ),
                                                          child: Container(
                                                            height: 1,
                                                            width:
                                                                double.infinity,
                                                            decoration: BoxDecoration(
                                                              color:
                                                                  Get
                                                                      .theme
                                                                      .customColors
                                                                      .lightGreyBackgroundColor,
                                                              boxShadow: [
                                                                BoxShadow(
                                                                  color: Colors
                                                                      .black
                                                                      .withValues(
                                                                        alpha:
                                                                            0.3,
                                                                      ),
                                                                  blurRadius: 4,
                                                                  offset:
                                                                      const Offset(
                                                                        0,
                                                                        2,
                                                                      ),
                                                                ),
                                                              ],
                                                            ),
                                                          ),
                                                        ),
                                                    ],
                                                  );
                                                },
                                              ),
                                            ),
                                            if (!plan.currentActiveSubscription)
                                              Gap(32.h),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),

                    if (plan.currentActiveSubscription)
                      Positioned(
                        right: 20.w,
                        top: -14.h,
                        child: Container(
                          height: 28.h,
                          width: 120.w,
                          decoration: BoxDecoration(
                            color:
                                isExpanded
                                    ? Get.theme.customColors.primaryColor
                                    : Get.theme.customColors.primaryColor,
                            border: Border.all(
                              color:
                                  isExpanded
                                      ? Get
                                          .theme
                                          .customColors
                                          .darkGreyTextColor!
                                      : Get.theme.customColors.primaryColor!,
                            ),
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Center(
                            child: Text(
                              AppStrings.T.lbl_active,
                              style: Get.theme.textTheme.labelLarge!.copyWith(
                                color: Get.theme.customColors.white,
                              ),
                            ),
                          ),
                        ),
                      ),

                    if (!plan.currentActiveSubscription && isExpanded)
                      Positioned(
                        bottom: -28,
                        left: 0,
                        right: 0,
                        child: Center(
                          child: SizedBox(
                            height: 56.h,
                            width: Get.size.width * 0.6,
                            child: CustomElevatedButton(
                              onPressed: () {
                                NavigationService.navigateWithSlideAnimation(
                                  AppRoutes.paymentPage,
                                  arguments: {'planId': plan.id.toString()},
                                );
                              },
                              text: _getButtonText(plan, activePlanPrice),
                            ),
                          ),
                        ),
                      ),
                  ],
                );
              });
            },
          ),
        );
      }),
    );
  }

  String _getButtonText(dynamic plan, double? activePlanPrice) {
    if (activePlanPrice == null) {
      return AppStrings.T.lbl_select_plan;
    }

    if (plan.price > activePlanPrice) {
      return AppStrings.T.lbl_upgrade_plan;
    } else if (plan.price < activePlanPrice) {
      return AppStrings.T.lbl_switch_plan;
    } else {
      return AppStrings.T.lbl_select_plan;
    }
  }

  String _formatDate(String date) {
    try {
      final parsedDate = DateTime.parse(date);
      return DateFormat('dd MMM, y').format(parsedDate);
    } catch (e) {
      return date;
    }
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getSubscriptionPlanData();
  }
}
