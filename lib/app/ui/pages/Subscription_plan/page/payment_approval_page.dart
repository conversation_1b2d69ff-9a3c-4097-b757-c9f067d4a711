import 'package:get/get.dart';
import 'package:v_card/app/controllers/subscription_plan_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class PaymentApprovalPage extends GetItHook<SubscriptionPlanController> {
  const PaymentApprovalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarColor: Get.theme.customColors.white,
        statusBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: Get.theme.customColors.bgOneColor,
        body: Padding(
          padding: EdgeInsets.only(
            left: 20.w,
            right: 20.w,
            top: 40.h,
            bottom: 70.h,
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    onTap: () {
                      SystemNavigator.pop();
                    },
                    child: Container(
                      height: 40.h,
                      width: 40.w,
                      decoration: BoxDecoration(
                        color: Get.theme.customColors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Get.theme.customColors.textfieldFillColor!,
                        ),
                      ),
                      child: CustomImageView(
                        imagePath: AssetConstants.icClose,
                        margin: const EdgeInsets.all(12),
                      ),
                    ),
                  ),
                ],
              ),
              Gap(20.h),

              CustomImageView(
                imagePath: AssetConstants.paymentPending,
                fit: BoxFit.cover,
              ),
              Gap(40.h),
              CenterText(
                AppStrings.T.lbl_payment_approval_pending,
                style: Get.theme.textTheme.headlineLarge?.copyWith(
                  color: Get.theme.customColors.blackColor,
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Gap(10.h),
              CenterText(
                AppStrings.T.lbl_payment_approval_pending_desc,
                
                style: Get.theme.textTheme.bodyLarge?.copyWith(
                  color: Get.theme.customColors.greyTextColor,
                  fontSize: 16.sp,
                ),
              ),
              Spacer(),
              CenterText(
                AppStrings.T.lbl_payment_approval_pending_desc2,
                style: Get.theme.textTheme.bodyLarge?.copyWith(
                  color: Get.theme.customColors.greyTextColor,
                  fontSize: 16.sp,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => true;

  @override
  void onDispose() {}

  @override
  void onInit() {}
}
