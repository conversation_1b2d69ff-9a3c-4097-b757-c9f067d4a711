import 'package:get/get.dart';
import 'package:v_card/app/controllers/subscription_plan_controller.dart';
import 'package:v_card/app/data/model/subscription_plan_model.dart/subscription_history_model.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class SubscriptionHistoryPage extends GetItHook<SubscriptionPlanController> {
  const SubscriptionHistoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: CustomAppbar(
        title: Text(
          AppStrings.T.lbl_subscription_history,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
      body: Obx(() {
        if (controller.isLoadingsubscriptionHistoryData.value) {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: ManageSubscriptionsimmerLoading(),
          );
        }

        final subscriptionHistory =
            controller.subscriptionHistoryData.value?.data ?? [];

        if (subscriptionHistory.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: () => controller.getSubscriptionHistoryData(),
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            itemCount: subscriptionHistory.length,
            itemBuilder: (context, index) {
              final history = subscriptionHistory[index];
              return _buildSubscriptionHistoryCard(history);
            },
          ),
        );
      }),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            AppStrings.T.lbl_no_subscription_history,
            style: Get.theme.textTheme.titleMedium!.copyWith(
              color: Get.theme.customColors.darkGreyTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          Gap(8.h),
          Text(
            AppStrings.T.lbl_no_data,
            textAlign: TextAlign.center,
            style: Get.theme.textTheme.bodyMedium!.copyWith(
              color: Get.theme.customColors.greyTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionHistoryCard(SubscriptionHistoryData history) {
    final isActive = history.status.toLowerCase() == 'active';
    final statusColor =
        isActive
            ? Colors.green
            : history.status.toLowerCase() == 'expired'
            ? Colors.red
            : Colors.orange;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Get.theme.customColors.primaryColor!.withValues(
                alpha: 0.1,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  history.planName,
                  style: Get.theme.textTheme.titleMedium!.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Get.theme.customColors.primaryColor,
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    history.status,
                    style: Get.theme.textTheme.labelMedium!.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildInfoRow(
                  title: AppStrings.T.lbl_amount,
                  value: '\$${history.amount}',
                  valueColor: Get.theme.customColors.darkGreyTextColor,
                  fontWeight: FontWeight.w700,
                ),
                Gap(12.h),
                _buildInfoRow(
                  title: AppStrings.T.lbl_subscribed_date,
                  value: history.subscribedDate,
                ),
                Gap(12.h),
                _buildInfoRow(
                  title: AppStrings.T.lbl_expired_date,
                  value: history.expiredDate,
                ),

                // Duration indicator
                if (isActive) ...[
                  Gap(16.h),
                  _buildSubscriptionProgress(history),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow({
    required String title,
    required String value,
    Color? valueColor,
    FontWeight? fontWeight,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Get.theme.textTheme.bodyMedium!.copyWith(
            color: Get.theme.customColors.greyTextColor,
          ),
        ),
        Text(
          value,
          style: Get.theme.textTheme.bodyMedium!.copyWith(
            color: valueColor ?? Get.theme.customColors.darkGreyTextColor,
            fontWeight: fontWeight ?? FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // Widget _buildSubscriptionProgress(SubscriptionHistoryData history) {
  //   // Calculate progress based on subscription and expiry dates
  //   double progress = 0.0;
  //   try {
  //     final startDate = DateTime.parse(history.subscribedDate);
  //     final endDate = DateTime.parse(history.expiredDate);
  //     final today = DateTime.now();

  //     final totalDuration = endDate.difference(startDate).inDays;
  //     final elapsedDuration = today.difference(startDate).inDays;

  //     if (totalDuration > 0) {
  //       progress = elapsedDuration / totalDuration;
  //       progress = progress.clamp(0.0, 1.0);
  //     }
  //   } catch (e) {
  //     // Handle date parsing errors
  //     progress = 0.0;
  //   }

  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Text(
  //         AppStrings.T.lbl_subscription_period,
  //         style: Get.theme.textTheme.bodyMedium!.copyWith(
  //           color: Get.theme.customColors.greyTextColor,
  //         ),
  //       ),
  //       Gap(8.h),
  //       ClipRRect(
  //         borderRadius: BorderRadius.circular(4),
  //         child: LinearProgressIndicator(
  //           value: progress,
  //           backgroundColor: Get.theme.customColors.lightGreyBackgroundColor,
  //           valueColor: AlwaysStoppedAnimation<Color>(
  //             Get.theme.customColors.primaryColor!,
  //           ),
  //           minHeight: 8,
  //         ),
  //       ),
  //       Gap(8.h),
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             history.subscribedDate,
  //             style: Get.theme.textTheme.labelSmall!.copyWith(
  //               color: Get.theme.customColors.greyTextColor,
  //             ),
  //           ),
  //           Text(
  //             history.expiredDate,
  //             style: Get.theme.textTheme.labelSmall!.copyWith(
  //               color: Get.theme.customColors.greyTextColor,
  //             ),
  //           ),
  //         ],
  //       ),
  //     ],
  //   );
  // }

  Widget _buildSubscriptionProgress(SubscriptionHistoryData history) {
    // Calculate progress based on subscription and expiry dates
    double progress = 0.0;
    int totalDays = 0;
    int usedDays = 0;
    int remainingDays = 0;

    try {
      final startDate = _parseCustomDate(history.subscribedDate);
      final endDate = _parseCustomDate(history.expiredDate);
      final today = DateTime.now();

      totalDays = endDate.difference(startDate).inDays;
      usedDays = today.difference(startDate).inDays;
      remainingDays = endDate.difference(today).inDays;

      // Ensure usedDays is not negative
      if (usedDays < 0) usedDays = 0;
      // Ensure remainingDays is not negative
      if (remainingDays < 0) remainingDays = 0;

      if (totalDays > 0) {
        progress = usedDays / totalDays;
        progress = progress.clamp(0.0, 1.0);
      }
    } catch (e) {
      // Handle date parsing errors
      print('Date parsing error: $e');
      progress = 0.0;
      totalDays = 0;
      usedDays = 0;
      remainingDays = 0;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.T.lbl_subscription_period,
          style: Get.theme.textTheme.bodyMedium!.copyWith(
            color: Get.theme.customColors.greyTextColor,
          ),
        ),
        Gap(8.h),

        // Progress bar
        ClipRRect(
          borderRadius: BorderRadius.circular(4),
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: Get.theme.customColors.lightGreyBackgroundColor,
            valueColor: AlwaysStoppedAnimation<Color>(
              Get.theme.customColors.primaryColor!,
            ),
            minHeight: 6.h,
          ),
        ),
        Gap(8.h),

        // Date range
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              history.subscribedDate,
              style: Get.theme.textTheme.labelSmall!.copyWith(
                color: Get.theme.customColors.greyTextColor,
              ),
            ),
            Text(
              history.expiredDate,
              style: Get.theme.textTheme.labelSmall!.copyWith(
                color: Get.theme.customColors.greyTextColor,
              ),
            ),
          ],
        ),
        Gap(8.h),

        // // Usage statistics
        // Row(
        //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //   children: [
        //     Column(
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: [
        //         Text(
        //           'Days Used',
        //           style: Get.theme.textTheme.labelSmall!.copyWith(
        //             color: Get.theme.customColors.greyTextColor,
        //           ),
        //         ),
        //         Text(
        //           '$usedDays',
        //           style: Get.theme.textTheme.bodyMedium!.copyWith(
        //             color: Get.theme.customColors.primaryColor,
        //             fontWeight: FontWeight.w600,
        //           ),
        //         ),
        //       ],
        //     ),
        //     Column(
        //       crossAxisAlignment: CrossAxisAlignment.end,
        //       children: [
        //         Text(
        //           'Days Remaining',
        //           style: Get.theme.textTheme.labelSmall!.copyWith(
        //             color: Get.theme.customColors.greyTextColor,
        //           ),
        //         ),
        //         Text(
        //           '$remainingDays',
        //           style: Get.theme.textTheme.bodyMedium!.copyWith(
        //             color:
        //                 remainingDays > 30
        //                     ? Colors.green
        //                     : remainingDays > 7
        //                     ? Colors.orange
        //                     : Colors.red,
        //             fontWeight: FontWeight.w600,
        //           ),
        //         ),
        //       ],
        //     ),
        //   ],
        // ),
      ],
    );
  }

  // Helper method to parse custom date format like "23rd May, 2025"
  DateTime _parseCustomDate(String dateStr) {
    try {
      // Remove ordinal indicators (st, nd, rd, th)
      String cleanDate = dateStr.replaceAll(
        RegExp(r'(\d+)(st|nd|rd|th)'),
        r'$1',
      );

      // Parse the cleaned date
      return DateTime.parse(
        DateTime.parse(
          '${cleanDate.split(' ')[2]}-${_getMonthNumber(cleanDate.split(' ')[1].replaceAll(',', ''))}-${cleanDate.split(' ')[0].padLeft(2, '0')}',
        ).toString(),
      );
    } catch (e) {
      // If parsing fails, try alternative method
      return _parseAlternativeDate(dateStr);
    }
  }

  // Alternative parsing method
  DateTime _parseAlternativeDate(String dateStr) {
    // Split the date string: "23rd May, 2025" -> ["23rd", "May,", "2025"]
    List<String> parts = dateStr.split(' ');

    if (parts.length != 3) {
      throw FormatException('Invalid date format: $dateStr');
    }

    // Extract day, month, year
    int day = int.parse(
      parts[0].replaceAll(RegExp(r'[^\d]'), ''),
    ); // Remove non-digits
    String monthStr = parts[1].replaceAll(',', ''); // Remove comma
    int year = int.parse(parts[2]);

    int month = _getMonthNumber(monthStr);

    return DateTime(year, month, day);
  }

  // Helper method to convert month name to number
  int _getMonthNumber(String monthName) {
    switch (monthName.toLowerCase()) {
      case 'january':
      case 'jan':
        return 1;
      case 'february':
      case 'feb':
        return 2;
      case 'march':
      case 'mar':
        return 3;
      case 'april':
      case 'apr':
        return 4;
      case 'may':
        return 5;
      case 'june':
      case 'jun':
        return 6;
      case 'july':
      case 'jul':
        return 7;
      case 'august':
      case 'aug':
        return 8;
      case 'september':
      case 'sep':
        return 9;
      case 'october':
      case 'oct':
        return 10;
      case 'november':
      case 'nov':
        return 11;
      case 'december':
      case 'dec':
        return 12;
      default:
        throw FormatException('Invalid month name: $monthName');
    }
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getSubscriptionHistoryData();
  }
}
