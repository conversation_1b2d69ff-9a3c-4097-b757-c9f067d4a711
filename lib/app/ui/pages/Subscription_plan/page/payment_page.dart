import 'package:get/get.dart';
import 'package:v_card/app/controllers/payment_controller.dart';
import 'package:v_card/app/controllers/subscription_plan_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:intl/intl.dart';

class PaymentPage extends GetItHook<PaymentController> {
  const PaymentPage({super.key});

  @override
  Widget build(BuildContext context) {
    var planId = Get.arguments['planId'] ?? 0;
    final subscriptionController = getIt<SubscriptionPlanController>();

    return Scaffold(
      appBar: CustomAppbar(
        title: Text(
          AppStrings.T.lbl_payment,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            color: Colors.black,
            fontSize: 20,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: ConstrainedBox(
          constraints: BoxConstraints(
            minHeight: MediaQuery.of(context).size.height,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Current Plan Section - Get from subscription history
                _buildCurrentPlanCard(subscriptionController),
                Gap(20.h),

                // New Plan Section - Get from subscription plans
                _buildNewPlanCard(subscriptionController, planId),
                Gap(20.h),

                // Payment Type Section
                Text(
                  AppStrings.T.lbl_payment_type,
                  style: Get.theme.textTheme.bodyMedium!.copyWith(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                _buildPaymentTypeTile(),
                Gap(20.h),

                // Payment Content
                _buildPaymentContent(planId),
                Gap(20.h),

                // Pay button
                Obx(
                  () => CustomElevatedButton(
                    checkConnectivity: true,
                    isLoading:
                        controller.isLoadingsubscriptionPlanBuyData.value,
                    text: AppStrings.T.lbl_pay,
                    onPressed: () {
                      controller.subscriptionPlanBuy(planId);
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentPlanCard(
    SubscriptionPlanController subscriptionController,
  ) {
    return Obx(() {
      // Find the current active plan
      final plans =
          subscriptionController.subscriptionPlanData.value?.data ?? [];
      final currentPlan = plans.firstWhereOrNull(
        (plan) => plan.currentActiveSubscription,
      );

      if (currentPlan == null) {
        return const SizedBox.shrink();
      }

      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Get.theme.customColors.primaryColor!.withValues(alpha: 0.3),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  AppStrings.T.lbl_current_plan,
                  style: Get.theme.textTheme.titleMedium!.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Get.theme.customColors.primaryColor,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    AppStrings.T.lbl_active,
                    style: Get.theme.textTheme.bodySmall!.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            Divider(height: 24),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_plan_name,
              value: "${currentPlan.name} / ${AppStrings.T.lbl_yearly}",
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_plan_price,
              value: "\$ ${currentPlan.price}",
              isBold: true,
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_start_date,
              value: _formatDate(currentPlan.startDate ?? ''),
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_end_date,
              value: _formatDate(currentPlan.endDate ?? ''),
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_no_of_vcards,
              value: "${currentPlan.noOfVcards}",
            ),
          ],
        ),
      );
    });
  }

  String _formatDate(String date) {
    try {
      final parsedDate = DateTime.parse(date);
      return DateFormat('dd MMM, yyyy').format(parsedDate);
    } catch (e) {
      return date;
    }
  }

  Widget _buildNewPlanCard(
    SubscriptionPlanController subscriptionController,
    String planId,
  ) {
    return Obx(() {
      // Find the selected plan from subscription plans
      final plans =
          subscriptionController.subscriptionPlanData.value?.data ?? [];
      final selectedPlan = plans.firstWhereOrNull(
        (plan) => plan.id.toString() == planId,
      );

      if (selectedPlan == null) {
        return const SizedBox.shrink();
      }

      // Calculate dates for new plan
      final now = DateTime.now();
      final formatter = DateFormat('dd MMM, yyyy');
      final startDate = formatter.format(now);
      final endDate = formatter.format(now.add(Duration(days: 365)));
      final remainingDays = 365;

      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Get.theme.customColors.primaryColor!.withValues(alpha: 0.3),
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.T.lbl_new_plan,
              style: Get.theme.textTheme.titleMedium!.copyWith(
                fontWeight: FontWeight.w700,
                color: Get.theme.customColors.primaryColor,
              ),
            ),
            Divider(height: 24),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_plan_name,
              value: "${selectedPlan.name} / ${AppStrings.T.lbl_yearly}",
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_plan_price,
              value: "\$ ${selectedPlan.price}",
              isBold: true,
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_start_date,
              value: startDate,
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_end_date,
              value: endDate,
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_remaining_days,
              value: "$remainingDays ${AppStrings.T.lbl_days}",
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_remaining_balance,
              value: "\$ 0.00",
            ),
            _buildPlanDetailRow(
              title: AppStrings.T.lbl_payable_amount,
              value: "\$ ${selectedPlan.price}",
              isBold: true,
              valueColor: Get.theme.customColors.primaryColor,
            ),
          ],
        ),
      );
    });
  }

  Widget _buildPaymentContent(String planId) {
    return Obx(() {
      if (controller.selectedPaymentType.value == AppStrings.T.lbl_manually) {
        return _buildManualPaymentContent(planId);
      } else {
        return _buildOnlinePaymentContent();
      }
    });
  }

  Widget _buildManualPaymentContent(String planId) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppStrings.T.lbl_manual_payment_guide,
          style: Get.theme.textTheme.bodyMedium!.copyWith(
            fontSize: 14.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(12.h),
        TextInputField(
          type: InputType.text,
          hintLabel: AppStrings.T.lbl_add_your_note,
          controller: controller.noteController,
        ),
      ],
    );
  }

  Widget _buildOnlinePaymentContent() {
    return Center(child: Text(AppStrings.T.lbl_online_payment_coming_soon));
  }

  Widget _buildPlanDetailRow({
    required String title,
    required String value,
    bool isBold = false,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Get.theme.textTheme.bodyMedium!.copyWith(
              color: Get.theme.customColors.greyTextColor,
            ),
          ),
          Text(
            value,
            style: Get.theme.textTheme.bodyMedium!.copyWith(
              color: valueColor ?? Get.theme.customColors.darkGreyTextColor,
              fontWeight: isBold ? FontWeight.w700 : FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentTypeTile() {
    return Obx(() {
      return Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        elevation: 2,
        child: Column(
          children: [
            GestureDetector(
              onTap: () => controller.isTileExpanded.toggle(),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Get.theme.customColors.primaryColor!,
                  ),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.r),
                    topRight: Radius.circular(8.r),
                    bottomLeft:
                        controller.isTileExpanded.value
                            ? Radius.circular(0)
                            : Radius.circular(8.0),
                    bottomRight:
                        controller.isTileExpanded.value
                            ? Radius.circular(0)
                            : Radius.circular(8.0),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      controller.selectedPaymentType.value,
                      style: Get.theme.textTheme.bodyMedium,
                    ),
                    Icon(
                      controller.isTileExpanded.value
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: Get.theme.customColors.blackColor,
                    ),
                  ],
                ),
              ),
            ),

            // Expanded options
            if (controller.isTileExpanded.value) ...[
              Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Get.theme.customColors.primaryColor!,
                  ),
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(8),
                    bottomRight: Radius.circular(8),
                  ),
                ),
                child: Column(
                  children: [
                    _buildPaymentOption(AppStrings.T.lbl_manually),
                    _buildPaymentOption(AppStrings.T.lbl_online),
                  ],
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildPaymentOption(String title) {
    return GestureDetector(
      onTap: () {
        controller.changePaymentType(title);
        controller.isTileExpanded.value = false;
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border(top: BorderSide(color: Colors.grey.shade200)),
        ),
        child: Row(
          children: [Text(title, style: Get.theme.textTheme.bodyMedium)],
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => true;

  @override
  void onDispose() {
    controller.noteController.clear();
    controller.selectedPaymentType.value = AppStrings.T.lbl_manually;
  }

  @override
  void onInit() {}
}
