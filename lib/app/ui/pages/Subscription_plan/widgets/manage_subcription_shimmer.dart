import 'package:v_card/app/utils/helpers/exporter.dart';

class ManageSubscriptionsimmerLoading extends StatelessWidget {
  const ManageSubscriptionsimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 2,
      itemBuilder: (context, index) {
        return Card(
          elevation: 3,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Product details shimmer
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Product name shimmer
                      ShimmerBox(height: 18.h, width: 150.w),
                      Gap(4.h),
                      // Product URL shimmer
                      ShimmerBox(height: 14.h, width: 120.w),
                    ],
                  ),
                ),
                // Action buttons shimmer
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Delete button shimmer
                    ShimmerBox(width: 70.w, height: 24.h),
                    Gap(12.w),
                    // Edit button shimmer
                    ShimmerBox(width: 24.w, height: 24.h),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
