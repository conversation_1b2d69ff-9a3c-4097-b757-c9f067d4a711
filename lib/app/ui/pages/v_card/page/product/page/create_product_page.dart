import 'package:get/get.dart';
import 'package:v_card/app/controllers/product_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/product/widgets/image_selection_widget.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateProductPage extends GetItHook<ProductController> {
   CreateProductPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    final arguments = Get.arguments ?? {};
    final productId = arguments['productId'];
    controller.getCurrencyList();

    // Reset controller state first

    if (productId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getProductById(id: productId);
        final product = controller.productDataById.value?.data;
        if (product != null) {
          controller.nameController.text = product.name;
          controller.priceController.text = product.price ?? '';
          controller.descriptionController.text = product.description ?? '';
          controller.productUrlController.text = product.productUrl ?? '';

          // Set existing images for editing - images are already set in getProductById
          // No need to call setExistingImages again here
        }
      });
    }
    controller.isProcessingPWAIcon = false.obs;
  }

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments;
    final vcardId = arguments['vcardId'] ?? 0;
    final productId = arguments['productId'];

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          productId == null
              ? AppStrings.T.lbl_create_product
              : AppStrings.T.lbl_edit_product,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createInstaEmbedPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoadingProductById.value == true) {
          return CustomFormShimmerLoading();
        }

        return IgnorePointer(
          ignoring: controller.isLoadingCreateProduct.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Gap(10.h),
                    Center(
                      child: Text.rich(
                        TextSpan(
                          text: AppStrings.T.lbl_product_image,
                          style: Get.theme.textTheme.bodyLarge?.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                          ),
                          children: [
                            TextSpan(
                              text: "*",
                              style: Get.theme.textTheme.bodyLarge?.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                color: Get.theme.customColors.primaryColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Gap(10.h),
                    ImageSelectionWidget(controller: controller),
                    Gap(24.h),
                    TextInputField(
                      isRequiredField: true,
                      type: InputType.text,
                      isCapitalized: true,
                      controller: controller.nameController,
                      label: AppStrings.T.lbl_enter_product_name,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_product_name,
                          ),
                    ),
                    Gap(12.h),
                    TextInputField(
                      type: InputType.text,
                      readOnly: true,
                      controller: controller.currencyIdController,
                      label: AppStrings.T.lbl_currency,
                      onTap: () {
                        final searchText = ''.obs;
                        final searchController = TextEditingController();
                        final currencyMap =
                            controller.currencyList.value?.data ?? {};

                        Get.bottomSheet(
                          Container(
                            height: Get.height * 0.45,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.vertical(
                                top: Radius.circular(20),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  margin: EdgeInsets.only(
                                    left: 20.0.w,
                                    right: 20.0.w,
                                    top: 8.0.h,
                                    bottom: 8.0.h,
                                  ),
                                  height: 5.0,
                                  width: 50.0,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(100.r),
                                    color: Get.theme.customColors.primaryColor
                                        ?.withValues(alpha: 0.6),
                                  ),
                                ),
                                Obx(
                                  () => Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 16.0,
                                    ),
                                    child: TextInputField(
                                      onChanged:
                                          (value) => searchText.value = value,
                                      hintLabel:
                                          AppStrings.T.hint_search_currency,
                                      controller: searchController,
                                      prefixIcon: CustomImageView(
                                        imagePath: AssetConstants.icSearch,
                                      ),
                                      type: InputType.text,
                                      suffixIcon:
                                          searchText.value.isNotEmpty
                                              ? IconButton(
                                                icon: Icon(
                                                  Icons.close,
                                                  color:
                                                      Get
                                                          .theme
                                                          .customColors
                                                          .greyTextColor,
                                                ),
                                                onPressed: () {
                                                  searchController.clear();
                                                  searchText.value = '';
                                                },
                                              )
                                              : null,
                                    ),
                                  ),
                                ),
                                Gap(8.h),
                                // Currency List
                                Expanded(
                                  child: Obx(() {
                                    final query =
                                        searchText.value.toLowerCase();
                                    final filteredList =
                                        currencyMap.entries
                                            .where(
                                              (e) => e.value
                                                  .toLowerCase()
                                                  .contains(query),
                                            )
                                            .toList();

                                    return ListView.separated(
                                      itemCount: filteredList.length,
                                      separatorBuilder: (_, __) => Divider(),
                                      itemBuilder: (_, index) {
                                        final entry = filteredList[index];
                                        return ListTile(
                                          title: Text(
                                            entry.value,
                                            style:
                                                Get.theme.textTheme.bodySmall,
                                          ),
                                          onTap: () {
                                            controller
                                                .selectedCurrencyId
                                                .value = entry.key;
                                            controller
                                                .selectedCurrencyName
                                                .value = entry.value;
                                            controller
                                                .currencyIdController
                                                .text = entry.value;
                                            NavigationService.navigateBack();
                                          },
                                        );
                                      },
                                    );
                                  }),
                                ),
                              ],
                            ),
                          ),
                          isScrollControlled: true,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.vertical(
                              top: Radius.circular(20),
                            ),
                          ),
                        );
                        // showModalBottomSheet(
                        //   context: context,
                        //   shape: RoundedRectangleBorder(
                        //     borderRadius: BorderRadius.vertical(
                        //       top: Radius.circular(20.r),
                        //     ),
                        //   ),
                        //   builder: (context) {
                        //     final currencyMap =
                        //         controller.currencyList.value?.data ?? {};
                        //     return ListView.separated(
                        //       shrinkWrap: true,
                        //       padding: const EdgeInsets.all(16.0),
                        //       itemCount: currencyMap.length,
                        //       separatorBuilder: (_, __) => Divider(),
                        //       itemBuilder: (context, index) {
                        //         final entry = currencyMap.entries.elementAt(
                        //           index,
                        //         );
                        //         return ListTile(
                        //           title: Text(
                        //             entry.value,
                        //             style: Get.theme.textTheme.bodySmall,
                        //           ),
                        //           onTap: () {
                        //             controller.selectedCurrencyId.value =
                        //                 entry.key;
                        //             controller.selectedCurrencyName.value =
                        //                 entry.value;
                        //             controller.currencyIdController.text =
                        //                 entry.value;
                        //             Navigator.pop(context);
                        //           },
                        //         );
                        //       },
                        //     );
                        //   },
                        // );
                      },
                    ),
                    Gap(12.h),
                    TextInputField(
                      type: InputType.phoneNumber,
                      controller: controller.priceController,
                      label: AppStrings.T.lbl_enter_price,
                    ),
                    Gap(12.h),
                    TextInputField(
                      maxLines: 4,
                      isCapitalized: true,
                      type: InputType.text,
                      controller: controller.descriptionController,
                      label: AppStrings.T.lbl_enter_description,
                    ),
                    Gap(12.h),
                    TextInputField(
                      type: InputType.text,
                      textInputAction: TextInputAction.done,
                      controller: controller.productUrlController,
                      label: AppStrings.T.lbl_enter_product_url,
                      validator: AppValidations.urlWithEmptyValidation,
                    ),
                    Gap(24.h),
                    _buildButtons(
                      _formKey,
                      vcardId.toString(),
                      productId,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildButtons(
    GlobalKey<FormState> formKey,
    String vcardId,
    dynamic productId,
  ) {
    return Column(
      children: [
        CustomElevatedButton(
          isLoading: controller.isLoadingCreateProduct.value,
          text: AppStrings.T.lbl_save,
          onPressed: () async {
            if (controller.isLoadingCreateProduct.value) return;
            if (controller.isShowingToast.value) return;
            if (!_formKey.currentState!.validate()) return;
            if (controller.isProcessingPWAIcon.value) {
              _showImageValidationToast(
                AppStrings.T.lbl_image_processing_message,
                false,
              );
              return;
            }

            if (productId == null) {
              await controller.createAdminProduct(
                currencyId: controller.selectedCurrencyId.value,
                name: controller.nameController.text,
                description: controller.descriptionController.text,
                vcardId: vcardId.toString(),
                price: controller.priceController.text,
                productUrl:
                    controller.productUrlController.text.trim().toLowerCase(),
              );
            } else {
              await controller.updateProduct(
                productId: productId.toString(),
                vcardId: vcardId.toString(),
                currencyId: controller.selectedCurrencyId.value,
                name: controller.nameController.text,
                description: controller.descriptionController.text,
                price: controller.priceController.text,
                productUrl:
                    controller.productUrlController.text.trim().toLowerCase(),
              );
            }
          },
        ),
      ],
    );
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.resetForm();
  }
}
