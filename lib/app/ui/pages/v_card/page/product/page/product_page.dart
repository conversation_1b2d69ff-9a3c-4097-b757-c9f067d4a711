import 'package:get/get.dart';
import 'package:v_card/app/controllers/product_controller.dart';
import 'package:v_card/app/data/model/product/product_detail_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/service/widget/shimmer_service_card.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/ui/widgets/custom_list_item_tile.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:carousel_slider/carousel_slider.dart';

class ProductPage extends GetItHook<ProductController> {
  ProductPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';
  final CarouselSliderController carouselController =
      CarouselSliderController();
  final RxInt currentImageIndex = 0.obs;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_products,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      floatingActionButton: GestureDetector(
        onTap: () {
          controller.resetForm();
          // Navigator.push(
          //   context,
          //   MaterialPageRoute(builder: (context) => AddProductScreen()),
          // );
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createProductPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        },
        child: CircleAvatar(
          radius: 32.r,
          backgroundColor: Get.theme.customColors.primaryColor,
          child: Icon(Icons.add, size: 32),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return ListTileShimmerLoading();
        }

        final productList = controller.productList.value?.data ?? [];

        // if (productList.isEmpty || controller.productList.value == null) {
        //   return Center(child: Text(AppStrings.T.lbl_no_data));
        // }

        return RefreshIndicator(
          onRefresh: () => controller.getProductList(vcardId),
          child: ListView.builder(
            padding: EdgeInsets.only(left: 16.0, right: 16.0, bottom: 120.h),
            itemCount: productList.isEmpty ? 1 : productList.length,
            itemBuilder: (context, index) {
              if (productList.isEmpty) {
                return NoDataWidget(message: AppStrings.T.lbl_no_data);
              }
              final product = productList[index];
              return CustomListItemTile(
                imageUrl:
                    product.productIcon != null &&
                            product.productIcon!.isNotEmpty
                        ? product.productIcon!.first.toString()
                        : AssetConstants.imageNotFound,
                title: product.name,
                subtitle: product.productUrl,
                id: product.id.toString(),
                vcardId: vcardId.toString(),
                onDelete: () {
                  Get.dialog(
                    LoadingConfirmationDialog(
                      title: AppStrings.T.lbl_delete_product,
                      message: AppStrings.T.lbl_delete_product_subtitle,
                      onCancel: () => NavigationService.navigateBack(),
                      onConfirm: () {
                        controller.deleteProductById(
                          id: product.id,
                          vcardId: vcardId.toString(),
                        );
                      },
                      isLoading: controller.isLoadingDeleteProductById,
                    ),
                  );
                },
                onEdit: () {
                  NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createProductPage,
                    arguments: {
                      'vcardId': vcardId.toString(),
                      'productId': product.id,
                    },
                  );
                },
                onTap: () {
                  controller.getProductById(id: product.id);
                  _openProductDetailBottomSheet(product.id);
                },
                isLoadingDelete: controller.isLoadingDeleteProductById.value,
              );
            },
          ),
        );
      }),
    );
  }

  Future<void> _openProductDetailBottomSheet(int id) async {
    // Reset current image index when opening new product
    currentImageIndex.value = 0;

    Get.bottomSheet(
      Obx(() {
        final detail = controller.productDataById.value;
        final isLoading = controller.isLoadingProductById.value;

        if (isLoading) return _buildBottomSheet(child: ShimmerServiceCard());
        if (detail == null) return _noDetailBottomSheet();

        final product = detail.data;

        return _buildBottomSheetContent(product);
      }),
      isScrollControlled: true,
      backgroundColor: Get.theme.customColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(40.0.r)),
      ),
    );
  }

  Widget _buildBottomSheet({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      width: double.maxFinite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
            height: 5.0,
            width: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.r),
              color: Get.theme.customColors.primaryColor?.withValues(
                alpha: 0.6,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {
                  NavigationService.navigateBack();
                },
                child: Container(
                  height: 30.0,
                  width: 30.0,
                  margin: EdgeInsets.symmetric(horizontal: 20.0.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Get.theme.customColors.darkBlueColor?.withValues(
                      alpha: 0.05,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.close_rounded,
                    color: Get.theme.customColors.blackColor,
                    size: 20.0,
                  ),
                ),
              ),
            ],
          ),
          Gap(10.0.h),
          child,
        ],
      ),
    );
  }

  Widget _noDetailBottomSheet() => _buildBottomSheet(
    child: Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 30.0, bottom: 50.0),
        child: Text(AppStrings.T.lbl_no_details_available),
      ),
    ),
  );

  Widget _buildBottomSheetContent(Product product) => _buildBottomSheet(
    child: Stack(
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(maxHeight: Get.height * 0.6),
          child: SingleChildScrollView(
            physics: BouncingScrollPhysics(),
            padding: EdgeInsets.only(
              top: 20.0.h,
              bottom: 110.0.h,
              left: 20.0.w,
              right: 20.0.w,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (product.productIcon != null &&
                    product.productIcon!.isNotEmpty)
                  Column(
                    children: [
                      CarouselSlider(
                        carouselController: carouselController,
                        options: CarouselOptions(
                          height: 200.0.h,
                          viewportFraction: 1.0,
                          enableInfiniteScroll: product.productIcon!.length > 1,
                          onPageChanged: (index, reason) {
                            currentImageIndex.value = index;
                          },
                        ),
                        items:
                            product.productIcon!.map((imageUrl) {
                              return Container(
                                width: double.maxFinite,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10.r),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(10.r),
                                  child: CustomImageView(
                                    imagePath: imageUrl,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              );
                            }).toList(),
                      ),
                      Gap(10.h),
                      if (product.productIcon!.length > 1)
                        Obx(
                          () => Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children:
                                product.productIcon!.asMap().entries.map((
                                  entry,
                                ) {
                                  return Container(
                                    width: 8.0,
                                    height: 8.0,
                                    margin: EdgeInsets.symmetric(
                                      horizontal: 4.0,
                                    ),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color:
                                          currentImageIndex.value == entry.key
                                              ? Get
                                                  .theme
                                                  .customColors
                                                  .primaryColor
                                              : Get
                                                  .theme
                                                  .customColors
                                                  .greyTextColor
                                                  ?.withValues(alpha: 0.3),
                                    ),
                                  );
                                }).toList(),
                          ),
                        ),
                    ],
                  ),
                Gap(25.0.h),
                Container(
                  clipBehavior: Clip.hardEdge,
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    color: Get.theme.customColors.white,
                    borderRadius: BorderRadius.circular(10.0),
                    boxShadow: [
                      BoxShadow(
                        color: Get.theme.customColors.shadowColor!.withValues(
                          alpha: 0.1,
                        ),
                        blurRadius: 16.0,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: 24.0.h,
                          horizontal: 18.0.w,
                        ),
                        child: Text(
                          product.name.toString(),
                          style: Get.textTheme.bodyLarge?.copyWith(
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Container(
                        width: double.maxFinite,
                        padding: EdgeInsets.all(18.0.w),
                        decoration: BoxDecoration(
                          color: Get.theme.customColors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Get.theme.customColors.shadowColor!
                                  .withValues(alpha: 0.1),
                              blurRadius: 16.0,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${AppStrings.T.lbl_description}:",
                              style: Get.textTheme.bodyLarge?.copyWith(
                                fontSize: 15.0.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Gap(5.h),
                            Text(
                              product.description ?? '-',
                              style: Get.textTheme.bodyLarge?.copyWith(
                                fontSize: 14.0.sp,
                                fontWeight: FontWeight.w500,
                                color: Get.theme.customColors.greyTextColor,
                              ),
                            ),
                            Gap(15.h),
                            Text(
                              "${AppStrings.T.lbl_price}:",
                              style: Get.textTheme.bodyLarge?.copyWith(
                                fontSize: 15.0.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Gap(5.h),
                            Text(
                              product.price ?? '-',
                              style: Get.textTheme.bodyLarge?.copyWith(
                                fontSize: 14.0.sp,
                                fontWeight: FontWeight.w500,
                                color: Get.theme.customColors.greyTextColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
          bottom: 20.0,
          right: 18.0,
          child: GestureDetector(
            onTap: () {
              Get.dialog(
                LoadingConfirmationDialog(
                  title: AppStrings.T.lbl_delete_product,
                  message: AppStrings.T.lbl_delete_product_subtitle,
                  onCancel: () => NavigationService.navigateBack(),
                  onConfirm: () {
                    controller.deleteProductById(
                      id: product.id,
                      vcardId: vcardId.toString(),
                    );
                  },
                  isLoading: controller.isLoadingDeleteProductById,
                ),
              );
            },
            child: Container(
              height: 65.0.h,
              width: 65.0.h,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Get.theme.customColors.primaryColor,
                boxShadow: [
                  BoxShadow(
                    color: Get.theme.customColors.black!.withValues(alpha: 0.4),
                    blurRadius: 8.0,
                    offset: Offset(0, 1),
                  ),
                ],
              ),
              alignment: Alignment.center,
              child: CustomImageView(
                imagePath: AssetConstants.icDelete2,
                color: Get.theme.customColors.white,
                height: 25.0,
              ),
            ),
          ),
        ),
      ],
    ),
  );

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getProductList(vcardId.toString());
  }
}
