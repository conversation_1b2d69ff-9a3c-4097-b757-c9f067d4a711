import 'dart:io';

import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ImageSelectionWidget extends StatelessWidget {
  final dynamic controller;

  const ImageSelectionWidget({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => Container(
        height: 145.h,
        decoration: _buildContainerDecoration(),
        child:
            _hasImages()
                ? ListView(
                  scrollDirection: Axis.horizontal,
                  children: [..._buildImageList(), _buildAddImageButton()],
                )
                : Center(child: _buildAddImageButton()),
      ),
    );
  }

  BoxDecoration _buildContainerDecoration() {
    return BoxDecoration(
      color: Get.theme.customColors.white,
      borderRadius: BorderRadius.circular(16.r),
    );
  }

  List<Widget> _buildImageList() {
    final totalImages =
        controller.existingImageUrls.length +
        controller.productImageFiles.length;

    return List.generate(totalImages, (index) {
      final isExistingImage = index < controller.existingImageUrls.length;

      return Padding(
        padding: EdgeInsets.only(right: 10.w),
        child: _buildImageItem(index, isExistingImage),
      );
    });
  }

  Widget _buildImageItem(int index, bool isExistingImage) {
    return Stack(
      children: [
        _buildImageContainer(index, isExistingImage),
        _buildDeleteButton(index),
      ],
    );
  }

  Widget _buildImageContainer(int index, bool isExistingImage) {
    return Container(
      width: 158.w,
      decoration: _buildImageContainerDecoration(),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: _buildImage(index, isExistingImage),
      ),
    );
  }

  BoxDecoration _buildImageContainerDecoration() {
    return BoxDecoration(
      color: Get.theme.customColors.white,
      borderRadius: BorderRadius.circular(16.r),
      border: Border.all(color: Get.theme.customColors.primaryColor!),
      boxShadow: [
        BoxShadow(
          color: Get.theme.customColors.black!.withValues(alpha: 0.1),
          blurRadius: 20,
          spreadRadius: 5,
          offset: const Offset(0, 1),
        ),
      ],
    );
  }

  Widget _buildImage(int index, bool isExistingImage) {
    if (isExistingImage) {
      return CustomImageView(
        height: 145.h,
        imagePath: controller.existingImageUrls[index],
        fit: BoxFit.cover,
      );
    } else {
      final fileIndex = index - controller.existingImageUrls.length;
      return Image.file(
        controller.productImageFiles[fileIndex],
        fit: BoxFit.cover,
      );
    }
  }

  Widget _buildDeleteButton(int index) {
    return Positioned(
      right: 8,
      top: 8,
      child: GestureDetector(
        onTap: () => controller.removeImage(index),
        child: Container(
          height: 24.h,
          width: 24.h,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Get.theme.customColors.white,
          ),
          child: Icon(
            Icons.close,
            size: 16,
            color: Get.theme.customColors.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildAddImageButton() {
    return Center(
      child: GestureDetector(
        onTap: _handleAddImage,
        child: _hasImages() ? _buildAddMoreButton() : _buildInitialAddButton(),
      ),
    );
  }

  bool _hasImages() {
    return controller.existingImageUrls.isNotEmpty ||
        controller.productImageFiles.isNotEmpty;
  }

  Widget _buildInitialAddButton() {
    return Container(
      padding: const EdgeInsets.all(3.0),
      decoration: _buildImageContainerDecoration(),
      child: Stack(
        children: [
          CustomImageView(
            height: 145.h,
            width: 158.w,
            fit: BoxFit.cover,
            radius: BorderRadius.circular(10.r),
            imagePath: 'assets/images/png/other/image_not_found.png',
          ),
          _buildCameraIcon(),
        ],
      ),
    );
  }

  Widget _buildCameraIcon() {
    return Positioned(
      right: 8,
      bottom: 13.0,
      child: Container(
        height: 35.0.h,
        width: 35.0.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Get.theme.customColors.white,
        ),
        alignment: Alignment.center,
        child: CustomImageView(
          imagePath: AssetConstants.icCamera,
          color: Get.theme.customColors.primaryColor,
        ),
      ),
    );
  }

  Widget _buildAddMoreButton() {
    return Container(
      height: 145.h,
      width: 158.w,
      padding: const EdgeInsets.all(3.0),
      decoration: _buildImageContainerDecoration(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.add_photo_alternate_outlined,
            size: 32,
            color: Get.theme.customColors.primaryColor,
          ),
          Gap(8.h),
          Text(
            AppStrings.T.lbl_add_more_images,
            // 'Add More Images',
            textAlign: TextAlign.center,
            style: Get.textTheme.bodySmall?.copyWith(
              color: Get.theme.customColors.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleAddImage() async {
    if (controller.isProcessingPWAIcon.value) {
      print("Processing is already in progress.");
      return;
    }

    // Add delay before setting processing state
    Future.delayed(const Duration(seconds: 1), () {
      controller.isProcessingPWAIcon.value = true;
    });

    try {
      final List<File> iconFiles =
          await PWAIconHandler.selectMultipleImagesFromGallery(
            context: Get.context!,
            targetSize: 512,
            maxKB: 50,
            format: 'png',
          );
      controller.productImageFiles.addAll(iconFiles);
    } catch (e) {
      print('Error creating PWA icon: $e');
    } finally {
      controller.isProcessingPWAIcon.value = false;
    }
  }
}
