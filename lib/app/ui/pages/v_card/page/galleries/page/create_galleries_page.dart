import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/galleries_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateGalleriesPage extends GetItHook<GalleriesController> {
  CreateGalleriesPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    final arguments = Get.arguments ?? {};
    final galleriesId = arguments['galleriesId'];

    if (galleriesId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getGalleriesById(id: galleriesId);
        final galleries = controller.galleriesDataById.value?.data;
        if (galleries != null) {
          controller.nameController.text = galleries.typeName;
          controller.galleriesUrlController.text = galleries.link ?? '';
          // Set gallery type based on data from API
          controller.selectedGalleryType.value = int.parse(galleries.type);
        }
      });
    }

    controller.isProcessingPWAIcon = false.obs;
  }

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments;
    final vcardId = arguments['vcardId'] ?? 0;
    final galleriesId = arguments['galleriesId'];

    if (galleriesId == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        controller.resetForm();
      });
    }

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          galleriesId == null
              ? AppStrings.T.lbl_create_galleries
              : AppStrings.T.lbl_edit_galleries,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createBlogPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),

      body: Obx(() {
        if (controller.isLoadingGalleriesById.value == true) {
          return CustomFormShimmerLoading();
        }

        return IgnorePointer(
          ignoring: controller.isLoadingCreateGalleries.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Gap(20.h),

                    AppText(
                      AppStrings.T.lbl_select_type,
                      style: Get.theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    Gap(8.h),
                    Obx(
                      () => Column(
                        children: [
                          // Main selection container
                          GestureDetector(
                            onTap: () {
                              controller.isExpanded.value =
                                  !controller.isExpanded.value;
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 16.w,
                                vertical: 12.h,
                              ),
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                                borderRadius:
                                    !controller.isExpanded.value
                                        ? BorderRadius.circular(8.r)
                                        : BorderRadius.only(
                                          topLeft: Radius.circular(8.r),
                                          topRight: Radius.circular(8.r),
                                        ),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  AppText(
                                    getGalleryTypeLabel(
                                      controller.selectedGalleryType.value,
                                    ),
                                    style: Get.theme.textTheme.bodyMedium
                                        ?.copyWith(fontWeight: FontWeight.w500),
                                  ),
                                  CustomImageView(
                                    imagePath:
                                        controller.isExpanded.value
                                            ? AssetConstants.icDownArrow2
                                            : AssetConstants.icRightArrow2,
                                    color:
                                        Get
                                            .theme
                                            .customColors
                                            .darkGreyTextColor,
                                  ),
                                ],
                              ),
                            ),
                          ),

                          // Dropdown options
                          if (controller.isExpanded.value) ...[
                            Container(
                              decoration: BoxDecoration(
                                border: Border(
                                  left: BorderSide(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  right: BorderSide(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  bottom: BorderSide(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  top: BorderSide.none,
                                ),
                                borderRadius: BorderRadius.only(
                                  bottomLeft: Radius.circular(8.r),
                                  bottomRight: Radius.circular(8.r),
                                ),
                              ),
                              child: Column(
                                children: List.generate(5, (index) {
                                  return GestureDetector(
                                    onTap: () {
                                      controller.selectedGalleryType.value =
                                          index;
                                      // Reset media files
                                      controller.profileImageFile.value = null;
                                      controller.videoFile.value = null;
                                      controller.audioFile.value = null;
                                      controller.galleryUploadFile.value = null;
                                      controller.isExpanded.value = false;
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.all(4.0),
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: 16.w,
                                          vertical: 12.h,
                                        ),
                                        decoration: BoxDecoration(
                                          color:
                                              controller
                                                          .selectedGalleryType
                                                          .value ==
                                                      index
                                                  ? Colors.grey[200]
                                                  : Colors.transparent,
                                          borderRadius: BorderRadius.circular(
                                            4.r,
                                          ),
                                        ),
                                        child: Row(
                                          children: [
                                            AppText(
                                              getGalleryTypeLabel(index),
                                              style:
                                                  Get.theme.textTheme.bodySmall,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                }),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),

                    Gap(20.h),

                    // Dynamic content based on gallery type
                    _buildGalleryTypeContent(context),
                    Gap(30.h),
                    _buildButtons(_formKey, vcardId, galleriesId),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildGalleryTypeContent(BuildContext context) {
    // Display different content based on gallery type
    if (controller.selectedGalleryType.value == -1) {
      return SizedBox.shrink(); // Or return a message like "Please select a type"
    }
    switch (controller.selectedGalleryType.value) {
      case 0: // Image
        return _buildImageSelector(context);
      case 1: // YouTube
        return _buildYoutubeField();
      case 2: // File
        return _buildFileSelector();
      case 3: // Video
        return _buildVideoSelector();
      case 4: // Audio
        return _buildAudioSelector();
      default:
        return _buildImageSelector(context);
    }
  }

  Widget _buildImageSelector(BuildContext context) {
    return Center(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          AppText(
            AppStrings.T.lbl_gallery_image,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Gap(8.h),
          // Obx(() {
          //   return \
          Stack(
            children: [
              Obx(() {
                return GestureDetector(
                  onTap: () async {
                    if (controller.isProcessingPWAIcon.value) {
                      print("Processing is already in progress.");
                      return; // Exit early if processing is ongoing
                    } else {
                      Future.delayed(Duration(seconds: 1), () {
                        controller.isProcessingPWAIcon.value = true;
                      });

                      try {
                        final File? iconFile =
                            await PWAIconHandler.createPWAIcon(
                              context: context,
                              source: ImageSource.gallery,
                              size: 512,
                              maxKB: 50,
                              format: 'png',
                            );

                        if (iconFile != null) {
                          controller.profileImageFile.value = iconFile;
                          // The icon is now ready for upload
                        }
                      } catch (e) {
                        print('Error creating PWA icon: $e');
                        // Display an error message or toast if needed
                      } finally {
                        // Reset the processing flag
                        controller.isProcessingPWAIcon.value = false;
                      }
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.all(3.0),
                    decoration: BoxDecoration(
                      color: Get.theme.customColors.white,
                      borderRadius: BorderRadius.circular(16.r),
                      border: Border.all(
                        color: Get.theme.customColors.primaryColor!,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Get.theme.customColors.black!.withValues(
                            alpha: 0.1,
                          ),
                          blurRadius: 20,
                          spreadRadius: 5,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        // Show shimmer while processing, else show image
                        if (controller.isProcessingPWAIcon.value)
                          ShimmerBox(
                            height: 145.h,
                            width: 158.w,
                            borderRadius: BorderRadius.circular(12.r),
                          )
                        else
                          CustomImageView(
                            height: 145.h,
                            width: 158.w,
                            fit: BoxFit.cover,
                            radius: BorderRadius.circular(10.r),
                            imagePath:
                                controller.profileImageFile.value?.path ??
                                controller
                                    .galleriesDataById
                                    .value
                                    ?.data
                                    .galleryImage,
                          ),
                        Positioned(
                          right: 8,
                          bottom: 13.0,
                          child: Container(
                            height: 35.0.h,
                            width: 35.0.h,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Get.theme.customColors.white,
                            ),
                            alignment: Alignment.center,
                            child: CustomImageView(
                              imagePath: AssetConstants.icCamera,
                              color: Get.theme.customColors.primaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }),
              // Container(
              //   height: 140.h,
              //   width: 150.w,
              //   decoration: BoxDecoration(
              //     color: Get.theme.customColors.white,
              //     borderRadius: BorderRadius.circular(16.r),
              //     border: Border.all(
              //       color: Get.theme.customColors.primaryColor!,
              //     ),
              //     boxShadow: [
              //       BoxShadow(
              //         color: Get.theme.customColors.black!.withValues(
              //           alpha: 0.1,
              //         ),
              //         blurRadius: 20,
              //         spreadRadius: 5,
              //         offset: Offset(0, 1),
              //       ),
              //     ],
              //   ),
              //   child: Padding(
              //     padding: const EdgeInsets.all(4.0),
              //     child: CustomImageView(
              //       height: 145.h,
              //       width: 145.w,
              //       fit: BoxFit.cover,
              //       radius: BorderRadius.circular(12.r),
              //       onTap: () async {
              //         if (controller.isProcessingPWAIcon.value) {
              //           Logger.log("Processing is already in progress.");
              //           return; // Exit early if processing is ongoing
              //         } else {
              //           controller.isProcessingPWAIcon.value = true;

              //           try {
              //             final File? image =
              //                 await PWAIconHandler.createPWAIcon(
              //                   context: context,
              //                   source: ImageSource.gallery,
              //                   size: 512,
              //                   maxKB: 50,
              //                   format: 'png',
              //                 );

              //             if (image != null) {
              //               controller.profileImageFile.value = File(
              //                 image.path,
              //               );
              //             }
              //           } catch (e) {
              //             print('Error creating PWA icon: $e');
              //             // Display an error message or toast if needed
              //           } finally {
              //             // Reset the processing flag
              //             controller.isProcessingPWAIcon.value = false;
              //           }
              //         }
              //       },
              //       imagePath:
              //           controller.profileImageFile.value?.path ??
              //           controller.galleriesDataById.value?.data.galleryImage,
              //     ),
              //   ),
              // ),
              Positioned(
                right: 10.w,
                bottom: 10.h,
                child: CircleAvatar(
                  radius: 22.r,
                  backgroundColor: Get.theme.customColors.white,
                  child: CustomImageView(
                    imagePath: AssetConstants.icCamera,
                    color: Get.theme.customColors.primaryColor,
                  ),
                ),
              ),
            ],
          ),
          // }),
        ],
      ),
    );
  }

  Widget _buildYoutubeField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_youtube_link,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        TextInputField(
          type: InputType.text,
          controller: controller.galleriesUrlController,
          label: AppStrings.T.lbl_youtube_link,
          validator: (value) {
            if (controller.selectedGalleryType.value == 1) {
              if (value == null || value.isEmpty) {
                return AppStrings.T.lbl_please_enter_youtube_link;
              }
              if (!value.contains('youtube.com') &&
                  !value.contains('youtu.be')) {
                return AppStrings.T.lbl_please_enter_valid_youtube_link;
              }
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildFileSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_upload_file,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Obx(
          () => Container(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            decoration: BoxDecoration(
              border: Border.all(color: Get.theme.customColors.primaryColor!),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: AppText(
                    controller.galleryUploadFile.value?.path.split('/').last ??
                        (controller.selectedGalleryType.value == 2 &&
                                controller
                                        .galleriesDataById
                                        .value
                                        ?.data
                                        .galleryImage !=
                                    null
                            ? controller
                                .galleriesDataById
                                .value!
                                .data
                                .galleryImage
                                .split('/')
                                .last
                            : AppStrings.T.lbl_no_file_selected),
                    maxLines: 1,
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                CustomElevatedButton(
                  text: AppStrings.T.lbl_browse,
                  height: 40.h,
                  width: 90.w,
                  onPressed: () async {
                    final FilePickerResult? result =
                        await FilePicker.platform.pickFiles();
                    if (result != null) {
                      controller.galleryUploadFile.value = File(
                        result.files.single.path!,
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_upload_video,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Obx(
          () => Container(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            decoration: BoxDecoration(
              border: Border.all(color: Get.theme.customColors.primaryColor!),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: AppText(
                    controller.videoFile.value?.path.split('/').last ??
                        (controller.selectedGalleryType.value == 3 &&
                                controller
                                        .galleriesDataById
                                        .value
                                        ?.data
                                        .galleryImage !=
                                    null
                            ? controller
                                .galleriesDataById
                                .value!
                                .data
                                .galleryImage
                                .split('/')
                                .last
                            : AppStrings.T.lbl_no_video_selected),
                    maxLines: 1,
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                CustomElevatedButton(
                  text: AppStrings.T.lbl_browse,
                  height: 40.h,
                  width: 90.w,
                  onPressed: () async {
                    final FilePickerResult? result = await FilePicker.platform
                        .pickFiles(type: FileType.video);
                    if (result != null) {
                      controller.videoFile.value = File(
                        result.files.single.path!,
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAudioSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_upload_audio,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Obx(
          () => Container(
            padding: EdgeInsets.symmetric(vertical: 12.h, horizontal: 16.w),
            decoration: BoxDecoration(
              border: Border.all(color: Get.theme.customColors.primaryColor!),
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Row(
              children: [
                Expanded(
                  child: AppText(
                    controller.audioFile.value?.path.split('/').last ??
                        (controller.selectedGalleryType.value == 4 &&
                                controller
                                        .galleriesDataById
                                        .value
                                        ?.data
                                        .galleryImage !=
                                    null
                            ? controller
                                .galleriesDataById
                                .value!
                                .data
                                .galleryImage
                                .split('/')
                                .last
                            : AppStrings.T.lbl_no_audio_selected),
                    maxLines: 1,
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                CustomElevatedButton(
                  text: AppStrings.T.lbl_browse,
                  height: 40.h,
                  width: 90.w,
                  onPressed: () async {
                    final FilePickerResult? result = await FilePicker.platform
                        .pickFiles(type: FileType.audio);
                    if (result != null) {
                      controller.audioFile.value = File(
                        result.files.single.path!,
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildButtons(
    GlobalKey<FormState> formKey,
    String vcardId,
    dynamic galleriesId,
  ) {
    return Column(
      children: [
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            isLoading: controller.isLoadingCreateGalleries.value,
            text: AppStrings.T.lbl_save,
            onPressed: () async {
              if (controller.isLoadingCreateGalleries.value) {
                return;
              }
              if (controller.isShowingToast.value) {
                return;
              }

              if (!_formKey.currentState!.validate()) {
                return;
              }
              if (galleriesId == null) {
                if (controller.selectedGalleryType.value == -1) {
                  toastification.show(
                    type: ToastificationType.error,
                    style: ToastificationStyle.flatColored,
                    showProgressBar: false,
                    alignment: Alignment.topCenter,
                    title: Text(AppStrings.T.lbl_please_select_type),
                    autoCloseDuration: const Duration(seconds: 3),
                  );
                  return;
                }

                // Validate file selection based on gallery type
                if (controller.selectedGalleryType.value == 0 &&
                    controller.profileImageFile.value == null &&
                    controller.galleriesDataById.value?.data.galleryImage ==
                        null) {
                  if (controller.isProcessingPWAIcon.value) {
                    _showImageValidationToast(
                      AppStrings.T.lbl_image_processing_message,
                      false,
                    );
                    return;
                  } else if (controller.profileImageFile.value == null) {
                    _showImageValidationToast(
                      AppStrings.T.lbl_please_select_image,
                      true,
                    );
                  }
                  return;
                } else if (controller.selectedGalleryType.value == 2 &&
                    controller.galleryUploadFile.value == null &&
                    controller.galleriesDataById.value?.data.galleryImage ==
                        null) {
                  toastification.show(
                    type: ToastificationType.error,
                    style: ToastificationStyle.flatColored,
                    showProgressBar: false,
                    alignment: Alignment.topCenter,
                    title: Text(AppStrings.T.lbl_please_select_file),
                    autoCloseDuration: const Duration(seconds: 3),
                  );

                  return;
                } else if (controller.selectedGalleryType.value == 3 &&
                    controller.videoFile.value == null &&
                    controller.galleriesDataById.value?.data.galleryImage ==
                        null) {
                  toastification.show(
                    type: ToastificationType.error,
                    style: ToastificationStyle.flatColored,
                    showProgressBar: false,
                    alignment: Alignment.topCenter,
                    title: Text(AppStrings.T.lbl_please_select_video),
                    autoCloseDuration: const Duration(seconds: 3),
                  );
                  return;
                } else if (controller.selectedGalleryType.value == 4 &&
                    controller.audioFile.value == null &&
                    controller.galleriesDataById.value?.data.galleryImage ==
                        null) {
                  toastification.show(
                    type: ToastificationType.error,
                    style: ToastificationStyle.flatColored,
                    showProgressBar: false,
                    alignment: Alignment.topCenter,
                    title: Text(AppStrings.T.lbl_please_select_audio),
                    autoCloseDuration: const Duration(seconds: 3),
                  );
                  return;
                }

                await controller.createAdminGalleries(
                  name: controller.nameController.text,
                  type: controller.selectedGalleryType.value,
                  profileImg: controller.profileImageFile.value?.path,
                  galleryFile: controller.galleryUploadFile.value?.path,
                  videoFile: controller.videoFile.value?.path,
                  audioFile: controller.audioFile.value?.path,
                  vcardId: vcardId.toString(),
                  galleriesUrl:
                      controller.galleriesUrlController.text
                          .trim()
                          .toLowerCase(),
                );
              } else {
                if (controller.isProcessingPWAIcon.value) {
                  _showImageValidationToast(
                    AppStrings.T.lbl_image_processing_message,
                    false,
                  );
                  return;
                }
                controller.isLoadingCreateGalleries.value = true;
                String? imagePath;
                String? filePath;
                String? videoPath;
                String? audioPath;

                // Image
                if (controller.selectedGalleryType.value == 0) {
                  if (controller.profileImageFile.value?.path != null) {
                    imagePath = controller.profileImageFile.value!.path;
                  } else if (controller
                          .galleriesDataById
                          .value
                          ?.data
                          .galleryImage !=
                      null) {
                    final file = await urlToFile(
                      controller.galleriesDataById.value!.data.galleryImage,
                    );
                    imagePath = file.path;
                  }
                }
                // File
                else if (controller.selectedGalleryType.value == 2) {
                  if (controller.galleryUploadFile.value?.path != null) {
                    filePath = controller.galleryUploadFile.value!.path;
                  } else if (controller
                          .galleriesDataById
                          .value
                          ?.data
                          .galleryImage !=
                      null) {
                    final file = await urlToFile(
                      controller.galleriesDataById.value!.data.galleryImage,
                    );
                    filePath = file.path;
                  }
                }
                // Video
                else if (controller.selectedGalleryType.value == 3) {
                  if (controller.videoFile.value?.path != null) {
                    videoPath = controller.videoFile.value!.path;
                  } else if (controller
                          .galleriesDataById
                          .value
                          ?.data
                          .galleryImage !=
                      null) {
                    final file = await urlToFile(
                      controller.galleriesDataById.value!.data.galleryImage,
                    );
                    videoPath = file.path;
                  }
                }
                // Audio
                else if (controller.selectedGalleryType.value == 4) {
                  if (controller.audioFile.value?.path != null) {
                    audioPath = controller.audioFile.value!.path;
                  } else if (controller
                          .galleriesDataById
                          .value
                          ?.data
                          .galleryImage !=
                      null) {
                    final file = await urlToFile(
                      controller.galleriesDataById.value!.data.galleryImage,
                    );
                    audioPath = file.path;
                  }
                }

                await controller.updateGalleries(
                  galleriesId: galleriesId,
                  vcardId: vcardId.toString(),
                  name: controller.nameController.text,
                  type: controller.selectedGalleryType.value,
                  profileImg: imagePath,
                  galleryFile: filePath,
                  videoFile: videoPath,
                  audioFile: audioPath,
                  galleriesUrl:
                      controller.galleriesUrlController.text
                          .trim()
                          .toLowerCase(),
                );
              }
            },
          ),
        ),
      ],
    );
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.nameController.clear();
    controller.galleriesUrlController.clear();
    controller.profileImageFile.value = null;
    controller.galleryUploadFile.value = null;
    controller.videoFile.value = null;
    controller.audioFile.value = null;
    controller.selectedGalleryType.value = -1;
    controller.isExpanded.value = false;
    controller.isLoadingCreateGalleries.value = false;
  }

  String getGalleryTypeLabel(int value) {
    if (value == -1) {
      return AppStrings.T.lbl_select_type;
    }
    switch (value) {
      case 0:
        return AppStrings.T.lbl_image;
      case 1:
        return AppStrings.T.lbl_youtube;
      case 2:
        return AppStrings.T.lbl_file;
      case 3:
        return AppStrings.T.lbl_video;
      case 4:
        return AppStrings.T.lbl_audio;
      default:
        return '';
    }
  }
}
