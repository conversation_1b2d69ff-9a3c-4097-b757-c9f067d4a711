import 'package:get/get.dart';
import 'package:v_card/app/controllers/galleries_controller.dart';
import 'package:v_card/app/data/model/galleries/galleries_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/blog/widget/action_button.dart';
import 'package:v_card/app/ui/pages/v_card/page/template/widgets/template_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class GalleriesPage extends GetItHook<GalleriesController> {
  GalleriesPage({super.key});
  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_galleries,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      floatingActionButton: GestureDetector(
        onTap: () {
          controller.resetForm();
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createGalleriesPage,
            arguments: {'vcardId': vcardId},
          );
        },
        child: CircleAvatar(
          radius: 32.r,
          backgroundColor: Get.theme.customColors.primaryColor,
          child: const Icon(Icons.add, size: 32),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () => controller.getgalleriesList(vcardId),
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: TemplatesShimmer());
          }

          final galleriesList = controller.galleriesList.value?.data ?? [];

          if (galleriesList.isEmpty) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: NoDataWidget(
                message: AppStrings.T.lbl_no_data,
                padding: EdgeInsets.only(top: 180.h),
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: GridView.builder(
              physics: const AlwaysScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
                childAspectRatio: 0.8,
              ),
              itemCount: galleriesList.length,
              itemBuilder:
                  (context, index) =>
                      _buildTemplateCard(context, galleriesList[index]),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildTemplateCard(BuildContext context, Gallery gallery) {
    return Card(
      clipBehavior: Clip.hardEdge,
      color: Get.theme.customColors.white,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Column(
        children: [
          Expanded(child: _buildImageBox(gallery)),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ActionButton(
                  iconPath: AssetConstants.svgEye,
                  onTap: () => _viewDetailDialog(context, gallery),
                ),
                ActionButton(
                  iconPath: AssetConstants.icEdit,
                  onTap: () {
                    NavigationService.navigateWithSlideAnimation(
                      AppRoutes.createGalleriesPage,
                      arguments: {
                        'vcardId': vcardId,
                        'galleriesId': gallery.id,
                      },
                    );
                  },
                ),
                ActionButton(
                  iconPath: AssetConstants.icDelete2,
                  onTap: () {
                    Get.dialog(
                      LoadingConfirmationDialog(
                        title: AppStrings.T.lbl_delete_galleries,
                        message: AppStrings.T.lbl_delete_galleries_subtitle,
                        onCancel: () => NavigationService.navigateBack(),
                        onConfirm:
                            () => controller.deleteGalleriesById(
                              id: gallery.id,
                              vcardId: vcardId,
                            ),
                        isLoading: controller.isLoadingDeleteGalleriesById,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<dynamic> _viewDetailDialog(BuildContext context, Gallery gallery) {
    return showDialog(
      context: context,
      builder:
          (_) => AlertDialog(
            insetPadding: EdgeInsets.symmetric(horizontal: 20.0.w),
            backgroundColor: Get.theme.customColors.white,
            title: Text(
              gallery.typeName,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 18.0.sp, fontWeight: FontWeight.w600),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    height: 200.0.h,
                    width: double.maxFinite,
                    clipBehavior: Clip.hardEdge,

                    decoration: BoxDecoration(
                      color: Get.theme.customColors.white,
                      borderRadius: BorderRadius.circular(10.0),
                      boxShadow: [
                        BoxShadow(
                          color: Get.theme.customColors.shadowColor!.withValues(
                            alpha: 0.2,
                          ),
                          blurRadius: 6.0,
                        ),
                      ],
                    ),
                    child: _buildImageBox(gallery),
                  ),
                  Gap(12.h),
                  Builder(
                    builder: (_) {
                      final String type = gallery.type;

                      switch (type) {
                        case "4": // Audio
                          return Text(
                            gallery.galleryImage,
                            style: Get.textTheme.titleMedium?.copyWith(
                              color: Get.theme.customColors.black,
                            ),
                            textAlign: TextAlign.center,
                          );
                        case "3": // Video
                          return Text(
                            gallery.galleryImage,
                            style: Get.textTheme.titleMedium?.copyWith(
                              color: Get.theme.customColors.black,
                            ),
                            textAlign: TextAlign.center,
                          );
                        case "2": // Document
                          return Text(
                            gallery.galleryImage,
                            style: Get.textTheme.titleMedium?.copyWith(
                              color: Get.theme.customColors.black,
                            ),
                            textAlign: TextAlign.center,
                          );
                        case "1": // YouTube
                          return Text(
                            "${gallery.link}",
                            style: Get.textTheme.titleMedium?.copyWith(
                              color: Get.theme.customColors.black,
                            ),
                            textAlign: TextAlign.center,
                          );
                        default: // Image
                          return SizedBox.shrink();
                      }
                    },
                  ),
                  Gap(10.h),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text("Close"),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildImageBox(Gallery gallery) {
    return Builder(
      builder: (_) {
        final String type = gallery.type;

        final Color blueBg = Color(0xFF1746A2).withValues(alpha: 0.1);
        final Color redBg = Color(0xFFDC2825).withValues(alpha: 0.1);

        switch (type) {
          case "4": // Audio
            return Container(
              width: double.maxFinite,
              decoration: BoxDecoration(color: blueBg),
              child: Icon(
                Icons.audio_file,
                color: Color(0xFF1746A2),
                size: 50.0,
              ),
            );
          case "3": // Video
            return Container(
              width: double.maxFinite,
              decoration: BoxDecoration(color: blueBg),
              child: Icon(
                Icons.video_file_rounded,
                color: Color(0xFF1746A2),
                size: 50.0,
              ),
            );
          case "2": // Document
            return Container(
              width: double.maxFinite,
              decoration: BoxDecoration(color: blueBg),
              child: Icon(
                Icons.insert_drive_file_rounded,
                color: Color(0xFF1746A2),
                size: 50.0,
              ),
            );
          case "1": // YouTube
            return Container(
              width: double.maxFinite,
              decoration: BoxDecoration(color: redBg),
              alignment: Alignment.center,
              child: CustomImageView(
                imagePath: AssetConstants.icYoutube,
                fit: BoxFit.contain,
                height: 50.0,
                width: 50.0,
              ),
            );
          default: // Image
            return CustomImageView(
              width: double.maxFinite,
              imagePath: gallery.galleryImage,
              fit: BoxFit.cover,
            );
        }
      },
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getgalleriesList(vcardId);
  }
}
