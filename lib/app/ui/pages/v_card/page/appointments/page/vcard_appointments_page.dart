import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/vcard_appointments_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/appointments/widget/vcard_appointment_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class VcardAppointmentsPage extends GetItHook<AppointmentsVcardController> {
  VcardAppointmentsPage({super.key});
  final vcardId = Get.arguments['vcardId'].toString();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_appointments,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.socialConnectPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(
        () =>
            controller.isLoadingAppointments.value
                ? VcardAppointmentsShimmer()
                : _buildAppointmentsForm(context, vcardId),
      ),
    );
  }

  Widget _buildAppointmentsForm(BuildContext context, String vcardId) {
    return IgnorePointer(
      ignoring: controller.isLoadingCreateAppointments.value,
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Days selection
            _buildDaysExpansionTiles(context),

            Gap(24.h),

            // Appointment Settings
            _buildPaidAppointmentsSection(context),

            Gap(24.h),
            // Buttons
            Obx(
              () => CustomElevatedButton(
                checkConnectivity: true,
                text: AppStrings.T.lbl_save,
                isLoading: controller.isLoadingCreateAppointments.value,
                onPressed: () => _saveAppointments(vcardId),
              ),
            ),
            Gap(10.h),
          ],
        ),
      ),
    );
  }

  Widget _buildDaysExpansionTiles(BuildContext context) {
    final dayNames = [
      AppStrings.T.lbl_monday,
      AppStrings.T.lbl_tuesday,
      AppStrings.T.lbl_wednesday,
      AppStrings.T.lbl_thursday,
      AppStrings.T.lbl_friday,
      AppStrings.T.lbl_saturday,
      AppStrings.T.lbl_sunday,
    ];

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 7,
      itemBuilder: (context, index) {
        final day = index + 1; // 1-7 where 1 is Monday
        return _buildDayExpansionTile(context, dayNames[index], day);
      },
    );
  }

  Widget _buildDayExpansionTile(
    BuildContext context,
    String dayName,
    int dayNumber,
  ) {
    return Obx(() {
      final isSelected = controller.selectedWeekDays.contains(dayNumber);
      final dayTimeSlots =
          controller.timeSlots
              .where((slot) => slot.dayOfWeek == dayNumber)
              .toList();

      return Card(
        color: Get.theme.customColors.white,
        margin: EdgeInsets.symmetric(vertical: 6.h),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        elevation: 0.5,
        child: Theme(
          data: Theme.of(context).copyWith(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            hoverColor: Colors.transparent,
            dividerColor: Colors.transparent,
          ),
          child: ExpansionTile(
            initiallyExpanded: isSelected && dayTimeSlots.isNotEmpty,
            onExpansionChanged: (expanded) {
              // if (expanded && !isSelected) {
              controller.toggleWeekDay(dayNumber);
              // }
            },
            title: Row(
              children: [
                GestureDetector(
                  // onTap: () => controller.toggleWeekDay(dayNumber),
                  child: Container(
                    width: 24.w,
                    height: 24.w,
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child:
                        isSelected
                            ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 18,
                            )
                            : Gap(0),
                  ),
                ),
                Gap(16.w),
                Text(
                  dayName,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: isSelected ? Colors.black : Colors.grey,
                  ),
                ),
              ],
            ),
            trailing:
                isSelected
                    ? CustomImageView(imagePath: AssetConstants.icDownArrow2)
                    : CustomImageView(imagePath: AssetConstants.icRightArrow2),
            children: [
              if (dayTimeSlots.isEmpty)
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0.r),
                    border: Border(
                      top: BorderSide(
                        color: Get.theme.customColors.borderColor!,
                      ),
                    ),
                  ),
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 8.h,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AppText(
                        AppStrings.T.lbl_no_time_slots_added,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Get.theme.customColors.greyTextColor,
                        ),
                      ),
                      TextButton.icon(
                        onPressed:
                            () => controller.addTimeSlotForDay(dayNumber),
                        icon: Icon(
                          Icons.add_circle_outline,
                          size: 18,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        label: Text(
                          AppStrings.T.lbl_add,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        style: TextButton.styleFrom(padding: EdgeInsets.zero),
                      ),
                    ],
                  ),
                )
              else
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.0.r),
                    border: Border(
                      top: BorderSide(
                        color: Get.theme.customColors.borderColor!,
                      ),
                    ),
                  ),
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: dayTimeSlots.length,
                    // padding: EdgeInsets.only(bottom: 16.h),
                    itemBuilder: (context, index) {
                      final slot = dayTimeSlots[index];
                      return Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 8.h,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: _buildTimeSelector(
                                context,
                                slot.startTime,
                                (value) => controller.updateTimeSlot(
                                  index: controller.timeSlots.indexOf(slot),
                                  dayOfWeek: dayNumber,
                                  startTime: value,
                                  endTime: slot.endTime,
                                ),
                                AppStrings.T.lbl_start,
                              ),
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 8.w),
                              child: Text(
                                AppStrings.T.lbl_to,
                                style: TextStyle(fontSize: 14.sp),
                              ),
                            ),
                            Expanded(
                              child: _buildTimeSelector(
                                context,
                                slot.endTime,
                                (value) => controller.updateTimeSlot(
                                  index: controller.timeSlots.indexOf(slot),
                                  dayOfWeek: dayNumber,
                                  startTime: slot.startTime,
                                  endTime: value,
                                ),
                                AppStrings.T.lbl_end,
                              ),
                            ),

                            Gap(10.0),
                            SizedBox(
                              height: 24.0,
                              width: 24.0,
                              child: IconButton(
                                icon: Container(
                                  height: 24.0,
                                  width: 24.0,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Get.theme.customColors.primaryColor!
                                        .withValues(alpha: 0.1),
                                  ),
                                  alignment: Alignment.center,
                                  child: CustomImageView(
                                    imagePath: AssetConstants.icDelete2,
                                    color: Get.theme.customColors.primaryColor,
                                    height: 16.0,
                                  ),
                                ),
                                onPressed:
                                    () => controller.removeTimeSlot(slot),
                                padding: EdgeInsets.zero,
                                constraints: const BoxConstraints(),
                                visualDensity: VisualDensity.compact,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),

              if (isSelected && dayTimeSlots.isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(
                    left: 16.w,
                    right: 16.w,
                    bottom: 16.h,
                  ),
                  child: Align(
                    alignment: Alignment.centerLeft,
                    child: TextButton.icon(
                      onPressed: () => controller.addTimeSlotForDay(dayNumber),
                      icon: Icon(
                        Icons.add,
                        size: 18,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      label: Text(
                        AppStrings.T.lbl_add_slot,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      style: TextButton.styleFrom(padding: EdgeInsets.zero),
                    ),
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildTimeSelector(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
    String label,
  ) {
    return GestureDetector(
      onTap:
          () => _showTimePopup(
            context,
            currentValue,
            onChanged,
            controller.timeOptions,
          ),
      child: Container(
        height: 44.h,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0),
          border: Border.all(
            color: Get.theme.customColors.lightGreyBackgroundColor!,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              currentValue,
              style: TextStyle(color: Colors.black, fontSize: 14.sp),
            ),
            CustomImageView(imagePath: AssetConstants.icDownArrow, height: 8.0),
            // Icon(
            //   Icons.access_time,
            //   size: 18,
            //   color: Theme.of(context).colorScheme.primary,
            // ),
          ],
        ),
      ),
    );
  }

  void _showTimePopup(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
    List<String> options, // Accept options parameter
  ) {
    final selectedIndex = options.indexOf(currentValue);

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
      ),
      builder:
          (BuildContext context) => Container(
            width: double.infinity,
            height: 300.h,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(16.r)),
            ),
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Gap(60.w),
                      Text(
                        AppStrings.T.lbl_select_time,
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(right: 16.w),
                        child: GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Text(
                            AppStrings.T.lbl_done,
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: CupertinoPicker(
                    scrollController: FixedExtentScrollController(
                      initialItem: selectedIndex != -1 ? selectedIndex : 0,
                    ),
                    itemExtent: 40,
                    onSelectedItemChanged: (index) {
                      onChanged(options[index]);
                    },
                    children:
                        options
                            .map(
                              (time) => Center(
                                child: Text(
                                  time,
                                  style: TextStyle(fontSize: 16.sp),
                                ),
                              ),
                            )
                            .toList(),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildPaidAppointmentsSection(BuildContext context) {
    return Card(
      color: Get.theme.customColors.white,
      margin: EdgeInsets.symmetric(vertical: 6.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 0.5,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppText(
              AppStrings.T.lbl_appointment_settings,
              style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
            ),
            Gap(16.h),
            Row(
              children: [
                Obx(
                  () => SizedBox(
                    width: 24.w,
                    height: 24.w,
                    child: Checkbox(
                      value: controller.isPaidAppointment.value,
                      onChanged:
                          (value) =>
                              controller.isPaidAppointment.value =
                                  value ?? false,
                      activeColor: Theme.of(context).colorScheme.primary,
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      visualDensity: VisualDensity.compact,
                    ),
                  ),
                ),
                Gap(12.w),
                AppText(
                  AppStrings.T.lbl_paid_appointments,
                  style: TextStyle(fontSize: 15.sp),
                ),
              ],
            ),
            Obx(
              () =>
                  controller.isPaidAppointment.value
                      ? Padding(
                        padding: EdgeInsets.only(top: 16.h),
                        child: TextInputField(
                          controller: controller.appointmentPriceController,
                          label: AppStrings.T.lbl_price,
                          type: InputType.phoneNumber,
                        ),
                      )
                      : const SizedBox.shrink(),
            ),
          ],
        ),
      ),
    );
  }

  void _saveAppointments(String vcardId) {
    if (controller.isLoadingCreateAppointments.value) {
      return;
    }
    if (controller.selectedWeekDays.isEmpty) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text(AppStrings.T.lbl_please_select_at_least_one_day),
        autoCloseDuration: const Duration(seconds: 3),
      );
      return;
    }

    if (controller.isPaidAppointment.value &&
        (controller.appointmentPriceController.text.isEmpty ||
            double.tryParse(controller.appointmentPriceController.text) ==
                null)) {
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text(AppStrings.T.lbl_please_enter_a_valid_price),
        autoCloseDuration: const Duration(seconds: 3),
      );
      return;
    }

    controller.saveVcardAppointments(vcardId: vcardId);
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.resetController();
  }

  @override
  void onInit() {
    controller.getVcardAppointments(vcardId: vcardId);
  }
}

class AppointmentTimeSlot {
  final int id;
  final int dayOfWeek;
  String startTime;
  String endTime;

  AppointmentTimeSlot({
    this.id = 0,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
  });
}
