import 'package:v_card/app/utils/helpers/exporter.dart';

class VcardAppointmentsShimmer extends StatelessWidget {
  const VcardAppointmentsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Days selection shimmer
          _buildDaysShimmer(context),
          Gap(24.h),
          // Appointment Settings shimmer
          _buildSettingsShimmer(context),
          Gap(24.h),
          // Buttons shimmer
          _buildButtonsShimmer(context),
        ],
      ),
    );
  }

  Widget _buildDaysShimmer(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: 7,
      itemBuilder: (context, index) {
        return Card(
          color: Colors.white,
          margin: EdgeInsets.symmetric(vertical: 6.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0.5,
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                Row(
                  children: [
                    ShimmerBox(width: 24.w, height: 24.w),
                    Gap(16.w),
                    ShimmerBox(width: 100.w, height: 20.h),
                    const Spacer(),
                    Container(width: 20.w, height: 20.h, color: Colors.white),
                  ],
                ),

                Gap(8.h),
                Row(
                  children: [
                    Expanded(
                      child: ShimmerBox(height: 44.w, width: double.infinity),
                    ),
                    Gap(16.w),
                    ShimmerBox(width: 20, height: 20.h),
                    Gap(16.w),
                    Expanded(
                      child: ShimmerBox(height: 44.h, width: double.infinity),
                    ),
                  ],
                ),
                Gap(16.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Row(
                    children: [
                      ShimmerBox(width: 20, height: 20.h),
                      Gap(8.w),
                      ShimmerBox(width: 100.w, height: 20.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsShimmer(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(vertical: 6.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 0.5,
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerBox(width: 150.w, height: 20.h),
            Gap(16.h),
            Row(
              children: [
                ShimmerBox(width: 24.w, height: 24.w),
                Gap(12.w),
                ShimmerBox(width: 150.w, height: 20.h),
              ],
            ),
            Gap(16.h),
            ShimmerBox(width: double.infinity, height: 56.h),
          ],
        ),
      ),
    );
  }

  Widget _buildButtonsShimmer(BuildContext context) {
    return Column(
      children: [
        ShimmerBox(width: double.infinity, height: 50.h),
        Gap(16.h),
        ShimmerBox(width: double.infinity, height: 50.h),
      ],
    );
  }
}
