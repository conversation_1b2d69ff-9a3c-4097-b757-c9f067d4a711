import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class BusinessHoursShimmerLoading extends StatelessWidget {
  const BusinessHoursShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Create 7 day tiles (one for each day of the week)
            ...List.generate(7, (index) => _buildDayShimmer(context)),

            // Add button shimmers at the bottom
            Gap(16.h),
            _buildButtonShimmer(context),
            Gap(16.h),
            _buildButtonShimmer(context, isSecondary: true),
          ],
        ),
      ),
    );
  }

  Widget _buildDayShimmer(BuildContext context) {
    return Card(
      color: Get.theme.customColors.white,
      margin: EdgeInsets.symmetric(vertical: 6.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      elevation: 0.5,
      child: Column(
        children: [
          // Day header (title and arrow)
          Padding(
            padding: EdgeInsets.only(
              left: 16.w,
              right: 16.w,
              bottom: 8.h,
              top: 16.h,
            ),
            child: Row(
              children: [
                // Checkbox placeholder
                ShimmerBox(width: 24.w, height: 24.w),
                Gap(16.w),
                // Day name placeholder
                ShimmerBox(width: 100.w, height: 20.h),
                Spacer(),
                // Arrow icon placeholder
                ShimmerBox(
                  width: 20.w,
                  height: 20.h,
                  borderRadius: BorderRadius.circular(100.r),
                ),
              ],
            ),
          ),

          Divider(),

          // Inner content shimmer (time selection fields)
          Padding(
            padding: EdgeInsets.only(
              left: 16.w,
              right: 16.w,
              bottom: 16.h,
              top: 8.h,
            ),
            child: Row(
              children: [
                // Start time selector
                Expanded(
                  child: Container(
                    height: 48.h,
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                      color: Colors.white,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Time text placeholder
                        ShimmerBox(width: 70.w, height: 18.h),
                        // Down arrow circle
                        ShimmerBox(
                          width: 28.r,
                          height: 28.r,
                          borderRadius: BorderRadius.circular(100.r),
                        ),
                      ],
                    ),
                  ),
                ),

                // "to" text placeholder
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 8.w),
                  child: ShimmerBox(width: 20.w, height: 16.h),
                ),

                // End time selector
                Expanded(
                  child: Container(
                    height: 48.h,
                    padding: EdgeInsets.symmetric(horizontal: 12.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                      color: Colors.white,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Time text placeholder
                        ShimmerBox(width: 70.w, height: 18.h),
                        // Down arrow circle
                        ShimmerBox(
                          width: 28.r,
                          height: 28.r,
                          borderRadius: BorderRadius.circular(100.r),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtonShimmer(BuildContext context, {bool isSecondary = false}) {
    return ShimmerBox(height: 50.h, width: double.infinity);
  }
}
