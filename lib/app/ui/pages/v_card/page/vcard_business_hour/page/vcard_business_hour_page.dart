import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/vcard_business_hour_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/vcard_business_hour/widgets/business_hour_page_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class VcardBusinessHoursPage extends GetItHook<VcardBusinessHoursController> {
  const VcardBusinessHoursPage({super.key});

  @override
  Widget build(BuildContext context) {
    final String vcardId = Get.arguments['vcardId'] ?? '0';

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_business_hours,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.customizeQRCodePage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: BusinessHoursShimmerLoading());
        }
        return IgnorePointer(
          ignoring: controller.isSaving.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  _buildDayExpansionTile(context, AppStrings.T.lbl_monday, 0),
                  _buildDayExpansionTile(context, AppStrings.T.lbl_tuesday, 1),
                  _buildDayExpansionTile(
                    context,
                    AppStrings.T.lbl_wednesday,
                    2,
                  ),
                  _buildDayExpansionTile(context, AppStrings.T.lbl_thursday, 3),
                  _buildDayExpansionTile(context, AppStrings.T.lbl_friday, 4),
                  _buildDayExpansionTile(context, AppStrings.T.lbl_saturday, 5),
                  _buildDayExpansionTile(context, AppStrings.T.lbl_sunday, 6),
                  _buildButtons(vcardId, context),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildButtons(String vcardId, BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Column(
        children: [
          Obx(
            () => CustomElevatedButton(
              checkConnectivity: true,
              isLoading: controller.isSaving.value,
              onPressed: () {
                if (controller.isSaving.value) {
                  return;
                }
                controller.saveBusinessHours(vcardId);
              },
              text: AppStrings.T.lbl_save,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDayExpansionTile(
    BuildContext context,
    String day,
    int dayIndex,
  ) {
    return Obx(() {
      final startTime = controller.startTimes[dayIndex];
      final endTime = controller.endTimes[dayIndex];
      return Container(
        margin: EdgeInsets.symmetric(vertical: 6.h),

        // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Get.theme.customColors.black!.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: Theme(
          data: Theme.of(context).copyWith(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            hoverColor: Colors.transparent,
            dividerColor: Colors.transparent,
          ),
          child: ExpansionTile(
            clipBehavior: Clip.none,
            initiallyExpanded: controller.isExpanded[dayIndex],
            // childrenPadding: EdgeInsets.only(bottom: 16.h, left: 16.w, right: 16.w),
            title: Row(
              children: [
                GestureDetector(
                  // onTap: () {
                  //   final shouldEnable =
                  //       !controller.isDayEnabled[dayIndex].value;
                  //   controller.isDayEnabled[dayIndex].value = shouldEnable;
                  //   controller.setDayExpanded(dayIndex, shouldEnable);
                  // },
                  child: Container(
                    width: 24.w,
                    height: 24.w,
                    decoration: BoxDecoration(
                      color:
                          controller.isExpanded[dayIndex]
                              ? Theme.of(context).primaryColor
                              : Get.theme.customColors.white,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: Get.theme.customColors.primaryColor!,
                      ),
                    ),
                    child:
                        controller.isExpanded[dayIndex]
                            ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 18,
                            )
                            : Gap(0),
                  ),
                ),
                Gap(16.w),
                Text(
                  day,
                  style: Get.theme.textTheme.bodyLarge?.copyWith(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color:
                        controller.isExpanded[dayIndex]
                            ? Get.theme.customColors.black
                            : Get.theme.customColors.greyTextColor,
                  ),
                ),
              ],
            ),
            trailing:
                controller.isExpanded[dayIndex]
                    ? CustomImageView(imagePath: AssetConstants.icDownArrow2)
                    : CustomImageView(imagePath: AssetConstants.icRightArrow2),
            onExpansionChanged: (expanded) {
              controller.setDayExpanded(dayIndex, expanded);
              controller.isDayEnabled[dayIndex].value = expanded;
            },
            children: [
              Container(
                padding: EdgeInsets.only(
                  bottom: 16.h,
                  left: 16.w,
                  right: 16.w,
                  top: 16.0.h,
                ),
                decoration: BoxDecoration(
                  color: Get.theme.customColors.white,
                  border: Border(
                    top: BorderSide(color: Get.theme.customColors.borderColor!),
                  ),
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: _buildTimeSelector(
                        context,
                        startTime.value,
                        (value) => controller.updateStartTime(dayIndex, value),
                        AppStrings.T.lbl_start_time,
                        controller.isExpanded[dayIndex],
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 8.w),
                      child: Text(
                        AppStrings.T.lbl_to,
                        style: Get.theme.textTheme.bodyLarge?.copyWith(
                          fontSize: 14.sp,
                        ),
                      ),
                    ),
                    Expanded(
                      child: _buildTimeSelector(
                        context,
                        endTime.value,
                        (value) => controller.updateEndTime(dayIndex, value),
                        AppStrings.T.lbl_end_time,
                        controller.isExpanded[dayIndex],
                        customOptions: controller.getFilteredEndTimes(
                          startTime.value,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildTimeSelector(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
    String label,
    bool isEnabled, {
    List<String>? customOptions,
  }) {
    final options = customOptions ?? controller.timeOptions;

    return GestureDetector(
      onTap:
          isEnabled
              ? () => _showTimePopup(context, currentValue, onChanged, options)
              : null,
      child: Container(
        height: 48.h,
        padding: EdgeInsets.symmetric(horizontal: 12.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
          color: isEnabled ? Colors.white : Colors.grey.shade100,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              currentValue,
              style: Get.theme.textTheme.bodyLarge?.copyWith(
                color:
                    isEnabled
                        ? Get.theme.customColors.black
                        : Get.theme.customColors.greyTextColor,
                fontSize: 15.sp,
              ),
            ),
            CircleAvatar(
              radius: 14.r,
              child: CustomImageView(imagePath: AssetConstants.icDownArrow2),
            ),
          ],
        ),
      ),
    );
  }

  void _showTimePopup(
    BuildContext context,
    String currentValue,
    Function(String) onChanged,
    List<String> options,
  ) {
    final selectedIndex = options.indexOf(currentValue);

    showDialog(
      context: context,
      builder:
          (BuildContext context) => Dialog(
            child: SizedBox(
              height: 400.h,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 12.h),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(color: Colors.grey.shade200),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Gap(60.w),
                        Text(
                          AppStrings.T.lbl_select_time,
                          style: Get.theme.textTheme.bodyLarge?.copyWith(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(right: 16.w),
                          child: GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Text(
                              AppStrings.T.lbl_done,
                              style: Get.theme.textTheme.bodyLarge?.copyWith(
                                fontSize: 16.sp,
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: CupertinoPicker(
                      scrollController: FixedExtentScrollController(
                        initialItem: selectedIndex != -1 ? selectedIndex : 0,
                      ),
                      itemExtent: 40,
                      onSelectedItemChanged: (index) {
                        onChanged(options[index]);
                      },
                      children:
                          options
                              .map(
                                (time) => Center(
                                  child: Text(
                                    time,
                                    style: Get.theme.textTheme.bodyLarge
                                        ?.copyWith(fontSize: 16.sp),
                                  ),
                                ),
                              )
                              .toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.isLoading.value = false;
    controller.isSaving.value = false;

    for (int i = 0; i < 7; i++) {
      controller.isExpanded[i] = false;
      controller.isDayEnabled[i].value = false;
      controller.startTimes[i].value = '12:00 AM';
      controller.endTimes[i].value = '12:30 AM';
    }
  }

  @override
  void onInit() {
    final String vcardId = Get.arguments['vcardId'] ?? '0';
    controller.fetchBusinessHours(vcardId);
  }
}
