import 'package:get/get.dart';
import 'package:v_card/app/controllers/seo_controller.dart';
import 'package:v_card/app/data/model/seo/seo_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/seo/widget/seo_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class SeoPage extends GetItHook<SeoController> {
  SeoPage({super.key});
  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_seo_setting,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Obx(() {
          if (controller.isLoading.value) {
            return SeoPageShimmer();
          }

          final seoData = controller.seoList.value?.data ?? [];

          if (seoData.isEmpty) {
            return _buildEmptySeoView();
          }

          final seo = seoData.first;
          return _buildSeoDetailsView(seo);
        }),
      ),
    );
  }

  Widget _buildEmptySeoView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_outlined,
            size: 80,
            color: Get.theme.colorScheme.primary.withValues(alpha: 0.7),
          ),
          Gap(20.h),
          Text(
            AppStrings.T.lbl_no_seo_settings,
            style: Get.theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Gap(12.h),
          Text(
            AppStrings.T.lbl_configure_seo,
            textAlign: TextAlign.center,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          Gap(32.h),
          CustomElevatedButton(
            text: AppStrings.T.lbl_configure_seo_short,
            onPressed: () {
              NavigationService.navigateWithSlideAnimation(
                AppRoutes.updateSeoPage,
                arguments: {'vcardId': vcardId.toString()},
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSeoDetailsView(Seo seo) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(24.h),

          // SEO Details
          _buildInfoSection(
            title: AppStrings.T.lbl_site_title,
            content: seo.siteTitle ?? '',
            icon: Icons.title,
          ),

          _buildInfoSection(
            title: AppStrings.T.lbl_home_title,
            content: seo.homeTitle ?? '',
            icon: Icons.home_outlined,
          ),

          if (seo.metaKeyword != null)
            _buildInfoSection(
              title: AppStrings.T.lbl_meta_keywords,
              content: seo.metaKeyword ?? '',
              icon: Icons.key_outlined,
            ),

          if (seo.metaDescription != null)
            _buildInfoSection(
              title: AppStrings.T.lbl_meta_description,
              content: seo.metaDescription ?? '',
              icon: Icons.description_outlined,
            ),

          if (seo.googleAnalytics != null)
            _buildInfoSection(
              title: AppStrings.T.lbl_google_analytics,
              content: seo.googleAnalytics ?? '',
              icon: Icons.analytics_outlined,
            ),

          Gap(32.h),

          // Update Button
          CustomElevatedButton(
            text: AppStrings.T.lbl_update_seo_settings,
            onPressed: () {
              NavigationService.navigateWithSlideAnimation(
                AppRoutes.updateSeoPage,
                arguments: {
                  'vcardId': vcardId.toString(),
                  'siteTitle': seo.siteTitle,
                  'homeTitle': seo.homeTitle,
                  'metaKeyword': seo.metaKeyword,
                  'metaDescription': seo.metaDescription,
                  'googleAnalytics': seo.googleAnalytics,
                  'isCreating': false,
                },
              );
            },
          ),
          Gap(24.h),
        ],
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: Get.theme.customColors.darkGreyTextColor!,
              ),
              Gap(8.w),
              Text(
                title,
                style: Get.theme.textTheme.bodyLarge?.copyWith(
                  color: Get.theme.customColors.darkGreyTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          Gap(4.w),
          Padding(
            padding: EdgeInsets.only(left: 26.w),
            child: Text(
              content,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Get.theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Get.theme.customColors.greyTextColor,
              ),
            ),
          ),
          Gap(8.h),
          Divider(),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getSeoData(vcardId);
  }
}
