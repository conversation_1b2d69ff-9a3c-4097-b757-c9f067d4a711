import 'package:get/get.dart';
import 'package:v_card/app/controllers/seo_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class UpdateSeoPage extends GetItHook<SeoController> {
  UpdateSeoPage({super.key});

  final vcardId = Get.arguments['vcardId'];
  final String? initialSiteTitle = Get.arguments['siteTitle'];
  final String? initialHomeTitle = Get.arguments['homeTitle'];
  final String? initialMetaKeyword = Get.arguments['metaKeyword'];
  final String? initialMetaDescription = Get.arguments['metaDescription'];
  final String? initialGoogleAnalytics = Get.arguments['googleAnalytics'];
  final bool isCreating = Get.arguments['isCreating'] ?? false;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    // Initialize form fields with existing values if editing
    if (!isCreating) {
      controller.siteTitleController.text = initialSiteTitle ?? '';
      controller.homeTitleController.text = initialHomeTitle ?? '';
      controller.metaKeywordController.text = initialMetaKeyword ?? '';
      controller.metaDescriptionController.text = initialMetaDescription ?? '';
      controller.googleAnalyticsController.text = initialGoogleAnalytics ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          isCreating
              ? AppStrings.T.lbl_create_seo_settings
              : AppStrings.T.lbl_edit_seo_settings,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.privacyPolicyPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const CustomFormShimmerLoading();
        }

        return IgnorePointer(
          ignoring: controller.isLoadingUpdateSeo.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    TextInputField(
                      type: InputType.name,
                      isCapitalized: true,
                      controller: controller.siteTitleController,
                      label: AppStrings.T.lbl_site_title,
                      // validator: (value) => AppValidations.validateRequired(
                      //   value,
                      //   fieldName: "Site Title",
                      // ),
                    ),
                    Gap(12.h),

                    TextInputField(
                      isCapitalized: true,
                      type: InputType.text,
                      controller: controller.homeTitleController,
                      label: AppStrings.T.lbl_home_title,
                      // validator: (value) => AppValidations.validateRequired(
                      //   value,
                      //   fieldName: "Home Title",
                      // ),
                    ),
                    Gap(12.h),

                    TextInputField(
                      type: InputType.text,
                      controller: controller.metaKeywordController,
                      label: AppStrings.T.lbl_meta_keywords,
                    ),
                    Gap(12.h),

                    TextInputField(
                      type: InputType.multiline,
                      textInputAction: TextInputAction.newline,
                      maxLines: 3,
                      controller: controller.metaDescriptionController,
                      label: AppStrings.T.lbl_meta_description,
                    ),
                    Gap(12.h),

                    TextInputField(
                      type: InputType.text,
                      controller: controller.googleAnalyticsController,
                      label: AppStrings.T.lbl_google_analytics_id,
                    ),
                    Gap(30.h),

                    _buildButtons(_formKey, vcardId),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildButtons(GlobalKey<FormState> formKey, String vcardId) {
    return Column(
      children: [
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            isLoading: controller.isLoadingUpdateSeo.value,
            text: AppStrings.T.lbl_save,
            onPressed: () async {
              if (controller.isLoadingUpdateSeo.value) {
                return;
              }
              if (!_formKey.currentState!.validate()) {
                return;
              }

              await controller.updateSeo(
                vcardId: vcardId,
                siteTitle: controller.siteTitleController.text,
                homeTitle: controller.homeTitleController.text,
                metaKeyword: controller.metaKeywordController.text,
                metaDescription: controller.metaDescriptionController.text,
                googleAnalytics: controller.googleAnalyticsController.text,
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.siteTitleController.clear();
    controller.homeTitleController.clear();
    controller.metaKeywordController.clear();
    controller.metaDescriptionController.clear();
    controller.googleAnalyticsController.clear();
    controller.isLoadingUpdateSeo.value = false;
  }
}
