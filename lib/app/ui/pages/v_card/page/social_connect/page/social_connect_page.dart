import 'dart:io';

import 'package:get/get.dart';
import 'package:v_card/app/controllers/social_connect_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/social_connect/widgets/social_connect_shimmer.dart';
import 'package:v_card/app/ui/pages/v_card/page/social_connect/widgets/social_link_field.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class SocialConnectPage extends GetItHook<SocialConnectController> {
  SocialConnectPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final String vcardId = Get.arguments['vcardId'] ?? '0';

    final socialFields = [
      {
        'icon': AssetConstants.icGoogle,
        'label': AppStrings.T.lbl_google_url,
        'controller': controller.websiteController,
        "backgroundColor": Color(0xFFEA4335),
      },
      {
        'icon': AssetConstants.icTwitter,
        'label': AppStrings.T.lbl_twitter_url,
        'controller': controller.twitterController,
        "backgroundColor": Color(0xFF55ADEE),
      },
      {
        'icon': AssetConstants.icFb,
        'label': AppStrings.T.lbl_facebook_url,
        'controller': controller.facebookController,
        "backgroundColor": Color(0xFF295396),
      },
      {
        'icon': AssetConstants.icInsta,
        'label': AppStrings.T.lbl_instagram_url,
        'controller': controller.instagramController,
        "backgroundColor": Color(0xFF5456DA),
      },
      {
        'icon': AssetConstants.icYoutube,
        'label': AppStrings.T.lbl_youtube_url,
        'controller': controller.youtubeController,
        "backgroundColor": Color(0xFFDC2825),
      },
      {
        'icon': AssetConstants.icTumblr,
        'label': AppStrings.T.lbl_tumblr_url,
        'controller': controller.tumblrController,
        "backgroundColor": Get.theme.customColors.black,
      },
      {
        'icon': AssetConstants.icReddit,
        'label': AppStrings.T.lbl_reddit_url,
        'controller': controller.redditController,
        "backgroundColor": Color(0xFFFF4500),
      },
      {
        'icon': AssetConstants.icLinkedin,
        'label': AppStrings.T.lbl_linkedin_url,
        'controller': controller.linkedinController,
        "backgroundColor": Color(0xFF1883BB),
      },
      {
        'icon': AssetConstants.icWp,
        'label': AppStrings.T.lbl_whatsapp_url,
        'controller': controller.whatsappController,
        "backgroundColor": Color(0xFF3AB346),
      },
      {
        'icon': AssetConstants.icPinterest,
        'label': AppStrings.T.lbl_pinterest_url,
        'controller': controller.pinterestController,
        "backgroundColor": Color(0xFFCC2127),
      },
      {
        'icon': AssetConstants.icTiktok,
        'label': AppStrings.T.lbl_tiktok_url,
        'controller': controller.tiktokController,
        "backgroundColor": Get.theme.customColors.black,
      },
      // {
      //   'icon': AssetConstants.icInsta,
      //   'label': 'Snapchat URL',
      //   'controller': controller.snapchatController,
      // },
    ];

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_social_links,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createCustomLinkPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return SocialConnectShimmer();
        }
        return IgnorePointer(
          ignoring: controller.isSaving.value,
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ...socialFields.map((field) {
                    return Padding(
                      padding: EdgeInsets.only(bottom: 16.h),
                      child: SocialLinkField(
                        imagePath: field['icon'] as String,
                        hintLabel: field['label'] as String,
                        controller:
                            field['controller'] as TextEditingController,
                        fieldName: field['label'] as String,
                        backgroundColor: field['backgroundColor'] as Color,
                      ),
                    );
                  }),
                  Gap(8.h),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${AppStrings.T.lbl_add_icon}:',
                        style: Get.theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Obx(
                        () => Transform.scale(
                          scale: 0.8,
                          alignment: Alignment.centerRight,
                          child: Switch(
                            value: controller.enableIcon.value,
                            activeColor: Get.theme.customColors.white,
                            inactiveThumbColor:
                                Get.theme.customColors.darkColor,
                            inactiveTrackColor: Get.theme.customColors.white,
                            activeTrackColor:
                                Get.theme.customColors.primaryColor,
                            onChanged: (value) {
                              controller.enableIcon.value = value;
                            },
                          ),
                        ),
                      ),
                    ],
                  ),

                  if (controller.enableIcon.value)
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              '${AppStrings.T.lbl_icon}:',
                              style: Get.theme.textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        Obx(() {
                          return GestureDetector(
                            onTap:
                                controller.isProcessingIcon.value
                                    ? null
                                    : () async {
                                      Future.delayed(Duration(seconds: 1), () {
                                        controller.isProcessingIcon.value =
                                            true;
                                      });

                                      try {
                                        final File?
                                        iconFile = await PWAIconHandler.createPWAIcon(
                                          context: context,
                                          source: ImageSource.gallery,
                                          size:
                                              512, // Match your example file size
                                          maxKB: 50,
                                          format:
                                              'png', // Use PNG since your example was PNG
                                        );

                                        if (iconFile != null) {
                                          controller.profileImageFile.value =
                                              iconFile;
                                          // Now the icon is ready for upload
                                        }
                                      } catch (e) {
                                        print('Error creating PWA icon: $e');
                                        // Show error toast or message here if needed
                                      } finally {
                                        // Set processing flag to false when done
                                        controller.isProcessingIcon.value =
                                            false;
                                      }
                                    },
                            child: Container(
                              height: 100.h,
                              width: 100.w,
                              decoration: BoxDecoration(
                                color: Get.theme.customColors.white,
                                borderRadius: BorderRadius.circular(10.r),
                                border: Border.all(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                              ),
                              child: Stack(
                                alignment: Alignment.center,
                                children: [
                                  if (controller.isProcessingIcon.value)
                                    // Show loading indicator
                                    ShimmerBox(height: 90.h, width: 90.w)
                                  else
                                    // Show image when not loading
                                    CustomImageView(
                                      height: 90.h,
                                      width: 90.w,
                                      fit: BoxFit.cover,
                                      radius: BorderRadius.circular(8.r),
                                      imagePath:
                                          controller
                                              .profileImageFile
                                              .value
                                              ?.path ??
                                          controller
                                              .socialConnectData
                                              .value
                                              ?.data
                                              .socialIcon,
                                    ),

                                  // Edit icon - only show when not processing
                                  if (!controller.isProcessingIcon.value)
                                    Positioned(
                                      top: 5,
                                      right: 5,
                                      child: Container(
                                        padding: EdgeInsets.all(4),
                                        decoration: BoxDecoration(
                                          color: Get.theme.customColors.white,
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color:
                                                Get
                                                    .theme
                                                    .customColors
                                                    .greyTextColor!,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.edit,
                                          size: 12,
                                          color:
                                              Get
                                                  .theme
                                                  .customColors
                                                  .primaryColor,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ],
                    ),
                  Gap(30.h),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Obx(
                        () => CustomElevatedButton(
                          checkConnectivity: true,
                          isLoading: controller.isSaving.value,

                          onPressed: () async {
                            if (controller.isSaving.value) {
                              return;
                            }

                            controller.isSaving.value = true;

                            try {
                              if (!_formKey.currentState!.validate()) {
                                controller.isSaving.value = false;
                                return;
                              }

                              if (controller.isProcessingIcon.value) {
                                _showImageValidationToast(
                                  AppStrings.T.lbl_image_processing_message,
                                  false,
                                );
                                controller.isSaving.value = false;
                                return;
                              }

                              String? imagePath;
                              if (controller.profileImageFile.value?.path !=
                                  null) {
                                imagePath =
                                    controller.profileImageFile.value!.path;
                              } else {
                                final file = await urlToFile(
                                  controller
                                          .socialConnectData
                                          .value
                                          ?.data
                                          .socialIcon ??
                                      '',
                                );
                                imagePath = file.path;
                              }

                              await controller.saveSocialConnectData({
                                'social_icon': [imagePath],
                                'vcard_id': vcardId,
                                'website': processProductUrl(
                                  controller.websiteController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'twitter': processProductUrl(
                                  controller.twitterController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'facebook': processProductUrl(
                                  controller.facebookController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'instagram': processProductUrl(
                                  controller.instagramController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'youtube': processProductUrl(
                                  controller.youtubeController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'tumblr': processProductUrl(
                                  controller.tumblrController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'reddit': processProductUrl(
                                  controller.redditController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'linkedin': processProductUrl(
                                  controller.linkedinController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'whatsapp': processProductUrl(
                                  controller.whatsappController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'pinterest': processProductUrl(
                                  controller.pinterestController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'tiktok': processProductUrl(
                                  controller.tiktokController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                                'snapchat': processProductUrl(
                                  controller.snapchatController.text
                                      .trim()
                                      .toLowerCase(),
                                ),
                              }, vcardId);
                            } catch (e) {
                              print('Save Error: $e');
                            } finally {
                              // Always release lock
                              controller.isSaving.value = false;
                            }
                          },

                          text: AppStrings.T.lbl_save,
                        ),
                      ),
                    ],
                  ),
                  Gap(32.h),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.profileImageFile.value = null;
  }

  @override
  void onInit() {
    final String vcardId = Get.arguments['vcardId'] ?? '0';
    controller.isProcessingIcon = false.obs;
    controller.getSocialConnectData(vcardId);
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }
}

