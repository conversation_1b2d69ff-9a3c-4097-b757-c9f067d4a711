import 'package:get/get_navigation/get_navigation.dart';
import 'package:get/instance_manager.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class SocialConnectShimmer extends StatelessWidget {
  const SocialConnectShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Create 11 shimmer fields representing each social media field
          ...List.generate(
            11,
            (index) => Padding(
              padding: EdgeInsets.only(bottom: 16.h),
              child: _buildSocialFieldShimmer(),
            ),
          ),
          Gap(16.h),
          // Shimmer for the buttons
          Row(children: [Expanded(child: _buildButtonShimmer())]),
          Gap(16.h),
          Row(
            children: [Expanded(child: _buildButtonShimmer(isSecondary: true))],
          ),
          Gap(32.h),
        ],
      ),
    );
  }

  Widget _buildSocialFieldShimmer() {
    return Card(
      color: Get.theme.customColors.white,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            // Social icon placeholder
            ShimmerBox(
              width: 48.w,
              height: 48.h,
              borderRadius: BorderRadius.circular(100),
            ),
            Gap(12.w),
            // Text field placeholder
            ShimmerBox(height: 30.h, width: 180.w),
          ],
        ),
      ),
    );
  }

  Widget _buildButtonShimmer({bool isSecondary = false}) {
    return ShimmerBox(height: 50.h, width: double.infinity);
  }
}
