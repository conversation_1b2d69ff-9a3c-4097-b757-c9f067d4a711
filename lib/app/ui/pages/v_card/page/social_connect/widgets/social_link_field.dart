import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class SocialLinkField extends StatelessWidget {
  final String imagePath;
  final String hintLabel;
  final TextEditingController controller;
  final String fieldName;
  final Color? backgroundColor;

  const SocialLinkField({
    required this.imagePath,
    required this.hintLabel,
    required this.controller,
    required this.fieldName,
    this.backgroundColor,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Get.theme;
    final customColors = theme.customColors;
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(10.r),
        boxShadow: [
          BoxShadow(
            color: Get.theme.customColors.black!.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 12.0.w, vertical: 16.0.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 24.r,
              backgroundColor:
                  backgroundColor?.withValues(alpha: 0.09) ??
                  Get.theme.customColors.textfieldFillColor,
              child: CustomImageView(
                imagePath: imagePath,
                margin: const EdgeInsets.all(12.0),
              ),
            ),
            Gap(10.0.w),
            Expanded(
              child: Center(
                child: TextInputField(
                  type: InputType.text,
                  hintLabel: hintLabel,
                  controller: controller,
                  validator: (value) => AppValidations.urlWithEmptyValidation(value),
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.symmetric(vertical: 5.0.h),
                    hintText: hintLabel,
                    isDense: true,
                    border: UnderlineInputBorder(),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: customColors.greyBorderColor!,
                      ),
                    ),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: customColors.primaryColor!),
                    ),
                    errorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: customColors.redColor!),
                    ),
                    disabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: customColors.greyBorderColor!,
                      ),
                    ),
                    focusedErrorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: customColors.redColor!),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
