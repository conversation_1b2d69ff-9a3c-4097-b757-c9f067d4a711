import 'package:v_card/app/utils/helpers/exporter.dart';

class AdvancedSettingShimmer extends StatelessWidget {
  const AdvancedSettingShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Password field shimmer
          _buildFieldShimmer(),
          Gap(16.h),

          // Custom CSS field shimmer (taller for multiline)
          _buildFieldShimmer(height: 100.h),
          Gap(16.h),

          // Custom JS field shimmer (taller for multiline)
          _buildFieldShimmer(height: 100.h),
          Gap(16.h),

          // Remove branding toggle shimmer
          _buildToggleRowShimmer(),
          Gap(24.h),

          // Save button shimmer
          _buildButtonShimmer(),
          Gap(16.h),

          // Cancel button shimmer
          _buildButtonShimmer(isSecondary: true),
        ],
      ),
    );
  }

  Widget _buildFieldShimmer({double height = 50.0}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label shimmer
        ShimmerBox(width: 120.w, height: 16.h),
        Gap(8.h),
        // Field shimmer
        ShimmerBox(height: height, width: double.infinity),
      ],
    );
  }

  Widget _buildToggleRowShimmer() {
    return Row(
      children: [
        // Label
        ShimmerBox(width: 150.w, height: 16.h),
        Gap(4.w),
        // Info icon
        ShimmerBox(width: 16.w, height: 16.h),
        Spacer(),
        // Switch
        ShimmerBox(width: 40.w, height: 20.h),
      ],
    );
  }

  Widget _buildButtonShimmer({bool isSecondary = false}) {
    return ShimmerBox(height: 50.h, width: double.infinity);
  }
}
