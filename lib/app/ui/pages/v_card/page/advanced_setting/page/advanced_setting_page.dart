import 'package:get/get.dart';
import 'package:v_card/app/controllers/advannced_setting_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/advanced_setting/widget/advanced_setting_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class AdvancedSettingPage extends GetItHook<AdvancedSettingController> {
   AdvancedSettingPage({super.key});
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    var vcardId = Get.arguments['vcardId'] ?? 0;

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_advanced,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.fontPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoadingAdvanceSettingsById.value) {
          return AdvancedSettingShimmer();
        }

        final data = controller.advanceSettingsDataById.value?.data;

        if (data != null) {
          controller.passwordController.text = data.password ?? '';
          controller.customCssController.text = data.customCss ?? '';
          controller.customJsController.text = data.customJs ?? '';
          controller.passwordController.text = data.password ?? '';
          controller.branding.value = data.branding.toString() == "1";
        }

        return IgnorePointer(
          ignoring: controller.isLoadingCreateAdvanceSettings.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Obx(
                      () => TextInputField(
                        type: InputType.password,
                        label: AppStrings.T.lbl_password,
                        controller: controller.passwordController,
                        obscureText: controller.isPasswordObsecure,
                        suffixIcon: IconButton(
                          icon: CustomImageView(
                            imagePath:
                                controller.isPasswordObsecure.value
                                    ? AssetConstants.svgCloseEye
                                    : AssetConstants.svgEye,
                            color: Get.theme.customColors.black,
                          ),
                          onPressed:
                              () =>
                                  controller.isPasswordObsecure.value =
                                      !controller.isPasswordObsecure.value,
                        ),
                        validator: AppValidations.passwordValidation,
                        isRequiredField: true,
                      ),
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.multiline,
                      textInputAction: TextInputAction.newline,
                      maxLines: 3,
                      controller: controller.customCssController,
                      label: AppStrings.T.lbl_custom_css,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_custom_css,
                          ),
                      isRequiredField: true,
                    ),
                    Gap(16.h),
                    TextInputField(
                      type: InputType.multiline,
                      textInputAction: TextInputAction.newline,
                      controller: controller.customJsController,
                      maxLines: 3,
                      label: AppStrings.T.lbl_custom_js,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_custom_js,
                          ),
                      isRequiredField: true,
                    ),
                    Gap(16.h),
                    Row(
                      children: [
                        Row(
                          children: [
                            Text(
                              AppStrings.T.lbl_remove_branding,
                              style: Get.theme.textTheme.bodyLarge!.copyWith(
                                color: Get.theme.customColors.primaryColor,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Gap(4.w),
                            CustomImageView(imagePath: AssetConstants.icQinfo),
                          ],
                        ),
                        Spacer(),
                        Obx(
                          () => Transform.scale(
                            scale: 0.8,
                            alignment: Alignment.centerRight,
                            child: Switch(
                              value: controller.branding.value,
                              activeColor: Get.theme.customColors.white,
                              inactiveThumbColor:
                                  Get.theme.customColors.darkColor,
                              inactiveTrackColor: Get.theme.customColors.white,
                              activeTrackColor:
                                  Get.theme.customColors.primaryColor,
                              onChanged: (value) {
                                controller.branding.value = value;
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                    Gap(24.h),
                    Column(
                      children: [
                        CustomElevatedButton(
                          checkConnectivity: true,
                          isLoading:
                              controller.isLoadingCreateAdvanceSettings.value,
                          onPressed: () {
                            if (controller
                                .isLoadingCreateAdvanceSettings
                                .value) {
                              return;
                            }
                            if (_formKey.currentState!.validate()) {
                              controller.createAdvanceSettings(
                                vcardId: vcardId,
                                password: controller.passwordController.text,
                                customCss: controller.customCssController.text,
                                customJs: controller.customJsController.text,
                                branding: controller.branding.value ? "1" : "0",
                              );
                            }
                          },

                          text: AppStrings.T.lbl_save,
                        ),
                      ],
                    ),
                    Gap(4.h),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    // Clear controllers when disposing
    controller.passwordController.clear();
    controller.customCssController.clear();
    controller.customJsController.clear();
    controller.branding.value = false;
  }

  @override
  void onInit() {
    var vcardId = Get.arguments['vcardId'] ?? 0;
    controller.getAdvanceSettingsById(id: vcardId.toString());
  }
}
