import 'package:flutter/material.dart';
import 'package:v_card/app/ui/widgets/shimmer_box.dart';


class TemplatesShimmer extends StatelessWidget {
  const TemplatesShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          Expanded(
            child: GridView.builder(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
                childAspectRatio: 0.8,
              ),
              itemCount: 10,
              itemBuilder: (context, index) {
                return _buildTemplateCardShimmer();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCardShimmer() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Column(
        children: [
          Expanded(
            child: ClipRRect(
              borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
              child: ShimmerBox(
                width: double.infinity,
                height: double.infinity,
              ),
            ),
          ),
          // Template name area
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: ShimmerBox(width: 80.0, height: 16.0),
          ),
        ],
      ),
    );
  }
}
