import 'package:get/get.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/data/model/v_card/vcard_tepmlate_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/template/widgets/template_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class VCardTemplatesPage extends GetItHook<VcardController> {
  VCardTemplatesPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: Obx(() => _buildBody(context)),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return CustomAppbar(
      title: AppText(
        AppStrings.T.lbl_vcard_templates,
        style: Get.theme.textTheme.bodyLarge?.copyWith(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
        ),
      ),
      actions: [
        if (!isCardCreated)
          IconButton(
            onPressed:
                () => NavigationService.navigateWithSlideAnimation(
                  AppRoutes.vcardBusinessHoursPage,
                  arguments: {'vcardId': vcardId.toString()},
                ),
            icon: Text(
              AppStrings.T.lbl_skip,
              style: Get.textTheme.bodySmall?.copyWith(
                color: Get.theme.customColors.primaryColor,
                fontSize: 14.0,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBody(BuildContext context) {
    if (controller.isLoadingTemplates.value) {
      return TemplatesShimmer();
    }

    final templates = controller.vCardTemplates.value?.templates ?? [];

    if (templates.isEmpty) {
      return Center(child: Text(AppStrings.T.lbl_no_templates_available));
    }

    controller.isActive.value = controller.vCardTemplates.value?.status == 1;

    return IgnorePointer(
      ignoring: controller.isLoadingUpdateTemplates.value,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Column(
          children: [
            Expanded(
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 4,
                  mainAxisSpacing: 4,
                  childAspectRatio: 0.8,
                ),
                itemCount: templates.length,
                itemBuilder:
                    (context, index) => _buildTemplateCard(templates[index]),
              ),
            ),
            _buildActionButtons(vcardId),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateCard(VCardTemplate template) {
    return Obx(() {
      final bool isSelected = controller.selectedTemplate.value == template;

      return Stack(
        children: [
          Card(
            color: Get.theme.customColors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                color: isSelected ? Get.theme.primaryColor : Colors.transparent,
                width: 2,
              ),
            ),
            child: InkWell(
              borderRadius: BorderRadius.circular(10),
              onTap: () {
                controller.selectedTemplate.value = template;
              },
              child: Column(
                children: [
                  Expanded(
                    child: ClipRRect(
                      borderRadius: BorderRadius.vertical(
                        top: Radius.circular(8),
                      ),
                      child: Image.network(
                        template.templateUrl,
                        fit: BoxFit.cover,
                        width: double.infinity,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      template.name,
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ),
                  if (template.isSelected)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Icon(
                        Icons.check_circle,
                        color: Get.theme.primaryColor,
                      ),
                    ),
                ],
              ),
            ),
          ),
          _buildViewIcon(template),
        ],
      );
    });
  }

  // Widget _buildTemplateCard(VCardTemplate template) {
  //   final isSelected = controller.selectedTemplate.value == template;
  //   controller.isSelected.value = isSelected;

  //   return Stack(
  //     children: [
  //       Card(
  //         color: Get.theme.customColors.white,
  //         elevation: 2,
  //         shape: RoundedRectangleBorder(
  //           borderRadius: BorderRadius.circular(10),
  //           side: BorderSide(
  //             color: isSelected ? Get.theme.primaryColor : Colors.transparent,
  //             width: 2,
  //           ),
  //         ),
  //         child: InkWell(
  //           borderRadius: BorderRadius.circular(10),
  //           onTap: () {
  //             controller.selectedTemplate.value = template;
  //           },
  //           child: Column(
  //             children: [
  //               Expanded(
  //                 child: ClipRRect(
  //                   borderRadius: BorderRadius.vertical(
  //                     top: Radius.circular(8),
  //                   ),
  //                   child: Image.network(
  //                     template.templateUrl,
  //                     fit: BoxFit.cover,
  //                     width: double.infinity,
  //                   ),
  //                 ),
  //               ),
  //               Padding(
  //                 padding: const EdgeInsets.all(8.0),
  //                 child: Text(
  //                   template.name,
  //                   style: TextStyle(fontWeight: FontWeight.bold),
  //                 ),
  //               ),
  //               if (template.isSelected)
  //                 Padding(
  //                   padding: const EdgeInsets.only(bottom: 8.0),
  //                   child: Icon(
  //                     Icons.check_circle,
  //                     color: Get.theme.primaryColor,
  //                   ),
  //                 ),
  //             ],
  //           ),
  //         ),
  //       ),
  //       _buildViewIcon(template),
  //     ],
  //   );
  // }

  Widget _buildViewIcon(VCardTemplate template) {
    return Positioned(
      right: 16,
      top: 16,
      child: GestureDetector(
        onTap: () => _showTemplatePreview(template),
        child: CircleAvatar(
          radius: 16.r,
          backgroundColor: Get.theme.customColors.black!.withValues(alpha: 0.3),
          child: CustomImageView(
            imagePath: AssetConstants.icView,
            margin: EdgeInsets.all(8.0),
            color: Get.theme.customColors.white,
          ),
        ),
      ),
    );
  }

  void _showTemplatePreview(VCardTemplate template) {
    showModalBottomSheet(
      context: Get.context!,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: Get.height * 0.7,
            decoration: BoxDecoration(
              color: Get.theme.cardColor,
              borderRadius: BorderRadius.vertical(top: Radius.circular(40)),
            ),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(
                          left: 20.0.w,
                          right: 20.0.w,
                          top: 8.0.h,
                        ),
                        height: 5.0,
                        width: 50.0,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(100.r),
                          color: Get.theme.customColors.primaryColor
                              ?.withValues(alpha: 0.6),
                        ),
                      ),
                      Gap(16.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            template.name,
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: CustomImageView(
                              imagePath: AssetConstants.icClose,
                            ),
                          ),
                        ],
                      ),
                      Gap(8.h),
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    physics: BouncingScrollPhysics(),
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: Column(
                      children: [
                        CustomImageView(
                          imagePath: template.templateUrl,
                          fit: BoxFit.contain,
                          radius: BorderRadius.circular(20),
                        ),
                        Gap(16.h),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildActionButtons(String vcardId) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, left: 16.0, right: 16.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                AppStrings.T.lbl_active,
                style: Get.theme.textTheme.bodyLarge!.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Obx(
                () => Transform.scale(
                  scale: 0.8,
                  alignment: Alignment.centerRight,
                  child: Switch(
                    value: controller.isActive.value,
                    onChanged: (val) => controller.isActive.value = val,
                    activeColor: Get.theme.customColors.white,
                    inactiveThumbColor: Get.theme.customColors.darkColor,
                    inactiveTrackColor: Get.theme.customColors.white,
                    activeTrackColor: Get.theme.customColors.primaryColor,
                  ),
                ),
              ),
            ],
          ),
          Row(
            children: [
              Expanded(
                child: CustomElevatedButton(
                  checkConnectivity: true,
                  isLoading: controller.isLoadingUpdateTemplates.value,
                  onPressed: () {
                    if (controller.isLoadingUpdateTemplates.value) {
                      return;
                    }

                    controller.updateVCardTemplate(vcardId);
                  },
                  text: AppStrings.T.lbl_save,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() => controller.selectedTemplate.value = null;

  @override
  void onInit() => controller.getVCardTemplates(vcardId: vcardId);
}
