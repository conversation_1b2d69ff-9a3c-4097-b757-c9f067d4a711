import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateInstaEmbedShimmer extends StatelessWidget {
  const CreateInstaEmbedShimmer({super.key});

  Widget _shimmerBox({
    double height = 20,
    double width = double.infinity,
    BorderRadius? borderRadius,
  }) {
    return ShimmerBox(height: height, width: width, borderRadius: borderRadius);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(16.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Type Selector
          _shimmerBox(height: 20, width: 120),
          Gap(8.h),
          Row(
            children: [
              Expanded(child: _shimmerBox(height: 80)),
              Gap(16.h),
              Expanded(child: _shimmerBox(height: 80)),
            ],
          ),
          Gap(16.h),
          // Embed Tag Field
          _shimmerBox(height: 20, width: 160),
          Gap(8.h), _shimmerBox(height: 100),

          Gap(16.h),

          // Instruction Box
          _shimmerBox(height: 20, width: 180),
          Gap(8.h), _shimmerBox(height: 80),

          Gap(16.h),

          // Save Button
          Align(
            alignment: Alignment.center,
            child: _shimmerBox(height: 48, width: 200),
          ),
        ],
      ),
    );
  }
}
