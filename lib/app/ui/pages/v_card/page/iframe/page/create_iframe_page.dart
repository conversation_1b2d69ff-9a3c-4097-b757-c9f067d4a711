import 'package:get/get.dart';
import 'package:v_card/app/controllers/iframe_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/insta_embed/widgets.dart/create_insta_embed_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateIframePage extends GetItHook<IframeController> {
  CreateIframePage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';
  final iframeId = Get.arguments['iframeId'];
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          iframeId != null
              ? AppStrings.T.btn_edit_iframe
              : AppStrings.T.btn_add_iframe,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.vcardAppointmentsPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: IgnorePointer(
        ignoring: controller.isSubmittingIframe.value,
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 16.h),
          child: Obx(() {
            if (controller.isLoadingIframeById.value) {
              return Center(child: CreateInstaEmbedShimmer());
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Gap(24.h),
                _buildUrlField(),
                Gap(24.h),
                _buildSaveButton(),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildUrlField() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(
            TextSpan(
              text: AppStrings.T.lbl_iframe_url,
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              children: [
                TextSpan(
                  text: "*",
                  style: Get.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Get.theme.customColors.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          Gap(8.h),
          TextInputField(
            isRequiredField: true,
            controller: controller.iframeUrlController,
            textInputAction: TextInputAction.done,
            hintLabel: AppStrings.T.msg_enter_iframe_url,
            keyboardType: TextInputType.url,
            type: InputType.text,
            validator: AppValidations.urlPathValidation,
          ),
          Gap(8.h),
          AppText(
            AppStrings.T.msg_invalid_iframe_url,
            style: Get.textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return Obx(
      () => CustomElevatedButton(
        checkConnectivity: true,
        text: AppStrings.T.lbl_save,
        isLoading: controller.isSubmittingIframe.value,
        onPressed: () async {
          if (controller.isSubmittingIframe.value) {
            return;
          }
          if (!_formKey.currentState!.validate()) {
            return;
          }

          if (iframeId == null) {
            await controller.createIframe(vcardId);
          } else {
            await controller.updateIframe(embedId: iframeId, vcardId: vcardId);
          }
        },
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.resetForm();
  }

  @override
  void onInit() {
    if (iframeId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getIframeById(iframeId.toString());
        final service = controller.iframDataById.value?.data;
        if (service != null) {
          controller.iframeUrlController.text = service.url.toString();
        }
      });
    }
  }
}
