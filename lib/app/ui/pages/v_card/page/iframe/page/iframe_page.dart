import 'package:get/get.dart';
import 'package:v_card/app/controllers/iframe_controller.dart';
import 'package:v_card/app/data/model/iframe/iframe_model.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/ui/widgets/custom_list_item_tile.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/helpers/generated/assets.gen.dart';

class IframePage extends GetItHook<IframeController> {
  IframePage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_iframe_links,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(100.0.r),
        ),
        backgroundColor: Get.theme.customColors.primaryColor,
        child: Icon(Icons.add, color: Colors.white),
        onPressed: () {
          controller.resetForm();
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createIframePage,
            arguments: {'vcardId': vcardId.toString()},
          );
        },
      ),
      body: Obx(() {
        if (controller.isLoadingIframe.value) {
          return ListTileShimmerLoading();
        }

        final iframeList = controller.iframeData.value?.data ?? [];

        return RefreshIndicator(
          onRefresh: () => controller.getIframeList(vcardId),
          child: ListView.builder(
            padding: EdgeInsets.only(left: 16.0, right: 16.0, bottom: 120.h),
            itemCount: iframeList.isEmpty ? 1 : iframeList.length,
            itemBuilder: (context, index) {
              if (iframeList.isEmpty) {
                return NoDataWidget(message: AppStrings.T.lbl_no_data);
              }
              final iframe = iframeList[index];
              return _buildWebsiteCard(iframe);
            },
          ),
        );
      }),
    );
  }

  Widget _buildWebsiteCard(Iframe iframe) {
    return CustomListItemTile(
      imageUrl: Assets.images.icon.vCard.icSvgCrop.path,
      iconWidget: Container(
        height: 50.0.h,
        width: 50.0.h,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Get.theme.customColors.primaryColor?.withValues(alpha: 0.1),
        ),
        alignment: Alignment.center,
        child: CustomImageView(
          imagePath: Assets.images.icon.vCard.icSvgCrop.path,
          fit: BoxFit.cover,
          height: 24.0,
          color: Get.theme.customColors.primaryColor,
          radius: BorderRadius.circular(100.r),
        ),
      ),
      title: _getDomainFromUrl(iframe.url ?? ''),
      subtitle: iframe.url ?? '',
      id: iframe.id.toString(),
      vcardId: vcardId,
      onDelete: () {
        Get.dialog(
          LoadingConfirmationDialog(
            title: AppStrings.T.btn_delete_iframe_link,
            message: AppStrings.T.msg_confirm_delete_iframe_link,
            onCancel: () => NavigationService.navigateBack(),
            onConfirm: () {
              controller.deleteIframe(
                vcardId: vcardId,
                embedId: iframe.id.toString(),
              );
            },
            isLoading: controller.isDeletingIframe,
          ),
        );
      },
      onEdit: () {
        NavigationService.navigateWithSlideAnimation(
          AppRoutes.createIframePage,
          arguments: {
            'vcardId': vcardId.toString(),
            'iframeId': iframe.id.toString(),
          },
        );
      },
      isLoadingDelete: controller.isDeletingIframe.value,
    );
  }

  String _getDomainFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host.replaceFirst('www.', '');
    } catch (e) {
      return url;
    }
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getIframeList(vcardId);
  }
}
