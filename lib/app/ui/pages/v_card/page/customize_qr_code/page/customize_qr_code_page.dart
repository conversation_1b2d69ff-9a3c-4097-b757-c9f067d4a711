import 'package:get/get.dart';
import 'package:v_card/app/controllers/customize_qr_code_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/customize_qr_code/widgets/customize_qr_code_page_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomizeQRCodePage extends GetItHook<CustomizeQRCodeController> {
  CustomizeQRCodePage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_customize_qr_code,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createServicePage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.h),
        child: Obx(() {
          if (controller.isLoadingGetFont.value ||
              controller.customizeQRCodeData.value == null) {
            return const CustomizeQRCodeShimmer();
          }

          return IgnorePointer(
            ignoring: controller.isLoadingSave.value,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildColorSection(
                    AppStrings.T.lbl_qr_code_color,
                    controller.qrcodeColor.value,
                    (color) => controller.updateQRCodeColor(color),
                  ),
                  Gap(16.h),
                  _buildColorSection(
                    AppStrings.T.lbl_background_color,
                    controller.backgroundColor.value,
                    (color) => controller.updateBackgroundColor(color),
                  ),
                  Gap(16.h),
                  _buildSelectionContainer(
                    AppStrings.T.lbl_qr_style,
                    controller.getStyleLabel(controller.style.value),
                    _buildStyleOptions(),
                    controller.isStyleExpanded,
                  ),
                  Gap(16.h),
                  _buildSelectionContainer(
                    AppStrings.T.lbl_select_eye_style,
                    controller.getEyeStyleLabel(controller.eyeStyle.value),
                    _buildEyeStyleOptions(),
                    controller.isEyeStyleExpanded,
                  ),
                  Gap(16.h),
                  _buildSwitchSection(),
                  Gap(24.h),
                  _buildButtons(vcardId),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildColorSection(
    String title,
    String currentColor,
    Function(Color) onColorChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          title,
          style: Get.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Row(
          children: [
            InkWell(
              onTap: () {
                _showColorPicker(
                  initialColor: controller.hexToColor(currentColor),
                  onColorChanged: onColorChanged,
                );
              },
              child: Container(
                width: 50.h,
                height: 50.h,
                decoration: BoxDecoration(
                  color: controller.hexToColor(currentColor),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey),
                ),
              ),
            ),
            Gap(16.w),
            AppText(currentColor, style: Get.textTheme.bodyMedium),
          ],
        ),
      ],
    );
  }

  Widget _buildSelectionContainer(
    String title,
    String currentValue,
    Widget options,
    RxBool isExpanded,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          title,
          style: Get.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Obx(
          () => Column(
            children: [
              // Main selection container
              GestureDetector(
                onTap: () {
                  isExpanded.value = !isExpanded.value;
                },
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 16.w,
                    vertical: 12.h,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Get.theme.customColors.primaryColor!,
                    ),
                    borderRadius:
                        !isExpanded.value
                            ? BorderRadius.circular(8.r)
                            : BorderRadius.only(
                              topLeft: Radius.circular(8.r),
                              topRight: Radius.circular(8.r),
                            ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      AppText(
                        currentValue,
                        style: Get.theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      CustomImageView(
                        imagePath:
                            isExpanded.value
                                ? AssetConstants.icUpArrow
                                : AssetConstants.icDownArrow2,
                        color: Get.theme.customColors.darkGreyTextColor,
                      ),
                    ],
                  ),
                ),
              ),

              // Dropdown options
              if (isExpanded.value) ...[
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                      left: BorderSide(
                        color: Get.theme.customColors.primaryColor!,
                      ),
                      right: BorderSide(
                        color: Get.theme.customColors.primaryColor!,
                      ),
                      bottom: BorderSide(
                        color: Get.theme.customColors.primaryColor!,
                      ),
                      top: BorderSide.none,
                    ),
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8.r),
                      bottomRight: Radius.circular(8.r),
                    ),
                  ),
                  child: options,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStyleOptions() {
    return Column(
      children: List.generate(controller.styleOptions.length, (index) {
        final option = controller.styleOptions[index];
        return GestureDetector(
          onTap: () {
            controller.style.value = option['value'];
            controller.isStyleExpanded.value = false;
          },
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color:
                    controller.style.value == option['value']
                        ? Colors.grey[200]
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Row(
                children: [
                  AppText(
                    option['label'],
                    style: Get.theme.textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildEyeStyleOptions() {
    return Column(
      children: List.generate(controller.eyeStyleOptions.length, (index) {
        final option = controller.eyeStyleOptions[index];
        return GestureDetector(
          onTap: () {
            controller.eyeStyle.value = option['value'];
            controller.isEyeStyleExpanded.value = false;
          },
          child: Padding(
            padding: const EdgeInsets.all(4.0),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                color:
                    controller.eyeStyle.value == option['value']
                        ? Colors.grey[200]
                        : Colors.transparent,
                borderRadius: BorderRadius.circular(4.r),
              ),
              child: Row(
                children: [
                  AppText(
                    option['label'],
                    style: Get.theme.textTheme.bodySmall,
                  ),
                ],
              ),
            ),
          ),
        );
      }),
    );
  }

  void _showColorPicker({
    required Color initialColor,
    required Function(Color) onColorChanged,
  }) {
    Get.dialog(
      AlertDialog(
        title: Text(AppStrings.T.lbl_select_color),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: initialColor,
            onColorChanged: onColorChanged,
            pickerAreaHeightPercent: 0.8,
            displayThumbColor: true,
            paletteType: PaletteType.hsv,
            pickerAreaBorderRadius: BorderRadius.circular(8.r),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => NavigationService.navigateBack(),
            child: Text(AppStrings.T.lbl_done),
          ),
        ],
      ),
    );
  }

  Widget _buildSwitchSection() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AppText(
          AppStrings.T.lbl_apply_changes,
          style: Get.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Obx(
          () => Transform.scale(
            scale: 0.8,
            alignment: Alignment.centerRight,
            child: Switch(
              value: controller.applySetting.value == 1,
              activeColor: Get.theme.customColors.white,
              inactiveThumbColor: Get.theme.customColors.darkColor,
              inactiveTrackColor: Get.theme.customColors.white,
              activeTrackColor: Get.theme.customColors.primaryColor,
              onChanged: (value) {
                controller.toggleApplySetting();
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildButtons(vcardId) {
    return Obx(
      () => CustomElevatedButton(
        checkConnectivity: true,
        text: AppStrings.T.lbl_save,
        isLoading: controller.isLoadingSave.value,
        onPressed: () {
          if (controller.isLoadingSave.value) {
            return;
          }
          controller.updateCustomizeQRCode(vcardId);
        },
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getCustomizeQRCode(vcardId);

    controller.styleOptions.value =
        [
          {'value': 'square', 'label': AppStrings.T.lbl_square},
          {'value': 'dot', 'label': AppStrings.T.lbl_dot},
          {'value': 'round', 'label': AppStrings.T.lbl_round},
        ].obs;

    controller.eyeStyleOptions.value =
        [
          {'value': 'square', 'label': AppStrings.T.lbl_square},
          {'value': 'circle', 'label': AppStrings.T.lbl_circle},
        ].obs;
  }
}
