import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomizeQRCodeShimmer extends StatelessWidget {
  const CustomizeQRCodeShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // QR Code Preview Shimmer
        // _buildQRPreviewShimmer(),
        // Gap(20.h),

        // QR Code Color Field
        _buildColorFieldShimmer('QR Code Color'),
        Gap(16.h),

        // Background Color Field
        _buildColorFieldShimmer('Background Color'),
        Gap(16.h),

        // QR Style Field
        _buildSelectionFieldShimmer('QR Style'),
        Gap(16.h),

        // Eye Style Field
        _buildSelectionFieldShimmer('Eye Style'),
        Gap(16.h),

        // Apply Settings Switch
        _buildSwitchShimmer(),
        Gap(24.h),

        // Save Button
        _buildButtonShimmer(),
      ],
    );
  }

  // Widget _buildQRPreviewShimmer() {
  //   return Center(
  //     child: Shimmer.fromColors(
  //       baseColor: Colors.grey.shade300,
  //       highlightColor: Colors.grey.shade100,
  //       child: Container(
  //         width: 200.w,
  //         height: 200.h,
  //         decoration: BoxDecoration(
  //           color: Colors.white,
  //           borderRadius: BorderRadius.circular(12.r),
  //           boxShadow: [
  //             BoxShadow(
  //               color: Colors.black.withValues(alpha: 0.1),
  //               blurRadius: 20,
  //               spreadRadius: 5,
  //               offset: Offset(0, 1),
  //             ),
  //           ],
  //         ),
  //         child: Column(
  //           mainAxisAlignment: MainAxisAlignment.center,
  //           children: [
  //             Container(
  //               width: 140.w,
  //               height: 140.h,
  //               decoration: BoxDecoration(
  //                 color: Colors.grey[200],
  //                 borderRadius: BorderRadius.circular(8.r),
  //               ),
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }

  Widget _buildColorFieldShimmer(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        ShimmerBox(
          width: 120.w,
          height: 16.h,
          borderRadius: BorderRadius.circular(100.r),
        ),
        Gap(8.h),

        // Color Picker
        Row(
          children: [
            ShimmerBox(width: 50.w, height: 50.h),
            Gap(16.w),
            ShimmerBox(width: 80.w, height: 20.h),
          ],
        ),
      ],
    );
  }

  Widget _buildSelectionFieldShimmer(String title) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label
        ShimmerBox(width: 120.w, height: 16.h),
        Gap(8.h),

        // Selection Container
        Container(
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: Colors.grey),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ShimmerBox(width: 100.w, height: 16.h),
                ShimmerBox(width: 24.w, height: 24.h),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchShimmer() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ShimmerBox(width: 120.w, height: 20.h),
        ShimmerBox(width: 50.w, height: 30.h),
      ],
    );
  }

  Widget _buildButtonShimmer() {
    return ShimmerBox(height: 50.h, width: double.infinity);
  }
}
