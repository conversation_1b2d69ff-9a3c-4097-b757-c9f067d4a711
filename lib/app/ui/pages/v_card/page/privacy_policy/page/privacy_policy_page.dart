import 'package:get/get.dart';
import 'package:v_card/app/controllers/privacy_policy_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/privacy_policy/widgets/privacy_policy_page_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

class PrivacyPolicyPage extends GetItHook<PrivacyPolicyController> {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    var vcardId = Get.arguments['vcardId'] ?? 0;

    print(controller.quillController.document.toPlainText());

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_privacy_policy,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.termsAndConditionsPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Obx(() {
          if (controller.isLoading.value &&
              controller.privacyPolicy.value == null) {
            return PrivacyPolicyPageShimmer();
          }

          return IgnorePointer(
            ignoring: controller.isSaveLoading.value,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text.rich(
                    TextSpan(
                      text: AppStrings.T.lbl_privacy_policy,
                      style: Get.theme.textTheme.titleMedium?.copyWith(
                        color: Get.theme.customColors.greyTextColor,
                        fontSize: 14.0.sp,
                        fontWeight: FontWeight.w600,
                      ),
                      children: [
                        TextSpan(
                          text: "*",
                          style: Get.theme.textTheme.titleMedium?.copyWith(
                            color: Get.theme.customColors.primaryColor,
                            fontSize: 14.0.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(12.0.h),
                  Container(
                    height: Get.height * 0.5.h,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Column(
                      children: [
                        quill.QuillSimpleToolbar(
                          controller: controller.quillController,
                          config: quill.QuillSimpleToolbarConfig(
                            showBoldButton: true,
                            showUnderLineButton: true,
                            showItalicButton: true,
                            showFontSize: true,
                            showClearFormat: false,
                            showClipboardPaste: false,
                            showLineHeightButton: false,
                            showListCheck: false,
                            showRedo: false,
                            showUndo: false,
                            showClipboardCopy: false,
                            showClipboardCut: false,
                            showAlignmentButtons: false,
                            showBackgroundColorButton: false,
                            showCenterAlignment: false,
                            showColorButton: false,
                            showCodeBlock: false,
                            showDirection: false,
                            showFontFamily: false,
                            showHeaderStyle: false,
                            showIndent: false,
                            showInlineCode: false,
                            showJustifyAlignment: false,
                            showLeftAlignment: false,
                            showLink: false,
                            showListNumbers: false,
                            showListBullets: false,
                            showQuote: false,
                            showRightAlignment: false,
                            showSearchButton: false,
                            showSmallButton: false,
                            showStrikeThrough: false,
                            showSubscript: false,
                            showSuperscript: false,
                            showDividers: false,
                            multiRowsDisplay: false,
                          ),
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.all(8.sp),
                            child: quill.QuillEditor.basic(
                              controller: controller.quillController,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(30.h),
                ],
              ),
            ),
          );
        }),
      ),
      bottomNavigationBar: Obx(
        () =>
            controller.isLoading.value
                ? SizedBox()
                : _buildButtons(vcardId, context),
      ),
    );
  }

  Widget _buildButtons(vcardId, BuildContext context) {
    return Obx(
      () => Container(
        padding: EdgeInsets.symmetric(vertical: 20.0.h, horizontal: 16.0.w),
        margin: EdgeInsets.only(
          bottom: MediaQuery.viewInsetsOf(context).bottom,
        ),
        child: CustomElevatedButton(
          checkConnectivity: true,
          text: AppStrings.T.lbl_save,
          isLoading: controller.isSaveLoading.value,
          onPressed: () {
            if (controller.isSaveLoading.value) {
              return;
            }

            final delta = controller.quillController.document.toDelta();
            final plainText =
                controller.quillController.document.toPlainText().trim();

            if (plainText.isEmpty) {
              toastification.show(
                type: ToastificationType.error,
                style: ToastificationStyle.flatColored,
                alignment: Alignment.topCenter,
                description: Text("Validation Error"),
                autoCloseDuration: const Duration(seconds: 3),
              );
              return;
            }

            final converter = QuillDeltaToHtmlConverter(
              delta.toJson(),
              ConverterOptions(),
            );
            final htmlContent = converter.convert();
            controller.updatePrivacyPolicy(vcardId.toString(), htmlContent);
          },
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.quillController.clear();
    controller.privacyPolicy.value = null;
    controller.getPrivacyPolicyState.value = ApiState.initial();
    controller.isLoading.value = false;
    controller.isSaveLoading.value = false;
  }

  @override
  void onInit() {
    var vcardId = Get.arguments['vcardId'] ?? 0;
    controller.getPrivacyPolicy(vcardId);
  }
}
