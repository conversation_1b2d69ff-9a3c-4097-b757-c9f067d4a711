import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class PrivacyPolicyPageShimmer extends StatelessWidget {
  const PrivacyPolicyPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Toolbar shimmer
        Container(
          height: 40.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8.r),
              topRight: Radius.circular(8.r),
            ),
            border: Border.all(color: Get.theme.customColors.dividercolor!),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Row(
              children: [
                Expanded(
                  child: Row(children: [ShimmerBox(height: 14.h, width: 80.w)]),
                ),
                Gap(12.w),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(child: <PERSON><PERSON><PERSON>ox(height: 18.h, width: 18.w)),
                      Gap(12.w),
                      Expanded(child: <PERSON><PERSON><PERSON><PERSON>(height: 18.h, width: 18.w)),
                      Gap(12.w),
                      Expanded(child: <PERSON><PERSON><PERSON><PERSON>(height: 18.h, width: 18.w)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),

        // Editor area shimmer
        Container(
          height: Get.height * 0.55.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8.r),
              bottomRight: Radius.circular(8.r),
            ),
            border: Border.all(color: Get.theme.customColors.dividercolor!),
          ),
          child: Padding(
            padding: EdgeInsets.all(12.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Text line shimmers
                for (int i = 0; i < 15; i++) ...[
                  ShimmerBox(
                    height: 14.h,
                    width: Get.width * (i % 3 == 0 ? 0.7 : 0.9),
                  ),
                  Gap(12.h),
                ],
              ],
            ),
          ),
        ),

        Gap(30.h),

        // Buttons shimmer
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [ShimmerBox(height: 48.h, width: double.infinity)],
        ),
      ],
    );
  }
}
