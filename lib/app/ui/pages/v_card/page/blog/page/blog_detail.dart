import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/blog_controller.dart';
import 'package:v_card/app/data/model/blog/blog_detail_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/blog/widget/blog_detail_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class BlogDetailPage extends GetItHook<BlogController> {
  const BlogDetailPage({super.key});

  @override
  void onInit() {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          "Blog Details",
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),

      body: Obx(() {
        if (controller.isLoadingBlogById.value == true) {
          return BlogDetailsShimmer();
        }

        return Builder(
          builder: (context) {
            final Blog blogById = controller.blogDataById.value!.data;
            return ListView(
              physics: BouncingScrollPhysics(),
              padding: EdgeInsets.all(16.0),
              children: [
                _buildBlogInfoTile(
                  isExpanded: controller.isBlogIconExpanded,
                  title: "Blog Icon",
                  children: CustomImageView(
                    imagePath: blogById.blogIcon,
                    fit: BoxFit.cover,
                    height: 200.0.h,
                    width: double.maxFinite,
                  ),
                ),
                Gap(18.0.h),
                _buildBlogInfoTile(
                  isExpanded: controller.isBlogNameExpanded,
                  title: "Blog Name",
                  children: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      blogById.title.toString(),
                      style: Get.textTheme.titleMedium?.copyWith(
                        fontSize: 15.0.sp,
                        fontWeight: FontWeight.w600,
                        color: Get.theme.customColors.greyTextColor,
                      ),
                    ),
                  ),
                ),
                Gap(18.0.h),
                _buildBlogInfoTile(
                  isExpanded: controller.isBlogDescriptionExpanded,
                  title: "Blog Description",
                  children: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Html(
                      data: blogById.description,
                      shrinkWrap: true,
                      style: {
                        "*": Style(
                          margin: Margins.zero,
                          padding: HtmlPaddings.zero,
                        ),
                      },
                    ),
                  ),
                ),
              ],
            );
          },
        );
      }),
    );
  }

  Widget _buildBlogInfoTile({
    required String title,
    required Widget children,
    required RxBool isExpanded,
  }) {
    return Obx(() {
      return Container(
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: BorderRadius.circular(10.0),
          boxShadow: [
            BoxShadow(
              color: Get.theme.customColors.shadowColor!.withValues(alpha: 0.2),
              blurRadius: 6.0,
              offset: Offset(0, 2),
            ),
          ],
        ),
        clipBehavior: Clip.hardEdge,
        child: ExpansionTile(
          onExpansionChanged: (expanded) {
            isExpanded.value = expanded;
          },
          trailing: CustomImageView(
            imagePath:
                isExpanded.value
                    ? AssetConstants.icDownArrow2
                    : AssetConstants.icRightArrow2,
          ),
          shape: RoundedRectangleBorder(),
          expandedAlignment: Alignment.centerLeft,
          title: Text(
            title,
            style: Get.textTheme.titleMedium?.copyWith(
              fontSize: 16.0.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          children: [
            Container(
              clipBehavior: Clip.hardEdge,
              width: double.maxFinite,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.vertical(top: Radius.circular(10.0)),
                border: Border(
                  top: BorderSide(color: Get.theme.customColors.borderColor!),
                ),
              ),
              child: children,
            ),
          ],
        ),
      );
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}
}
