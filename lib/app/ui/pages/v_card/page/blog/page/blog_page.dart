import 'package:get/get.dart';
import 'package:v_card/app/controllers/blog_controller.dart';
import 'package:v_card/app/data/model/blog/blog_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/blog/widget/action_button.dart';
import 'package:v_card/app/ui/pages/v_card/page/template/widgets/template_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class BlogPage extends GetItHook<BlogController> {
  BlogPage({super.key});
  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_blog,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      floatingActionButton: GestureDetector(
        onTap: () {
          controller.resetForm();
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createBlogPage,
            arguments: {'vcardId': vcardId},
          );
        },
        child: CircleAvatar(
          radius: 32.r,
          backgroundColor: Get.theme.customColors.primaryColor,
          child: const Icon(Icons.add, size: 32),
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () => controller.getBlogList(vcardId),
        child: Obx(() {
          if (controller.isLoading.value) {
            return const Center(child: TemplatesShimmer());
          }

          final blogList = controller.blogList.value?.data ?? [];

          if (blogList.isEmpty) {
            return SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: NoDataWidget(
                message: AppStrings.T.lbl_no_data,
                padding: EdgeInsets.only(top: 180.h),
              ),
            );
          }

          return GridView.builder(
            padding: EdgeInsets.only(left: 16.0, right: 16.0, bottom: 120.h),

            physics: const AlwaysScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 4,
              mainAxisSpacing: 4,
              childAspectRatio: 0.8,
            ),
            itemCount: blogList.length,
            itemBuilder:
                (context, index) =>
                    _buildTemplateCard(context, blogList[index]),
          );
        }),
      ),
    );
  }

  Widget _buildTemplateCard(BuildContext context, Blog blog) {
    return Card(
      clipBehavior: Clip.hardEdge,
      color: Get.theme.customColors.white,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Column(
        children: [
          Expanded(
            child: CustomImageView(
              width: double.maxFinite,
              imagePath: blog.blogIcon,
              fit: BoxFit.cover,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ActionButton(
                  iconPath: AssetConstants.svgEye,
                  onTap: () {
                    controller.getBlogById(id: blog.id);
                    NavigationService.navigateWithSlideAnimation(
                      AppRoutes.blogDetailPage,
                      arguments: {'vcardId': vcardId},
                    );
                  },
                ),
                ActionButton(
                  iconPath: AssetConstants.icEdit,
                  onTap: () {
                    NavigationService.navigateWithSlideAnimation(
                      AppRoutes.createBlogPage,
                      arguments: {'vcardId': vcardId, 'blogId': blog.id},
                    );
                  },
                ),
                ActionButton(
                  iconPath: AssetConstants.icDelete2,
                  onTap: () {
                    Get.dialog(
                      LoadingConfirmationDialog(
                        title: AppStrings.T.lbl_delete_blog,
                        message: AppStrings.T.lbl_delete_blog_subtitle,
                        onCancel: () => NavigationService.navigateBack(),
                        onConfirm:
                            () => controller.deleteBlogById(
                              id: blog.id,
                              vcardId: vcardId,
                            ),
                        isLoading: controller.isLoadingDeleteBlogById,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getBlogList(vcardId);
  }
}
