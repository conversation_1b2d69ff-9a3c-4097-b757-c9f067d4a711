import 'dart:io';

import 'package:get/get.dart';
import 'package:v_card/app/controllers/blog_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

class CreateBlogPage extends GetItHook<BlogController> {
  CreateBlogPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    final arguments = Get.arguments ?? {};
    final blogId = arguments['blogId'];

    if (blogId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getBlogById(id: blogId);
        final blog = controller.blogDataById.value?.data;
        if (blog != null) {
          controller.nameController.text = blog.title;
          controller.descriptionController.text = blog.description ?? '';
          controller.blogUrlController.text = blog.blogIcon;
        }
      });
    }
    controller.isProcessingPWAIcon = false.obs;
  }

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments;
    final vcardId = arguments['vcardId'] ?? 0;
    final blogId = arguments['blogId'];

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          blogId == null
              ? AppStrings.T.lbl_create_blog
              : AppStrings.T.lbl_edit_blog,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createTestimonialsPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),

      body: Obx(() {
        if (controller.isLoadingBlogById.value == true) {
          return CustomFormShimmerLoading();
        }

        return IgnorePointer(
          ignoring: controller.isLoadingCreateBlog.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Gap(10.h),
                          Text.rich(
                            TextSpan(
                              text: AppStrings.T.lbl_blog_icon,
                              style: Get.theme.textTheme.bodyLarge?.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                              children: [
                                TextSpan(
                                  text: "*",
                                  style: Get.theme.textTheme.bodyLarge
                                      ?.copyWith(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600,
                                        color:
                                            Get.theme.customColors.primaryColor,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          Gap(10.h),
                          Obx(() {
                            return GestureDetector(
                              onTap: () async {
                                if (controller.isProcessingPWAIcon.value) {
                                  print("Processing is already in progress.");
                                  return; // Exit early if processing is ongoing
                                } else {
                                  Future.delayed(Duration(seconds: 1), () {
                                    controller.isProcessingPWAIcon.value = true;
                                  });

                                  try {
                                    final File? iconFile =
                                        await PWAIconHandler.createPWAIcon(
                                          context: context,
                                          source: ImageSource.gallery,
                                          size: 512,
                                          maxKB: 50,
                                          format: 'png',
                                        );

                                    if (iconFile != null) {
                                      controller.profileImageFile.value =
                                          iconFile;
                                      // The icon is now ready for upload
                                    }
                                  } catch (e) {
                                    print('Error creating PWA icon: $e');
                                    // Display an error message or toast if needed
                                  } finally {
                                    // Reset the processing flag
                                    controller.isProcessingPWAIcon.value =
                                        false;
                                  }
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(3.0),
                                decoration: BoxDecoration(
                                  color: Get.theme.customColors.white,
                                  borderRadius: BorderRadius.circular(16.r),
                                  border: Border.all(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Get.theme.customColors.black!
                                          .withValues(alpha: 0.1),
                                      blurRadius: 20,
                                      spreadRadius: 5,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Stack(
                                  children: [
                                    // Show shimmer while processing, else show image
                                    if (controller.isProcessingPWAIcon.value)
                                      ShimmerBox(
                                        height: 145.h,
                                        width: 158.w,
                                        borderRadius: BorderRadius.circular(
                                          16.r,
                                        ),
                                      )
                                    else
                                      CustomImageView(
                                        height: 145.h,
                                        width: 158.w,
                                        fit: BoxFit.cover,
                                        radius: BorderRadius.circular(10.r),
                                        imagePath:
                                            controller
                                                .profileImageFile
                                                .value
                                                ?.path ??
                                            controller
                                                .blogDataById
                                                .value
                                                ?.data
                                                .blogIcon,
                                      ),
                                    Positioned(
                                      right: 8,
                                      bottom: 13.0,
                                      child: Container(
                                        height: 35.0.h,
                                        width: 35.0.h,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Get.theme.customColors.white,
                                        ),
                                        alignment: Alignment.center,
                                        child: CustomImageView(
                                          imagePath: AssetConstants.icCamera,
                                          color:
                                              Get
                                                  .theme
                                                  .customColors
                                                  .primaryColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                    Gap(24.h),

                    TextInputField(
                      type: InputType.name,
                      isCapitalized: true,
                      controller: controller.nameController,
                      label: AppStrings.T.lbl_blogs_name,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_blogs_name,
                          ),
                    ),

                    Gap(24.h),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text.rich(
                          TextSpan(
                            text: AppStrings.T.lbl_blogs_description,
                            style: Get.theme.textTheme.bodyMedium?.copyWith(
                              fontSize: 14.sp,
                              color: Get.theme.customColors.greyTextColor,
                              fontWeight: FontWeight.w600,
                            ),
                            children: [
                              TextSpan(
                                text: ' *',
                                style: Get.theme.textTheme.bodyMedium?.copyWith(
                                  fontSize: 14.sp,
                                  color: Get.theme.customColors.primaryColor,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Gap(12.0),
                        Container(
                          height: Get.height * 0.22.h,
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8.r),
                          ),
                          child: Column(
                            children: [
                              quill.QuillSimpleToolbar(
                                controller: controller.quillController,
                                config: quill.QuillSimpleToolbarConfig(
                                  showBoldButton: true,
                                  showUnderLineButton: true,
                                  showItalicButton: true,
                                  showFontSize: true,
                                  showClearFormat: false,
                                  showClipboardPaste: false,
                                  showLineHeightButton: false,
                                  showListCheck: false,
                                  showRedo: false,
                                  showUndo: false,
                                  showClipboardCopy: false,
                                  showClipboardCut: false,
                                  showAlignmentButtons: false,
                                  showBackgroundColorButton: false,
                                  showCenterAlignment: false,
                                  showColorButton: false,
                                  showCodeBlock: false,
                                  showDirection: false,
                                  showFontFamily: false,
                                  showHeaderStyle: false,
                                  showIndent: false,
                                  showInlineCode: false,
                                  showJustifyAlignment: false,
                                  showLeftAlignment: false,
                                  showLink: false,
                                  showListNumbers: false,
                                  showListBullets: false,
                                  showQuote: false,
                                  showRightAlignment: false,
                                  showSearchButton: false,
                                  showSmallButton: false,
                                  showStrikeThrough: false,
                                  showSubscript: false,
                                  showSuperscript: false,
                                  showDividers: false,
                                  multiRowsDisplay: false,
                                ),
                              ),
                              Expanded(
                                child: Container(
                                  padding: EdgeInsets.all(8.sp),
                                  child: quill.QuillEditor.basic(
                                    controller: controller.quillController,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Gap(24.h),
                    _buildButtons(_formKey, vcardId, blogId, context),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildButtons(
    GlobalKey<FormState> formKey,
    String vcardId,
    dynamic blogId,
    BuildContext context,
  ) {
    return Obx(
      () => Container(
        margin: EdgeInsets.only(
          bottom: MediaQuery.viewInsetsOf(context).bottom,
        ),
        child: CustomElevatedButton(
          checkConnectivity: true,
          isLoading: controller.isLoadingCreateBlog.value,
          text: AppStrings.T.lbl_save,
          onPressed: () async {
            if (controller.isLoadingCreateBlog.value) {
              return;
            }
            if (controller.isShowingToast.value) {
              return;
            }

            if (!_formKey.currentState!.validate()) {
              return;
            }

            final delta = controller.quillController.document.toDelta();
            final plainText =
                controller.quillController.document.toPlainText().trim();

            if (plainText.isEmpty) {
              toastification.show(
                type: ToastificationType.error,
                style: ToastificationStyle.flatColored,
                alignment: Alignment.topCenter,
                description: Text("Description is required"),
                autoCloseDuration: const Duration(seconds: 3),
              );
              return;
            }

            final converter = QuillDeltaToHtmlConverter(
              delta.toJson(),
              ConverterOptions(),
            );
            final htmlContent = converter.convert();

            if (blogId == null) {
              if (controller.isProcessingPWAIcon.value) {
                _showImageValidationToast(
                  AppStrings.T.lbl_image_processing_message,
                  false,
                );
                return;
              } else if (controller.profileImageFile.value == null) {
                _showImageValidationToast(
                  AppStrings.T.lbl_please_select_image,
                  true,
                );
                return;
              }

              await controller.createAdminBlog(
                name: controller.nameController.text,
                profileImg: controller.profileImageFile.value?.path,
                description: htmlContent,
                vcardId: vcardId.toString(),
                blogUrl: controller.blogUrlController.text,
              );
            } else {
              if (controller.isProcessingPWAIcon.value) {
                _showImageValidationToast(
                  AppStrings.T.lbl_image_processing_message,
                  false,
                );
                return;
              }
              controller.isLoadingCreateBlog.value = true;
              String? imagePath;
              if (controller.profileImageFile.value?.path != null) {
                imagePath = controller.profileImageFile.value!.path;
              } else {
                final file = await urlToFile(
                  controller.blogDataById.value?.data.blogIcon ?? '',
                );
                imagePath = file.path;
              }
              // Update existing blog
              await controller.updateBlog(
                blogId: blogId,
                name: controller.nameController.text,
                profileImg: imagePath,
                description: htmlContent,
                vcardId: vcardId.toString(),
                blogUrl: controller.blogUrlController.text,
              );
            }
          },
        ),
      ),
    );
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.quillController.clear();
    controller.nameController.clear();
    controller.descriptionController.clear();
    controller.blogUrlController.clear();
    controller.profileImageFile.value = null;
  }
}
