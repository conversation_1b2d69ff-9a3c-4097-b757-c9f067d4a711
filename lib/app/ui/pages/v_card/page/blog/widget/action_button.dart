import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:v_card/app/ui/widgets/custom_image_view.dart';
import 'package:v_card/app/utils/themes/custom_color_extension.dart';

class ActionButton extends StatelessWidget {
  final String iconPath;
  final VoidCallback onTap;
  final bool isLoading;

  const ActionButton({super.key, required this.iconPath, required this.onTap, this.isLoading = false});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      child: CircleAvatar(
        radius: 16.r,
        backgroundColor: Get.theme.customColors.darkBlueColor!.withAlpha(13), // 0.05 * 255 ≈ 13
        child:
            isLoading
                ? const Center(child: SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)))
                : CustomImageView(
                  imagePath: iconPath,
                  margin: const EdgeInsets.all(7.0),
                  color: Get.theme.customColors.greyTextColor,
                ),
      ),
    );
  }
}
