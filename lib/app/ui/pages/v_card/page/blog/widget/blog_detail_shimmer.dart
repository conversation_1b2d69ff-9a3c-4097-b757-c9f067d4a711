import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class BlogDetailsShimmer extends StatelessWidget {
  const BlogDetailsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      physics: const BouncingScrollPhysics(),
      padding: EdgeInsets.all(16.0),
      children: [
        _buildShimmerTile(height: 72.0.h),
        Gap(18.0.h),
        _buildShimmerTile(height: 72.0.h),
        Gap(18.0.h),
        _buildShimmerTile(height: 72.0.h),
      ],
    );
  }

  Widget _buildShimmerTile({required double height}) {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(10.0.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 6.0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      clipBehavior: Clip.hardEdge,
      child: ExpansionTile(
        iconColor: Colors.transparent,
        collapsedIconColor: Colors.transparent,
        initiallyExpanded: true,
        shape: const RoundedRectangleBorder(),
        title: ShimmerBox(height: 20.0.h, width: 120.0.w),

        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.0.w, vertical: 10.0.h),
            child: ShimmerBox(height: height, width: double.infinity),
          ),
        ],
      ),
    );
  }
}
