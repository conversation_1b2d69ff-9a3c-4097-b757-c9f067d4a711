import 'dart:io';
import 'dart:math';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateVcardPage extends GetItHook<VcardController> {
  CreateVcardPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  String _generateRandomString(int length) {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final rand = Random.secure();
    return List.generate(
      length,
      (_) => chars[rand.nextInt(chars.length)],
    ).join();
  }

  Future<File?> _pickCompressedImage({
    required String imageType,
    int? targetSize,
    int? maxKB,
    String format = 'png',
    required BuildContext context,
  }) async {
    try {
      final File? compressedImage = await PWAIconHandler.createPWAIcon(
        context: context,
        source: ImageSource.gallery,
        size: targetSize ?? 512, 
        maxKB: maxKB ?? 100, 
        format: format,
      );

      // if (compressedImage != null) {
      //   _showImageValidationToast('$imageType processed successfully!', false);
      // }

      return compressedImage;
    } catch (e) {
      print('Error selecting $imageType: $e');
      _showImageValidationToast(
        'Failed to process $imageType. Please try again.',
        true,
      );
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_create_vcard,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: IgnorePointer(
        ignoring: controller.isLoadingCreateVcard.value,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            Text.rich(
                              TextSpan(
                                text: AppStrings.T.lbl_profile_image,
                                style: Get.theme.textTheme.bodyLarge!.copyWith(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                                children: [
                                  TextSpan(
                                    text: "*",
                                    style: Get.theme.textTheme.bodyLarge!
                                        .copyWith(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                          color:
                                              Get
                                                  .theme
                                                  .customColors
                                                  .primaryColor,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            Gap(10.0),
                            Container(
                              height: 140.h,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Get.theme.customColors.white,
                                border: Border.all(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                                borderRadius: BorderRadius.circular(16.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 25,
                                    offset: const Offset(0, 0),
                                  ),
                                ],
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: InkWell(
                                  onTap:
                                      controller.isProcessingPWAIcon.value
                                          ? null
                                          : () async {
                                            controller
                                                .isProcessingPWAIcon
                                                .value = true;
                                            try {
                                              final File? compressedImage =
                                                  await _pickCompressedImage(
                                                    context: context,
                                                    imageType: 'Profile image',
                                                    targetSize:
                                                        512, // Good size for profile images
                                                    maxKB:
                                                        150, // Generous limit for profile images
                                                    format:
                                                        'jpg', // JPEG for photos
                                                  );

                                              if (compressedImage != null) {
                                                controller
                                                    .profileImageFile
                                                    .value = compressedImage;
                                              }
                                            } finally {
                                              controller
                                                  .isProcessingPWAIcon
                                                  .value = false;
                                            }
                                          },
                                  child: Obx(
                                    () => Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        if (controller
                                            .isProcessingPWAIcon
                                            .value)
                                          ShimmerBox(
                                            height: 140.h,
                                            width: double.infinity,
                                            borderRadius: BorderRadius.circular(
                                              12.0,
                                            ),
                                          )
                                        else
                                          CustomImageView(
                                            height: double.infinity,
                                            width: double.infinity,
                                            fit: BoxFit.cover,
                                            radius: BorderRadius.circular(11.r),
                                            imagePath:
                                                controller
                                                    .profileImageFile
                                                    .value
                                                    ?.path,
                                          ),
                                        if (!controller
                                            .isProcessingPWAIcon
                                            .value)
                                          Positioned(
                                            right: 6,
                                            bottom: 6,
                                            child: CircleAvatar(
                                              radius: 22.r,
                                              backgroundColor:
                                                  Get.theme.customColors.white,
                                              child: CustomImageView(
                                                imagePath:
                                                    AssetConstants.icCamera,
                                                color:
                                                    Get
                                                        .theme
                                                        .customColors
                                                        .primaryColor,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Gap(38.h),
                      Expanded(
                        child: Column(
                          children: [
                            Text.rich(
                              TextSpan(
                                text: AppStrings.T.lbl_favicon_image,
                                style: Get.theme.textTheme.bodyLarge!.copyWith(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                                children: [
                                  TextSpan(
                                    text: "*",
                                    style: Get.theme.textTheme.bodyLarge!
                                        .copyWith(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w600,
                                          color:
                                              Get
                                                  .theme
                                                  .customColors
                                                  .primaryColor,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            Gap(10.0),
                            Container(
                              height: 140.h,
                              width: double.infinity,
                              decoration: BoxDecoration(
                                color: Get.theme.customColors.white,
                                border: Border.all(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                                borderRadius: BorderRadius.circular(16.r),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.1),
                                    blurRadius: 25,
                                    offset: const Offset(0, 0),
                                  ),
                                ],
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: InkWell(
                                  onTap:
                                      controller
                                              .isProcessingFaviconPWAIcon
                                              .value
                                          ? null
                                          : () async {
                                            controller
                                                .isProcessingFaviconPWAIcon
                                                .value = true;
                                            try {
                                              final File? compressedImage =
                                                  await _pickCompressedImage(
                                                    context: context,
                                                    imageType: 'Favicon',
                                                    targetSize:
                                                        192, 
                                                    maxKB:
                                                        50, 
                                                    format:
                                                        'png', 
                                                  );

                                              if (compressedImage != null) {
                                                controller.faviconFile.value =
                                                    compressedImage;
                                              }
                                            } finally {
                                              controller
                                                  .isProcessingFaviconPWAIcon
                                                  .value = false;
                                            }
                                          },
                                  child: Obx(() {
                                    return Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        if (controller
                                            .isProcessingFaviconPWAIcon
                                            .value)
                                          ShimmerBox(
                                            height: 140.h,
                                            width: double.infinity,
                                            borderRadius: BorderRadius.circular(
                                              12.0,
                                            ),
                                          )
                                        else
                                          CustomImageView(
                                            height: double.infinity,
                                            width: double.infinity,
                                            fit: BoxFit.cover,
                                            radius: BorderRadius.circular(11.r),
                                            imagePath:
                                                controller
                                                    .faviconFile
                                                    .value
                                                    ?.path,
                                          ),
                                        if (!controller
                                            .isProcessingFaviconPWAIcon
                                            .value)
                                          Positioned(
                                            right: 6,
                                            bottom: 6,
                                            child: CircleAvatar(
                                              radius: 22.r,
                                              backgroundColor:
                                                  Get.theme.customColors.white,
                                              child: CustomImageView(
                                                imagePath:
                                                    AssetConstants.icCamera,
                                                color:
                                                    Get
                                                        .theme
                                                        .customColors
                                                        .primaryColor,
                                              ),
                                            ),
                                          ),
                                      ],
                                    );
                                  }),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Gap(12.h),

                  Text.rich(
                    TextSpan(
                      text: AppStrings.T.lbl_cover_type,
                      style: Get.theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w800,
                      ),
                      children: [
                        TextSpan(
                          text: "*",
                          style: Get.theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w800,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Gap(8.h),
                  Obx(
                    () => Column(
                      children: [
                        GestureDetector(
                          onTap: () {
                            controller.isExpanded.value =
                                !controller.isExpanded.value;
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 12.h,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Get.theme.customColors.primaryColor!,
                              ),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppText(
                                  _getCoverTypeLabel(
                                    controller.selectedCoverType.value,
                                  ),
                                  style: Get.theme.textTheme.bodyMedium
                                      ?.copyWith(fontWeight: FontWeight.w500),
                                ),
                                CustomImageView(
                                  imagePath:
                                      controller.isExpanded.value
                                          ? AssetConstants.icDownArrow2
                                          : AssetConstants.icRightArrow2,
                                  color:
                                      Get.theme.customColors.darkGreyTextColor,
                                ),
                              ],
                            ),
                          ),
                        ),

                        if (controller.isExpanded.value) ...[
                          Gap(8.h),
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Get.theme.customColors.primaryColor!,
                              ),
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            child: Column(
                              children: List.generate(3, (index) {
                                return GestureDetector(
                                  onTap: () {
                                    controller.selectedCoverType.value = index;
                                    controller.coverImageFile.value = null;
                                    controller.youtubeLinkController.clear();
                                    controller.isExpanded.value = false;
                                  },
                                  child: Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 16.w,
                                      vertical: 12.h,
                                    ),
                                    decoration: BoxDecoration(
                                      color:
                                          controller.selectedCoverType.value ==
                                                  index
                                              ? Colors.grey[200]
                                              : Colors.transparent,
                                      borderRadius: BorderRadius.circular(4.r),
                                    ),
                                    child: Row(
                                      children: [
                                        AppText(
                                          _getCoverTypeLabel(index),
                                          style: Get.theme.textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              }),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Gap(12.h),

                  Obx(() {
                    switch (controller.selectedCoverType.value) {
                      case 0: // Image
                        return _buildImageCoverSelector(context);
                      case 1: // Video
                        return _buildVideoCoverSelector();
                      case 2: // YouTube
                        return _buildYoutubeCoverField();
                      default:
                        return _buildImageCoverSelector(context);
                    }
                  }),

                  Gap(20.h),
                  TextInputField(
                    type: InputType.text,
                    isCapitalized: true,
                    label: AppStrings.T.lbl_vcard_name,
                    controller: controller.nameController,
                    keyboardType: TextInputType.text,
                    isRequiredField: true,
                    validator:
                        (value) => AppValidations.vCardNameValidation(
                          value,
                          fieldName: AppStrings.T.lbl_vcard_name,
                        ),
                  ),
                  Gap(12.h),
                  TextInputField(
                    type: InputType.text,
                    hintLabel: AppStrings.T.lbl_my_vcard_page_url,
                    label: AppStrings.T.lbl_url_alias,
                    controller: controller.urlAliasController,
                    suffixIcon: IconButton(
                      onPressed: () {
                        final randomString = _generateRandomString(12);
                        controller.urlAliasController.text = randomString;
                      },
                      icon: Icon(
                        Icons.autorenew,
                        color: Get.theme.customColors.greyTextColor,
                      ),
                    ),
                    keyboardType: TextInputType.text,
                    isRequiredField: true,
                    validator:
                        (value) => AppValidations.validateRequired(
                          value,
                          fieldName: AppStrings.T.lbl_url_alias,
                        ),
                  ),
                  Gap(12.h),
                  TextInputField(
                    type: InputType.multiline,
                    textInputAction: TextInputAction.newline,
                    isCapitalized: true,
                    label: AppStrings.T.lbl_description,
                    controller: controller.descriptionController,
                    maxLines: 4,
                  ),
                  Gap(12.h),
                  TextInputField(
                    type: InputType.multiline,
                    textInputAction: TextInputAction.newline,
                    isCapitalized: true,
                    label: AppStrings.T.lbl_occupation,
                    controller: controller.occupationController,
                    maxLines: 3,
                    minLines: 1,
                  ),
                  Gap(20.h),
                  Obx(
                    () => CustomElevatedButton(
                      checkConnectivity: true,
                      isLoading: controller.isLoadingCreateVcard.value,
                      onPressed: () async {
                        if (controller.isLoadingCreateVcard.value) return;
                        if (controller.isShowingToast.value) return;

                        if (controller.isProcessingPWAIcon.value) {
                          _showImageValidationToast(
                            AppStrings.T.lbl_image_processing_message,
                            false,
                          );
                          return;
                        }

                        if (controller.isProcessingFaviconPWAIcon.value) {
                          _showImageValidationToast(
                            AppStrings.T.lbl_image_processing_message,
                            false,
                          );
                          return;
                        }

                        if (controller.isProcessingCoverPWAIcon.value &&
                            controller.selectedCoverType.value == 0) {
                          _showImageValidationToast(
                            AppStrings.T.lbl_image_processing_message,
                            false,
                          );
                          return;
                        }

                        if (controller.profileImageFile.value == null) {
                          _showImageValidationToast(
                            AppStrings.T.msg_select_profile_image,
                            true,
                          );
                          return;
                        }

                        if (controller.faviconFile.value == null) {
                          _showImageValidationToast(
                            AppStrings.T.msg_select_favicon_image,
                            true,
                          );
                          return;
                        }

                        if (controller.selectedCoverType.value == 0 &&
                            controller.coverImageFile.value == null) {
                          _showImageValidationToast(
                            AppStrings.T.msg_select_cover_image,
                            true,
                          );
                          return;
                        }

                        if (controller.selectedCoverType.value == 1 &&
                            controller.coverImageFile.value == null) {
                          _showImageValidationToast(
                            AppStrings.T.msg_select_cover_video,
                            true,
                          );
                          return;
                        }
                        if (_formKey.currentState?.validate() ?? false) {
                          await controller.createAdminVcard(
                            urlAlias: controller.urlAliasController.text.trim(),
                            name: controller.nameController.text,
                            profileImg: controller.profileImageFile.value!.path,
                            description: controller.descriptionController.text,
                            occupation: controller.occupationController.text,
                            coverImg:
                                controller.selectedCoverType.value != 2
                                    ? controller.coverImageFile.value!.path
                                    : '',
                            faviconImg: controller.faviconFile.value!.path,
                            coverType: controller.selectedCoverType.value,
                            youtubeLink:
                                controller.selectedCoverType.value == 2
                                    ? controller.youtubeLinkController.text
                                        .trim()
                                    : null,
                          );
                        }
                      },
                      text: AppStrings.T.lbl_create_vcard,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageCoverSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_select_cover_image,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        Gap(8.h),
        GestureDetector(
          onTap:
              controller.isProcessingCoverPWAIcon.value
                  ? null
                  : () async {
                    if (controller.coverImageFile.value == null) {
                      if (controller.isProcessingCoverPWAIcon.value) {
                        Logger.log("Processing is already in progress.");
                        return;
                      } else {
                        controller.isProcessingCoverPWAIcon.value = true;
                        try {
                          final File?
                          compressedImage = await _pickCompressedImage(
                            context: context,
                            imageType: 'Cover image',
                            targetSize: 1024, 
                            maxKB: 300, 
                            format: 'jpg', 
                          );

                          if (compressedImage != null) {
                            controller.coverImageFile.value = compressedImage;
                          }
                        } finally {
                          controller.isProcessingCoverPWAIcon.value = false;
                        }
                      }
                    }
                  },
          child: Container(
            height: 120.h,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              border: Border.all(color: Get.theme.customColors.primaryColor!),
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: Obx(
              () => Stack(
                alignment: Alignment.center,
                children: [
                  if (controller.isProcessingCoverPWAIcon.value)
                    Padding(
                      padding: const EdgeInsets.all(2.0),
                      child: ShimmerBox(
                        height: double.infinity,
                        width: double.infinity,
                        borderRadius: BorderRadius.circular(12.0),
                      ),
                    )
                  else
                    controller.coverImageFile.value != null
                        ? Stack(
                          children: [
                            CustomImageView(
                              height: double.infinity,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              margin: const EdgeInsets.all(2.0),
                              radius: BorderRadius.circular(16.r),
                              imagePath: controller.coverImageFile.value?.path,
                            ),
                            Positioned(
                              right: 8,
                              top: 8,
                              child: IconButton(
                                icon: Icon(Icons.close, color: Colors.red),
                                onPressed:
                                    () =>
                                        controller.coverImageFile.value = null,
                              ),
                            ),
                          ],
                        )
                        : GestureDetector(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add,
                                  size: 40.sp,
                                  color: Get.theme.customColors.primaryColor,
                                ),
                                Text(
                                  AppStrings.T.lbl_add_image,
                                  style: Get.theme.textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          ),
                        ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoCoverSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_select_cover_video,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        Gap(8.h),
        GestureDetector(
          onTap: () async {
            if (controller.coverImageFile.value != null) {
            } else {
              await controller.pickCoverVideo();
            }
          },
          child: Container(
            height: 120.h,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              border: Border.all(color: Get.theme.customColors.primaryColor!),
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: Obx(
              () =>
                  controller.coverImageFile.value != null
                      ? Stack(
                        children: [
                          Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.videocam,
                                  size: 40.sp,
                                  color: Get.theme.customColors.primaryColor,
                                ),
                                Text(
                                  controller.coverImageFile.value!.path
                                      .split('/')
                                      .last,
                                  style: Get.theme.textTheme.bodyMedium,
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                            right: 8,
                            top: 8,
                            child: IconButton(
                              icon: Icon(Icons.close, color: Colors.red),
                              onPressed:
                                  () => controller.coverImageFile.value = null,
                            ),
                          ),
                        ],
                      )
                      : GestureDetector(
                        onTap: () async {
                          await controller.pickCoverVideo();
                        },
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add,
                                size: 40.sp,
                                color: Get.theme.customColors.primaryColor,
                              ),
                              Text(
                                AppStrings.T.lbl_add_video,
                                style: Get.theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                      ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildYoutubeCoverField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_youtube_url,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        Gap(8.h),
        TextInputField(
          type: InputType.text,
          label: AppStrings.T.lbl_youtube_url,
          controller: controller.youtubeLinkController,
          keyboardType: TextInputType.url,
          validator: (value) {
            if (controller.selectedCoverType.value == 2) {
              return AppValidations.validateRequired(
                value,
                fieldName: AppStrings.T.lbl_youtube_url,
              );
            }
            return null;
          },
        ),
      ],
    );
  }

  String _getCoverTypeLabel(int value) {
    switch (value) {
      case 0:
        return AppStrings.T.lbl_image;
      case 1:
        return AppStrings.T.lbl_video;
      case 2:
        return AppStrings.T.lbl_youtube_link;
      default:
        return AppStrings.T.lbl_select_type;
    }
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.profileImageFile.value = null;
    controller.coverImageFile.value = null;
    controller.faviconFile.value = null;
    controller.selectedCoverType.value = 0;
    controller.isExpanded.value = false;
    controller.youtubeLinkController.clear();
    controller.nameController.clear();
    controller.urlAliasController.clear();
    controller.descriptionController.clear();
    controller.occupationController.clear();
    controller.isLoadingCreateVcard.value = false;
  }

  @override
  void onInit() {
    controller.isProcessingPWAIcon = false.obs;
    controller.isProcessingFaviconPWAIcon = false.obs;
    controller.isProcessingCoverPWAIcon = false.obs;
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }
}
