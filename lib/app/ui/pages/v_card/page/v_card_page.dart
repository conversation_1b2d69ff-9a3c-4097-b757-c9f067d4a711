import 'package:get/get.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/data/model/v_card/v_card_model.dart';
import 'package:v_card/app/ui/pages/v_card/widgets/vcard_bottomsheet.dart';
import 'package:v_card/app/ui/pages/v_card/widgets/vcard_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class VcardPage extends GetItHook<VcardController> {
  const VcardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(appBar: _buildAppBar(), floatingActionButton: _buildFloatingActionButton(), body: _buildBody());
  }

  CustomAppbar _buildAppBar() {
    return CustomAppbar(
      title: AppText(
        AppStrings.T.lbl_vcard,
        style: Get.theme.textTheme.bodyLarge?.copyWith(fontSize: 18.sp, fontWeight: FontWeight.w500),
      ),
      hasLeadingIcon: false,
      actions: [
        IconButton(
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
          highlightColor: Colors.transparent,
          icon: Container(
            margin: EdgeInsets.only(left: 8.w),
            height: 40.h,
            width: 40.w,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Get.theme.customColors.textfieldFillColor!),
            ),
            child: CustomImageView(imagePath: AssetConstants.icSearch, margin: const EdgeInsets.all(8.0)),
          ),
          onPressed: controller.toggleSearchMode,
        ),
      ],
    );
  }

  FloatingActionButton _buildFloatingActionButton() {
    return FloatingActionButton(
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(100.0))),
      heroTag: 'v_card_fab',
      onPressed: () => NavigationService.navigateWithSlideAnimation(AppRoutes.createVcardPage),
      backgroundColor: Get.theme.customColors.primaryColor,
      child: const Icon(Icons.add, size: 32),
    );
  }

  Widget _buildBody() {
    return Stack(children: [_buildVCardListSection(), _buildSearchOverlay()]);
  }

  Widget _buildVCardListSection() {
    return Obx(() {
      final hasData = controller.vCardList.value?.data.isNotEmpty ?? false;

      // Show shimmer only during initial load/refresh with no existing data
      if ((!controller.hasInitialData.value || controller.isLoading.value) && !hasData) {
        return RefreshIndicator(
          onRefresh: () => controller.checkNetworkAndLoad(),
          child: VCardShimmer(controller: controller.isSearchActive.value),
        );
      }

      // Always show existing data if available
      if (hasData) {
        return _buildVCardList();
      }

      // Show proper empty state based on connection
      return RefreshIndicator(
        onRefresh: () => controller.checkNetworkAndLoad(),
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: SizedBox(
            height: Get.height * 0.7,
            child: Center(
              child:
                  controller.isConnected.value
                      ? NoDataWidget(message: AppStrings.T.lbl_no_vcard_found, padding: EdgeInsets.zero)
                      : NoInternetWidget(message: AppStrings.T.no_internet_connection),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildSearchOverlay() {
    return Obx(
      () =>
          controller.isSearchActive.value
              ? Positioned(
                top: 10,
                left: 0,
                right: 0,
                child: Container(
                  color: Get.theme.scaffoldBackgroundColor,
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: TextInputField(
                    key: controller.searchBarKey,
                    controller: controller.searchController,
                    focusNode: controller.searchFocusNode,
                    label: AppStrings.T.lbl_search_vcards,
                    onChanged: (value) => controller.searchText.value = value,
                    type: InputType.text,
                    textInputAction: TextInputAction.done,
                    suffixIcon:
                        controller.searchText.value.isNotEmpty
                            ? IconButton(
                              icon: Icon(Icons.close, color: Get.theme.customColors.greyTextColor),
                              onPressed: _clearSearch,
                            )
                            : null,
                  ),
                ),
              )
              : const SizedBox.shrink(),
    );
  }

  void _clearSearch() {
    controller.searchController.clear();
    controller.searchText.value = '';
    controller.toggleSearchMode();
  }

  Widget _buildVCardList() {
    return RefreshIndicator(
      onRefresh: () {
        if (controller.isConnected.value) {
          controller.vCardList.value = null;
          return controller.checkNetworkAndLoad();
        } else {
          return Future.value();
        }
      },
      child: Obx(() {
        final filteredVCards = _getFilteredVCards();

        return Padding(
          padding: EdgeInsets.only(top: controller.isSearchActive.value ? 78.h : 8.h),
          child: ListView.separated(
            padding: EdgeInsets.only(bottom: 120.h),
            itemCount: filteredVCards.isEmpty ? 1 : filteredVCards.length,
            itemBuilder: (context, index) {
              if (filteredVCards.isEmpty) {
                return _buildEmptyResultsWidget();
              }
              return _buildVCardTile(context, filteredVCards[index]);
            },
            separatorBuilder: (_, __) => Gap(8.h),
          ),
        );
      }),
    );
  }

  Widget _buildEmptyResultsWidget() {
    final message =
        controller.isSearchActive.value && controller.searchText.value.isNotEmpty
            ? "${AppStrings.T.lbl_no_results_found_for} '${controller.searchText.value}'"
            : AppStrings.T.lbl_no_vcard_found;
    return NoDataWidget(message: message);
  }

  List<VCardData> _getFilteredVCards() {
    final allVCards = controller.vCardList.value?.data.reversed.toList() ?? [];
    if (!controller.isSearchActive.value) return allVCards;

    final searchLower = controller.searchText.value.toLowerCase();
    return allVCards.where((vCard) {
      return (vCard.name?.toLowerCase().contains(searchLower) ?? false) ||
          (vCard.occupation?.toLowerCase().contains(searchLower) ?? false) ||
          (vCard.createdAt?.toLowerCase().contains(searchLower) ?? false);
    }).toList();
  }

  Widget _buildVCardTile(BuildContext context, VCardData vCard) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Card(
        color: Get.theme.customColors.white,
        elevation: 3,
        margin: EdgeInsets.symmetric(vertical: 3.h),
        child: ListTile(
          leading: CustomImageView(
            height: 50.h,
            width: 50.w,
            imagePath: vCard.image ?? 'default_image_url',
            radius: BorderRadius.circular(100.r),
            fit: BoxFit.cover,
          ),
          title: Text(vCard.name ?? AppStrings.T.lbl_no_name, style: Get.textTheme.bodyLarge),
          subtitle: Text(vCard.occupation ?? AppStrings.T.lbl_no_occupation, style: Get.textTheme.labelSmall),
          trailing: _buildShareButton(vCard),
          onTap: () => _openVcardDetailBottomSheet(vCard.id.toString()),
        ),
      ),
    );
  }

  Widget _buildShareButton(VCardData vCard) {
    return _buildActionIconButton(
      iconPath: AssetConstants.icShareLink,
      imageMargin: const EdgeInsets.all(2.0),
      imageColor: Get.theme.customColors.greyTextColor,
      onTap: () async {
        final url = Uri.parse(vCard.urlAlias.toString());
        await launchUrl(url, mode: LaunchMode.externalApplication);
      },
    );
  }

  Future<void> _openVcardDetailBottomSheet(String id) async {
    // Create a new widget that handles its own state
    final bottomSheet = VCardDetailBottomSheet(controller: controller, vcardId: id);

    // Show the bottom sheet
    Get.bottomSheet(bottomSheet, isScrollControlled: true);

    // Then trigger API calls after bottom sheet is shown
    controller.getVcardById(id: id);
    controller.getVcardQrCodeLink(id: id);
  }

  Widget _buildActionIconButton({
    required String iconPath,
    required VoidCallback onTap,
    bool isLoading = false,
    Color? imageColor,
    EdgeInsets? imageMargin,
  }) {
    return GestureDetector(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: 36.w,
        height: 36.h,
        decoration: BoxDecoration(color: Get.theme.customColors.blueColor?.withAlpha(25), shape: BoxShape.circle),
        child:
            isLoading
                ? const Center(child: SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)))
                : CustomImageView(imagePath: iconPath, color: imageColor, margin: imageMargin),
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.searchController.clear();
    controller.isSearchActive.value = false;
    controller.searchText.value = '';
    controller.searchFocusNode.unfocus();
  }

  @override
  void onInit() {
    isCardCreated = true;
    controller.checkNetworkAndLoad();
  }
}
