import 'dart:io';

import 'package:get/get.dart';
import 'package:v_card/app/controllers/service_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateServicePage extends GetItHook<ServiceController> {
  CreateServicePage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    final arguments = Get.arguments ?? {};
    final serviceId = arguments['serviceId'];
    // controller.isProcessingPWAIcon.value = false;

    if (serviceId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getServiceById(id: serviceId);
        final service = controller.serviceDataById.value?.data;
        if (service != null) {
          controller.nameController.text = service.name;

          controller.descriptionController.text = service.description ?? '';
          controller.serviceUrlController.text = service.serviceUrl ?? '';
        }
      });
    }
    controller.isProcessingPWAIcon = false.obs;
  }

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments;
    final vcardId = arguments['vcardId'] ?? 0;
    final serviceId = arguments['serviceId'];

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          serviceId == null
              ? AppStrings.T.lbl_create_service
              : AppStrings.T.lbl_edit_service,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createProductPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoadingServiceById.value == true) {
          return CustomFormShimmerLoading();
        }

        return IgnorePointer(
          ignoring: controller.isLoadingCreateService.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Gap(20.h),
                          Text.rich(
                            TextSpan(
                              text: AppStrings.T.lbl_service_icon,
                              style: Get.theme.textTheme.bodyLarge?.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                              children: [
                                TextSpan(
                                  text: "*",
                                  style: Get.theme.textTheme.bodyLarge
                                      ?.copyWith(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600,
                                        color:
                                            Get.theme.customColors.primaryColor,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          Gap(10.h),
                          Obx(() {
                            return GestureDetector(
                              onTap: () async {
                                if (controller.isProcessingPWAIcon.value) {
                                  print("Processing is already in progress.");
                                  return; // Exit early if processing is ongoing
                                } else {
                                  Future.delayed(Duration(seconds: 1), () {
                                    controller.isProcessingPWAIcon.value = true;
                                  });

                                  try {
                                    final File? iconFile =
                                        await PWAIconHandler.createPWAIcon(
                                          context: context,
                                          source: ImageSource.gallery,
                                          size: 512,
                                          maxKB: 50,
                                          format: 'png',
                                        );

                                    if (iconFile != null) {
                                      controller.profileImageFile.value =
                                          iconFile;
                                      // The icon is now ready for upload
                                    }
                                  } catch (e) {
                                    print('Error creating PWA icon: $e');
                                    // Display an error message or toast if needed
                                  } finally {
                                    // Reset the processing flag
                                    controller.isProcessingPWAIcon.value =
                                        false;
                                  }
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(3.0),
                                decoration: BoxDecoration(
                                  color: Get.theme.customColors.white,
                                  borderRadius: BorderRadius.circular(16.r),
                                  border: Border.all(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Get.theme.customColors.black!
                                          .withValues(alpha: 0.1),
                                      blurRadius: 20,
                                      spreadRadius: 5,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Stack(
                                  children: [
                                    // Show shimmer while processing, else show image
                                    if (controller.isProcessingPWAIcon.value)
                                      ShimmerBox(height: 145.h, width: 158.w)
                                    else
                                      CustomImageView(
                                        height: 145.h,
                                        width: 158.w,
                                        fit: BoxFit.cover,
                                        radius: BorderRadius.circular(10.r),
                                        imagePath:
                                            controller
                                                .profileImageFile
                                                .value
                                                ?.path ??
                                            controller
                                                .serviceDataById
                                                .value
                                                ?.data
                                                .serviceIcon,
                                      ),
                                    Positioned(
                                      right: 8,
                                      bottom: 13.0,
                                      child: Container(
                                        height: 35.0.h,
                                        width: 35.0.h,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Get.theme.customColors.white,
                                        ),
                                        alignment: Alignment.center,
                                        child: CustomImageView(
                                          imagePath: AssetConstants.icCamera,
                                          color:
                                              Get
                                                  .theme
                                                  .customColors
                                                  .primaryColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                    Gap(24.h),
                    TextInputField(
                      type: InputType.text,
                      isCapitalized: true,
                      isRequiredField: true,
                      controller: controller.nameController,
                      label: AppStrings.T.lbl_enter_service_name,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_service_name,
                          ),
                    ),
                    Gap(12.h),
                    TextInputField(
                      maxLines: 4,
                      type: InputType.multiline,
                      textInputAction: TextInputAction.newline,
                      isCapitalized: true,
                      isRequiredField: true,
                      controller: controller.descriptionController,
                      label: AppStrings.T.lbl_enter_description,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_description,
                          ),
                    ),
                    Gap(12.h),
                    TextInputField(
                      type: InputType.text,
                      textInputAction: TextInputAction.done,
                      controller: controller.serviceUrlController,
                      label: AppStrings.T.lbl_enter_service_url,
                      validator: AppValidations.urlWithEmptyValidation,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
      bottomNavigationBar: _buildButtons(_formKey, vcardId, serviceId, context),
    );
  }

  Widget _buildButtons(
    GlobalKey<FormState> formKey,
    String vcardId,
    dynamic serviceId,
    BuildContext context,
  ) {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(20.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.viewInsetsOf(context).bottom,
        ),
        child: CustomElevatedButton(
          checkConnectivity: true,
          isLoading: controller.isLoadingCreateService.value,
          text: AppStrings.T.lbl_save,
          onPressed: () async {
            if (controller.isLoadingCreateService.value) {
              return;
            }
            if (controller.isShowingToast.value) {
              return;
            }

            if (!_formKey.currentState!.validate()) {
              return;
            }

            if (serviceId == null) {
              if (controller.isProcessingPWAIcon.value) {
                _showImageValidationToast(
                  AppStrings.T.lbl_image_processing_message,
                  false,
                );
                return;
              } else if (controller.profileImageFile.value == null) {
                _showImageValidationToast(
                  AppStrings.T.lbl_please_select_image,
                  true,
                );
                return;
              }

              await controller.createAdminService(
                name: controller.nameController.text,
                profileImg: controller.profileImageFile.value?.path,
                description: controller.descriptionController.text,
                vcardId: vcardId.toString(),
                serviceUrl: controller.serviceUrlController.text,
              );
            } else {
              if (controller.isProcessingPWAIcon.value) {
                _showImageValidationToast(
                  AppStrings.T.lbl_image_processing_message,
                  false,
                );
                return;
              }
              controller.isLoadingCreateService.value = true;
              String? imagePath;
              if (controller.profileImageFile.value?.path != null) {
                imagePath = controller.profileImageFile.value!.path;
              } else {
                final file = await urlToFile(
                  controller.serviceDataById.value!.data.serviceIcon,
                );
                imagePath = file.path;
              }
              await controller.updateService(
                serviceId: serviceId,
                name: controller.nameController.text,
                profileImg: imagePath,
                description: controller.descriptionController.text,
                vcardId: vcardId.toString(),
                serviceUrl: controller.serviceUrlController.text,
              );
            }
          },
        ),
      ),
    );
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.nameController.clear();
    controller.descriptionController.clear();
    controller.serviceUrlController.clear();
    controller.profileImageFile.value = null;
  }
}
