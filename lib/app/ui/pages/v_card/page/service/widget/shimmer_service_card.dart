import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ShimmerServiceCard extends StatelessWidget {
  const ShimmerServiceCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
      constraints: BoxConstraints(maxHeight: Get.height * 0.6),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        padding: EdgeInsets.only(
          top: 20.0.h,
          bottom: 110.0.h,
          left: 20.0.w,
          right: 20.0.w,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _shimmerBox(
              height: 200.0.h,
              width: double.infinity,
              borderRadius: BorderRadius.circular(10.r),
            ),
            Gap(25.h),
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Get.theme.customColors.white,
                borderRadius: BorderRadius.circular(10.0),
                boxShadow: [
                  BoxShadow(
                    color: Get.theme.customColors.shadowColor!.withValues(
                      alpha: 0.1,
                    ),
                    blurRadius: 16.0,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      vertical: 24.0.h,
                      horizontal: 18.0.w,
                    ),
                    child: _shimmerBox(
                      height: 16.0.h,
                      width: 150.0.w,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(18.0.w),
                    child: _shimmerBox(
                      height: 14.0.h,
                      width: double.infinity,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(18.0.w),
                    child: _shimmerBox(
                      height: 14.0.h,
                      width: double.infinity,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(18.0.w),
                    child: _shimmerBox(
                      height: 14.0.h,
                      width: double.infinity,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(18.0.w),
                    child: _shimmerBox(
                      height: 14.0.h,
                      width: double.infinity,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _shimmerBox({
    required double height,
    required double width,
    BorderRadius? borderRadius,
    BoxShape shape = BoxShape.rectangle,
  }) {
    return ShimmerBox(
      height: height,
      width: width,
      borderRadius: shape == BoxShape.rectangle ? borderRadius : null,
    );
  }
}
