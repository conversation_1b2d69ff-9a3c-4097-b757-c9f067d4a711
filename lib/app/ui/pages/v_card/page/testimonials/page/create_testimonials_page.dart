import 'dart:io';

import 'package:get/get.dart';
import 'package:v_card/app/controllers/testimonials_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateTestimonialsPage extends GetItHook<TestimonialsController> {
  CreateTestimonialsPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void onInit() {
    final arguments = Get.arguments ?? {};
    final testimonialsId = arguments['testimonialsId'];

    if (testimonialsId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getTestimonialsById(id: testimonialsId);
        final testimonials = controller.testimonialsDataById.value?.data;
        if (testimonials != null) {
          controller.nameController.text = testimonials.name;
          controller.descriptionController.text =
              testimonials.description ?? '';
        }
      });
    } else {
      controller.resetForm();
    }

    controller.isProcessingPWAIcon = false.obs;
  }

  @override
  Widget build(BuildContext context) {
    final arguments = Get.arguments;
    final vcardId = arguments['vcardId'] ?? 0;
    final testimonialsId = arguments['testimonialsId'];

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          testimonialsId == null
              ? AppStrings.T.lbl_create_testimonials
              : AppStrings.T.lbl_edit_testimonials,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createIframePage,
                    arguments: {'vcardId': vcardId},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoadingTestimonialsById.value == true) {
          return CustomFormShimmerLoading();
        }
        return IgnorePointer(
          ignoring: controller.isLoadingCreateTestimonials.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Gap(10.h),
                          Text.rich(
                            TextSpan(
                              text: AppStrings.T.lbl_testimonials_image,
                              style: Get.theme.textTheme.bodyLarge?.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                              children: [
                                TextSpan(
                                  text: "*",
                                  style: Get.theme.textTheme.bodyLarge
                                      ?.copyWith(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w600,
                                        color:
                                            Get.theme.customColors.primaryColor,
                                      ),
                                ),
                              ],
                            ),
                          ),
                          Gap(10.h),
                          Obx(() {
                            return GestureDetector(
                              onTap: () async {
                                if (controller.isProcessingPWAIcon.value) {
                                  print("Processing is already in progress.");
                                  return; // Exit early if processing is ongoing
                                } else {
                                  Future.delayed(Duration(seconds: 1), () {
                                    controller.isProcessingPWAIcon.value = true;
                                  });

                                  try {
                                    final File? iconFile =
                                        await PWAIconHandler.createPWAIcon(
                                          context: context,
                                          source: ImageSource.gallery,
                                          size: 512,
                                          maxKB: 50,
                                          format: 'png',
                                        );

                                    if (iconFile != null) {
                                      controller.profileImageFile.value =
                                          iconFile;
                                      // The icon is now ready for upload
                                    }
                                  } catch (e) {
                                    print('Error creating PWA icon: $e');
                                    // Display an error message or toast if needed
                                  } finally {
                                    // Reset the processing flag
                                    controller.isProcessingPWAIcon.value =
                                        false;
                                  }
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.all(3.0),
                                decoration: BoxDecoration(
                                  color: Get.theme.customColors.white,
                                  borderRadius: BorderRadius.circular(16.r),
                                  border: Border.all(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Get.theme.customColors.black!
                                          .withValues(alpha: 0.1),
                                      blurRadius: 20,
                                      spreadRadius: 5,
                                      offset: Offset(0, 1),
                                    ),
                                  ],
                                ),
                                child: Stack(
                                  children: [
                                    // Show shimmer while processing, else show image
                                    if (controller.isProcessingPWAIcon.value)
                                      ShimmerBox(height: 145.h, width: 158.w)
                                    else
                                      CustomImageView(
                                        height: 145.h,
                                        width: 158.w,
                                        fit: BoxFit.cover,
                                        radius: BorderRadius.circular(10.r),
                                        imagePath:
                                            controller
                                                .profileImageFile
                                                .value
                                                ?.path ??
                                            controller
                                                .testimonialsDataById
                                                .value
                                                ?.data
                                                .imageUrl,
                                      ),
                                    Positioned(
                                      right: 8,
                                      bottom: 13.0,
                                      child: Container(
                                        height: 35.0.h,
                                        width: 35.0.h,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Get.theme.customColors.white,
                                        ),
                                        alignment: Alignment.center,
                                        child: CustomImageView(
                                          imagePath: AssetConstants.icCamera,
                                          color:
                                              Get
                                                  .theme
                                                  .customColors
                                                  .primaryColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }),
                        ],
                      ),
                    ),
                    Gap(24.h),
                    TextInputField(
                      type: InputType.text,
                      isCapitalized: true,
                      controller: controller.nameController,
                      label: AppStrings.T.lbl_enter_testimonials_name,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_testimonials_name,
                          ),
                      isRequiredField: true,
                    ),
                    Gap(12.h),
                    TextInputField(
                      minLines: 1,
                      maxLines: 4,
                      type: InputType.multiline,
                      textInputAction: TextInputAction.newline,
                      // textInputAction: TextInputAction.done,
                      textAlignVertical: TextAlignVertical.top,
                      isCapitalized: true,
                      controller: controller.descriptionController,
                      label: AppStrings.T.lbl_enter_description,
                      isRequiredField: true,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_description,
                          ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
      bottomNavigationBar: _buildButtons(
        _formKey,
        vcardId,
        testimonialsId,
        context,
      ),
    );
  }

  Widget _buildButtons(
    GlobalKey<FormState> formKey,
    String vcardId,
    dynamic testimonialsId,
    BuildContext context,
  ) {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(16.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.viewInsetsOf(context).bottom,
        ),
        child: CustomElevatedButton(
          checkConnectivity: true,
          isLoading: controller.isLoadingCreateTestimonials.value,
          text: AppStrings.T.lbl_save,
          onPressed: () async {
            if (controller.isLoadingCreateTestimonials.value) {
              return;
            }
            if (controller.isShowingToast.value) {
              return;
            }

            if (!_formKey.currentState!.validate()) return;

            if (testimonialsId == null) {
              if (controller.isProcessingPWAIcon.value) {
                _showImageValidationToast(
                  AppStrings.T.lbl_image_processing_message,
                  false,
                );
                return;
              } else if (controller.profileImageFile.value == null) {
                _showImageValidationToast(
                  AppStrings.T.lbl_please_select_image,
                  true,
                );
                return;
              }

              await controller.createAdminTestimonials(
                name: controller.nameController.text,
                profileImg: controller.profileImageFile.value?.path,
                description: controller.descriptionController.text,
                vcardId: vcardId.toString(),
              );
            } else {
              if (controller.profileImageFile.value == null) {
                _showImageValidationToast(
                  AppStrings.T.lbl_please_select_image,
                  true,
                );
                return;
              }
              controller.isLoadingCreateTestimonials.value = true;
              String? imagePath;
              if (controller.profileImageFile.value?.path != null) {
                imagePath = controller.profileImageFile.value!.path;
              } else {
                final file = await urlToFile(
                  controller.testimonialsDataById.value!.data.imageUrl ?? '',
                );
                imagePath = file.path;
              }
              await controller.updateTestimonials(
                testimonialsId: testimonialsId,
                name: controller.nameController.text,
                profileImg: imagePath,
                description: controller.descriptionController.text,
                vcardId: vcardId.toString(),
              );
            }
          },
        ),
      ),
    );
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.nameController.clear();
    controller.descriptionController.clear();
    controller.profileImageFile.value = null;
  }
}
