import 'package:get/get.dart';
import 'package:v_card/app/controllers/testimonials_controller.dart';
import 'package:v_card/app/data/model/testimonials/testimonials_detail_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/service/widget/shimmer_service_card.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/ui/widgets/custom_list_item_tile.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class TestimonialsPage extends GetItHook<TestimonialsController> {
  TestimonialsPage({super.key});
  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_testimonials,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      floatingActionButton: GestureDetector(
        onTap: () {
          controller.resetForm();
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createTestimonialsPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        },
        child: CircleAvatar(
          radius: 32.r,
          backgroundColor: Get.theme.customColors.primaryColor,
          child: Icon(Icons.add, size: 32),
        ),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return ListTileShimmerLoading();
        }

        final testimonialsList = controller.testimonialsList.value?.data ?? [];

        return RefreshIndicator(
          onRefresh: () => controller.getTestimonialsList(vcardId),
          child: ListView.builder(
            padding: EdgeInsets.only(left: 16.0, right: 16.0, bottom: 120.h),
            itemCount: testimonialsList.isEmpty ? 1 : testimonialsList.length,
            itemBuilder: (context, index) {
              if (testimonialsList.isEmpty) {
                return NoDataWidget(message: AppStrings.T.lbl_no_data);
              }
              final testimonial = testimonialsList[index];
              return CustomListItemTile(
                imageUrl: testimonial.imageUrl ?? '',
                title: testimonial.name,
                id: testimonial.id.toString(),
                vcardId: vcardId.toString(),
                onDelete: () {
                  Get.dialog(
                    LoadingConfirmationDialog(
                      title: AppStrings.T.lbl_delete_testimonial,
                      message: AppStrings.T.lbl_delete_testimonial_subtitle,
                      onCancel: () => NavigationService.navigateBack(),
                      onConfirm: () {
                        controller.deleteTestimonialsById(
                          id: testimonial.id,
                          vcardId: vcardId.toString(),
                        );
                      },
                      isLoading: controller.isLoadingDeleteTestimonialsById,
                    ),
                  );
                },
                onEdit: () {
                  NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createTestimonialsPage,
                    arguments: {
                      'vcardId': vcardId.toString(),
                      'testimonialsId': testimonial.id,
                    },
                  );
                },
                onTap: () {
                  controller.getTestimonialsById(id: testimonial.id);
                  _openServiceDetailBottomSheet(testimonial.id);
                },
                isLoadingDelete:
                    controller.isLoadingDeleteTestimonialsById.value,
              );
            },
          ),
        );
      }),
    );
  }

  Future<void> _openServiceDetailBottomSheet(int id) async {
    Get.bottomSheet(
      Obx(() {
        final detail = controller.testimonialsDataById.value;
        final isLoading = controller.isLoadingTestimonialsById.value;

        if (isLoading) return _buildBottomSheet(child: ShimmerServiceCard());
        if (detail == null) return _noDetailBottomSheet();

        final service = detail.data;

        return _buildBottomSheetContent(service);
      }),
      isScrollControlled: true,
      backgroundColor: Get.theme.customColors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(40.0.r)),
      ),
    );
  }

  Widget _buildBottomSheet({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      width: double.maxFinite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
            height: 5.0,
            width: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.r),
              color: Get.theme.customColors.primaryColor?.withValues(
                alpha: 0.6,
              ),
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              GestureDetector(
                onTap: () {
                  NavigationService.navigateBack();
                },
                child: Container(
                  height: 30.0,
                  width: 30.0,
                  margin: EdgeInsets.symmetric(horizontal: 20.0.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Get.theme.customColors.darkBlueColor?.withValues(
                      alpha: 0.05,
                    ),
                  ),
                  alignment: Alignment.center,
                  child: Icon(
                    Icons.close_rounded,
                    color: Get.theme.customColors.blackColor,
                    size: 20.0,
                  ),
                ),
              ),
            ],
          ),
          Gap(10.0.h),
          child,
        ],
      ),
    );
  }

  Widget _noDetailBottomSheet() => _buildBottomSheet(
    child: Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 30.0, bottom: 50.0),
        child: Text(AppStrings.T.lbl_no_details_available),
      ),
    ),
  );

  Widget _buildBottomSheetContent(Testimonials testimonials) =>
      _buildBottomSheet(
        child: Stack(
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(maxHeight: Get.height * 0.6),
              child: SingleChildScrollView(
                physics: BouncingScrollPhysics(),
                padding: EdgeInsets.only(
                  top: 20.0.h,
                  bottom: 110.0.h,
                  left: 20.0.w,
                  right: 20.0.w,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 200.0.h,
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10.0),
                        boxShadow: [
                          BoxShadow(
                            color: Get.theme.customColors.black!.withValues(
                              alpha: 0.15,
                            ),
                            blurRadius: 6.0,
                          ),
                        ],
                      ),
                      child: CustomImageView(
                        imagePath: testimonials.imageUrl.toString(),
                        fit: BoxFit.cover,
                        radius: BorderRadius.circular(10.r),
                      ),
                    ),
                    Gap(25.0.h),
                    Container(
                      clipBehavior: Clip.hardEdge,
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                        color: Get.theme.customColors.white,
                        borderRadius: BorderRadius.circular(10.0),
                        boxShadow: [
                          BoxShadow(
                            color: Get.theme.customColors.shadowColor!
                                .withValues(alpha: 0.1),
                            blurRadius: 16.0,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: 24.0.h,
                              horizontal: 18.0.w,
                            ),
                            child: Text(
                              testimonials.name.toString(),
                              style: Get.textTheme.bodyLarge?.copyWith(
                                fontSize: 16.0.sp,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Container(
                            width: double.maxFinite,
                            padding: EdgeInsets.all(18.0.w),
                            decoration: BoxDecoration(
                              color: Get.theme.customColors.white,
                              boxShadow: [
                                BoxShadow(
                                  color: Get.theme.customColors.shadowColor!
                                      .withValues(alpha: 0.1),
                                  blurRadius: 16.0,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${AppStrings.T.lbl_description}:",
                                  style: Get.textTheme.bodyLarge?.copyWith(
                                    fontSize: 14.0.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Gap(5.0),
                                Text(
                                  testimonials.description ?? '',
                                  style: Get.textTheme.bodyLarge?.copyWith(
                                    fontSize: 14.0.sp,
                                    fontWeight: FontWeight.w500,
                                    color: Get.theme.customColors.greyTextColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              bottom: 20.0,
              right: 18.0,
              child: GestureDetector(
                onTap: () {
                  Get.dialog(
                    LoadingConfirmationDialog(
                      title: AppStrings.T.lbl_delete_testimonial,
                      message: AppStrings.T.lbl_delete_testimonial_subtitle,
                      onCancel: () => NavigationService.navigateBack(),
                      onConfirm: () {
                        controller.deleteTestimonialsById(
                          id: testimonials.id,
                          vcardId: vcardId.toString(),
                        );
                      },
                      isLoading: controller.isLoadingDeleteTestimonialsById,
                    ),
                  );
                },
                child: Container(
                  height: 65.0.h,
                  width: 65.0.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Get.theme.customColors.primaryColor,
                    boxShadow: [
                      BoxShadow(
                        color: Get.theme.customColors.black!.withValues(
                          alpha: 0.4,
                        ),
                        blurRadius: 8.0,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  alignment: Alignment.center,
                  child: CustomImageView(
                    imagePath: AssetConstants.icDelete2,
                    color: Get.theme.customColors.white,
                    height: 25.0,
                  ),
                ),
              ),
            ),
          ],
        ),
      );

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getTestimonialsList(vcardId.toString());
  }
}
