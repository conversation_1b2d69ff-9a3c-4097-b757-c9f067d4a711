import 'package:get/get.dart';
import 'package:v_card/app/controllers/custom_link_controller.dart';
import 'package:v_card/app/data/model/custom_link/custom_link_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/custom_link/widgets.dart/custom_link_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/helpers/generated/assets.gen.dart';

class CustomLinkPage extends GetItHook<CustomLinkController> {
  CustomLinkPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_custom_links,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(100.0),
        ),
        backgroundColor: Get.theme.customColors.primaryColor,
        child: Icon(Icons.add, color: Colors.white),
        onPressed: () {
          controller.resetForm();
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createCustomLinkPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        },
      ),
      body: Obx(() {
        if (controller.isLoadingCustomLinks.value) {
          return CustomLinkShimmer();
        }

        final customLinks = controller.customLinkData.value?.data ?? [];

        return RefreshIndicator(
          onRefresh: () => controller.getCustomLinksList(vcardId),
          child: ListView.builder(
            padding: EdgeInsets.only(
              left: 16.0,
              right: 16.0,
              bottom: 120.h,
              top: 10.h,
            ),
            itemCount: customLinks.isEmpty ? 1 : customLinks.length,
            itemBuilder: (context, index) {
              if (customLinks.isEmpty) {
                return NoDataWidget(message: AppStrings.T.lbl_no_data);
              }
              final customLink = customLinks[index];
              return Container(
                margin: EdgeInsets.only(bottom: 16.h),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12.r),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withValues(alpha: 0.2),
                      spreadRadius: 1,
                      blurRadius: 10,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Padding(
                      padding: EdgeInsets.all(16.r),
                      child: Row(
                        children: [
                          Container(
                            width: 40.w,
                            height: 40.h,
                            decoration: BoxDecoration(
                              color: Get.theme.customColors.primaryColor!
                                  .withValues(alpha: 0.1),
                              // borderRadius: BorderRadius.circular(8.r),
                              shape: BoxShape.circle,
                            ),
                            alignment: Alignment.center,
                            child: CustomImageView(
                              imagePath:
                                  Assets.images.icon.vCard.icSvgLink.path,
                              height: 24.0,
                              width: 20.0,
                              color: Get.theme.customColors.primaryColor,
                            ),
                          ),
                          Gap(12.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  customLink.linkName,
                                  style: Get.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Gap(4.h),
                                Text(
                                  customLink.link,
                                  style: Get.textTheme.bodySmall?.copyWith(
                                    color: Get.theme.customColors.primaryColor,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          Gap(6.w),
                          InkWell(
                            onTap: () {
                              NavigationService.navigateWithSlideAnimation(
                                AppRoutes.createCustomLinkPage,
                                arguments: {
                                  'vcardId': vcardId,
                                  'customLinkId': customLink.id.toString(),
                                },
                              );
                            },
                            child: Container(
                              width: 32.w,
                              height: 32.h,
                              decoration: BoxDecoration(
                                color: Get.theme.customColors.darkBlueColor
                                    ?.withValues(alpha: 0.05),
                                shape: BoxShape.circle,
                              ),
                              child: CustomImageView(
                                imagePath: AssetConstants.icEdit,
                                margin: const EdgeInsets.all(8.0),
                              ),
                            ),
                          ),
                          Gap(6.w),
                          InkWell(
                            onTap: () {
                              Get.dialog(
                                LoadingConfirmationDialog(
                                  title: AppStrings.T.btn_delete_custom_link,
                                  message:
                                      AppStrings
                                          .T
                                          .msg_confirm_delete_custom_link,
                                  onCancel:
                                      () => NavigationService.navigateBack(),
                                  onConfirm: () {
                                    controller.deleteCustomLink(
                                      vcardId: vcardId,
                                      linkId: customLink.id.toString(),
                                    );
                                  },
                                  isLoading: controller.isDeletingCustomLink,
                                ),
                              );
                            },
                            child: Container(
                              width: 32.w,
                              height: 32.h,
                              decoration: BoxDecoration(
                                color: Get.theme.customColors.darkBlueColor
                                    ?.withValues(alpha: 0.05),
                                shape: BoxShape.circle,
                              ),
                              child: CustomImageView(
                                imagePath: AssetConstants.icDelete2,
                                margin: const EdgeInsets.all(8.0),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Divider(height: 1),

                    // Details Section
                    Padding(
                      padding: EdgeInsets.all(16.r),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildPaymentMethodToggle(
                            title: AppStrings.T.lbl_open_in_new_tab,
                            isEnabled: customLink.openNewTab == 1,
                            onChanged: (value) async {
                              await controller.updateOpenNewTab(
                                linkId: customLink.id.toString(),
                                vcardId: vcardId,
                                value: value,
                              );
                            },
                            customLink: customLink,
                          ),
                          Gap(8.h),
                          _buildPaymentMethodToggle(
                            title: AppStrings.T.lbl_display_as_button,
                            isEnabled: customLink.showAsButton == 1,
                            onChanged: (value) async {
                              await controller.updateShowAsButton(
                                linkId: customLink.id.toString(),
                                vcardId: vcardId,
                                value: value,
                              );
                            },
                            customLink: customLink,
                          ),

                          if (customLink.showAsButton == 1) ...[
                            _buildDetailRow(
                              AppStrings.T.lbl_button_color,
                              customLink.buttonColor,
                              color: Color(
                                int.parse(
                                  customLink.buttonColor.replaceAll(
                                    '#',
                                    '0xFF',
                                  ),
                                ),
                              ),
                            ),
                            _buildDetailRow(
                              AppStrings.T.lbl_button_type,
                              customLink.buttonType.capitalize ??
                                  customLink.buttonType,
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      }),
    );
  }

  Widget _buildPaymentMethodToggle({
    required String title,
    required bool isEnabled,
    required Function(bool) onChanged,
    required CustomLink customLink,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: Get.theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Obx(() {
          final isLoading =
              title == AppStrings.T.lbl_open_in_new_tab
                  ? controller.updatingOpenNewTabMap.containsKey(
                    customLink.id.toString(),
                  )
                  : controller.updatingShowAsButtonMap.containsKey(
                    customLink.id.toString(),
                  );
          return SizedBox(
            width: 59.w * 0.8,
            height: 34.h * 0.8,
            child:
                isLoading
                    ? Transform.scale(
                      scale: 0.8,
                      alignment: Alignment.centerRight,
                      child: ShimmerBox(
                        width: 59.w * 0.8,
                        height: 34.h * 0.8,
                        borderRadius: BorderRadius.circular(100),
                      ),
                    )
                    : Transform.scale(
                      scale: 0.8,
                      alignment: Alignment.centerRight,
                      child: Switch(
                        value: isEnabled,
                        activeColor: Get.theme.customColors.white,
                        inactiveThumbColor: Get.theme.customColors.darkColor,
                        inactiveTrackColor: Get.theme.customColors.white,
                        activeTrackColor: Get.theme.customColors.primaryColor,
                        onChanged: onChanged,
                      ),
                    ),
          );
        }),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? color}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Gap(8.w),
          Text(
            ':',
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Gap(8.w),
          if (color != null)
            Expanded(
              child: Row(
                children: [
                  Container(
                    width: 16.w,
                    height: 16.h,
                    decoration: BoxDecoration(
                      color: color,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                  ),
                  Gap(8.w),
                  Text(
                    value,
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )
          else
            Expanded(
              child: Text(
                value,
                style: Get.theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getCustomLinksList(vcardId);
  }
}
