import 'package:get/get.dart';
import 'package:v_card/app/controllers/custom_link_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/insta_embed/widgets.dart/create_insta_embed_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateCustomLinkPage extends GetItHook<CustomLinkController> {
  CreateCustomLinkPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';
  final customLinkId = Get.arguments['customLinkId'];
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          customLinkId != null
              ? AppStrings.T.btn_edit_custom_link
              : AppStrings.T.btn_add_custom_link,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.updateBannerPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: IgnorePointer(
        ignoring: controller.isSubmittingCustomLink.value,
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 16.h),
          child: Obx(() {
            if (controller.isLoadingCustomLinkById.value) {
              return Center(child: CreateInstaEmbedShimmer());
            }

            return Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gap(24.h),
                  _buildLinkNameField(),
                  Gap(16.h),
                  _buildLinkUrlField(),
                  Gap(16.h),
                  _buildButtonOptions(),
                  Gap(24.h),
                  _buildSaveButton(),
                  Gap(24.h),
                ],
              ),
            );
          }),
        ),
      ),
    );
  }

  Widget _buildLinkNameField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextInputField(
          isRequiredField: true,
          controller: controller.linkNameController,
          textInputAction: TextInputAction.next,
          isCapitalized: true,
          label: AppStrings.T.msg_enter_link_name,
          keyboardType: TextInputType.text,
          type: InputType.text,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return AppStrings.T.msg_link_name_required;
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLinkUrlField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextInputField(
          controller: controller.linkUrlController,
          textInputAction: TextInputAction.done,
          label: AppStrings.T.msg_enter_link_url,
          keyboardType: TextInputType.url,
          type: InputType.text,
          validator: (value) => AppValidations.urlWithEmptyValidation(value),
          isRequiredField: true,
        ),
      ],
    );
  }

  Widget _buildButtonOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Open in new tab toggle
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppStrings.T.lbl_open_in_new_tab,
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Transform.scale(
              scale: 0.8,
              alignment: Alignment.centerRight,
              child: Switch(
                value: controller.openInNewTab.value,
                activeColor: Get.theme.customColors.white,
                inactiveThumbColor: Get.theme.customColors.darkColor,
                inactiveTrackColor: Get.theme.customColors.white,
                activeTrackColor: Get.theme.customColors.primaryColor,
                onChanged: controller.toggleOpenInNewTab,
              ),
            ),
          ],
        ),

        // Show as button toggle
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              AppStrings.T.lbl_show_as_button,
              style: Get.theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Transform.scale(
              scale: 0.8,
              alignment: Alignment.centerRight,
              child: Switch(
                value: controller.showAsButton.value,
                activeColor: Get.theme.customColors.white,
                inactiveThumbColor: Get.theme.customColors.darkColor,
                inactiveTrackColor: Get.theme.customColors.white,
                activeTrackColor: Get.theme.customColors.primaryColor,
                onChanged: controller.toggleShowAsButton,
              ),
            ),
          ],
        ),

        // Show button settings if showAsButton is true
        Obx(() {
          if (!controller.showAsButton.value) {
            return SizedBox();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildButtonTypeSelector(),
              Gap(20.h),
              AppText(
                AppStrings.T.lbl_button_color,
                style: Get.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Gap(4.h),
              _buildColorPicker(
                AppStrings.T.lbl_qr_code_color,
                controller.buttonColor.value,
                (color) => controller.updateQRCodeColor(color),
              ),
            ],
          );
        }),
      ],
    );
  }

  Widget _buildButtonTypeSelector() {
    return Obx(
      () => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label (optional: add a label if needed)
          Padding(
            padding: EdgeInsets.only(bottom: 4.h),
            child: Text(
              AppStrings.T.lbl_button_type,
              style: Get.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // Main Selection Container
          GestureDetector(
            onTap: () {
              controller.isButtonTypeExpanded.value =
                  !controller.isButtonTypeExpanded.value;
            },
            child: Container(
              height: 55.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
              decoration: BoxDecoration(
                border: Border.all(color: Get.theme.customColors.primaryColor!),
                borderRadius:
                    !controller.isButtonTypeExpanded.value
                        ? BorderRadius.circular(8.r)
                        : BorderRadius.only(
                          topLeft: Radius.circular(8.r),
                          topRight: Radius.circular(8.r),
                        ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AppText(
                    _getButtonTypeLabel(controller.buttonType.value),
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  CustomImageView(
                    imagePath:
                        controller.isButtonTypeExpanded.value
                            ? AssetConstants.icUpArrow
                            : AssetConstants.icDownArrow2,
                    color: Get.theme.customColors.darkGreyTextColor,
                  ),
                ],
              ),
            ),
          ),

          // Dropdown Options
          if (controller.isButtonTypeExpanded.value)
            Container(
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(color: Get.theme.customColors.primaryColor!),
                  right: BorderSide(
                    color: Get.theme.customColors.primaryColor!,
                  ),
                  bottom: BorderSide(
                    color: Get.theme.customColors.primaryColor!,
                  ),
                ),
                borderRadius: BorderRadius.only(
                  bottomLeft: Radius.circular(8.r),
                  bottomRight: Radius.circular(8.r),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: Column(
                  children: [
                    _buildButtonTypeOption(AppStrings.T.lbl_square),
                    _buildButtonTypeOption(AppStrings.T.lbl_rounded),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildButtonTypeOption(String value) {
    return GestureDetector(
      onTap: () {
        controller.setButtonType(value);
        controller.isButtonTypeExpanded.value = false;
      },
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
        decoration: BoxDecoration(
          color:
              controller.buttonType.value == value
                  ? Colors.grey[200]
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: AppText(
          _getButtonTypeLabel(value),
          style: Get.theme.textTheme.bodySmall,
        ),
      ),
    );
  }

  String _getButtonTypeLabel(String value) {
    if (value == AppStrings.T.lbl_rounded) {
      return AppStrings.T.lbl_button_type_rounded;
    }
    return AppStrings.T.lbl_button_type_square;
  }

  Widget _buildSaveButton() {
    return Obx(
      () => CustomElevatedButton(
        checkConnectivity: true,
        text: AppStrings.T.lbl_save,
        isLoading: controller.isSubmittingCustomLink.value,
        onPressed: () async {
          if (controller.isSubmittingCustomLink.value) {
            return;
          }

          if (!_formKey.currentState!.validate()) {
            return;
          }

          if (customLinkId == null) {
            await controller.createCustomLink(vcardId);
          } else {
            await controller.updateCustomLink(
              linkId: customLinkId,
              vcardId: vcardId,
            );
          }
        },
      ),
    );
  }

  Widget _buildColorPicker(
    String title,
    String currentColor,
    Function(Color) onColorChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          title,
          style: Get.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Row(
          children: [
            InkWell(
              onTap: () {
                _showColorPicker(
                  initialColor: controller.hexToColor(currentColor),
                  onColorChanged: onColorChanged,
                );
              },
              child: Container(
                width: 50.h,
                height: 50.h,
                decoration: BoxDecoration(
                  color: controller.hexToColor(currentColor),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: Colors.grey),
                ),
              ),
            ),
            Gap(16.w),
            AppText(currentColor, style: Get.textTheme.bodyMedium),
          ],
        ),
      ],
    );
  }

  void _showColorPicker({
    required Color initialColor,
    required Function(Color) onColorChanged,
  }) {
    Get.dialog(
      AlertDialog(
        title: Text(AppStrings.T.lbl_select_color),
        content: SingleChildScrollView(
          child: ColorPicker(
            pickerColor: initialColor,
            onColorChanged: onColorChanged,
            pickerAreaHeightPercent: 0.8,
            displayThumbColor: true,
            paletteType: PaletteType.hsv,
            pickerAreaBorderRadius: BorderRadius.circular(8.r),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => NavigationService.navigateBack(),
            child: Text(AppStrings.T.lbl_done),
          ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.resetForm();
  }

  @override
  void onInit() {
    if (customLinkId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getCustomLinkById(customLinkId.toString());
        final service = controller.customLinkDataById.value?.data;
        if (service != null) {
          controller.linkNameController.text = service.linkName;
          controller.linkUrlController.text = service.link;
          controller.showAsButton.value =
              service.showAsButton == 1 ? true : false;
          controller.openInNewTab.value =
              service.openNewTab == 1 ? true : false;
          controller.buttonColor.value = service.buttonColor;
          controller.buttonType.value = service.buttonType;
        }
      });
    }
  }
}
