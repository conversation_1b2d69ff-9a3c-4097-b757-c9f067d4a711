import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomLinkShimmer extends StatelessWidget {
  const CustomLinkShimmer({super.key});

  Widget _shimmerBox({
    double height = 20,
    double width = double.infinity,
    BorderRadius? borderRadius,
  }) {
    return ShimmerBox(width: width, height: height);
  }

  Widget _shimmerCircle({double size = 32}) {
    return ShimmerBox(
      width: size,
      height: size,
      borderRadius: BorderRadius.circular(100),
    );
  }

  Widget _shimmerCard() {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.r),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Header row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _shimmerBox(
                height: 40.h,
                width: 40.w,
                borderRadius: BorderRadius.circular(8.r),
              ),
              Gap(12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _shimmerBox(height: 16.h, width: double.infinity),
                    Gap(6.h),
                    _shimmerBox(height: 14.h, width: 150.w),
                  ],
                ),
              ),
              Gap(6.w),
              _shimmerCircle(),
              Gap(6.w),
              _shimmerCircle(),
            ],
          ),
          Divider(height: 24.h),
          // Toggle rows
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _shimmerBox(height: 20.h, width: 120.w),
              _shimmerBox(
                width: 59.w * 0.8,
                height: 34.h * 0.8,
                borderRadius: BorderRadius.circular(100.r),
              ),
            ],
          ),
          Gap(12.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _shimmerBox(height: 20.h, width: 120.w),
              _shimmerBox(
                width: 59.w * 0.8,
                height: 34.h * 0.8,
                borderRadius: BorderRadius.circular(100.r),
              ),
            ],
          ),
          Gap(12.h),
          // Conditional: Button Color and Type
          _shimmerBox(height: 20.h, width: double.infinity),
          Gap(8.h),
          _shimmerBox(height: 20.h, width: double.infinity),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16.h, vertical: 10.h),
      itemCount: 3,
      itemBuilder: (context, index) {
        return _shimmerCard();
      },
    );
  }
}
