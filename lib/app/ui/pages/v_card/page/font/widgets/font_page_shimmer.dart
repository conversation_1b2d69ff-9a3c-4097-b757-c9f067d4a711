import 'package:v_card/app/utils/helpers/exporter.dart';

class FontPageShimmer extends StatelessWidget {
  const FontPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Font Family Field Shimmer
        _buildFieldShimmer(hasDropdown: true),
        Gap(20.h),

        // Font Size Field Shimmer
        _buildFieldShimmer(),

        // Additional space
        Expanded(child: Gap(0)),

        // Buttons at the bottom
        _buildButtonShimmer(),
      ],
    );
  }

  Widget _buildFieldShimmer({bool hasDropdown = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label shimmer
        ShimmerBox(width: 120.w, height: 16.h),
        Gap(8.h),
        // Field shimmer
        Container(
          height: 50.h,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey),
          ),
          child:
              hasDropdown
                  ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        ShimmerBox(width: 120.w, height: 14.w),
                        ShimmerBox(width: 32.w, height: 32.w),
                      ],
                    ),
                  )
                  : null,
        ),
      ],
    );
  }

  Widget _buildButtonShimmer() {
    return ShimmerBox(height: 50.h, width: double.infinity);
  }
}
