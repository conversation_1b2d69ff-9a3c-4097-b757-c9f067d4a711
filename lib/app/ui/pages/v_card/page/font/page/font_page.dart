import 'package:get/get.dart';
import 'package:v_card/app/controllers/font_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/font/widgets/font_page_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/themes/text_field_theme.dart';

class FontPage extends GetItHook<FontController> {
  FontPage({super.key});
  
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    var vcardId = Get.arguments['vcardId'] ?? 0;

    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_fonts,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.updateSeoPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0.sp),
        child: Obx(() {
          if (controller.isLoadingGetFont.value ||
              controller.fontList.value == null) {
            return const FontPageShimmer();
          }

          return IgnorePointer(
            ignoring: controller.isLoadingSaveFont.value,
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () {
                      showModalBottomSheet(
                        context: context,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(20.r),
                          ),
                        ),
                        builder: (context) {
                          final fontMap =
                              controller.fontList.value?.fonts ?? {};
                          return ListView.separated(
                            shrinkWrap: true,
                            padding: const EdgeInsets.all(16),
                            itemCount: fontMap.length,
                            separatorBuilder: (_, __) => Divider(),
                            itemBuilder: (context, index) {
                              final entry = fontMap.entries.elementAt(index);
                              return ListTile(
                                title: Text(
                                  entry.key,
                                  style: Get.theme.textTheme.bodyLarge!,
                                ),
                                onTap: () {
                                  controller.selectedFontFamily.text =
                                      entry.key;
                                  Navigator.pop(context);
                                },
                              );
                            },
                          );
                        },
                      );
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        vertical: 5.0.h,
                        horizontal: 5.0.w,
                      ),
                      decoration: BoxDecoration(
                        color: Get.theme.customColors.white,
                        borderRadius: BorderRadius.circular(10.0.r),
                        boxShadow: [
                          BoxShadow(
                            color: Get.theme.customColors.shadowColor!
                                .withValues(alpha: 0.1),
                            blurRadius: 6.0,
                            offset: Offset(0, 3),
                          ),
                        ],
                      ),
                      child: AbsorbPointer(
                        child: TextInputField(
                          type: InputType.text,
                          decoration: TextFieldThemeHelper.borderlessWithLabel(
                            labelText: AppStrings.T.lbl_font_family,
                            suffixIcon: CustomImageView(
                              imagePath: AssetConstants.icDownArrow2,
                              margin: EdgeInsets.all(10.0),
                            ),
                          ),
                          controller: controller.selectedFontFamily,
                          label: AppStrings.T.lbl_font_family,
                        ),
                      ),
                    ),
                  ),
                  Gap(20.h),
                  Container(
                    padding: EdgeInsets.symmetric(
                      vertical: 5.0.h,
                      horizontal: 5.0.w,
                    ),
                    decoration: BoxDecoration(
                      color: Get.theme.customColors.white,
                      borderRadius: BorderRadius.circular(10.0.r),
                      boxShadow: [
                        BoxShadow(
                          color: Get.theme.customColors.shadowColor!.withValues(
                            alpha: 0.1,
                          ),
                          blurRadius: 6.0,
                          offset: Offset(0, 3),
                        ),
                      ],
                    ),
                    child: TextInputField(
                      decoration: TextFieldThemeHelper.borderlessWithLabel(
                        labelText: AppStrings.T.lbl_font_size_in_px,
                      ),
                      type: InputType.phoneNumber,
                      controller: controller.selectedFontSize,
                      keyboardType: TextInputType.number,
                      label: AppStrings.T.lbl_font_size_in_px,
                      onChanged: (value) {
                        controller.selectedFontSize.text = value;
                      },
                      validator: (value) {
                        final fontSize = int.tryParse(value ?? '') ?? 0;
                        if (fontSize > 40) {
                          return AppStrings.T.lbl_value_must_less;
                        }
                        return null;
                      },
                    ),
                  ),
                  // Gap(30.h),
                  Spacer(),
                  _buildButtons(vcardId),
                ],
              ),
            ),
          );
        }),
      ),
      // bottomNavigationBar: _buildButtons(vcardId),
    );
  }

  Widget _buildButtons(vcardId) {
    return Obx(
      () => CustomElevatedButton(
        checkConnectivity: true,
        text: AppStrings.T.lbl_save,
        isLoading: controller.isLoadingSaveFont.value,
        onPressed: () {
          if (controller.isLoadingSaveFont.value) {
            return;
          }
          if (_formKey.currentState!.validate()) {
            controller.updateFont(vcardId);
          }
        },
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.selectedFontFamily.clear();
    controller.selectedFontSize.clear();
  }

  @override
  void onInit() {
    var vcardId = Get.arguments['vcardId'] ?? 0;
    controller.getFontList();
    controller.getFontById(vcardId.toString());
  }
}
