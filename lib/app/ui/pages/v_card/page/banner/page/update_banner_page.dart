import 'package:get/get.dart';
import 'package:v_card/app/controllers/banner_controller.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class UpdateBannerPage extends GetItHook<BannerController> {
  UpdateBannerPage({super.key});

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final vcardId = Get.arguments['vcardId'];
  final bannerId = Get.arguments['bannerId'];
  final String? initialTitle = Get.arguments['title'];
  final String? initialDescription = Get.arguments['description'];
  final String? initialUrl = Get.arguments['url'];
  final String? initialBannerButton = Get.arguments['bannerButton'];
  final String? initialBannerImage = Get.arguments['bannerImage'];

  @override
  void onInit() {
    // Initialize form fields with existing values if editing
    if (bannerId != null) {
      controller.titleController.text = initialTitle ?? '';
      controller.descriptionController.text = initialDescription ?? '';
      controller.urlController.text = initialUrl ?? '';
      controller.bannerButtonController.text = initialBannerButton ?? '';
      // Note: For image, you'll need to handle it separately
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          bannerId == null
              ? AppStrings.T.lbl_create_banner
              : AppStrings.T.lbl_edit_banner,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.advancedSettingPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const CustomFormShimmerLoading();
        }

        return IgnorePointer(
          ignoring: controller.isLoadingUpdateBanner.value,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    TextInputField(
                      isRequiredField: true,
                      type: InputType.name,
                      isCapitalized: true,
                      controller: controller.titleController,
                      label: AppStrings.T.lbl_banner_title,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_banner_title,
                          ),
                    ),
                    Gap(12.h),

                    TextInputField(
                      isCapitalized: true,
                      isRequiredField: true,
                      type: InputType.multiline,
                      textInputAction: TextInputAction.newline,
                      maxLines: 3,
                      controller: controller.descriptionController,
                      label: AppStrings.T.lbl_banner_description,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_banner_description,
                          ),
                    ),
                    Gap(12.h),
                    TextInputField(
                      isRequiredField: true,
                      type: InputType.text,
                      controller: controller.urlController,
                      label: AppStrings.T.lbl_banner_url,
                      validator:
                          (value) => AppValidations.urlPathValidation(value),
                    ),
                    Gap(12.h),

                    TextInputField(
                      isRequiredField: true,
                      type: InputType.text,
                      textInputAction: TextInputAction.done,
                      controller: controller.bannerButtonController,
                      label: AppStrings.T.lbl_banner_button_text,
                      validator:
                          (value) => AppValidations.validateRequired(
                            value,
                            fieldName: AppStrings.T.lbl_banner_button_text,
                          ),
                    ),
                    Gap(12.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          AppStrings.T.lbl_banner,
                          style: Get.theme.textTheme.bodyMedium?.copyWith(
                            color: Get.theme.customColors.darkGreyTextColor,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Transform.scale(
                          scale: 0.8,
                          alignment: Alignment.centerRight,
                          child: Obx(
                            () => Switch(
                              value: controller.isBannerActive.value,
                              activeColor: Get.theme.customColors.white,
                              inactiveThumbColor:
                                  Get.theme.customColors.darkColor,
                              inactiveTrackColor: Get.theme.customColors.white,
                              activeTrackColor:
                                  Get.theme.customColors.primaryColor,
                              onChanged: (value) {
                                controller.toggleBannerStatus(value);
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                    Gap(30.h),

                    _buildButtons(_formKey, vcardId, bannerId),
                  ],
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildButtons(
    GlobalKey<FormState> formKey,
    String vcardId,
    dynamic bannerId,
  ) {
    return Column(
      children: [
        Obx(
          () => CustomElevatedButton(
            checkConnectivity: true,
            isLoading: controller.isLoadingUpdateBanner.value,
            text: AppStrings.T.lbl_save,
            onPressed: () async {
              if (controller.isLoadingUpdateBanner.value) {
                return;
              }
              if (!_formKey.currentState!.validate()) {
                return;
              }

              await controller.updateBanner(
                vcardId: vcardId,
                title: controller.titleController.text,
                description: controller.descriptionController.text,
                url: controller.urlController.text,
                bannerButton: controller.bannerButtonController.text,
              );
            },
          ),
        ),
      ],
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.titleController.clear();
    controller.descriptionController.clear();
    controller.urlController.clear();
    controller.bannerButtonController.clear();
    controller.isBannerActive.value = false;
    controller.isLoadingUpdateBanner.value = false;
  }
}
