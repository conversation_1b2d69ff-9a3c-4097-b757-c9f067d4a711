import 'package:get/get.dart';
import 'package:v_card/app/controllers/banner_controller.dart';
import 'package:v_card/app/data/model/banner/banner_model.dart';
import 'package:v_card/app/ui/pages/v_card/page/banner/widget/banner_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class BannerPage extends GetItHook<BannerController> {
  BannerPage({super.key});
  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_banner,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Obx(() {
          if (controller.isLoading.value) {
            return BannerPageShimmer();
          }

          final bannerData = controller.bannerList.value?.data ?? [];

          if (bannerData.isEmpty) {
            return _buildEmptyBannerView();
          }

          final banner = bannerData.first;
          return _buildBannerDetailsView(banner);
        }),
      ),
    );
  }

  Widget _buildEmptyBannerView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported_outlined,
            size: 80,
            color: Get.theme.colorScheme.primary.withValues(alpha: 0.7),
          ),
          Gap(20.h),
          Text(
            AppStrings.T.lbl_no_banner_available,
            style: Get.theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Gap(12.h),
          Text(
            AppStrings.T.lbl_create_banner_instruction,
            textAlign: TextAlign.center,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              color: Get.theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          Gap(32.h),
          CustomElevatedButton(
            text: AppStrings.T.lbl_create_banner,
            // icon: Icons.add_photo_alternate_outlined,
            onPressed: () {
              NavigationService.navigateWithSlideAnimation(
                AppRoutes.updateBannerPage,
                arguments: {'vcardId': vcardId, 'isCreating': true},
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBannerDetailsView(BannerData banner) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gap(24.h),

          // Banner Details
          _buildInfoSection(
            title: AppStrings.T.lbl_banner_title,
            content: banner.title,
            icon: Icons.title,
          ),

          if (banner.description.isNotEmpty)
            _buildInfoSection(
              title: AppStrings.T.lbl_description,
              content: banner.description,
              icon: Icons.description_outlined,
            ),

          if (banner.url.isNotEmpty)
            _buildInfoSection(
              title: AppStrings.T.lbl_url,
              content: banner.url,
              icon: Icons.link,
            ),

          if (banner.bannerButton.isNotEmpty)
            _buildInfoSection(
              title: AppStrings.T.lbl_banner_button_text,
              content: banner.bannerButton,
              icon: Icons.smart_button,
            ),

          if (banner.bannerButton.isNotEmpty)
            _buildInfoSection(
              title: AppStrings.T.lbl_banner,
              content:
                  banner.banner == "1"
                      ? AppStrings.T.lbl_enabled
                      : AppStrings.T.lbl_disabled,
              icon: Icons.branding_watermark_outlined,
            ),

          Gap(32.h),

          // Update Button
          CustomElevatedButton(
            text: 'Update Banner',
            // icon: Icons.edit_outlined,
            onPressed: () {
              NavigationService.navigateWithSlideAnimation(
                AppRoutes.updateBannerPage,
                arguments: {
                  'vcardId': vcardId,
                  'bannerId': banner.vcardId,
                  'title': banner.title,
                  'description': banner.description,
                  'url': banner.url,
                  'bannerButton': banner.bannerButton,
                  'bannerImage': banner.banner,
                  'isCreating': false,
                },
              );
            },
          ),
          Gap(24.h),
        ],
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 18,
                color: Get.theme.customColors.darkGreyTextColor!,
              ),
              Gap(8.w),
              Text(
                title,
                style: Get.theme.textTheme.bodyLarge?.copyWith(
                  color: Get.theme.customColors.darkGreyTextColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          Gap(4.w),
          Padding(
            padding: EdgeInsets.only(left: 26.w),
            child: Text(
              content,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: Get.theme.textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Get.theme.customColors.greyTextColor,
              ),
            ),
          ),
          Gap(8.h),
          Divider(),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getBannerData(vcardId);
  }
}
