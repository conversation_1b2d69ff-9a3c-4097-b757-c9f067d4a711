import 'package:v_card/app/utils/helpers/exporter.dart';

class BannerPageShimmer extends StatelessWidget {
  const BannerPageShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
      child: Column(
        children: List.generate(5, (index) => _buildShimmerSection(theme)),
      ),
    );
  }

  Widget _buildShimmerSection(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and Title shimmer
          Row(
            children: [
              <PERSON><PERSON><PERSON><PERSON>(width: 18, height: 18),
              Gap(8.w),
              <PERSON><PERSON><PERSON><PERSON>(width: 100, height: 18),
            ],
          ),
          Gap(8.h),

          // Content shimmer
          Padding(
            padding: const EdgeInsets.only(left: 26),
            child: <PERSON><PERSON><PERSON><PERSON>(width: double.infinity, height: 14),
          ),
          Gap(8.h),
          Divider(),
        ],
      ),
    );
  }
}
