import 'package:get/get.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/ui/widgets/custom_tile.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/helpers/generated/assets.gen.dart';

class EditVcardPage extends GetItHook<VcardController> {
  EditVcardPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_edit_vcard,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              CustomTile(
                icon: AssetConstants.icBasicDetail,
                title: AppStrings.T.lbl_basic_details,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.vcardAddBasicDetailPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icVcardTemplate,
                title: AppStrings.T.lbl_template,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.vCardTemplatesPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icBusinessHour,
                title: AppStrings.T.lbl_business_hours,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.vcardBusinessHoursPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: Assets.images.icon.vCard.icQr.path,
                title: AppStrings.T.lbl_customize_qr_code,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.customizeQRCodePage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icService,
                title: AppStrings.T.lbl_service,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.servicePage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icProducts,
                title: AppStrings.T.lbl_products,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.productPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: Assets.images.icon.vCard.icInstaEmbed.path,
                title: AppStrings.T.lbl_insta_embed,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.instaEmbedPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icGallary,
                title: AppStrings.T.lbl_galleries,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.galleriesPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icBlog,
                title: AppStrings.T.lbl_blog,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.blogPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icMessage,
                title: AppStrings.T.lbl_testimonials,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.testimonialsPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: Assets.images.icon.vCard.icSvgCrop.path,
                title: AppStrings.T.lbl_iframe,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.iframePage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icAppointmentV,
                title: AppStrings.T.lbl_appointments,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.vcardAppointmentsPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icSocialLink,
                title: AppStrings.T.lbl_social_links,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.socialConnectPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: Assets.images.icon.vCard.icSvgLink.path,
                title: AppStrings.T.lbl_custom_links,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.customLinkPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: Assets.images.icon.vCard.icSvgBanner.path,
                title: AppStrings.T.lbl_banner,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.bannerPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icAdvancedSetting,
                title: AppStrings.T.lbl_advanced,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.advancedSettingPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icFont,
                title: AppStrings.T.lbl_fonts,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.fontPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icSearch,
                title: AppStrings.T.lbl_seo,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.seoPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icPrivacyPolicyV,
                title: AppStrings.T.lbl_privacy_policy,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.privacyPolicyPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: AssetConstants.icTermAndCondition,
                title: AppStrings.T.lbl_terms_and_conditions,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.termsAndConditionsPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
              CustomTile(
                icon: Assets.images.icon.vCard.icManageSection.path,
                title: AppStrings.T.lbl_manage_section,
                onTap:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.manageSectionPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    isCardCreated = true;
  }
}
