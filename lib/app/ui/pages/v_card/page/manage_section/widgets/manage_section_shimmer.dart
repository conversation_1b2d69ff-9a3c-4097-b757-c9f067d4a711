import 'package:v_card/app/utils/helpers/exporter.dart';

class ManageSectionShimmer extends StatelessWidget {
  const ManageSectionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children:
            List.generate(15, (index) => _buildShimmerItem())
              ..add(Gap(32.h))
              ..add(_buildShimmerButton()),
      ),
    );
  }

  Widget _buildShimmerItem() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ShimmerBox(
            width: 200.w,
            height: 20.h,
            borderRadius: BorderRadius.circular(100.r),
          ),
          ShimmerBox(
            width: 40.w,
            height: 20.h,
            borderRadius: BorderRadius.circular(20.r),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerButton() {
    return ShimmerBox(height: 48.h, width: double.infinity);
  }
}
