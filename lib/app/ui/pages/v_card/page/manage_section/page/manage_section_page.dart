// manage_section_page.dart
import 'package:get/get.dart';
import 'package:v_card/app/controllers/manage_section_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/manage_section/widgets/manage_section_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class ManageSectionPage extends GetItHook<ManageSectionController> {
  ManageSectionPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_manage_section,
          style: Get.theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed: () {
                NavigationService.navigateWithSlideAnimation(
                  AppRoutes.nevigationMenu,
                );
                // final navigationController = getIt<NavigationMenuController>();
                // navigationController.changePage(1);
              },
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return ManageSectionShimmer();
        }

        return IgnorePointer(
          ignoring: controller.isUpdateLoading.value,
          child: SingleChildScrollView(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Column(
              children: [
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_header_section,
                  value: controller.header.value,
                  onChanged: (val) => controller.header.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_contact_list,
                  value: controller.contactList.value,
                  onChanged: (val) => controller.contactList.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_services,
                  value: controller.services.value,
                  onChanged: (val) => controller.services.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_products,
                  value: controller.products.value,
                  onChanged: (val) => controller.products.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_language_enabled,
                  value: controller.instaEmbed.value,
                  onChanged: (val) => controller.instaEmbed.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_galleries,
                  value: controller.galleries.value,
                  onChanged: (val) => controller.galleries.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_blogs,
                  value: controller.blogs.value,
                  onChanged: (val) => controller.blogs.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_iframe,
                  value: controller.iframe.value,
                  onChanged: (val) => controller.iframe.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_map,
                  value: controller.map.value,
                  onChanged: (val) => controller.map.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_testimonials,
                  value: controller.testimonials.value,
                  onChanged: (val) => controller.testimonials.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_business_hours,
                  value: controller.businessHours.value,
                  onChanged: (val) => controller.businessHours.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_appointments,
                  value: controller.appointments.value,
                  onChanged: (val) => controller.appointments.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_one_signal_notification,
                  value: controller.oneSignalNotification.value,
                  onChanged:
                      (val) => controller.oneSignalNotification.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_banner,
                  value: controller.banner.value,
                  onChanged: (val) => controller.banner.value = val,
                ),
                _buildSectionSwitch(
                  title: AppStrings.T.lbl_news_letter_popup,
                  value: controller.newsLatterPopup.value,
                  onChanged: (val) => controller.newsLatterPopup.value = val,
                ),
                Gap(32.h),
                CustomElevatedButton(
                  checkConnectivity: true,
                  text: AppStrings.T.lbl_save_changes,
                  isLoading: controller.isUpdateLoading.value,
                  onPressed: () {
                    if (controller.isUpdateLoading.value) {
                      return;
                    }

                    controller.updateManageSection(vcardId.toString());
                  },
                ),
                Gap(20.h),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildSectionSwitch({
    required String title,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            title,
            style: Get.theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Transform.scale(
            scale: 0.8,
            alignment: Alignment.centerRight,
            child: Switch(
              value: value,
              activeColor: Get.theme.customColors.white,
              inactiveThumbColor: Get.theme.customColors.darkColor,
              inactiveTrackColor: Get.theme.customColors.white,
              activeTrackColor: Get.theme.customColors.primaryColor,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getManageSection(vcardId.toString());
  }
}
