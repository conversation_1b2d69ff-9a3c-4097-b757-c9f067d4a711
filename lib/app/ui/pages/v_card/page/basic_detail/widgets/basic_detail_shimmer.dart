import 'package:v_card/app/utils/helpers/exporter.dart';

class BasicDetailsShimmer extends StatelessWidget {
  const BasicDetailsShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile and Favicon images
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      ShimmerBox(width: 100, height: 16),
                      const Gap(10),
                      ShimmerBox(height: 140, width: double.infinity),
                    ],
                  ),
                ),
                const Gap(38),
                Expanded(
                  child: Column(
                    children: [
                      ShimmerBox(width: 100, height: 16),
                      const Gap(10),
                      ShimmerBox(height: 140, width: double.infinity),
                    ],
                  ),
                ),
              ],
            ),
            const Gap(20),

            // Cover Type
            ShimmerBox(width: 80, height: 16),
            const Gap(8),
            ShimmerBox(width: double.infinity, height: 48),
            const Gap(12),
            ShimmerBox(width: double.infinity, height: 120),
            const Gap(40),

            // Form fields
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(height: 80),
                const Gap(28),
                ShimmerBox(width: 160, height: 20),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(),
                const Gap(28),
                ShimmerBox(width: 160, height: 20),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(),
                const Gap(28),
                ShimmerBox(width: 160, height: 20),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(),
                const Gap(28),
                ShimmerBox(width: 160, height: 20),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildShimmerField(),
                const Gap(28),
                ShimmerBox(width: 160, height: 20),
                const Gap(12),
                _buildToggleShimmer(),
                const Gap(8),
                _buildToggleShimmer(),
                const Gap(28),
                ShimmerBox(width: 160, height: 20),
                const Gap(12),
                _buildShimmerField(),
                const Gap(12),
                _buildToggleShimmer(),
                const Gap(30),
                Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerField({double height = 56}) {
    return ShimmerBox(height: height, width: double.infinity);
  }

  Widget _buildToggleShimmer() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ShimmerBox(width: 200, height: 16),
        ShimmerBox(
          width: 48,
          height: 24,
          borderRadius: BorderRadius.circular(100),
        ),
      ],
    );
  }
}
