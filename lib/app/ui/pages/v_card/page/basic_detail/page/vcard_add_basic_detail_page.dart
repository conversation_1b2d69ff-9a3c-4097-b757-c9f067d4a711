// ignore_for_file: depend_on_referenced_packages

import 'dart:io';
import 'package:country_picker/country_picker.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/basic_detail/widgets/basic_detail_shimmer.dart';
import 'package:v_card/app/ui/pages/v_card/widgets/language_bottom_sheet.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class VcardAddBasicDetailPage extends GetItHook<VcardController> {
  VcardAddBasicDetailPage({super.key});
  final String vcardId = Get.arguments['vcardId'] ?? '0';
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  Future<File?> _pickCompressedImage({
    required String imageType,
    int? targetSize,
    int? maxKB,
    String format = 'png',
    required BuildContext context,
  }) async {
    try {
      final File? compressedImage = await PWAIconHandler.createPWAIcon(
        context: context,
        source: ImageSource.gallery,
        size: targetSize ?? 512,
        maxKB: maxKB ?? 100,
        format: format,
      );

      // if (compressedImage != null) {
      //   _showImageValidationToast('$imageType processed successfully!', false);
      // }

      return compressedImage;
    } catch (e) {
      print('Error selecting $imageType: $e');
      _showImageValidationToast(
        'Failed to process $imageType. Please try again.',
        true,
      );
      return null;
    }
  }

  void _showImageValidationToast(String message, bool isError) {
    controller.isShowingToast.value = true;
    toastification.show(
      type: isError ? ToastificationType.error : ToastificationType.info,
      style: ToastificationStyle.flatColored,
      showProgressBar: false,
      alignment: Alignment.topCenter,
      title: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
    );
    Future.delayed(const Duration(seconds: 3), () {
      controller.isShowingToast.value = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: isCardCreated ? true : false,
      onPopInvokedWithResult: (didPop, result) {
        if (!isCardCreated) {
          Get.offAllNamed(AppRoutes.nevigationMenu);
        }
      },
      child: Scaffold(
        appBar: CustomAppbar(
          title: AppText(
            AppStrings.T.lbl_basic_details,
            style: Get.theme.textTheme.bodyLarge?.copyWith(
              fontSize: 18.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          onLeadingTap: () {
            if (!isCardCreated) {
              Get.offAllNamed(AppRoutes.nevigationMenu);
            } else {
              NavigationService.navigateBack();
            }
          },
          actions: [
            if (!isCardCreated)
              IconButton(
                onPressed:
                    () => NavigationService.navigateWithSlideAnimation(
                      AppRoutes.vCardTemplatesPage,
                      arguments: {'vcardId': vcardId.toString()},
                    ),
                icon: Text(
                  AppStrings.T.lbl_skip,
                  style: Get.textTheme.bodySmall?.copyWith(
                    color: Get.theme.customColors.primaryColor,
                    fontSize: 14.0,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        body: Obx(() {
          if (controller.isLoadingBasicDetails.value) {
            return Center(child: BasicDetailsShimmer());
          }
          return IgnorePointer(
            ignoring: controller.isLoadingUpdateBasicdetail.value,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            AppText(
                              AppStrings.T.lbl_profile_image,
                              style: Get.theme.textTheme.bodyLarge!.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Gap(10.0),
                            GestureDetector(
                              onTap:
                                  controller.isProcessingPWAIcon.value
                                      ? null
                                      : () async {
                                        if (controller
                                            .isProcessingPWAIcon
                                            .value) {
                                          Logger.log(
                                            "Processing is already in progress.",
                                          );
                                          return;
                                        } else {
                                          controller.isProcessingPWAIcon.value =
                                              true;
                                          try {
                                            final File? compressedImage =
                                                await _pickCompressedImage(
                                                  context: context,
                                                  imageType: 'Profile image',
                                                  targetSize: 512,
                                                  maxKB: 150,
                                                  format: 'jpg',
                                                );
                                            if (compressedImage != null) {
                                              controller
                                                  .profileImageFile
                                                  .value = compressedImage;
                                            }
                                          } finally {
                                            controller
                                                .isProcessingPWAIcon
                                                .value = false;
                                          }
                                        }
                                      },

                              child: Container(
                                height: 140.h,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Get.theme.customColors.white,
                                  border: Border.all(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  borderRadius: BorderRadius.circular(16.r),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.3,
                                      ),
                                      blurRadius: 10,
                                      offset: const Offset(0, 0),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Obx(
                                    () => Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        if (controller
                                            .isProcessingPWAIcon
                                            .value)
                                          ShimmerBox(
                                            height: 140.h,
                                            width: double.infinity,
                                            borderRadius: BorderRadius.circular(
                                              12.r,
                                            ),
                                          )
                                        else
                                          CustomImageView(
                                            height: double.infinity,
                                            width: double.infinity,
                                            fit: BoxFit.cover,
                                            radius: BorderRadius.circular(11.r),
                                            imagePath:
                                                controller
                                                    .profileImageFile
                                                    .value
                                                    ?.path ??
                                                controller
                                                    .vCardBasicDetails
                                                    .value
                                                    ?.data
                                                    .profileUrl,
                                          ),

                                        if (!controller
                                            .isProcessingPWAIcon
                                            .value)
                                          Positioned(
                                            right: 6,
                                            bottom: 6,
                                            child: CircleAvatar(
                                              radius: 22.r,
                                              backgroundColor:
                                                  Get.theme.customColors.white,
                                              child: CustomImageView(
                                                imagePath:
                                                    AssetConstants.icCamera,
                                                color:
                                                    Get
                                                        .theme
                                                        .customColors
                                                        .primaryColor,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Gap(38.h),
                      Expanded(
                        child: Column(
                          children: [
                            AppText(
                              AppStrings.T.lbl_favicon_image,
                              style: Get.theme.textTheme.bodyLarge!.copyWith(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            GestureDetector(
                              onTap:
                                  controller.isProcessingFaviconPWAIcon.value
                                      ? null
                                      : () async {
                                        if (controller
                                            .isProcessingFaviconPWAIcon
                                            .value) {
                                          Logger.log(
                                            "Processing is already in progress.",
                                          );
                                          return;
                                        } else {
                                          controller
                                              .isProcessingFaviconPWAIcon
                                              .value = true;
                                          try {
                                            final File? compressedImage =
                                                await _pickCompressedImage(
                                                  context: context,
                                                  imageType: 'Favicon',
                                                  targetSize: 192,
                                                  maxKB: 50,
                                                  format: 'png',
                                                );
                                            if (compressedImage != null) {
                                              controller.faviconFile.value =
                                                  compressedImage;
                                            }
                                          } finally {
                                            controller
                                                .isProcessingFaviconPWAIcon
                                                .value = false;
                                          }
                                        }
                                      },

                              child: Container(
                                height: 140.h,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  color: Get.theme.customColors.white,
                                  border: Border.all(
                                    color: Get.theme.customColors.primaryColor!,
                                  ),
                                  borderRadius: BorderRadius.circular(16.r),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Colors.black.withValues(
                                        alpha: 0.3,
                                      ),
                                      blurRadius: 10,
                                      offset: const Offset(0, 0),
                                    ),
                                  ],
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Obx(() {
                                    return Stack(
                                      children: [
                                        if (controller
                                            .isProcessingFaviconPWAIcon
                                            .value)
                                          ShimmerBox(
                                            height: 140.h,
                                            width: double.infinity,
                                            borderRadius: BorderRadius.circular(
                                              12.r,
                                            ),
                                          )
                                        else
                                          CustomImageView(
                                            height: double.infinity,
                                            width: double.infinity,
                                            fit: BoxFit.cover,
                                            radius: BorderRadius.circular(11.r),
                                            imagePath:
                                                controller
                                                    .faviconFile
                                                    .value
                                                    ?.path ??
                                                controller
                                                    .vCardBasicDetails
                                                    .value
                                                    ?.data
                                                    .faviconUrl,
                                          ),

                                        if (!controller
                                            .isProcessingFaviconPWAIcon
                                            .value)
                                          Positioned(
                                            right: 6,
                                            bottom: 6,
                                            child: CircleAvatar(
                                              radius: 22.r,
                                              backgroundColor:
                                                  Get.theme.customColors.white,
                                              child: CustomImageView(
                                                imagePath:
                                                    AssetConstants.icCamera,
                                                color:
                                                    Get
                                                        .theme
                                                        .customColors
                                                        .primaryColor,
                                              ),
                                            ),
                                          ),
                                      ],
                                    );
                                  }),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Gap(20.h),

                  AppText(
                    AppStrings.T.lbl_cover_type,
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                  Gap(8.h),
                  Obx(
                    () => Column(
                      children: [
                        GestureDetector(
                          onTap: () {
                            controller.isExpanded.value =
                                !controller.isExpanded.value;
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16.w,
                              vertical: 12.h,
                            ),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: Get.theme.customColors.primaryColor!,
                              ),
                              borderRadius:
                                  !controller.isExpanded.value
                                      ? BorderRadius.circular(8.r)
                                      : BorderRadius.only(
                                        topLeft: Radius.circular(8.r),
                                        topRight: Radius.circular(8.r),
                                      ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                AppText(
                                  _getCoverTypeLabel(
                                    controller.selectedCoverType.value,
                                  ),
                                  style: Get.theme.textTheme.bodyMedium
                                      ?.copyWith(fontWeight: FontWeight.w500),
                                ),
                                CustomImageView(
                                  imagePath:
                                      controller.isExpanded.value
                                          ? AssetConstants.icUpArrow
                                          : AssetConstants.icDownArrow2,
                                  color:
                                      Get.theme.customColors.darkGreyTextColor,
                                ),
                              ],
                            ),
                          ),
                        ),

                        if (controller.isExpanded.value) ...[
                          Container(
                            decoration: BoxDecoration(
                              border: Border(
                                left: BorderSide(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                                right: BorderSide(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                                bottom: BorderSide(
                                  color: Get.theme.customColors.primaryColor!,
                                ),
                                top: BorderSide.none,
                              ),
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(8.r),
                                bottomRight: Radius.circular(8.r),
                              ),
                            ),
                            child: Column(
                              children: List.generate(3, (index) {
                                return GestureDetector(
                                  onTap: () {
                                    controller.selectedCoverType.value = index;
                                    controller.coverImageFile.value = null;
                                    controller.youtubeLinkController.clear();
                                    controller.isExpanded.value = false;
                                  },
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 16.w,
                                        vertical: 12.h,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            controller
                                                        .selectedCoverType
                                                        .value ==
                                                    index
                                                ? Colors.grey[200]
                                                : Colors.transparent,
                                        borderRadius: BorderRadius.circular(
                                          4.r,
                                        ),
                                      ),
                                      child: Row(
                                        children: [
                                          AppText(
                                            _getCoverTypeLabel(index),
                                            style:
                                                Get.theme.textTheme.bodySmall,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              }),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Gap(12.h),

                  Obx(() {
                    switch (controller.selectedCoverType.value) {
                      case 0: // Image
                        return _buildImageCoverSelector(context);
                      case 1: // Video
                        return _buildVideoCoverSelector();
                      case 2: // YouTube
                        return _buildYoutubeCoverField();
                      default:
                        return _buildImageCoverSelector(context);
                    }
                  }),
                  Gap(40.h),
                  Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextInputField(
                          controller:
                              controller.addBasicDetailUrlAliasController,
                          label: AppStrings.T.lbl_url_alias,
                          isRequiredField: true,
                          type: InputType.name,
                          readOnly: true,
                          validator:
                              (value) => AppValidations.validateRequired(
                                value,
                                fieldName: AppStrings.T.lbl_url_alias,
                              ),
                        ),

                        Gap(12.h),
                        TextInputField(
                          controller: controller.addBasicDetailNameController,
                          label: AppStrings.T.lbl_vcard_name,
                          isCapitalized: true,
                          isRequiredField: true,
                          type: InputType.name,
                          validator:
                              (value) => AppValidations.validateRequired(
                                value,
                                fieldName: AppStrings.T.lbl_vcard_name,
                              ),
                        ),
                        Gap(12.h),
                        TextInputField(
                          controller:
                              controller.addBasicDetailOccupationController,
                          label: AppStrings.T.lbl_occupation,
                          isCapitalized: true,
                          type: InputType.name,
                        ),
                        Gap(12.h),
                        TextInputField(
                          controller:
                              controller.addBasicDetailDescriptionController,
                          label: AppStrings.T.lbl_description,
                          isCapitalized: true,
                          type: InputType.multiline,
                          textInputAction: TextInputAction.newline,

                          maxLines: 4,
                          minLines: 1,
                        ),
                        Gap(28.h),
                        Text(
                          AppStrings.T.lbl_personal_information,
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 18.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),
                        Gap(12.h),
                        TextInputField(
                          controller:
                              controller.addBasicDetailFirstNameController,
                          label: AppStrings.T.lbl_first_name,
                          isCapitalized: true,
                          type: InputType.name,
                          isRequiredField: true,
                          validator:
                              (value) => AppValidations.validateRequired(
                                value,
                                fieldName: AppStrings.T.lbl_first_name,
                              ),
                        ),
                        Gap(12.h),

                        // Last Name (required)
                        TextInputField(
                          controller:
                              controller.addBasicDetailLastNameController,
                          label: AppStrings.T.lbl_last_name,
                          isCapitalized: true,
                          isRequiredField: true,
                          type: InputType.name,
                          validator:
                              (value) => AppValidations.validateRequired(
                                value,
                                fieldName: AppStrings.T.lbl_last_name,
                              ),
                        ),
                        Gap(12.h),

                        // Date of Birth
                        TextInputField(
                          controller: controller.dobController,
                          label: AppStrings.T.lbl_date_of_birth,
                          readOnly: true,
                          type: InputType.text,
                          onTap: () => _selectDate(context),
                        ),
                        Gap(28.h),

                        // Contact Information Section
                        Text(
                          AppStrings.T.lbl_contact_information,
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 18.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),
                        Gap(12.h),

                        // Primary Phone
                        buildPhoneNumberField(
                          context,
                          isRequiredField: true,
                          controller.addBasicDetailPhoneController,
                          AppStrings.T.lbl_phone,
                          controller.addBasicDetailRegionCodeController,
                          controller.registerCountryflag,
                        ),
                        Gap(12.h),

                        // Alternative Phone
                        buildPhoneNumberField(
                          context,
                          isRequiredField: false,
                          controller.alternativePhoneController,
                          AppStrings.T.lbl_alternative_phone,
                          controller.alternativeRegionCodeController,
                          RxString('🇮🇳'), // Default flag
                        ),
                        Gap(12.h),

                        // Primary Email
                        TextInputField(
                          controller: controller.addBasicDetailEmailController,
                          label: AppStrings.T.lbl_email,
                          type: InputType.email,
                          isRequiredField: true,
                          validator: AppValidations.emailValidation,
                        ),
                        Gap(12.h),

                        // Alternative Email
                        TextInputField(
                          controller: controller.alternativeEmailController,
                          label: AppStrings.T.lbl_alternative_email,
                          type: InputType.email,
                        ),
                        Gap(28.h),

                        // Company Information Section
                        Text(
                          AppStrings.T.lbl_company_information,
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 18.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),
                        Gap(12.h),

                        // Company Name
                        TextInputField(
                          controller: controller.companyController,
                          label: AppStrings.T.lbl_company,
                          isCapitalized: true,
                          type: InputType.name,
                        ),
                        Gap(12.h),

                        // Job Title
                        TextInputField(
                          controller: controller.jobTitleController,
                          label: AppStrings.T.lbl_job_title,
                          isCapitalized: true,
                          type: InputType.name,
                        ),
                        Gap(28.h),

                        // Location Information Section
                        Text(
                          AppStrings.T.lbl_location_information,
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 18.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),

                        Gap(12.h),

                        // Location URL
                        TextInputField(
                          controller: controller.locationController,
                          label: AppStrings.T.lbl_location,
                          type: InputType.text,
                        ),
                        Gap(12.h),

                        // Location Type Dropdown
                        Obx(
                          () => Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Label
                              Padding(
                                padding: EdgeInsets.only(bottom: 4.h),
                                child: AppText(
                                  AppStrings.T.lbl_location_type,
                                  style: Get.theme.textTheme.bodyMedium
                                      ?.copyWith(fontWeight: FontWeight.w800),
                                ),
                              ),

                              // Main selection container
                              GestureDetector(
                                onTap: () {
                                  controller.isLocationTypeExpanded.value =
                                      !controller.isLocationTypeExpanded.value;
                                },
                                child: Container(
                                  height: 55,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 16.w,
                                    vertical: 12.h,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color:
                                          Get.theme.customColors.primaryColor!,
                                    ),
                                    borderRadius:
                                        !controller.isLocationTypeExpanded.value
                                            ? BorderRadius.circular(8.r)
                                            : BorderRadius.only(
                                              topLeft: Radius.circular(8.r),
                                              topRight: Radius.circular(8.r),
                                            ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      AppText(
                                        _getLocationTypeLabel(
                                          int.tryParse(
                                                controller.locationType.value,
                                              ) ??
                                              0,
                                        ),
                                        style: Get.theme.textTheme.bodyMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                      CustomImageView(
                                        imagePath:
                                            controller
                                                    .isLocationTypeExpanded
                                                    .value
                                                ? AssetConstants.icUpArrow
                                                : AssetConstants.icDownArrow2,
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .darkGreyTextColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              // Dropdown options
                              if (controller.isLocationTypeExpanded.value)
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                      left: BorderSide(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .primaryColor!,
                                      ),
                                      right: BorderSide(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .primaryColor!,
                                      ),
                                      bottom: BorderSide(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .primaryColor!,
                                      ),
                                      top: BorderSide.none,
                                    ),
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(8.r),
                                      bottomRight: Radius.circular(8.r),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Column(
                                      children: List.generate(2, (index) {
                                        return GestureDetector(
                                          onTap: () {
                                            controller.locationType.value =
                                                index.toString();
                                            controller
                                                .isLocationTypeExpanded
                                                .value = false;
                                          },
                                          child: Container(
                                            width: double.infinity,
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 16.w,
                                              vertical: 12.h,
                                            ),
                                            decoration: BoxDecoration(
                                              color:
                                                  (int.tryParse(
                                                                controller
                                                                    .locationType
                                                                    .value,
                                                              ) ??
                                                              0) ==
                                                          index
                                                      ? Colors.grey[200]
                                                      : Colors.transparent,
                                              borderRadius:
                                                  BorderRadius.circular(4.r),
                                            ),
                                            child: AppText(
                                              _getLocationTypeLabel(index),
                                              style:
                                                  Get.theme.textTheme.bodySmall,
                                            ),
                                          ),
                                        );
                                      }),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),

                        // DropdownButtonFormField<int>(
                        //   value:
                        //       int.tryParse(controller.locationType.value) ?? 0,
                        //   decoration: InputDecoration(
                        //     labelText: AppStrings.T.lbl_location_type,
                        //     labelStyle: Get.theme.textTheme.bodyMedium
                        //         ?.copyWith(
                        //           fontSize: 14.sp,
                        //           color: Get.theme.customColors.greyTextColor,
                        //           fontWeight: FontWeight.w700,
                        //         ),
                        //     border: OutlineInputBorder(),
                        //   ),
                        //   items: [
                        //     DropdownMenuItem<int>(
                        //       value: 0,
                        //       child: Text(AppStrings.T.lbl_link),
                        //     ),
                        //     DropdownMenuItem<int>(
                        //       value: 1,
                        //       child: Text(AppStrings.T.lbl_embed_tag),
                        //     ),
                        //   ],
                        //   onChanged: (newValue) {
                        //     controller.locationType.value =
                        //         newValue!.toString();
                        //   },
                        // ),
                        Gap(12.h),

                        // Location
                        TextInputField(
                          controller:
                              controller.locationType.value == '0'
                                  ? controller.locationUrlController
                                  : controller.locationEmbededController,
                          label:
                              controller.locationType.value == '0'
                                  ? AppStrings.T.lbl_location_url
                                  : AppStrings.T.lbl_embed_tag,
                          isCapitalized: true,
                          type: InputType.text,
                        ),

                        Gap(28.h),

                        // Toggle Options Section
                        Text(
                          AppStrings.T.lbl_qr_code_options,
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 18.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),

                        // Show QR Code
                        _buildToggleOption(
                          AppStrings.T.lbl_show_qr_code,
                          controller.showQrCode.value,
                          (value) => controller.showQrCode.value = value,
                        ),

                        // Show QR Code
                        _buildToggleOption(
                          AppStrings.T.lbl_download_qr_code,
                          controller.enableDownloadQrCode.value,
                          (value) =>
                              controller.enableDownloadQrCode.value = value,
                        ),
                        Gap(8.h),

                        // add slider to set size of qr code
                        if (controller.enableDownloadQrCode.value)
                          Obx(
                            () => Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  '${AppStrings.T.lbl_qr_code_size}: ${controller.qrCodeSize.value.toInt()}px',
                                  style: Get.theme.textTheme.bodyMedium
                                      ?.copyWith(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .darkGreyTextColor,
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w500,
                                      ),
                                ),
                                Gap(20.h),
                                SliderTheme(
                                  data: SliderThemeData(
                                    overlayShape: RoundSliderOverlayShape(
                                      overlayRadius: 0,
                                    ),
                                  ),
                                  child: Slider(
                                    value: controller.qrCodeSize.value,
                                    min: controller.minQrSize,
                                    max: controller.maxQrSize,
                                    divisions:
                                        ((controller.maxQrSize -
                                                    controller.minQrSize) /
                                                controller.qrSizeInterval)
                                            .toInt(),
                                    label:
                                        '${controller.qrCodeSize.value.round()}px',

                                    onChanged: (value) {
                                      controller.qrCodeSize.value = value;
                                    },
                                    activeColor:
                                        Get.theme.customColors.primaryColor,
                                    inactiveColor:
                                        Get.theme.customColors.greyTextColor,
                                  ),
                                ),
                                Gap(10.h),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      '${controller.minQrSize.toInt()}px',
                                      style: Get.theme.textTheme.bodySmall,
                                    ),
                                    Text(
                                      '${controller.maxQrSize.toInt()}px',
                                      style: Get.theme.textTheme.bodySmall,
                                    ),
                                  ],
                                ),
                                Gap(12.h),
                              ],
                            ),
                          ),

                        Gap(28.h),

                        // Additional Information Section
                        Text(
                          AppStrings.T.lbl_additional_information,
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 18.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),
                        Gap(12.h),

                        // Made By
                        TextInputField(
                          controller: controller.madeByController,
                          label: AppStrings.T.lbl_made_by,
                          isCapitalized: true,
                          type: InputType.name,
                        ),
                        Gap(12.h),

                        // Made By URL
                        TextInputField(
                          controller: controller.madeByUrlController,
                          textInputAction: TextInputAction.done,
                          validator:
                              (value) =>
                                  AppValidations.urlWithEmptyValidation(value),
                          label: AppStrings.T.lbl_made_by_url,
                          type: InputType.text,
                        ),
                        Gap(12.h),

                        // Cover Image Type Dropdown
                        // DropdownButtonFormField<int>(
                        //   value:
                        //       int.tryParse(controller.coverImageType.value) ??
                        //       0,
                        //   decoration: InputDecoration(
                        //     labelText: AppStrings.T.lbl_cover_image_type,
                        //     labelStyle: Get.theme.textTheme.bodyMedium
                        //         ?.copyWith(
                        //           fontSize: 14.sp,
                        //           color: Get.theme.customColors.greyTextColor,
                        //           fontWeight: FontWeight.w700,
                        //         ),
                        //     border: OutlineInputBorder(),
                        //   ),
                        //   items: [
                        //     DropdownMenuItem<int>(
                        //       value: 0,
                        //       child: Text(AppStrings.T.lbl_cover),
                        //     ),
                        //     DropdownMenuItem<int>(
                        //       value: 1,
                        //       child: Text(AppStrings.T.lbl_contain),
                        //     ),
                        //   ],
                        //   onChanged: (newValue) {
                        //     controller.coverImageType.value =
                        //         newValue!.toString();
                        //   },
                        // ),
                        Obx(
                          () => Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Label
                              Padding(
                                padding: EdgeInsets.only(bottom: 4.h),
                                child: Text(
                                  AppStrings.T.lbl_cover_image_type,
                                  style: Get.theme.textTheme.bodyMedium
                                      ?.copyWith(
                                        fontSize: 14.sp,
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .greyTextColor,
                                        fontWeight: FontWeight.w700,
                                      ),
                                ),
                              ),

                              // Main Selection Container
                              GestureDetector(
                                onTap: () {
                                  controller.isCoverImageTypeExpanded.value =
                                      !controller
                                          .isCoverImageTypeExpanded
                                          .value;
                                },
                                child: Container(
                                  height: 55.h,
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 16.w,
                                    vertical: 12.h,
                                  ),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                      color:
                                          Get.theme.customColors.primaryColor!,
                                    ),
                                    borderRadius:
                                        !controller
                                                .isCoverImageTypeExpanded
                                                .value
                                            ? BorderRadius.circular(8.r)
                                            : BorderRadius.only(
                                              topLeft: Radius.circular(8.r),
                                              topRight: Radius.circular(8.r),
                                            ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      AppText(
                                        _getCoverImageTypeLabel(
                                          int.tryParse(
                                                controller.coverImageType.value,
                                              ) ??
                                              0,
                                        ),
                                        style: Get.theme.textTheme.bodyMedium
                                            ?.copyWith(
                                              fontWeight: FontWeight.w500,
                                            ),
                                      ),
                                      CustomImageView(
                                        imagePath:
                                            controller
                                                    .isCoverImageTypeExpanded
                                                    .value
                                                ? AssetConstants.icUpArrow
                                                : AssetConstants.icDownArrow2,
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .darkGreyTextColor,
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              // Dropdown Options
                              if (controller.isCoverImageTypeExpanded.value)
                                Container(
                                  decoration: BoxDecoration(
                                    border: Border(
                                      left: BorderSide(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .primaryColor!,
                                      ),
                                      right: BorderSide(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .primaryColor!,
                                      ),
                                      bottom: BorderSide(
                                        color:
                                            Get
                                                .theme
                                                .customColors
                                                .primaryColor!,
                                      ),
                                    ),
                                    borderRadius: BorderRadius.only(
                                      bottomLeft: Radius.circular(8.r),
                                      bottomRight: Radius.circular(8.r),
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(4.0),
                                    child: Column(
                                      children: List.generate(2, (index) {
                                        return GestureDetector(
                                          onTap: () {
                                            controller.coverImageType.value =
                                                index.toString();
                                            controller
                                                .isCoverImageTypeExpanded
                                                .value = false;
                                          },
                                          child: Container(
                                            width: double.infinity,
                                            padding: EdgeInsets.symmetric(
                                              horizontal: 16.w,
                                              vertical: 12.h,
                                            ),
                                            decoration: BoxDecoration(
                                              color:
                                                  (int.tryParse(
                                                                controller
                                                                    .coverImageType
                                                                    .value,
                                                              ) ??
                                                              0) ==
                                                          index
                                                      ? Colors.grey[200]
                                                      : Colors.transparent,
                                              borderRadius:
                                                  BorderRadius.circular(4.r),
                                            ),
                                            child: AppText(
                                              _getCoverImageTypeLabel(index),
                                              style:
                                                  Get.theme.textTheme.bodySmall,
                                            ),
                                          ),
                                        );
                                      }),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        ),

                        Gap(28.h),

                        // WhatsApp Share
                        _buildToggleOption(
                          AppStrings.T.lbl_whatsapp_share,
                          controller.whatsappShare.value,
                          (value) => controller.whatsappShare.value = value,
                        ),
                        Gap(8.h),

                        // Enable Contact
                        _buildToggleOption(
                          AppStrings.T.lbl_enable_contact,
                          controller.enableContact.value,
                          (value) => controller.enableContact.value = value,
                        ),
                        Gap(8.h),

                        // Enable Affiliation
                        _buildToggleOption(
                          AppStrings.T.lbl_enable_affiliation,
                          controller.enableAffiliation.value,
                          (value) => controller.enableAffiliation.value = value,
                        ),
                        Gap(8.h),

                        // Enable Enquiry Form
                        _buildToggleOption(
                          AppStrings.T.lbl_enable_enquiry_form,
                          controller.enableEnquiryForm.value,
                          (value) => controller.enableEnquiryForm.value = value,
                        ),
                        Gap(8.h),

                        // Hide Stickybar
                        _buildToggleOption(
                          AppStrings.T.lbl_hide_stickybar,
                          controller.hideStickybar.value,
                          (value) => controller.hideStickybar.value = value,
                        ),
                        Gap(28.h),
                        Text(
                          AppStrings.T.language,
                          style: Get.theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                            fontSize: 18.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),
                        Gap(12.h),

                        TextInputField(
                          readOnly: true,
                          controller:
                              controller
                                  .addBasicDetailDefaultLanguageController,
                          label: AppStrings.T.lbl_default_language,
                          type: InputType.name,
                          suffixIcon: CustomImageView(
                            imagePath: AssetConstants.icDownArrow2,
                            margin: EdgeInsets.symmetric(horizontal: 16.0),
                            color: Get.theme.customColors.darkGreyTextColor,
                          ),
                          onTap: () {
                            showModalBottomSheet(
                              context: context,
                              builder:
                                  (_) => LanguageSelectionBottomSheet(
                                    onSelect: (selectedLanguage) {
                                      controller
                                          .addBasicDetailDefaultLanguageController
                                          .text = selectedLanguage;
                                      Navigator.pop(context);
                                    },
                                  ),
                            );
                          },
                        ),
                        Gap(12.h),

                        _buildToggleOption(
                          AppStrings.T.lbl_language_enabled,
                          controller.addBasicDetailLanguageEnabled.value,
                          (value) =>
                              controller.addBasicDetailLanguageEnabled.value =
                                  value,
                        ),

                        Gap(30.h),

                        _buildButtons(vcardId),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget buildPhoneNumberField(
    BuildContext context,
    TextEditingController controller,
    String? label,
    RxString regionCode,
    RxString flag, {
    bool isRequiredField = false,
  }) {
    return TextInputField(
      controller: controller,
      label: label,
      isRequiredField: isRequiredField,
      type: InputType.phoneNumber,
      keyboardType: TextInputType.phone,
      boxConstraints: BoxConstraints(),
      validator:
          (value) =>
              isRequiredField
                  ? AppValidations.phoneNumberValidation(value)
                  : null,
      prefixIcon: GestureDetector(
        onTap: () {
          showCountryPicker(
            context: context,
            showPhoneCode: true,
            countryListTheme: CountryListThemeData(
              searchTextStyle: Get.theme.textTheme.labelLarge,
              textStyle: Get.theme.textTheme.labelLarge,
              inputDecoration: InputDecoration(
                hintText: AppStrings.T.lbl_search,
                hintStyle: Get.theme.textTheme.bodySmall?.copyWith(
                  fontSize: 14.sp,
                  color: Get.theme.customColors.greyTextColor,
                ),
                border: InputBorder.none,
                prefixIcon: Icon(
                  Icons.search,
                  color: Get.theme.customColors.greyTextColor,
                ),
              ),
            ),
            onSelect: (country) {
              regionCode.value = country.phoneCode;
              flag.value = country.flagEmoji;
            },
          );
        },
        child: Obx(
          () => Padding(
            padding: EdgeInsets.symmetric(horizontal: 12.w),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  flag.value.isNotEmpty ? flag.value : '🇮🇳',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(4.w),
                Text(
                  "+${regionCode.value}",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 16.sp,
                    color: Get.theme.customColors.black,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Gap(16.w),
                Text(
                  "|",
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontSize: 20.sp,
                    color: Get.theme.customColors.greyTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildButtons(String vcardId) {
    return Column(
      children: [
        CustomElevatedButton(
          checkConnectivity: true,
          isLoading: controller.isLoadingUpdateBasicdetail.value,
          text: AppStrings.T.lbl_save,
          onPressed: () async {
            if (controller.isLoadingUpdateBasicdetail.value) {
              return;
            }

            // First validate the form
            if (_formKey.currentState?.validate() ?? false) {
              // Check for profile image
              if (controller.profileImageFile.value == null &&
                  (controller.vCardBasicDetails.value?.data.profileUrl ?? '')
                      .isEmpty) {
                toastification.show(
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  showProgressBar: false,
                  alignment: Alignment.topCenter,
                  title: Text(AppStrings.T.msg_select_profile_image),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                return;
              }

              // Check for cover media based on type
              if (controller.selectedCoverType.value == 0 ||
                  controller.selectedCoverType.value == 1) {
                if (controller.coverImageFile.value == null &&
                    (controller.vCardBasicDetails.value?.data.coverUrl ?? '')
                        .isEmpty) {
                  toastification.show(
                    type: ToastificationType.error,
                    style: ToastificationStyle.flatColored,
                    showProgressBar: false,
                    alignment: Alignment.topCenter,
                    title: Text(
                      controller.selectedCoverType.value == 0
                          ? AppStrings.T.msg_select_cover_image
                          : AppStrings.T.msg_select_cover_video,
                    ),
                    autoCloseDuration: const Duration(seconds: 3),
                  );
                  return;
                }
              } else if (controller.selectedCoverType.value == 2) {
                if (controller.youtubeLinkController.text.isEmpty) {
                  toastification.show(
                    type: ToastificationType.error,
                    style: ToastificationStyle.flatColored,
                    showProgressBar: false,
                    alignment: Alignment.topCenter,
                    title: Text(AppStrings.T.msg_enter_youtube_url),
                    autoCloseDuration: const Duration(seconds: 3),
                  );
                  return;
                }
              }

              // Check other required fields
              if (controller.addBasicDetailNameController.text.isEmpty) {
                toastification.show(
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  showProgressBar: false,
                  alignment: Alignment.topCenter,
                  title: Text(AppStrings.T.msg_enter_vcard_name),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                return;
              }

              if (controller.addBasicDetailFirstNameController.text.isEmpty) {
                toastification.show(
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  showProgressBar: false,
                  alignment: Alignment.topCenter,
                  title: Text(AppStrings.T.msg_enter_first_name),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                return;
              }

              if (controller.addBasicDetailLastNameController.text.isEmpty) {
                toastification.show(
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  showProgressBar: false,
                  alignment: Alignment.topCenter,
                  title: Text(AppStrings.T.msg_enter_last_name),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                return;
              }

              if (controller.addBasicDetailEmailController.text.isEmpty) {
                toastification.show(
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  showProgressBar: false,
                  alignment: Alignment.topCenter,
                  title: Text(AppStrings.T.msg_enter_email),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                return;
              }

              if (controller.addBasicDetailPhoneController.text.isEmpty) {
                toastification.show(
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  showProgressBar: false,
                  alignment: Alignment.topCenter,
                  title: Text(AppStrings.T.msg_enter_phone),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                return;
              }

              if (controller
                  .addBasicDetailDefaultLanguageController
                  .text
                  .isEmpty) {
                toastification.show(
                  type: ToastificationType.error,
                  style: ToastificationStyle.flatColored,
                  showProgressBar: false,
                  alignment: Alignment.topCenter,
                  title: Text(AppStrings.T.msg_select_default_language),
                  autoCloseDuration: const Duration(seconds: 3),
                );
                return;
              }

              controller.isLoadingUpdateBasicdetail.value = true;

              // Handle file paths safely
              String? profilePath = controller.profileImageFile.value?.path;
              String? coverPath = controller.coverImageFile.value?.path;
              String? faviconPath = controller.faviconFile.value?.path;

              // If no new files selected but API has URLs, use those
              if (profilePath == null &&
                  controller.vCardBasicDetails.value?.data.profileUrl != null) {
                final profileFile = await urlToFile(
                  controller.vCardBasicDetails.value!.data.profileUrl,
                );
                profilePath = profileFile.path;
              }

              if (coverPath == null &&
                  controller.vCardBasicDetails.value?.data.coverUrl != null &&
                  (controller.selectedCoverType.value == 0 ||
                      controller.selectedCoverType.value == 1)) {
                final coverFile = await urlToFile(
                  controller.vCardBasicDetails.value!.data.coverUrl,
                );
                coverPath = coverFile.path;
              }

              if (faviconPath == null &&
                  controller.vCardBasicDetails.value?.data.faviconUrl != null) {
                final faviconFile = await urlToFile(
                  controller.vCardBasicDetails.value!.data.faviconUrl,
                );
                faviconPath = faviconFile.path;
              }

              await controller.updateVCardBasicdetail(
                vcardId: vcardId,
                profilePath: profilePath ?? '',
                coverPath: coverPath ?? '',
                faviconPath: faviconPath ?? '',
              );
            } else {
              // Show general error message if form validation fails
              toastification.show(
                type: ToastificationType.error,
                style: ToastificationStyle.flatColored,
                showProgressBar: false,
                alignment: Alignment.topCenter,
                title: Text(AppStrings.T.msg_fill_required_fields),
                autoCloseDuration: const Duration(seconds: 3),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildToggleOption(
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Text(
            label,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              color: Get.theme.customColors.darkGreyTextColor,
              fontSize: 16.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Transform.scale(
          scale: 0.8,
          alignment: Alignment.centerRight,
          child: Switch(
            value: value,
            activeColor: Get.theme.customColors.white,
            inactiveThumbColor: Get.theme.customColors.darkColor,
            inactiveTrackColor: Get.theme.customColors.white,
            activeTrackColor: Get.theme.customColors.primaryColor,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime.now(),
      fieldHintText: "dd/mm/yyyy",
      locale: const Locale('en', 'GB'),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Get.theme.customColors.primaryColor!,
              onPrimary: Colors.white, // text on selected date
              onSurface: Colors.black87, // default text
              surface: Colors.white, // background
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: Get.theme.customColors.primaryColor!,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      final String day = picked.day.toString().padLeft(2, '0');
      final String month = _getMonthName(picked.month);
      final String year = picked.year.toString();
      controller.dobController.text = "$day $month, $year";
    }
  }

  String _getMonthName(int month) {
    const List<String> monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return monthNames[month - 1];
  }

  Widget _buildImageCoverSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_select_cover_image,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        Gap(8.h),
        GestureDetector(
          onTap:
              controller.isProcessingCoverPWAIcon.value
                  ? null
                  : () async {
                    if (controller.isProcessingCoverPWAIcon.value) {
                      Logger.log("Processing is already in progress.");
                      return;
                    } else {
                      controller.isProcessingCoverPWAIcon.value = true;
                      try {
                        final File? compressedImage =
                            await _pickCompressedImage(
                              context: context,
                              imageType: 'Cover image',
                              targetSize: 1024,
                              maxKB: 300,
                              format: 'jpg',
                            );
                        if (compressedImage != null) {
                          controller.coverImageFile.value = compressedImage;
                        }
                      } finally {
                        controller.isProcessingCoverPWAIcon.value = false;
                      }
                    }
                  },

          child: Container(
            height: 120.h,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              border: Border.all(color: Get.theme.customColors.primaryColor!),
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              ],
            ),

            child: Obx(
              () => Stack(
                alignment: Alignment.center,
                children: [
                  if (controller.isProcessingCoverPWAIcon.value)
                    ShimmerBox(
                      height: double.infinity,
                      width: double.infinity,
                      borderRadius: BorderRadius.circular(16.r),
                    )
                  else
                    controller.vCardBasicDetails.value!.data.coverUrl.isNotEmpty
                        ? controller.coverImageFile.value != null
                            ? Stack(
                              children: [
                                CustomImageView(
                                  height: double.infinity,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                  margin: const EdgeInsets.all(2.0),
                                  radius: BorderRadius.circular(16.r),
                                  imagePath:
                                      controller.coverImageFile.value?.path,
                                ),
                                Positioned(
                                  right: 8,
                                  top: 8,
                                  child: IconButton(
                                    icon: Icon(Icons.close, color: Colors.red),
                                    onPressed:
                                        () =>
                                            controller.coverImageFile.value =
                                                null,
                                  ),
                                ),
                              ],
                            )
                            : Stack(
                              children: [
                                CustomImageView(
                                  height: double.infinity,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                  margin: const EdgeInsets.all(2.0),
                                  radius: BorderRadius.circular(16.r),
                                  imagePath:
                                      controller
                                          .vCardBasicDetails
                                          .value!
                                          .data
                                          .coverUrl,
                                ),
                              ],
                            )
                        : Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add,
                                size: 40.sp,
                                color: Get.theme.customColors.primaryColor,
                              ),
                              Text(
                                AppStrings.T.lbl_add_image,
                                style: Get.theme.textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVideoCoverSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_select_cover_video,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        Gap(8.h),
        GestureDetector(
          onTap: () async {
            await controller.pickCoverVideo();
          },
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.0.w),
            height: 120.h,
            width: double.infinity,
            decoration: BoxDecoration(
              color: Get.theme.customColors.white,
              border: Border.all(color: Get.theme.customColors.primaryColor!),
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 0),
                ),
              ],
            ),
            child: Obx(() {
              if (controller.coverImageFile.value != null) {
                // Show video file preview
                return Stack(
                  children: [
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.videocam,
                            size: 40.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                          Text(
                            controller.coverImageFile.value!.path
                                .split('/')
                                .last,
                            style: Get.theme.textTheme.bodyMedium,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              } else if (controller.vCardBasicDetails.value?.data.coverUrl !=
                      null &&
                  controller.selectedCoverType.value == 1) {
                // Show API video URL preview
                return Stack(
                  children: [
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.videocam,
                            size: 40.sp,
                            color: Get.theme.customColors.primaryColor,
                          ),
                          Text(
                            controller.vCardBasicDetails.value!.data.coverUrl,
                            style: Get.theme.textTheme.bodyMedium,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              } else {
                // Show add video button
                return GestureDetector(
                  onTap: () async {
                    await controller.pickCoverVideo();
                  },
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.add,
                          size: 40.sp,
                          color: Get.theme.customColors.primaryColor,
                        ),
                        Text(
                          AppStrings.T.lbl_add_video,
                          style: Get.theme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                );
              }
            }),
          ),
        ),
      ],
    );
  }

  Widget _buildYoutubeCoverField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_youtube_url,
          style: Get.theme.textTheme.bodyLarge!.copyWith(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        Gap(8.h),
        TextInputField(
          type: InputType.text,
          label: AppStrings.T.lbl_youtube_url,
          controller: controller.youtubeLinkController,
          keyboardType: TextInputType.url,
          validator: (value) {
            if (controller.selectedCoverType.value == 2) {
              return AppValidations.validateRequired(
                value,
                fieldName: AppStrings.T.lbl_youtube_url,
              );
            }
            return null;
          },
        ),
      ],
    );
  }

  String _getCoverTypeLabel(int value) {
    switch (value) {
      case 0:
        return AppStrings.T.lbl_image;
      case 1:
        return AppStrings.T.lbl_video;
      case 2:
        return AppStrings.T.lbl_youtube_link;
      default:
        return AppStrings.T.lbl_select_type;
    }
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.profileImageFile.value = null;
    controller.coverImageFile.value = null;
    controller.faviconFile.value = null;
  }

  @override
  void onInit() {
    controller.getVCardBasicDetails(vcardId: vcardId);
    controller.isProcessingPWAIcon = false.obs;
    controller.isProcessingFaviconPWAIcon = false.obs;
    controller.isProcessingCoverPWAIcon = false.obs;
  }

  String _getLocationTypeLabel(int index) {
    switch (index) {
      case 1:
        return AppStrings.T.lbl_embed_tag;
      case 0:
      default:
        return AppStrings.T.lbl_link;
    }
  }

  String _getCoverImageTypeLabel(int index) {
    switch (index) {
      case 1:
        return AppStrings.T.lbl_contain;
      case 0:
      default:
        return AppStrings.T.lbl_cover;
    }
  }
}
