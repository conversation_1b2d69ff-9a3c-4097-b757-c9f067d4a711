import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateInstaEmbedShimmer extends StatelessWidget {
  const CreateInstaEmbedShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Type selector section
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerBox(width: 120.w, height: 20.h),
            Gap(8.h),
            Row(
              children: [
                Expanded(child: _buildTypeOptionShimmer()),
                Gap(16.h),
                Expanded(child: _buildTypeOptionShimmer()),
              ],
            ),
          ],
        ),
        Gap(16.h),

        // Embed tag field section
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerBox(width: 200.w, height: 20.h),
            Gap(8.h),
            ShimmerBox(
              width: double.infinity,
              height: 150.h,
              borderRadius: BorderRadius.circular(8.sp),
            ),
          ],
        ),
        Gap(16.h),

        // Instructions section
        Container(
          padding: EdgeInsets.all(12.sp),
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.sp)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ShimmerBox(width: 20.w, height: 20.h),
                  Gap(8.h),
                  ShimmerBox(width: 180.w, height: 20.h),
                ],
              ),
              Gap(8.h),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShimmerBox(width: double.infinity, height: 16.h),
                  Gap(8.h),
                  ShimmerBox(width: double.infinity, height: 16.h),
                  Gap(8.h),
                  ShimmerBox(width: 250.w, height: 16.h),
                ],
              ),
            ],
          ),
        ),
        Gap(16.h),

        // Save button
        ShimmerBox(
          width: double.infinity,
          height: 50.h,
          borderRadius: BorderRadius.circular(8.sp),
        ),
      ],
    );
  }

  Widget _buildTypeOptionShimmer() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.sp),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.sp),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
      ),

      child: Column(
        children: [
          ShimmerBox(width: 32.w, height: 32.h),
          Gap(8.h),
          ShimmerBox(width: 60.w, height: 20.h),
        ],
      ),
    );
  }
}
