import 'package:get/get.dart';
import 'package:v_card/app/controllers/insta_embed_controller.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/ui/widgets/custom_list_item_tile.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class InstaEmbedPage extends GetItHook<InstaEmbedController> {
  InstaEmbedPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_insta_embed,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      floatingActionButton: GestureDetector(
        onTap: () {
          controller.resetForm();
          NavigationService.navigateWithSlideAnimation(
            AppRoutes.createInstaEmbedPage,
            arguments: {'vcardId': vcardId.toString()},
          );
        },
        child: CircleAvatar(
          radius: 32.r,
          backgroundColor: Get.theme.customColors.primaryColor,
          child: Icon(Icons.add, size: 32),
        ),
      ),
      body: Obx(() {
        if (controller.isLoadingEmbeds.value) {
          return ListTileShimmerLoading();
        }

        final embeds = controller.instaEmbedData.value?.data ?? [];

        return RefreshIndicator(
          onRefresh: () => controller.getInstaEmbeds(vcardId),
          child: ListView.builder(
            padding: EdgeInsets.only(left: 16.0, right: 16.0, bottom: 120.h),
            itemCount: embeds.isEmpty ? 1 : embeds.length,
            itemBuilder: (context, index) {
              if (embeds.isEmpty) {
                return NoDataWidget(message: AppStrings.T.lbl_no_data);
              }
              final embed = embeds[index];
              return CustomListItemTile(
                id: embed.id.toString(),
                isLoadingDelete: false,
                onDelete: () {
                  Get.dialog(
                    LoadingConfirmationDialog(
                      title: AppStrings.T.btn_delete_insta_embed,
                      message: AppStrings.T.msg_confirm_delete_insta_embed,
                      onCancel: () => NavigationService.navigateBack(),
                      onConfirm: () {
                        controller.deleteInstaEmbed(
                          vcardId: vcardId,
                          embedId: embed.id.toString(),
                        );
                      },
                      isLoading: controller.isDeletingEmbed,
                    ),
                  );
                },
                onEdit: () {
                  NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createInstaEmbedPage,
                    arguments: {
                      'vcardId': vcardId.toString(),
                      'embedId': embed.id.toString(),
                    },
                  );
                },
                vcardId: vcardId,
                isServiceTile: true,
                onTap: () {},
                title:
                    embed.type == "0"
                        ? AppStrings.T.lbl_post
                        : AppStrings.T.lbl_reel,
                subtitle: embed.embedtag ?? AppStrings.T.lbl_no_tag,
                imageUrl: AssetConstants.pngInsta,
              );
            },
          ),
        );
      }),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getInstaEmbeds(vcardId);
  }
}
