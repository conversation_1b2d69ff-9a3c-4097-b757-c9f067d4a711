import 'package:get/get.dart';
import 'package:v_card/app/controllers/insta_embed_controller.dart';
import 'package:v_card/app/ui/pages/v_card/page/insta_embed/widgets.dart/create_insta_embed_shimmer.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CreateInstaEmbedPage extends GetItHook<InstaEmbedController> {
  CreateInstaEmbedPage({super.key});

  final String vcardId = Get.arguments['vcardId'] ?? '0';
  final embedId = Get.arguments['embedId'];
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          embedId != null
              ? AppStrings.T.btn_edit_insta_embed
              : AppStrings.T.btn_add_insta_embed,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
        actions: [
          if (!isCardCreated)
            IconButton(
              onPressed:
                  () => NavigationService.navigateWithSlideAnimation(
                    AppRoutes.createGalleriesPage,
                    arguments: {'vcardId': vcardId.toString()},
                  ),
              icon: Text(
                AppStrings.T.lbl_skip,
                style: Get.textTheme.bodySmall?.copyWith(
                  color: Get.theme.customColors.primaryColor,
                  fontSize: 14.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
      body: IgnorePointer(
        ignoring: controller.isSubmittingEmbed.value,
        child: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 16.h),
          child: Obx(() {
            if (controller.isLoadingEmbedsById.value) {
              return Center(child: CreateInstaEmbedShimmer());
            }

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTypeSelector(),
                Gap(16.h),
                _buildEmbedTagField(),
                Gap(16.h),
                _buildInstructions(),
                Gap(16.h),
                _buildSaveButton(),
                Gap(16.h),
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppText(
          AppStrings.T.lbl_select_type,
          style: Get.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Gap(8.h),
        Obx(
          () => Row(
            children: [
              _buildTypeOption('0', AppStrings.T.lbl_post, Icons.post_add),
              Gap(16.h),
              _buildTypeOption(
                '1',
                AppStrings.T.lbl_reel,
                Icons.video_collection,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTypeOption(String value, String label, IconData icon) {
    final isSelected = controller.selectedType.value == value;

    return Expanded(
      child: InkWell(
        onTap: () => controller.selectedType.value = value,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.sp),
          decoration: BoxDecoration(
            color: isSelected ? Get.theme.primaryColor : Colors.grey.shade200,
            borderRadius: BorderRadius.circular(8.sp),
            border: Border.all(
              color: isSelected ? Get.theme.primaryColor : Colors.grey.shade300,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: isSelected ? Colors.white : Colors.grey.shade700,
                size: 32.sp,
              ),
              Gap(8.h),
              AppText(
                label,
                style: Get.textTheme.bodyMedium?.copyWith(
                  color: isSelected ? Colors.white : Colors.grey.shade800,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmbedTagField() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppText(
            AppStrings.T.lbl_instagram_embed_tag,
            style: Get.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Gap(8.h),
          TextInputField(
            controller: controller.embedTagController,
            isRequiredField: true,
            hintLabel: AppStrings.T.lbl_paste_embed_code,
            label: AppStrings.T.lbl_paste_embed_code,
            maxLines: 10,
            keyboardType: TextInputType.multiline,
            type: InputType.text,
            validator:
                (value) => AppValidations.validateRequired(
                  value,
                  fieldName: AppStrings.T.lbl_instagram_embed_tag,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructions() {
    return Container(
      padding: EdgeInsets.all(12.sp),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8.sp),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.blue.shade700,
                size: 20.sp,
              ),
              Gap(8.h),
              AppText(
                AppStrings.T.lbl_how_to_get_embed_code,
                style: Get.textTheme.titleSmall?.copyWith(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Gap(8.h),
          AppText(
            AppStrings.T.lbl_embed_steps,
            style: Get.textTheme.bodyMedium?.copyWith(
              color: Colors.blue.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return Obx(
      () => CustomElevatedButton(
        checkConnectivity: true,
        text: AppStrings.T.lbl_save,
        isLoading: controller.isSubmittingEmbed.value,
        onPressed: () async {
          if (controller.isSubmittingEmbed.value) {
            return;
          }

          if (!_formKey.currentState!.validate()) {
            return;
          }

          if (embedId == null) {
            await controller.createInstaEmbed(vcardId);
          } else {
            //update
            await controller.updateInstaEmbed(
              embedId: embedId,
              vcardId: vcardId,
            );
          }
        },
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {
    controller.resetForm();
  }

  @override
  void onInit() {
    if (embedId != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await controller.getInstaEmbedsById(embedId.toString());
        final service = controller.instaEmbedDataById.value?.data;
        if (service != null) {
          controller.embedTagController.text = service.embedtag.toString();
          controller.selectedType.value = service.type.toString();
        }
      });
    }
  }
}
