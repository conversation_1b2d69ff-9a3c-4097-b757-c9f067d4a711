import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:v_card/app/controllers/v_card_controller.dart';
import 'package:v_card/app/data/model/v_card/single_vcard_model.dart';
import 'package:v_card/app/data/model/v_card/v_card_qr_code_model.dart';
import 'package:v_card/app/ui/pages/v_card/widgets/vcard_shimmer.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/themes/button_theme.dart';
import 'package:nfc_manager/nfc_manager.dart';
import 'package:permission_handler/permission_handler.dart';

class VCardDetailBottomSheet extends StatelessWidget {
  final VcardController controller;
  final String vcardId;

  const VCardDetailBottomSheet({
    super.key,
    required this.controller,
    required this.vcardId,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      final detail = controller.vCardByIdData.value;
      final qrData = controller.vCardQrCodeData.value;
      final isLoading =
          controller.isLoadingVcardById.value ||
          controller.isLoadingVcardQrCode.value;

      if (isLoading) return _shimmerBottomSheet();
      if (detail == null) return _noDetailBottomSheet();

      final vcardData = detail.data;
      final formattedDate = DateFormat(
        'dd MMM, y',
      ).format(DateTime.parse(vcardData.createdAt!));

      return _buildBottomSheetContent(vcardData, formattedDate, qrData!);
    });
  }

  // ... rest of your bottom sheet methods (_shimmerBottomSheet, etc)
  Widget _shimmerBottomSheet() => _bottomSheetContainer(
    child: VCardShimmer(
      isBottomSheet: true,
      controller: controller.isSearchActive.value,
    ),
  );

  Widget _noDetailBottomSheet() => _bottomSheetContainer(
    child: Center(child: Text(AppStrings.T.lbl_no_details_available)),
  );

  Widget _bottomSheetContainer({required Widget child}) => Container(
    decoration: BoxDecoration(
      color: Get.theme.customColors.white,
      borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
    ),
    // padding: const EdgeInsets.all(20.0),
    child: SingleChildScrollView(
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
            height: 5.0,
            width: 50.0,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(100.r),
              color: Get.theme.customColors.primaryColor?.withValues(
                alpha: 0.6,
              ),
            ),
          ),
          Padding(padding: const EdgeInsets.all(20.0), child: child),
        ],
      ),
    ),
  );

  Widget _buildBottomSheetContent(
    SingleVCardData vcardData,
    String date,
    VCardQrCodeModel qrData,
  ) {
    return _bottomSheetContainer(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Column(
            children: [
              _buildVCardHeader(vcardData, date),
              Gap(12.h),
              _buildActionButtons(vcardData, qrData),
              Gap(20.h),
              _buildNfcButton(qrData),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVCardHeader(SingleVCardData vcardData, String date) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            SizedBox(
              height: 50,
              width: 50,
              child: CustomImageView(
                imagePath: vcardData.image,
                radius: BorderRadius.circular(100.r),
                fit: BoxFit.cover,
              ),
            ),
            Gap(10.w),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  vcardData.name ?? AppStrings.T.lbl_no_name,
                  style: Get.textTheme.bodyLarge,
                ),
                Text(
                  vcardData.occupation ?? AppStrings.T.lbl_no_occupation,
                  style: Get.textTheme.labelMedium,
                ),
                Text(
                  '${AppStrings.T.lbl_created_at}: $date',
                  style: Get.textTheme.labelSmall,
                ),
              ],
            ),
          ],
        ),
        _buildEditButton(vcardData),
      ],
    );
  }

  Widget _buildEditButton(SingleVCardData vcardData) {
    return CustomElevatedButton(
      width: 90.w,
      height: 32.h,
      text: AppStrings.T.lbl_edit,
      buttonTextStyle: Get.textTheme.labelLarge?.copyWith(
        color: Get.theme.customColors.primaryColor,
      ),
      textPadding: const EdgeInsets.symmetric(horizontal: 8.0),
      secondary: true,
      buttonStyle: ButtonThemeHelper.secondaryButtonStyle(Get.context!),
      onPressed: () {
        NavigationService.navigateBack();
        NavigationService.navigateWithSlideAnimation(
          AppRoutes.editVcardPage,
          arguments: {'vcardId': vcardData.id.toString()},
        );
      },
    );
  }

  Widget _buildActionButtons(
    SingleVCardData vcardData,
    VCardQrCodeModel qrData,
  ) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(child: _buildEnquiriesButton(vcardData)),
            Gap(6.w),
            Expanded(child: _buildAppointmentButton(vcardData)),
          ],
        ),
        Gap(6.h),
        Row(
          children: [
            Expanded(
              child: Obx(
                () =>
                    controller.isDownloadingQr.value
                        ? _buildQrDownloadingShimmer()
                        : _buildQrDownloadButton(qrData),
              ),
            ),
            Gap(6.w),
            Expanded(child: _buildDeleteButton(vcardData)),
          ],
        ),
      ],
    );
  }

  Widget _buildQrDownloadingShimmer() {
    return ShimmerBox(height: 40, width: 200);
  }

  Widget _buildQrDownloadButton(VCardQrCodeModel qrData) {
    return _buildActionButton(
      icon: AssetConstants.icQrCode2,
      label: AppStrings.T.lbl_download_qr,
      color: Get.theme.customColors.primaryColor!,
      onTap:
          () => _downloadQrCode(
            qrData,
            controller.vCardByIdData.value?.data.name.toString() ?? '',
          ),
    );
    // return SizedBox(
    //   width: double.infinity,
    //   height: 48.h,
    //   child: CustomElevatedButton(
    //     text:
    //         controller.isDownloadingQr.value
    //             ? 'Downloading...'
    //             : AppStrings.T.lbl_download_qr,
    //     onPressed:
    //         controller.isDownloadingQr.value
    //             ? null
    //             : () => _downloadQrCode(
    //               qrData,
    //               controller.vCardByIdData.value?.data.name.toString() ?? '',
    //             ),
    //   ),
    // );
  }

  Widget _buildDeleteButton(SingleVCardData vcardData) {
    return _buildActionButton(
      icon: AssetConstants.icDelete2,
      label: AppStrings.T.lbl_delete,
      color: Get.theme.customColors.redColor!,
      onTap: () => _confirmDeleteVCard(vcardData),
    );
  }

  Widget _buildEnquiriesButton(SingleVCardData vcardData) {
    return _buildActionButton(
      icon: AssetConstants.icDashEnquiries,
      label: AppStrings.T.lbl_enquiries,
      color: Get.theme.customColors.blueColor!,
      onTap: () {
        NavigationService.navigateWithSlideAnimation(
          AppRoutes.filterdEnquiriesPage,
          arguments: {'vcardName': vcardData.name.toString()},
        );
      },
    );
  }

  Widget _buildAppointmentButton(SingleVCardData vcardData) {
    return _buildActionButton(
      icon: AssetConstants.icDashAppointment,
      label: AppStrings.T.lbl_appointments,
      color: Get.theme.customColors.greenColor!,
      onTap: () {
        NavigationService.navigateWithSlideAnimation(
          AppRoutes.filterdAppointmentPage,
          arguments: {'vcardName': vcardData.name.toString()},
        );
      },
    );
  }

  Widget _buildNfcButton(VCardQrCodeModel qrData) {
    return CustomElevatedButton(
      text: AppStrings.T.lbl_write_nfc,
      onPressed: () => _writeToNfc(qrData.data.first.qrCodeUrl!),
    );
  }

  Future<void> _downloadQrCode(
    VCardQrCodeModel qrData,
    String vcardName,
  ) async {
    final url = qrData.data.first.qrCodeUrl;

    if (url == null || url.isEmpty) {
      _showToast(
        type: ToastificationType.error,
        message: 'QR Code URL not found',
      );
      return;
    }

    try {
      controller.isDownloadingQr.value = true;

      // Check and request permissions with better flow
      final hasPermission = await _checkAndRequestPermissions();
      if (!hasPermission) {
        return;
      }

      // Download using the controller method
      final result = await controller.downloadQrCodeFile(
        url,
        '${vcardName}_qr_code',
      );

      if (result.isNotEmpty) {
        _showToast(
          type: ToastificationType.success,
          message: AppStrings.T.msg_qr_code_downloaded,
          //  'QR Code Downloaded',
        );
      } else {
        _showToast(
          type: ToastificationType.error,
          message: AppStrings.T.msg_qr_code_download_failed,
          // 'Failed to download QR Code',
        );
      }
    } catch (e) {
      String errorMessage = AppStrings.T.msg_qr_code_download_error;
      // 'An error occurred while downloading';

      if (e.toString().contains('permission')) {
        errorMessage = AppStrings.T.msg_permission_denied;
        // 'Permission denied. Please allow access to save QR codes.';
        _showPermissionSettingsDialog();
      } else {
        errorMessage =
            '${AppStrings.T.msg_download_failed_description} ${e.toString()}';
      }

      _showToast(type: ToastificationType.error, message: errorMessage);
    } finally {
      controller.isDownloadingQr.value = false;
    }
  }

  Future<bool> _checkAndRequestPermissions() async {
    try {
      // Check current permission status
      await controller.checkStoragePermission();

      // If already granted, return true
      if (controller.hasStoragePermission.value) {
        return true;
      }

      // Check if we should show rationale
      final shouldShowRationale =
          await controller.shouldShowPermissionRationale();
      if (shouldShowRationale) {
        return await _showPermissionRationaleDialog();
      }

      // Request permission directly
      final granted = await controller.requestStoragePermission();

      if (!granted) {
        // Check if permanently denied
        await controller.checkStoragePermission();
        if (!controller.hasStoragePermission.value) {
          _showPermissionSettingsDialog();
        }
      }

      return granted;
    } catch (e) {
      debugPrint('Permission check error: $e');
      return false;
    }
  }

  Future<bool> _showPermissionRationaleDialog() async {
    bool granted = false;

    await Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(
          AppStrings.T.lbl_permission_required,
          style: Get.textTheme.titleLarge,
        ),
        content: Text(
          AppStrings.T.msg_storage_access_description,
          // 'This app needs access to your device storage to save QR codes to your Downloads folder. The QR codes will be visible in your Gallery app.',
          style: Get.textTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () {
              granted = false;
              NavigationService.navigateBack();
            },
            child: Text(
              AppStrings.T.lbl_cancel,
              style: TextStyle(color: Get.theme.customColors.greyTextColor),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              NavigationService.navigateBack();
              granted = await controller.requestStoragePermission();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.customColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              AppStrings.T.lbl_allow,
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );

    return granted;
  }

  void _showPermissionSettingsDialog() {
    Get.dialog(
      AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Text(
          AppStrings.T.lbl_permission_required,
          style: Get.textTheme.titleLarge,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.T.msg_permission_instructions,
              // 'To save QR codes, please grant storage permission in Settings:',
              style: Get.textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            Text(
              AppStrings.T.msg_permission_detail_instructions,
              // '1. Go to App Settings\n2. Tap Permissions\n3. Allow Photos and media or Storage',
              style: Get.textTheme.bodySmall?.copyWith(
                color: Get.theme.customColors.greyTextColor,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: Get.back,
            child: Text(
              AppStrings.T.lbl_cancel,
              style: TextStyle(color: Get.theme.customColors.greyTextColor),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              NavigationService.navigateBack();
              openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Get.theme.customColors.primaryColor,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              AppStrings.T.lbl_open_settings,
              // 'Open Settings',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  void _confirmDeleteVCard(SingleVCardData vcardData) {
    NavigationService.navigateBack();
    Get.dialog(
      LoadingConfirmationDialog(
        title: AppStrings.T.lbl_delete_vcard,
        message: AppStrings.T.lbl_delete_vcard_subtitle,
        onCancel: Get.back,
        onConfirm: () => controller.deleteVcardById(id: vcardData.id!),
        isLoading: controller.isLoadingVcardDelete,
      ),
    );
  }


  Widget _buildActionButton({
    required String icon,
    required String label,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: Get.theme.customColors.textfieldFillColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CustomImageView(
              imagePath: icon,
              width: 24,
              height: 24,
              color: color,
            ),
            Gap(8.w),
            Expanded(
              child: Text(
                label,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _writeToNfc(String url) async {
    try {
      final isAvailable = await NfcManager.instance.isAvailable();
      if (!isAvailable) {
        _showToast(
          type: ToastificationType.error,
          message: AppStrings.T.lbl_nfc_not_available,
        );
        return;
      }

      _showNfcWriteDialog();
      await _startNfcWriteSession(url);
    } catch (_) {
      NavigationService.navigateBack();
      _showToast(
        type: ToastificationType.error,
        message: AppStrings.T.lbl_nfc_write_failed,
      );
    }
  }

  void _showNfcWriteDialog() {
    Get.bottomSheet(
      Container(
        // padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Get.theme.customColors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
              height: 5.0,
              width: 50.0,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100.r),
                color: Get.theme.customColors.primaryColor?.withValues(
                  alpha: 0.6,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    AppStrings.T.lbl_nfc_ready_to_write,
                    style: Get.textTheme.titleLarge,
                  ),
                  Gap(12),
                  CustomImageView(
                    height: 120.h,
                    width: 120.h,
                    imagePath: AssetConstants.nfcInfo,
                  ),
                  Gap(12),
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: Get.size.width * 0.1,
                    ),
                    child: Text(
                      AppStrings.T.lbl_nfc_hold_device,
                      style: Get.textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Gap(12),
                  SizedBox(
                    width: double.infinity,
                    child: CustomElevatedButton(
                      onPressed: () {
                        NfcManager.instance.stopSession();
                        NavigationService.navigateBack();
                      },
                      text: AppStrings.T.lbl_cancel,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      isDismissible: false,
      enableDrag: false,
    );
  }

  Future<void> _startNfcWriteSession(String url) async {
    await NfcManager.instance.startSession(
      onDiscovered: (tag) async {
        try {
          final ndef = Ndef.from(tag);
          if (ndef == null || !ndef.isWritable) {
            throw Exception(AppStrings.T.lbl_nfc_tag_not_writable);
          }

          final message = NdefMessage([NdefRecord.createUri(Uri.parse(url))]);
          await ndef.write(message);

          NavigationService.navigateBack();
          _showToast(
            type: ToastificationType.success,
            message: AppStrings.T.lbl_nfc_write_success,
          );
        } catch (e) {
          NavigationService.navigateBack();
          _showToast(
            type: ToastificationType.error,
            message: AppStrings.T.lbl_nfc_write_failed,
          );
        } finally {
          await NfcManager.instance.stopSession();
        }
      },
    );
  }

  void _showToast({required ToastificationType type, required String message}) {
    toastification.show(
      type: type,
      style: ToastificationStyle.flatColored,
      alignment: Alignment.topCenter,
      description: Text(message),
      autoCloseDuration: const Duration(seconds: 3),
      showProgressBar: false,
    );
  }
}
