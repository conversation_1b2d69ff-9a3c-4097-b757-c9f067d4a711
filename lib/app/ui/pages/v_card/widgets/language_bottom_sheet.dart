import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class LanguageSelectionBottomSheet extends StatelessWidget {
  final void Function(String) onSelect;

  const LanguageSelectionBottomSheet({required this.onSelect, super.key});

  @override
  Widget build(BuildContext context) {
    final languages = [
      'Arabic',
      'Chinese',
      'English',
      'French',
      'German',
      'Portuguese',
      'Russian',
      'Spanish',
      'Turkish',
    ];

    return SafeArea(
      child: Column(
        children: [
          Center(
            child: Container(
              margin: EdgeInsets.only(left: 20.0.w, right: 20.0.w, top: 8.0.h),
              height: 5.0,
              width: 50.0,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100.r),
                color: Get.theme.customColors.primaryColor?.withValues(
                  alpha: 0.6,
                ),
              ),
            ),
          ),
          Expanded(
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: languages.length,
              separatorBuilder:
                  (context, index) => Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: const Divider(height: 1),
                  ),
              itemBuilder: (context, index) {
                final lang = languages[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: ListTile(
                    title: Text(
                      lang,
                      style: Get.theme.textTheme.bodyLarge!.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    onTap: () => onSelect(lang),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
