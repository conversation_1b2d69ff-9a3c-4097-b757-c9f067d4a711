import 'package:v_card/app/utils/helpers/exporter.dart';

class VCardShimmer extends StatelessWidget {
  final bool isBottomSheet;
  final bool controller;

  const VCardShimmer({
    super.key,
    this.isBottomSheet = false,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return isBottomSheet ? _buildBottomSheetShimmer() : _buildMainShimmer();
  }

  Widget _buildMainShimmer() {
    return ListView.separated(
      padding: EdgeInsets.symmetric(vertical: controller ? 78.h : 8.h),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Card(
            elevation: 3,
            margin: EdgeInsets.zero,
            child: Container(
              padding: EdgeInsets.all(16),
              child: Row(
                children: [
                  ShimmerBox(
                    width: 50,
                    height: 50,
                    borderRadius: BorderRadius.circular(100),
                  ),
                  Gap(8.w),
                  // Text placeholders
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShimmerBox(
                        width: 120,
                        height: 16,
                        borderRadius: BorderRadius.circular(100),
                      ),
                      Gap(8),
                      ShimmerBox(
                        width: 80,
                        height: 14,
                        borderRadius: BorderRadius.circular(100),
                      ),
                    ],
                  ),
                  // Trailing icon placeholder
                  Spacer(),
                  ShimmerBox(
                    width: 24,
                    height: 24,
                    borderRadius: BorderRadius.circular(100),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      separatorBuilder: (BuildContext context, int index) {
        return Gap(8.h);
      },
    );
  }

  Widget _buildBottomSheetShimmer() {
    return Column(
      children: [
        // Header with avatar
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                ShimmerBox(
                  width: 50,
                  height: 50,
                  borderRadius: BorderRadius.circular(100),
                ),
                Gap(10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ShimmerBox(
                      width: 50,
                      height: 18,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    Gap(4),
                    ShimmerBox(
                      width: 120,
                      height: 14,
                      borderRadius: BorderRadius.circular(100),
                    ),
                    Gap(4),
                    ShimmerBox(
                      width: 180,
                      height: 14,
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ],
                ),
              ],
            ),
            Container(
              width: 90.w,
              height: 32.h,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(100),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8,
                ),
                child: ShimmerBox(
                  width: 60.w,
                  height: 14.h,
                  borderRadius: BorderRadius.circular(100),
                ),
              ),
            ),
          ],
        ),
        Gap(12),

        Row(
          children: [
            Expanded(child: _buildShimmerActionButton()),
            Gap(6),
            Expanded(child: _buildShimmerActionButton()),
          ],
        ),
        Gap(6),
        Row(
          children: [
            Expanded(child: _buildShimmerActionButton()),
            Gap(6),
            Expanded(child: _buildShimmerActionButton()),
          ],
        ),
        Gap(20),
        ShimmerBox(width: double.infinity, height: 48),
      ],
    );
  }

  Widget _buildShimmerActionButton() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          ShimmerBox(width: 24, height: 24),
          Gap(8),
          ShimmerBox(width: 60, height: 16),
        ],
      ),
    );
  }
}
