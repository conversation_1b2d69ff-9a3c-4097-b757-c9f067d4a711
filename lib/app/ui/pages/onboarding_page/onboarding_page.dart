import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:v_card/app/controllers/onboarding_controller.dart';
import '../../../utils/helpers/exporter.dart';

class OnboardingPage extends GetItHook<OnboardingController> {
  const OnboardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Get.theme;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarColor: theme.customColors.white,
        statusBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: theme.customColors.bgOneColor,
        body: Stack(
          children: [
            Positioned(
              top: 0,
              child: CustomImageView(imagePath: AssetConstants.svgOnboardingBg),
            ),
            PageView(
              allowImplicitScrolling: true,
              pageSnapping: true,
              controller: controller.pageController,
              onPageChanged: (index) => controller.currentPage.value = index,
              children: [
                _buildOnboardingSlide(
                  imagePath: AssetConstants.ob1,
                  title: 'Explore Fashion',
                  description:
                      'Lorem ipsum dolor sit amet consectetur. Lorem id sit',
                ),
                _buildOnboardingSlide(
                  imagePath: AssetConstants.ob2,
                  title: 'Discover Styles',
                  description:
                      'Lorem ipsum dolor sit amet consectetur. Lorem id sit',
                ),
                _buildOnboardingSlide(
                  imagePath: AssetConstants.ob3,
                  title: 'Latest Trends',
                  description:
                      'Lorem ipsum dolor sit amet consectetur. Lorem id sit',
                ),
                _buildOnboardingSlide(
                  imagePath: AssetConstants.ob4,
                  title: 'Get Started',
                  description:
                      'Lorem ipsum dolor sit amet consectetur. Lorem id sit',
                ),
              ],
            ),

            Positioned(
              top: 30,
              left: 0,
              right: 0,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Back button
                    SizedBox.shrink(),
                    // GestureDetector(
                    //   onTap: controller.previousPage,
                    //   child: Container(
                    //     height: 40.h,
                    //     width: 40.w,
                    //     decoration: BoxDecoration(
                    //       color: theme.customColors.white,
                    //       borderRadius: BorderRadius.circular(8),
                    //     ),
                    //     child: CustomImageView(
                    //       imagePath: AssetConstants.icAppbarBack,
                    //       margin: const EdgeInsets.all(12),
                    //     ),
                    //   ),
                    // ),
                    TextButton(
                      onPressed: controller.skipOnboarding,
                      child: Text(
                        'Skip',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.customColors.black,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Bottom controls with percent indicator
            Positioned(
              bottom: 60,
              left: 0,
              right: 0,
              child: Obx(
                () => GestureDetector(
                  onTap: controller.nextPage,
                  child: Center(
                    child: CircularPercentIndicator(
                      radius: 40.w,
                      lineWidth: 3.0,
                      percent:
                          (controller.currentPage.value + 1) /
                          controller.pageCount.value,
                      center: CircleAvatar(
                        radius: 32.w,
                        backgroundColor: Get.theme.customColors.primaryColor,
                        child: CustomImageView(
                          height: 18.h,
                          imagePath: AssetConstants.icRightArrow,
                          color: Get.theme.customColors.white,
                        ),
                      ),
                      backgroundColor: Color(0xFFF1F3F6),
                      progressColor: Get.theme.customColors.primaryColor!,
                      circularStrokeCap: CircularStrokeCap.round,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingSlide({
    required String imagePath,
    required String title,
    required String description,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomImageView(
            imagePath: imagePath,
            fit: BoxFit.contain,
            height: Get.height * 0.4,
          ),
          Gap(16.h),
          Text(
            title,
            style: Get.theme.textTheme.headlineMedium?.copyWith(
              color: Get.theme.customColors.primaryColor,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          Gap(16.h),
          Text(
            description,
            style: Get.theme.textTheme.bodyMedium?.copyWith(
              color: Get.theme.customColors.greyTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  @override
  bool get canDisposeController => true;

  @override
  void onDispose() {}

  @override
  void onInit() {}
}
