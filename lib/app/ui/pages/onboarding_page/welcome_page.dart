import 'package:get/get.dart';
import 'package:v_card/app/controllers/onboarding_controller.dart';
import '../../../utils/helpers/exporter.dart';

class WelcomePage extends GetItHook<OnboardingController> {
  const WelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Get.theme;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarColor: theme.customColors.white,
        statusBarIconBrightness: Brightness.light,
      ),
      child: Scaffold(
        backgroundColor: theme.customColors.bgOneColor,
        body: Stack(
          children: [
            // Background
            Positioned(
              top: 0,
              child: Stack(
                children: [
                  CustomImageView(
                    imagePath: AssetConstants.pngWelcome,
                    height: Get.height * 0.78,
                    width: Get.width,
                    fit: BoxFit.cover,
                  ),
                  Positioned(
                    top: Get.height * 0.25,
                    child: Center(
                      child: SizedBox(
                        width: Get.width,
                        child: Container(
                          margin: EdgeInsets.symmetric(
                            horizontal: Get.width * 0.2,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            Padding(
              padding: EdgeInsets.only(left: 24.w, right: 70.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gap(Get.height * 0.64),

                  // Welcome text
                  Text(
                    'Welcome',
                    style: theme.textTheme.displayLarge?.copyWith(
                      color: Color(0xFF424242),
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  Gap(16.h),

                  // Description
                  Text(
                    'Lorem ipsum dolor sit amet consectetur. Lorem id sit',
                    style: theme.textTheme.displayLarge?.copyWith(
                      color: Color(0xFFBDBDBD),
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
            // Next button
            Positioned(
              bottom: 40,
              right: 16.w,
              child: SizedBox(
                height: 36.h,
                width: Get.width * 0.4,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Continue',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.customColors.greyTextColor,
                        fontWeight: FontWeight.w500,
                        fontSize: 14.sp,
                      ),
                    ),
                    Gap(12.w),
                    CustomImageView(
                      onTap:
                          () => NavigationService.navigateWithSlideAnimation(
                            AppRoutes.onboardingPage,
                          ),
                      imagePath: AssetConstants.svgWlcomeNextButton,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {}
}
