import 'package:get/get.dart';
import 'package:v_card/app/controllers/storage_controller.dart';
import 'package:v_card/app/data/model/storage/storage_model.dart';
import 'package:v_card/app/ui/pages/storage/widgets/storage_shimme.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:fl_chart/fl_chart.dart';

class StoragePage extends GetItHook<StorageController> {
  const StoragePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppbar(
        title: AppText(
          AppStrings.T.lbl_storage,
          style: Get.theme.textTheme.bodyLarge?.copyWith(
            fontSize: 18.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      body: Obx(() {
        final isLoading = controller.isLoadingStorage.value;
        final state = controller.storageState.value;
        final storageData = controller.storageData.value?.data;

        if (isLoading) {
          return StorageShimmerEffect();
        }

        if (state is FailedState) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  state.error.title,
                  style: Get.theme.textTheme.bodyMedium!.copyWith(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Gap(8.h),
                Text(state.error.description),
                Gap(16.h),
                CustomElevatedButton(
                  checkConnectivity: true,
                  text: AppStrings.T.retry,
                  onPressed: () => controller.getStorageData(),
                ),
              ],
            ),
          );
        }

        if (storageData == null) {
          return Center(child: Text(AppStrings.T.msg_no_storage_data));
        }

        return RefreshIndicator(
          onRefresh: () => controller.getStorageData(),
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildStorageSummaryCard(storageData),
                Gap(16.h),
                _buildCategoryBreakdownSection(storageData),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildStorageSummaryCard(StorageData storageData) {
    return Container(
      clipBehavior: Clip.hardEdge,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Get.theme.customColors.black!.withValues(alpha: 0.2),
            blurRadius: 5,
            offset: Offset(0, 0),
          ),
        ],
      ),
      // elevation: 6,
      // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.T.lbl_storage_usage_overview,
              style: Get.theme.textTheme.bodyMedium!.copyWith(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            Gap(24.h),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  // Pie Chart
                  SizedBox(
                    width: Get.width * 0.45,
                    height: Get.width * 0.45,
                    child: PieChart(
                      PieChartData(
                        sectionsSpace: 2,
                        centerSpaceRadius: 40,
                        sections: [
                          PieChartSectionData(
                            color: Get.theme.customColors.blueColor,
                            value: storageData.storagePercentageUsed,
                            title:
                                '${storageData.storagePercentageUsed.toStringAsFixed(1)}%',
                            radius: 40,
                            titleStyle: Get.theme.textTheme.bodyMedium!
                                .copyWith(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                  color: Get.theme.customColors.white,
                                ),
                          ),
                          PieChartSectionData(
                            color: Get.theme.customColors.greyTextColor!
                                .withValues(alpha: 0.3),
                            value: 100 - storageData.storagePercentageUsed,
                            title:
                                '${(100 - storageData.storagePercentageUsed).toStringAsFixed(1)}%',
                            radius: 40,
                            titleStyle: Get.theme.textTheme.bodyMedium!
                                .copyWith(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Gap(16.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildLegendItem(
                            AppStrings.T.lbl_used_storage,
                            Get.theme.customColors.blueColor!,
                          ),
                          Gap(8.h),
                          _buildLegendItem(
                            AppStrings.T.lbl_unused_storage,
                            Get.theme.customColors.greyTextColor!.withValues(
                              alpha: 0.3,
                            ),
                          ),
                        ],
                      ),
                      Gap(16.h),

                      LinearProgressIndicator(
                        value: storageData.storagePercentageUsed / 100,
                        backgroundColor: Colors.grey.shade200,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          storageData.storagePercentageUsed > 90
                              ? Colors.red
                              : Colors.blue,
                        ),
                        minHeight: 8,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      Gap(8.h),
                      AppText(
                        '${storageData.totalUsedMb.toStringAsFixed(2)}/${storageData.storageLimitMb} MB',
                        style: Get.theme.textTheme.bodyMedium!.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Gap(8.h),
                      Text(
                        '${AppStrings.T.lbl_free_space}: ${(storageData.storageLimitMb - storageData.totalUsedMb).toStringAsFixed(2)} MB',
                        style: Get.theme.textTheme.bodyMedium!.copyWith(
                          color: Get.theme.customColors.greyTextColor
                              ?.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdownSection(StorageData storageData) {
    final sortedCategories =
        storageData.storageData.entries.toList()
          ..sort((a, b) => b.value.compareTo(a.value));

    return Container(
      clipBehavior: Clip.hardEdge,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Get.theme.customColors.black!.withValues(alpha: 0.2),
            blurRadius: 5,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,

          children: [
            Text(
              AppStrings.T.lbl_storage_by_category,
              style: Get.theme.textTheme.bodyMedium!.copyWith(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            Gap(24.h),

            // SizedBox(
            //   height: 220.h,
            //   child: PieChart(
            //     PieChartData(
            //       sectionsSpace: 2,
            //       centerSpaceRadius: 50,
            //       sections: _getCategoryPieChartSections(sortedCategories),
            //     ),
            //   ),
            // ),
            // Gap(8.h),

            // Category Legend
            // Wrap(
            //   spacing: 16,
            //   runSpacing: 8,
            //   children: _getCategoryLegendItems(sortedCategories),
            // ),

            // Gap(24.h),

            // List of category usage
            ...sortedCategories.map(
              (entry) => _buildCategoryUsageItem(
                entry.key,
                entry.value,
                storageData.totalUsedMb,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // List<PieChartSectionData> _getCategoryPieChartSections(
  //   List<MapEntry<String, double>> categories,
  // ) {
  //   final colors = [
  //     Colors.blue,
  //     Colors.green,
  //     Colors.orange,
  //     Colors.purple,
  //     Colors.red,
  //     Colors.teal,
  //     Colors.amber,
  //     Colors.cyan,
  //     Colors.indigo,
  //   ];

  //   return List.generate(categories.length, (index) {
  //     final category = categories[index];
  //     final color = colors[index % colors.length];

  //     return PieChartSectionData(
  //       color: color,
  //       value: category.value,
  //       title: '',
  //       radius: 50,
  //       showTitle: false,
  //     );
  //   });
  // }

  // List<Widget> _getCategoryLegendItems(
  //   List<MapEntry<String, double>> categories,
  // ) {
  //   final colors = [
  //     Colors.blue,
  //     Colors.green,
  //     Colors.orange,
  //     Colors.purple,
  //     Colors.red,
  //     Colors.teal,
  //     Colors.amber,
  //     Colors.cyan,
  //     Colors.indigo,
  //   ];

  //   return List.generate(categories.length, (index) {
  //     final category = categories[index];
  //     final color = colors[index % colors.length];

  //     return _buildLegendItem(_formatCategoryName(category.key), color);
  //   });
  // }

  Widget _buildCategoryUsageItem(
    String category,
    double usageMb,
    double totalUsedMb,
  ) {
    final percentage = (usageMb / totalUsedMb) * 100;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatCategoryName(category),
                style: Get.theme.textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${usageMb.toStringAsFixed(2)} MB (${percentage.toStringAsFixed(1)}%)',
                style: Get.theme.textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          Gap(4.h),
          LinearProgressIndicator(
            value: usageMb / totalUsedMb,
            backgroundColor: Colors.grey.shade200,
            minHeight: 6,
            borderRadius: BorderRadius.circular(3),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String title, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        Gap(4.h),
        Text(
          title,
          style: Get.theme.textTheme.bodyMedium!.copyWith(fontSize: 12),
        ),
      ],
    );
  }

  String _formatCategoryName(String name) {
    return name.substring(0, 1).toUpperCase() + name.substring(1);
  }

  @override
  bool get canDisposeController => false;

  @override
  void onDispose() {}

  @override
  void onInit() {
    controller.getStorageData();
  }
}
