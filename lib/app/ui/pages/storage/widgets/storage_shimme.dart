import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class StorageShimmerEffect extends StatelessWidget {
  const StorageShimmerEffect({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Storage Summary Card Shimmer
          _buildStorageSummaryShimmer(),
          Gap(16.h),
          // Category Breakdown Shimmer
          _buildCategoryBreakdownShimmer(),
        ],
      ),
    );
  }

  Widget _buildStorageSummaryShimmer() {
    return Container(
      clipBehavior: Clip.hardEdge,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Get.theme.customColors.black!.withValues(alpha: 0.2),
            blurRadius: 5,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerBox(
              width: 200,
              height: 24,
              borderRadius: BorderRadius.circular(4),
            ),
            Gap(24.h),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  // Pie Chart Shimmer
                  Stack(
                    children: [
                      SizedBox(
                        width: Get.width * 0.45,
                        height: Get.width * 0.45,
                        child: ShimmerBox(
                          width: Get.width * 0.45,
                          height: Get.width * 0.45,
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                      Positioned.fill(
                        child: Center(
                          child: CircleAvatar(
                            radius: Get.width * 0.45 / 4,
                            backgroundColor: Get.theme.customColors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Gap(16.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _buildLegendItemShimmer(),
                          Gap(8.h),
                          _buildLegendItemShimmer(),
                        ],
                      ),
                      Gap(16.h),
                      ShimmerBox(
                        height: 8,
                        width: double.infinity,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      Gap(8.h),
                      ShimmerBox(
                        width: 150,
                        height: 16,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      Gap(8.h),
                      ShimmerBox(
                        width: 200,
                        height: 14,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdownShimmer() {
    return Container(
      clipBehavior: Clip.hardEdge,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Get.theme.customColors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Get.theme.customColors.black!.withValues(alpha: 0.2),
            blurRadius: 5,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ShimmerBox(
              width: 220,
              height: 24,
              borderRadius: BorderRadius.circular(4),
            ),
            Gap(24.h),
            ...List.generate(5, (index) => _buildCategoryItemShimmer()),
          ],
        ),
      ),
    );
  }

  Widget _buildLegendItemShimmer() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        ShimmerBox(
          width: 12,
          height: 12,
          borderRadius: BorderRadius.circular(100),
        ),
        Gap(4.h),
        ShimmerBox(
          width: 80,
          height: 14,
          borderRadius: BorderRadius.circular(4),
        ),
      ],
    );
  }

  Widget _buildCategoryItemShimmer() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              ShimmerBox(
                width: 120,
                height: 18,
                borderRadius: BorderRadius.circular(4),
              ),
              ShimmerBox(
                width: 140,
                height: 18,
                borderRadius: BorderRadius.circular(4),
              ),
            ],
          ),
          Gap(4.h),
          ShimmerBox(
            height: 6,
            width: double.infinity,
            borderRadius: BorderRadius.circular(3),
          ),
        ],
      ),
    );
  }
}
