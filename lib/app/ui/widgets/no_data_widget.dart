import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class NoDataWidget extends StatelessWidget {
  final String? imagePath;
  final String message;
  final double imageHeight;
  final EdgeInsets padding;

  const NoDataWidget({
    super.key,
    this.imagePath,
    required this.message,
    this.imageHeight = 220,
    this.padding = const EdgeInsets.only(top: 180.0),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CustomImageView(
              height: imageHeight,
              imagePath: imagePath ?? AssetConstants.noData,
            ),
            Gap(20.h),
            Text(
              message,
              style: Get.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

class NoInternetWidget extends StatelessWidget {
  final String message;
  final double imageHeight;

  const NoInternetWidget({
    super.key,
    required this.message,
    this.imageHeight = 60,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CustomImageView(
            height: imageHeight,
            imagePath: AssetConstants.noInternet,
          ),
          Gap(20.h),
          Text(
            message,
            style: Get.textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
