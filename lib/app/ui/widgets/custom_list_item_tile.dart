import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomListItemTile extends StatelessWidget {
  final String imageUrl;
  final String title;
  final String? subtitle;
  final String id;
  final String vcardId;
  final Function() onDelete;
  final Function() onEdit;
  final Function()? onTap;
  final bool isLoadingDelete;
  final bool isServiceTile;
  final Widget? iconWidget;

  const CustomListItemTile({
    super.key,
    required this.imageUrl,
    required this.title,
    this.subtitle,
    required this.id,
    required this.vcardId,
    required this.onDelete,
    required this.onEdit,
    this.onTap,
    required this.isLoadingDelete,
    this.isServiceTile = false,
    this.iconWidget,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        elevation: 3,
        color: Get.theme.customColors.white,
        margin: const EdgeInsets.symmetric(vertical: 8),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              iconWidget ??
                  SizedBox(
                    height: 50,
                    width: 50,
                    child: CustomImageView(
                      imagePath: imageUrl,
                      fit: BoxFit.cover,
                      radius: BorderRadius.circular(100.r),
                    ),
                  ),
              Gap(8.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(title, style: Get.theme.textTheme.bodyLarge),
                    if (subtitle != null) ...[
                      Gap(0.h),
                      Text(
                        subtitle!,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: Get.theme.textTheme.labelSmall?.copyWith(
                          color: Get.theme.customColors.primaryColor,
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildActionButton(
                    iconPath: AssetConstants.icDelete2,
                    onTap: onDelete,
                    isLoading: isLoadingDelete,
                  ),
                  Gap(6.w),
                  _buildActionButton(
                    iconPath: AssetConstants.icEdit,
                    onTap: onEdit,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String iconPath,
    required Function() onTap,
    bool isLoading = false,
  }) {
    return InkWell(
      onTap: isLoading ? null : onTap,
      child: Container(
        width: 32.w,
        height: 32.h,
        decoration: BoxDecoration(
          color: Get.theme.customColors.darkBlueColor?.withValues(alpha: 0.05),
          shape: BoxShape.circle,
        ),
        child:
            isLoading
                ? const Center(
                  child: SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                )
                : CustomImageView(
                  imagePath: iconPath,
                  margin: const EdgeInsets.all(8.0),
                ),
      ),
    );
  }
}

class ListTileShimmerLoading extends StatelessWidget {
  const ListTileShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      itemCount: 10,
      itemBuilder: (context, index) {
        return Card(
          color: Get.theme.customColors.white,
          elevation: 3,
          margin: const EdgeInsets.symmetric(vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // Product icon shimmer
                ShimmerBox(
                  height: 50,
                  width: 50,
                  borderRadius: BorderRadius.circular(100.r),
                ),
                Gap(8.w),
                // Product details shimmer
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Product name shimmer
                      ShimmerBox(height: 18.h, width: 150.w),
                      Gap(4.h),
                      // Product URL shimmer
                      ShimmerBox(height: 14.h, width: 120.w),
                    ],
                  ),
                ),
                // Action buttons shimmer
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Delete button shimmer
                    ShimmerBox(
                      width: 32.w,
                      height: 32.h,
                      borderRadius: BorderRadius.circular(100.r),
                    ),
                    Gap(6.w),
                    // Edit button shimmer
                    ShimmerBox(
                      width: 32.w,
                      height: 32.h,
                      borderRadius: BorderRadius.circular(100.r),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
