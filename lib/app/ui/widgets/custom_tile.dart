import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomTile extends StatelessWidget {
  const CustomTile({super.key, required this.icon, required this.title, required this.onTap});

  final String icon;
  final String title;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.only(bottom: 12.h),
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            color: Get.theme.customColors.white,
            boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 8, offset: Offset(0, 4))],
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.0.h),
            child: Row(
              children: [
                Container(
                  height: 40.0.h,
                  width: 40.0.h,
                  decoration: BoxDecoration(shape: BoxShape.circle, color: Get.theme.customColors.textfieldFillColor),
                  alignment: Alignment.center,
                  child: CustomImageView(
                    imagePath: icon,
                    margin: EdgeInsets.all(8.0),
                    height: 20.0,
                    width: 20.0,
                    color: Get.theme.customColors.greyTextColor,
                  ),
                ),
                Gap(16.w),
                AppText(
                  title,
                  style: Get.theme.textTheme.bodyLarge!.copyWith(
                    color: Get.theme.customColors.darkGreyTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                CustomImageView(imagePath: AssetConstants.icRightArrow),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
