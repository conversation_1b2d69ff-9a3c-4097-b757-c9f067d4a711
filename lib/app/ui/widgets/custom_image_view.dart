// ignore_for_file: deprecated_member_use, depend_on_referenced_packages

import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomImageView extends StatelessWidget {
  const CustomImageView({
    super.key,
    this.imagePath,
    this.imageData,
    this.height,
    this.width,
    this.color,
    this.fit,
    this.alignment,
    this.onTap,
    this.radius,
    this.margin,
    this.border,
  });

  /// [imagePath] is required parameter for showing image

  final String? imagePath;
  final Uint8List? imageData;
  final double? height;
  final double? width;
  final Color? color;
  final BoxFit? fit;
  final Alignment? alignment;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  final BorderRadius? radius;
  final BoxBorder? border;

  @override
  Widget build(BuildContext context) {
    if (alignment != null) {
      return Align(alignment: alignment!, child: _buildWidget());
    }

    return _buildWidget();
  }

  Widget _buildWidget() {
    var buildCircleImage = _buildCircleImage();
    if (onTap != null) {
      buildCircleImage = InkWell(onTap: onTap, child: buildCircleImage);
    }
    if (margin != null) {
      buildCircleImage = Padding(
        padding: margin ?? EdgeInsets.zero,
        child: buildCircleImage,
      );
    }
    return buildCircleImage;
  }

  Widget _buildCircleImage() {
    if (radius != null) {
      return ClipRRect(
        borderRadius: radius ?? BorderRadius.zero,
        child: _buildImageWithBorder(),
      );
    }
    return _buildImageWithBorder();
  }

  Widget _buildImageWithBorder() {
    if (border != null) {
      return Container(
        decoration: BoxDecoration(border: border, borderRadius: radius),
        child: _buildImageView(),
      );
    }
    return _buildImageView();
  }

  Widget _buildImageView() {
    if (imagePath == null || imagePath!.trim().isEmpty) {
      return Image.asset(
        'assets/images/png/other/image_not_found.png',
        height: height,
        width: width,
        fit: fit ?? BoxFit.cover,
      );
    }

    switch (imagePath!.imageType) {
      case ImageType.svg:
        return SizedBox(
          height: height,
          width: width,
          child: SvgPicture.asset(
            imagePath!,
            height: height,
            width: width,
            fit: fit ?? BoxFit.contain,
            color: color,
          ),
        );
      case ImageType.file:
        return Image.file(
          File(imagePath!),
          height: height,
          width: width,
          fit: fit ?? BoxFit.cover,
          color: color,
        );
      case ImageType.network:
        return CachedNetworkImage(
          height: height,
          width: width,
          fit: fit,
          imageUrl: imagePath!,
          color: color,
          filterQuality: FilterQuality.medium,
          placeholder:
              (context, url) => SizedBox(
                height: 30.h,
                width: 30.h,
                child: LinearProgressIndicator(
                  color: Colors.grey.shade200,
                  backgroundColor: Colors.grey.shade100,
                ),
              ),
          cacheManager: CacheManager(
            Config(
              "infyVcard",
              stalePeriod: const Duration(days: 1),
              maxNrOfCacheObjects: 1000,
            ),
          ),
          errorWidget:
              (context, url, error) => Image.asset(
                'assets/images/png/other/image_not_found.png',
                height: height,
                width: width,
                fit: fit ?? BoxFit.cover,
              ),
        );
      case ImageType.png:
        return Image.asset(
          imagePath!,
          height: height,
          width: width,
          fit: fit ?? BoxFit.cover,
          color: color,
        );
    }
  }
}

extension ImageTypeExtension on String {
  ImageType get imageType {
    if (startsWith('http') || startsWith('https')) {
      return ImageType.network;
    } else if (endsWith('.svg')) {
      return ImageType.svg;
    } else if (startsWith('file://') || startsWith('/data')) {
      return ImageType.file;
    } else {
      return ImageType.png;
    }
  }
}

enum ImageType { svg, png, network, file }
