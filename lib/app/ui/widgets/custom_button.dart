import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:v_card/app/ui/widgets/no_internet_widget.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomElevatedButton extends StatelessWidget {
  const CustomElevatedButton({
    super.key,
    this.decoration,
    this.leftIcon,
    this.rightIcon,
    this.margin,
    this.onPressed,
    this.buttonStyle,
    this.alignment,
    this.buttonTextStyle,
    this.isDisabled = false,
    this.height,
    this.width,
    this.textPadding,
    this.iconSpacing,
    this.isLoading = false,
    this.secondary = false,
    this.borderRadius,
    required this.text,
    this.checkConnectivity = false,
  });

  final BoxDecoration? decoration;
  final Widget? leftIcon;
  final Widget? rightIcon;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onPressed;
  final ButtonStyle? buttonStyle;
  final Alignment? alignment;
  final TextStyle? buttonTextStyle;
  final bool isDisabled;
  final double? height;
  final double? width;
  final EdgeInsets? textPadding;
  final double? iconSpacing;
  final bool isLoading;
  final String text;
  final bool secondary;
  final double? borderRadius;
  final bool checkConnectivity;

  void _showNoInternetDialog() {
    Get.dialog(const NoInternetDialog());
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 55.h,
      width: width ?? MediaQuery.of(context).size.width,
      margin: margin,
      decoration: decoration,
      child: ElevatedButton(
        style:
            buttonStyle ??
            ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(borderRadius ?? 15),
              ),
              padding: EdgeInsets.zero,
              textStyle: Get.textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.w700,
              ),
              overlayColor: Get.theme.customColors.white,
            ),

        onPressed:
            isDisabled
                ? null
                : () async {
                  HapticFeedback.lightImpact();
                  if (onPressed == null) return;

                  if (checkConnectivity) {
                    final networkHelper = NetworkHelper();

                    final hasConnection =
                        await networkHelper.hasNetworkConnection();
                    if (!hasConnection) {
                      _showNoInternetDialog();
                      return;
                    }
                  }

                  onPressed!();
                },
        child: Padding(
          padding: textPadding ?? EdgeInsets.zero,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (leftIcon != null && !isLoading) leftIcon!,
              if (leftIcon != null && iconSpacing != null)
                SizedBox(width: iconSpacing),
              if (isLoading)
                CupertinoActivityIndicator(
                  color:
                      secondary
                          ? Get.theme.customColors.primaryColor
                          : Get.theme.customColors.white,
                )
              else
                Center(
                  child: Text(
                    text,
                    style:
                        secondary
                            ? buttonTextStyle ??
                                Theme.of(
                                  context,
                                ).textTheme.labelLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16.sp,
                                  overflow: TextOverflow.ellipsis,
                                  color: Get.theme.customColors.primaryColor,
                                )
                            : buttonTextStyle ??
                                Theme.of(
                                  context,
                                ).textTheme.labelLarge?.copyWith(
                                  fontWeight: FontWeight.w700,
                                  fontSize: 16.sp,
                                  overflow: TextOverflow.ellipsis,
                                  color:
                                      isDisabled
                                          ? Theme.of(context).disabledColor
                                          : Theme.of(
                                            context,
                                          ).colorScheme.onPrimary,
                                ),
                  ),
                ),
              if (rightIcon != null && iconSpacing != null)
                SizedBox(width: iconSpacing),
              if (rightIcon != null) rightIcon!,
            ],
          ),
        ),
      ),
    );
  }
}
