import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class NoInternetDialog extends StatelessWidget {
  const NoInternetDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            CustomImageView(
              imagePath: AssetConstants.noInternet,
              height: 60.h,
              width: 60.h,
              fit: BoxFit.contain,
              color: Colors.redAccent,
            ),
            Gap(4.h),
            Text(
              AppStrings.T.lbl_no_network_title,
              style: Get.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                fontSize: 16.sp,
              ),
              textAlign: TextAlign.center,
            ),
            Gap(4.h),
            Text(
              AppStrings.T.msg_no_network,
              style: Get.textTheme.bodyMedium?.copyWith(
                color: Colors.grey[700],
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
            Gap(16.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.size.width * 0.2),
              child: CustomElevatedButton(
                height: 32.h,
                text: AppStrings.T.lbl_ok,
                onPressed: () => NavigationService.navigateBack(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ExceptionWidget extends StatelessWidget {
  final String imagePath;
  final String title;
  final String subtitle;
  final VoidCallback onButtonPressed;
  final String buttonText;

  const ExceptionWidget({
    super.key,
    required this.imagePath,
    required this.title,
    required this.subtitle,
    required this.onButtonPressed,
    required this.buttonText,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            imagePath,
            height: 80,
            color: Color(0xffFF7731),
            errorBuilder: (_, __, ___) => Icon(Icons.error_outline),
          ),
          Gap(20.h),
          Text(
            title,
            style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          Gap(10.h),
          Text(subtitle, textAlign: TextAlign.center),
          Gap(20.h),
          ElevatedButton(
            onPressed: onButtonPressed,
            child: Text(
              buttonText,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
