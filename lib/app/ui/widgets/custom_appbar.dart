import 'dart:io';
import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppbar({
    super.key,
    this.title,
    this.appbarBgColor,
    this.leadingIcon,
    this.hasLeadingIcon = true,
    this.actions,
    this.onLeadingTap,
  });

  final Widget? title;
  final Color? appbarBgColor;
  final Widget? leadingIcon;
  final bool hasLeadingIcon;
  final List<Widget>? actions;
  final VoidCallback? onLeadingTap;

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 0,
      shadowColor: Get.theme.customColors.black!.withValues(alpha: 0.2),
      child: Container(
        color: appbarBgColor ?? Get.theme.customColors.white,
        child: Padding(
          padding: EdgeInsets.only(
            top: Platform.isIOS ? 55.0.h : 40.0.w,
            right: 20.w,
            left: 20.w,
            bottom: 8.h,
          ),
          child: Stack(
            alignment: Alignment.center,
            children: [
              if (hasLeadingIcon)
                Row(
                  children: [
                    InkWell(
                      onTap: onLeadingTap ?? () => NavigationService.navigateBack(),
                      child:
                          leadingIcon ??
                          Container(
                            height: 40.h,
                            width: 40.w,
                            decoration: BoxDecoration(
                              color: Get.theme.customColors.white,
                              borderRadius: BorderRadius.circular(12.0),
                              border: Border.all(
                                color: Get.theme.customColors.borderColor!,
                              ),
                            ),
                            child: CustomImageView(
                              imagePath: AssetConstants.icAppbarBack,
                              margin: const EdgeInsets.all(12),
                            ),
                          ),
                    ),
                  ],
                ),
              Align(
                alignment:
                    hasLeadingIcon ? Alignment.center : Alignment.centerLeft,
                child: title,
              ),
              Positioned(
                right: 0,
                child: Row(children: [if (actions != null) ...actions!]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(kToolbarHeight + 40.h);
}
