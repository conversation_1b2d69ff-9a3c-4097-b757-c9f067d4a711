import 'package:v_card/app/utils/helpers/exporter.dart';

class CustomFormShimmerLoading extends StatelessWidget {
  const CustomFormShimmerLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Center(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Gap(20.h),
                  _buildShimmerWidget(width: 150, height: 20, radius: 8),
                  Gap(10.h),
                  _buildShimmerWidget(height: 150, width: 150, radius: 16),
                ],
              ),
            ),
            Gap(10.h),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerWidget(width: 120, height: 16, radius: 4),
                Gap(5.h),
                _buildShimmerTextField(),
              ],
            ),

            Gap(10.h),

            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildShimmerWidget(width: 100, height: 16, radius: 4),
                Gap(5.h),
                _buildShimmerTextField(height: 120),
              ],
            ),

            Gap(20.h),

            _buildShimmerWidget(width: double.infinity, height: 50, radius: 8),
            Gap(16.h),
            _buildShimmerWidget(width: double.infinity, height: 50, radius: 8),
          ],
        ),
      ),
    );
  }

  Widget _buildShimmerWidget({
    required double width,
    required double height,
    double radius = 4,
  }) {
    return ShimmerBox(
      width: width,
      height: height,
      borderRadius: BorderRadius.circular(radius),
    );
  }

  Widget _buildShimmerTextField({double height = 55}) {
    return ShimmerBox(width: double.infinity, height: height);
  }
}
