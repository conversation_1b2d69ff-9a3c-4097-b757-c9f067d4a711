import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';
import 'package:v_card/app/utils/themes/button_theme.dart';

class LoadingConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;
  final RxBool? isLoading;

  const LoadingConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    required this.onConfirm,
    required this.onCancel,
    this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(
      () => IgnorePointer(
        ignoring: isLoading?.value ?? false,
        child: Dialog(
          insetPadding: EdgeInsets.only(
            top: 30.0.h,
            left: 20.0.w,
            right: 20.0.w,
            bottom: 20.0.h,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.white,
          child: Container(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  style: Get.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    fontSize: 18.0.sp,
                  ),
                ),
                Gap(12.h),
                Text(
                  message,
                  textAlign: TextAlign.center,
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: Colors.black54,
                    fontSize: 16.0.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                Gap(30.h),
                Row(
                  children: [
                    Expanded(
                      child: CustomElevatedButton(
                        height: 45.h,
                        onPressed: onCancel,
                        secondary: true,
                        buttonStyle: ButtonThemeHelper.secondaryButtonStyle(
                          Get.context!,
                        ),
                        text: AppStrings.T.lbl_no,
                        buttonTextStyle: Get.textTheme.bodyMedium?.copyWith(
                          color: Get.theme.customColors.primaryColor,
                          fontSize: 16.0.sp,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Gap(12.w),
                    Expanded(
                      child: Obx(
                        () => CustomElevatedButton(
                          checkConnectivity: true,
                          height: 45.h,
                          isLoading: isLoading?.value ?? false,
                          onPressed: onConfirm,
                          text: AppStrings.T.lbl_yes,
                          buttonTextStyle: Get.textTheme.bodyMedium?.copyWith(
                            color: Get.theme.customColors.white,
                            fontSize: 16.0.sp,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class ExitAppConfirmationDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const ExitAppConfirmationDialog({
    super.key,
    required this.title,
    required this.message,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      elevation: 0,
      backgroundColor: Colors.white,
      child: Container(
        padding: EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: Get.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
            ),
            Gap(12.h),
            Text(
              message,
              textAlign: TextAlign.center,
              style: Get.textTheme.bodyMedium?.copyWith(color: Colors.black54),
            ),
            Gap(24.h),
            Row(
              children: [
                Expanded(
                  child: CustomElevatedButton(
                    height: 45.h,
                    onPressed: onCancel,
                    secondary: true,
                    buttonStyle: ButtonThemeHelper.secondaryButtonStyle(
                      Get.context!,
                    ),
                    text: AppStrings.T.lbl_no,
                  ),
                ),
                Gap(12.w),
                Expanded(
                  child: CustomElevatedButton(
                    // checkConnectivity: true,
                    height: 45.h,
                    onPressed: onConfirm,
                    text: AppStrings.T.lbl_yes,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
