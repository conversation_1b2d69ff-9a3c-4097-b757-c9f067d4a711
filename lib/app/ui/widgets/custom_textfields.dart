import 'package:get/get.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class _SuffixIcon extends StatelessWidget {
  const _SuffixIcon({required this.showing});

  final RxBool showing;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: Icon(showing.value ? Icons.visibility : Icons.visibility_off),
      onPressed: showing.toggle,
    );
  }
}

class TextInputField extends TextFormField {
  TextInputField({
    super.key,
    required InputType type,
    String? hintLabel,
    required super.controller,
    super.textInputAction = TextInputAction.next,
    super.maxLines,
    super.minLines,
    super.autovalidateMode = AutovalidateMode.onUnfocus,
    super.validator,
    super.enabled,
    super.readOnly,
    super.expands,
    RxBool? obscureText,
    super.obscuringCharacter,
    TextInputType? keyboardType,
    Iterable<String>? autoFillHints,
    Widget? suffixIcon,
    Widget? prefixIcon,
    BoxConstraints? boxConstraints,
    List<TextInputFormatter>? inputFormatters,
    EdgeInsetsGeometry? contentPadding,
    Color? fillColor,
    bool? filled,
    TextStyle? hintStyle,
    bool isCapitalized = false,
    super.onTap,
    super.onChanged,
    Function(String)? super.onFieldSubmitted,
    super.focusNode,
    String? label,
    InputDecoration? decoration,
    bool isRequiredField = false,
    TextAlignVertical? textAlignVertical,
    double super.cursorHeight = 18,
    super.cursorWidth,
  }) : assert(
         type != InputType.multiline ||
             textInputAction == TextInputAction.newline,
         'Make textInputAction = TextInputAction.newline',
       ),
       assert(
         (type != InputType.password &&
                 type != InputType.newPassword &&
                 type != InputType.confirmPassword) ||
             obscureText != null,
         'Make sure your providing obscureText and Wrap Obx on TextInputField',
       ),
       super(
         keyboardType:
             keyboardType ??
             switch (type) {
               InputType.name => TextInputType.name,
               InputType.text => TextInputType.text,
               InputType.email => TextInputType.emailAddress,
               InputType.password => TextInputType.visiblePassword,
               InputType.confirmPassword => TextInputType.visiblePassword,
               InputType.newPassword => TextInputType.visiblePassword,
               InputType.phoneNumber => TextInputType.phone,
               InputType.digits => TextInputType.number,
               InputType.decimalDigits => const TextInputType.numberWithOptions(
                 decimal: true,
               ),
               InputType.multiline => TextInputType.multiline,
             },
         autofillHints: [
           if (autoFillHints != null) ...autoFillHints,
           switch (type) {
             InputType.name => AutofillHints.name,
             InputType.email => AutofillHints.email,
             InputType.password => AutofillHints.password,
             InputType.confirmPassword => AutofillHints.password,
             InputType.newPassword => AutofillHints.newPassword,
             InputType.phoneNumber => AutofillHints.telephoneNumber,
             _ => '',
           },
         ],
         textCapitalization:
             isCapitalized ? TextCapitalization.words : TextCapitalization.none,
         inputFormatters: [
           if (inputFormatters != null) ...inputFormatters,
           if (type == InputType.digits) FilteringTextInputFormatter.digitsOnly,
           if (type == InputType.decimalDigits)
             FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
         ],
         obscureText: obscureText?.value ?? false,
         textAlignVertical:
             textAlignVertical ??
             ((maxLines != null && maxLines > 1)
                 ? TextAlignVertical.center
                 : TextAlignVertical.center),
         style: Get.textTheme.bodyMedium?.copyWith(
           fontWeight: FontWeight.w600,
           fontSize: 14.sp,
           height: 2.5,
         ),
         decoration:
             decoration ??
             InputDecoration(
               hintStyle:
                   hintStyle ??
                   Get.theme.textTheme.bodyMedium?.copyWith(
                     fontSize: 14.sp,
                     color: Get.theme.customColors.greyTextColor,
                     fontWeight: FontWeight.w600,
                   ),
               label:
                   isRequiredField && label != null
                       ? RichText(
                         text: TextSpan(
                           text: label,
                           style: Get.textTheme.bodyMedium?.copyWith(
                             fontSize: 14.sp,
                             color: Get.theme.customColors.greyTextColor,
                             fontWeight: FontWeight.w600,
                           ),
                           children: [
                             TextSpan(
                               text: ' *',
                               style: Get.textTheme.bodyMedium?.copyWith(
                                 fontSize: 14.sp,
                                 color: Get.theme.customColors.primaryColor,
                                 fontWeight: FontWeight.w600,
                               ),
                             ),
                           ],
                         ),
                       )
                       : null,
               labelText: (!isRequiredField && label != null) ? label : null,
               floatingLabelBehavior: FloatingLabelBehavior.auto,
               alignLabelWithHint: true,
               hintText: isRequiredField ? null : hintLabel ?? '',
               labelStyle: Get.theme.textTheme.bodyMedium?.copyWith(
                 fontSize: 14.sp,
                 color: Get.theme.customColors.greyTextColor,
                 fontWeight: FontWeight.w700,
               ),
               floatingLabelStyle: Get.theme.textTheme.bodyMedium?.copyWith(
                 fontSize: 16.sp,
                 color: Get.theme.customColors.greyTextColor,
                 fontWeight: FontWeight.w700,
               ),
               enabledBorder: OutlineInputBorder(
                 borderRadius: BorderRadius.circular(10.0.r),
                 borderSide: BorderSide(
                   color: Get.theme.customColors.greyBorderColor!,
                 ),
               ),
               focusedBorder: OutlineInputBorder(
                 borderRadius: BorderRadius.circular(10.0.r),
                 borderSide: BorderSide(
                   color: Get.theme.customColors.primaryColor!,
                 ),
               ),
               errorBorder: OutlineInputBorder(
                 borderRadius: BorderRadius.circular(10.0.r),
                 borderSide: BorderSide(
                   color: Get.theme.customColors.redColor!,
                 ),
               ),
               disabledBorder: OutlineInputBorder(
                 borderRadius: BorderRadius.circular(10.0.r),
                 borderSide: BorderSide(
                   color: Get.theme.customColors.greyBorderColor!,
                 ),
               ),
               focusedErrorBorder: OutlineInputBorder(
                 borderRadius: BorderRadius.circular(10.0.r),
                 borderSide: BorderSide(
                   color: Get.theme.customColors.redColor!,
                 ),
               ),
               contentPadding:
                   contentPadding ??
                   EdgeInsets.symmetric(vertical: 10.0, horizontal: 16.0.w),
               prefixIcon: prefixIcon,
               prefixIconConstraints:
                   boxConstraints ??
                   const BoxConstraints(maxWidth: 40.0, minWidth: 40.0),
               fillColor:
                   fillColor ?? Get.theme.customColors.textfieldFillColor,
               filled: false,
               suffixIcon:
                   suffixIcon ??
                   (obscureText == null
                       ? null
                       : Builder(
                         builder: (context) {
                           if (type == InputType.email ||
                               type == InputType.password) {}
                           return _SuffixIcon(showing: obscureText);
                         },
                       )),
             ),
       );
}

enum InputType {
  name,
  text,
  email,
  password,
  confirmPassword,
  newPassword,
  phoneNumber,
  digits,
  decimalDigits,
  multiline,
}
