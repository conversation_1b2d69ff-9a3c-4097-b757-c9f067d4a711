import 'package:get/get.dart';
import 'package:v_card/app/controllers/subscription_plan_controller.dart';
import 'package:v_card/app/ui/pages/dashboard/helper_fuction/dashboard_helper_fuction.dart';
import 'package:v_card/app/ui/widgets/conf_dialog.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

class NoInternetPage extends StatelessWidget {
  const NoInternetPage({super.key});

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldExit = await Get.dialog<bool>(
            ExitAppConfirmationDialog(
              title: AppStrings.T.lbl_exit_app,
              message: AppStrings.T.lbl_exit_subtitle,
              onCancel: () => Get.back(result: false),
              onConfirm: () => Get.back(result: true),
            ),
          );
          if (shouldExit == true) {
            SystemNavigator.pop();
          }
        }
      },
      child: Scaffold(
        backgroundColor: Get.theme.customColors.white,
        body: Safe<PERSON>rea(
          child: Center(
            child: Padding(
              padding: EdgeInsets.all(24.w),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // No network illustration/icon
                  Container(
                    padding: EdgeInsets.all(32.w),
                    decoration: BoxDecoration(
                      color: Get.theme.customColors.greyTextColor?.withValues(
                        alpha: 0.1,
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: CustomImageView(
                      height: 120.h,
                      imagePath: AssetConstants.noInternet,
                      color: Get.theme.customColors.darkGreyTextColor,
                    ),
                  ),

                  Gap(32.h),

                  // Title
                  Text(
                    'No Internet Connection',
                    style: Get.theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Get.theme.customColors.primaryColor,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  Gap(16.h),

                  // Description
                  Text(
                    'Please check your internet connection and try again. Make sure you are connected to WiFi or mobile data.',
                    style: Get.theme.textTheme.bodyMedium?.copyWith(
                      color: Get.theme.customColors.greyTextColor,
                      height: 1.5,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  Gap(40.h),

                  // Retry button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => _retryConnection(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Get.theme.customColors.primaryColor,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        'Try Again',
                        style: Get.theme.textTheme.bodyMedium!.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  Gap(16.h),

                  // Go back button
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      onPressed: () async {
                        final shouldExit = await Get.dialog<bool>(
                          ExitAppConfirmationDialog(
                            title: AppStrings.T.lbl_exit_app,
                            message: AppStrings.T.lbl_exit_subtitle,
                            onCancel: () => Get.back(result: false),
                            onConfirm: () => Get.back(result: true),
                          ),
                        );
                        if (shouldExit == true) {
                          SystemNavigator.pop();
                        }
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Get.theme.customColors.primaryColor,
                        side: BorderSide(
                          color: Get.theme.customColors.primaryColor!,
                        ),
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        'Go Back',
                        style: Get.theme.textTheme.bodyMedium!.copyWith(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  Gap(32.h),

                  // Tips section
                  Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                      color: Get.theme.customColors.greyTextColor?.withValues(
                        alpha: 0.05,
                      ),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Troubleshooting Tips:',
                          style: Get.theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: Get.theme.customColors.primaryColor,
                          ),
                        ),
                        Gap(8.h),
                        _buildTipItem(
                          '• Check if WiFi or mobile data is enabled',
                        ),
                        _buildTipItem('• Try turning airplane mode on and off'),
                        _buildTipItem('• Restart your internet connection'),
                        _buildTipItem('• Move to an area with better signal'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTipItem(String tip) {
    return Padding(
      padding: EdgeInsets.only(bottom: 4.h),
      child: Text(
        tip,
        style: Get.theme.textTheme.bodySmall?.copyWith(
          color: Get.theme.customColors.greyTextColor,
        ),
      ),
    );
  }

  Future<void> _retryConnection() async {
    // Show loading
    Get.dialog(
      const Center(child: CircularProgressIndicator()),
      barrierDismissible: false,
    );

    // Check network connection
    final networkHelper = NetworkHelper();
    final hasNetwork = await networkHelper.hasNetworkConnection();

    // Close loading dialog
    NavigationService.navigateBack();

    if (hasNetwork) {
      NavigationService.navigateWithSlideAnimation(AppRoutes.nevigationMenu);
      toastification.show(
        type: ToastificationType.success,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text("Internet connection restored!"),
        autoCloseDuration: const Duration(seconds: 3),
        showProgressBar: false,
      );
    } else {
      // Still no connection
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text("Still no internet connection. Please try again."),
        autoCloseDuration: const Duration(seconds: 3),
        showProgressBar: false,
      );
    }
  }

  Future<void> handleAdminSubscriptionFlow() async {
    final subscriptionController = getIt<SubscriptionPlanController>();
    await subscriptionController.getPaymentRequestStatusData();

    final paymentStatus =
        subscriptionController.paymentRequestStatusData.value?.data;

    if (paymentStatus == 'Approved') {
      await subscriptionController.getSubscriptionPlanData();

      final plans =
          subscriptionController.subscriptionPlanData.value?.data ?? [];
      if (Functions.isSubscriptionExpire(plans)) {
        NavigationService.navigateWithSlideAnimation(
          AppRoutes.manageSubscriptionPage,
        );
      }
    } else {
      NavigationService.navigateWithSlideAnimation(
        AppRoutes.paymentApprovalPage,
      );
    }
  }
}
