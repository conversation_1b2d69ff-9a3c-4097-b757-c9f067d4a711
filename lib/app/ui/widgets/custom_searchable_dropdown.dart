// import 'dart:async';

// import 'package:v_card/app/ui/widgets/custom_image_view.dart';
// import 'package:v_card/app/utils/constants/app_assets.dart';
// import 'package:v_card/app/utils/helpers/exporter.dart';
// import 'package:v_card/app/utils/themes/custom_color_extension.dart';

// class SearchableDropdown extends StatefulWidget {
//   const SearchableDropdown({
//     super.key,
//     required this.items,
//     required this.onItemSelected,
//   });

//   final List<String> items;
//   final void Function(String selectedItem) onItemSelected;

//   @override
//   State<SearchableDropdown> createState() => _SearchableDropdownState();
// }

// class _SearchableDropdownState extends State<SearchableDropdown> {
//   late List<String> _filteredItems;
//   final TextEditingController _searchController = TextEditingController();
//   bool _isItemSelected = false;

//   @override
//   void initState() {
//     super.initState();
//     _filteredItems = widget.items;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         TextField(
//           controller: _searchController,
//           style: Get.textTheme.bodyMedium
//               ?.copyWith(fontWeight: FontWeight.w400, fontSize: 18.sp),
//           decoration: InputDecoration(
//             hintText: AppStrings.T.lbl_search,
//             filled: true,
//             fillColor: Get.theme.customColors.white,
//             prefixIcon: CustomImageView(
//               imagePath: AssetConstants.icSearch,
//               margin: EdgeInsets.symmetric(horizontal: 15.w),
//               height: 20.h,
//             ),
//             hintStyle: Get.theme.textTheme.bodyMedium?.copyWith(
//                 fontSize: 18.sp,
//                 color: Get.theme.customColors.greyTextColor,
//                 fontWeight: FontWeight.w400),
//             contentPadding: const EdgeInsets.all(15),
//           ),
//           onChanged: (query) {
//             _filterItems(query);
//             setState(() {
//               _isItemSelected = false;
//             });
//           },
//         ),
//         Gap(16.h),
//         if (!_isItemSelected && _searchController.text.isNotEmpty)
//           AnimatedOpacity(
//             opacity: _filteredItems.isNotEmpty ? 1.0 : 0.0,
//             duration: const Duration(milliseconds: 300),
//             child: ListView.builder(
//               shrinkWrap: true,
//               padding: EdgeInsets.zero,
//               itemCount: _filteredItems.length,
//               itemBuilder: (context, index) {
//                 return ListTile(
//                   title: Text(
//                     _filteredItems[index],
//                     style: Get.theme.textTheme.bodyMedium
//                         ?.copyWith(fontSize: 16.sp),
//                   ),
//                   onTap: () {
//                     _searchController.text = _filteredItems[index];
//                     widget.onItemSelected(_filteredItems[index]);
//                     setState(() {
//                       _isItemSelected = true;
//                     });
//                   },
//                 );
//               },
//             ),
//           ),
//       ],
//     );
//   }

//   Timer? _debounce;

//   void _filterItems(String query) {
//     if (_debounce?.isActive ?? false) _debounce?.cancel();
//     _debounce = Timer(const Duration(milliseconds: 300), () {
//       setState(() {
//         _filteredItems = widget.items
//             .where((item) => item.toLowerCase().contains(query.toLowerCase()))
//             .toList();
//       });
//     });
//   }
// }
