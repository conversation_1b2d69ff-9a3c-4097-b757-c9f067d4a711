// ignore_for_file: depend_on_referenced_packages, library_prefixes

import 'dart:developer' as Logger;
import 'dart:io';
import 'dart:math';
import 'package:http/http.dart' as http;
import 'package:media_scanner/media_scanner.dart';
import 'package:path_provider/path_provider.dart';
import 'package:image/image.dart';
import 'package:path/path.dart' as path;
import 'package:qr_image/qr_image.dart';

Future<File> urlToFile(String imageUrl) async {
  final response = await http.get(Uri.parse(imageUrl));
  final documentDirectory = await getTemporaryDirectory();
  final fileName = imageUrl.split('/').last;
  final file = File('${documentDirectory.path}/$fileName');
  return file.writeAsBytes(response.bodyBytes);
}

bool isCardCreated = true;

String processProductUrl(String url) {
  if (url.trim().isEmpty) return url;

  url = url.trim();

  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }

  if (url.startsWith('www.')) {
    return 'https://$url';
  }

  return 'https://www.$url';
}

// Future<String> downloadQrcode(String url, String name) async {
//   // Add your function code here!
//   int randomNumber = Random().nextInt(1000000);
//   String imageName = '$name-$randomNumber';
//   Directory directory1 = Directory('/storage/emulated/0/Download');
//   // Directory directory1 = Directory('/storage/emulated/0/Documents/Vcard');

//   if (!await directory1.exists()) {
//     await directory1.create(recursive: true);
//   }
//   final filePath = '${directory1.path}/$imageName.png';
//   try {
//     var img1 =
//         QRImage(
//           url,
//           backgroundColor: ColorUint8.rgb(255, 255, 255),
//           size: 300,
//         ).generate();

//     File imageFile = File(filePath);
//     await imageFile.writeAsBytes(encodePng(img1));
//     return 'QR Code downloaded successfully';
//     // return 'Image downloaded successfully at: Document/Vcard';
//   } catch (e) {
//     return '';
//   }
// }

Future<String> downloadQrcode(String url, String name) async {
  try {
    int randomNumber = Random().nextInt(1000000);
    String imageName = '$name-$randomNumber.png';

    Directory downloadDir;

    if (Platform.isAndroid) {
      downloadDir = Directory('/storage/emulated/0/Download/VCard_QRCodes');
    } else {
      downloadDir = Directory.systemTemp;
    }

    if (!await downloadDir.exists()) {
      await downloadDir.create(recursive: true);
    }

    String filePath = path.join(downloadDir.path, imageName);

    // Ensure unique filename if it already exists
    int counter = 1;
    while (await File(filePath).exists()) {
      imageName = '$name-$randomNumber($counter).png';
      filePath = path.join(downloadDir.path, imageName);
      counter++;
    }

    // Generate QR image
    final img =
        QRImage(
          url,
          backgroundColor: ColorUint8.rgb(255, 255, 255),
          size: 300,
        ).generate();

    File imageFile = File(filePath);
    await imageFile.writeAsBytes(encodePng(img));

    Logger.log('QR code saved to: $filePath');

    if (!await imageFile.exists()) {
      throw Exception('File not found after writing.');
    }

    // Trigger media scan
    if (Platform.isAndroid) {
      try {
        await MediaScanner.loadMedia(path: filePath);
        Logger.log('Media scan completed successfully.');
      } catch (e) {
        Logger.log('MediaScanner failed: $e');
        await _broadcastMediaScan(filePath);
      }
    }

    return 'QR Code downloaded successfully to Downloads folder';
  } catch (e) {
    Logger.log('Download error: $e');
    return 'Failed to download QR code: ${e.toString()}';
  }
}

/// Fallback broadcast-based media scanner
Future<void> _broadcastMediaScan(String filePath) async {
  try {
    if (Platform.isAndroid) {
      final result = await Process.run('am', [
        'broadcast',
        '-a',
        'android.intent.action.MEDIA_SCANNER_SCAN_FILE',
        '-d',
        'file://$filePath',
      ]);
      Logger.log('Broadcast media scan result: ${result.exitCode}');
    }
  } catch (e) {
    Logger.log('Broadcast media scan failed: $e');
  }
}
