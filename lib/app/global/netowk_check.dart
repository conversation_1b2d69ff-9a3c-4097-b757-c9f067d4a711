// ignore_for_file: unrelated_type_equality_checks

import 'dart:io';
import 'package:v_card/app/utils/helpers/exporter.dart';

class NetworkHelper {
  static final NetworkHelper _instance = NetworkHelper._internal();
  factory NetworkHelper() => _instance;
  NetworkHelper._internal();

  /// Check if device has network connection
  Future<bool> hasNetworkConnection() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    if (connectivityResult == ConnectivityResult.none) {
      return false;
    }

    try {
      final result = await InternetAddress.lookup('example.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      }
      return false;
    } on SocketException catch (_) {
      return false;
    }
  }

  /// Execute API call with network check
  /// Returns null if no network, otherwise executes the API call
  Future<T?> executeWithNetworkCheck<T>(
    Future<T> Function() apiCall, {
    bool showNoNetworkPage = true,
  }) async {
    final hasNetwork = await hasNetworkConnection();

    if (!hasNetwork) {
      if (showNoNetworkPage) {
        // Navigate to no network page
        NavigationService.navigateWithSlideAnimation(AppRoutes.noInternetPage);
      } else {
        // Show snackbar instead
        toastification.show(
          type: ToastificationType.error,
          style: ToastificationStyle.flatColored,
          alignment: Alignment.topCenter,
          description: Text("Please check your internet connection"),
          autoCloseDuration: const Duration(seconds: 3),
          showProgressBar: false,
        );
      }
      return null;
    }

    try {
      return await apiCall();
    } catch (e) {
      // Handle API errors here
      toastification.show(
        type: ToastificationType.error,
        style: ToastificationStyle.flatColored,
        alignment: Alignment.topCenter,
        description: Text("Something went wrong. Please try again."),
        autoCloseDuration: const Duration(seconds: 3),
        showProgressBar: false,
      );
      return null;
    }
  }
}

/// Extension to easily add network checking to any function
extension NetworkCheckExtension on Function {
  Future<T?> withNetworkCheck<T>() async {
    return NetworkHelper().executeWithNetworkCheck(() async => await this());
  }
}
