import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:v_card/app/controllers/app_controller.dart';
import 'package:v_card/app/routes/app_pages.dart';
import 'package:v_card/app/utils/helpers/generated/l10n.dart';
import 'package:v_card/app/utils/themes/app_theme.dart';
import 'package:v_card/app/utils/helpers/exporter.dart';

Future<void> main() async {
  configuration(myApp: const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late PersistentTabController _controller;

  @override
  void initState() {
    super.initState();
    _controller = PersistentTabController(initialIndex: 0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    Get.put(AppController());
    // Get.put(NetworkController());
    FlutterNativeSplash.remove();
    return ScreenUtilInit(
      designSize: Size(MediaQuery.of(context).size.width, MediaQuery.of(context).size.height),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.dark,
            systemNavigationBarDividerColor: Colors.transparent,
            systemNavigationBarColor: Get.theme.customColors.white,
            systemNavigationBarIconBrightness: Brightness.dark,
          ),
          child: ToastificationWrapper(
            child: Obx(() {
              final appController = Get.find<AppController>();
              // final networkController = Get.find<NetworkController>();

              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  highContrast: true,
                  displayFeatures: MediaQuery.of(context).displayFeatures,
                  gestureSettings: MediaQuery.of(context).gestureSettings,
                  textScaler: TextScaler.noScaling,
                  invertColors: false,
                  boldText: false,
                ),
                child: GetMaterialApp(
                  debugShowCheckedModeBanner: false,
                  title: 'Infy VCard',
                  getPages: AppPages.routes,
                  initialRoute: AppRoutes.splash,
                  localizationsDelegates: [
                    AppLocalizations.delegate,
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ],
                  supportedLocales: AppLocalizations.delegate.supportedLocales,
                  locale: appController.currentLocale.value,
                  themeMode: ThemeMode.light,
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  // builder: (context, child) {
                  //   return Obx(() {
                  //     if (networkController.hasInitialized.value &&
                  //         !networkController.isConnected.value) {
                  //       return Scaffold(
                  //         body: Center(
                  //           child: ExceptionWidget(
                  //             imagePath: AssetConstants.noInternet,
                  //             title: 'No Internet Connection',
                  //             subtitle:
                  //                 'Please check your internet connection and try again.',
                  //             onButtonPressed:
                  //                 () => networkController.initialCheck(),
                  //             buttonText: 'Retry',
                  //           ),
                  //         ),
                  //       );
                  //     }

                  //     return EasyLoading.init()(context, child);
                  //   });
                  // },
                  builder: (context, child) {
                    child = EasyLoading.init()(context, child);
                    return child;
                  },
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
