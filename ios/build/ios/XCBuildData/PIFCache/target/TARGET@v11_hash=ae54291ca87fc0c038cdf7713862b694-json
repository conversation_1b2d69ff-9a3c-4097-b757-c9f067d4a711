{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f1f2cdcd4327df4bbda973f816b9b85a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985a0e8a3f6f58d3b701991f707d2f09f8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a4c19e2f63db0275dfe488a14287353", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e2fb72b0b2ed6321b8cbbce6dc48a3b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a4c19e2f63db0275dfe488a14287353", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808a3d546e89e31a6cd62137f768cf78b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e71dd0e3ec9327c1d0099fca4535ed4a", "guid": "bfdfe7dc352907fc980b868725387e98a0d1710b862ba25bf9ef744fae856496", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab338ceb5c16c395e7043b07346a4096", "guid": "bfdfe7dc352907fc980b868725387e98fcb3df4714c8c13d164f07d531f2feb0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a29bc3dd9ff68403ac70f946fee065f8", "guid": "bfdfe7dc352907fc980b868725387e983f61fddd79d4fa9e978ad3a0a26a744e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982caec58a397145191a5b563d24d6cfc4", "guid": "bfdfe7dc352907fc980b868725387e980ef820fbcfb0203f1402137cef7c1f8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb99d6ae36811c3e57c61a685673b85f", "guid": "bfdfe7dc352907fc980b868725387e9899f23a3081fafa5aa6e6f3eca5f7acd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb3fe98551dfb3a806c3338798645a51", "guid": "bfdfe7dc352907fc980b868725387e984856430b5f20cd38973b88781c949b72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e2c2a54029584046582c7c9f48fb7e1", "guid": "bfdfe7dc352907fc980b868725387e98c7127b25b0d74115c16870b61edace2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b601a033b10244a3386148e705d7ba35", "guid": "bfdfe7dc352907fc980b868725387e988f0601bf742b1c4ac19a1ab3c6fd88ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983484473d0f7fd54f7ad5b8e7bdcfad47", "guid": "bfdfe7dc352907fc980b868725387e9857ea72926d75a50128f4bee2241cf07f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f97085d6b79874754f6db2dc60be837b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c211a2a2b7c430a9104c236b922b98b5", "guid": "bfdfe7dc352907fc980b868725387e98d34d6dfee108282a76bd913965977907"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb1017cb0c8c8720cd11050cd330a10d", "guid": "bfdfe7dc352907fc980b868725387e987e6e8eb3fc9b68f954a1e9558e8df7ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3303193462177181ce4dd4c3a1ea9b4", "guid": "bfdfe7dc352907fc980b868725387e98b3f30e2ee2e809dafba8a617553fee07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b845bb8c5de218ec211aa32a40f0e71", "guid": "bfdfe7dc352907fc980b868725387e9860619cd4c5cd4d731ae29e83367b0312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2226947dad5045aacb32a30d37adbe5", "guid": "bfdfe7dc352907fc980b868725387e98a2c7ae8c92b71e464f371ada60a0e42c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d71ad44eb95daf53b2b79576716ec33", "guid": "bfdfe7dc352907fc980b868725387e98d64ca2c1d2f051fa2db3a9d997679a66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d0c0288a5eb1e0d8704c744aca024af", "guid": "bfdfe7dc352907fc980b868725387e986de80ebb84c209d6eeb3bda3dc1ef1e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98208b93b38b227c8c6cd5e082c15fa7a9", "guid": "bfdfe7dc352907fc980b868725387e98efa0a7c30e77f5d35d88b7ad1f456a19"}], "guid": "bfdfe7dc352907fc980b868725387e98140080d667419c3fbe09e9d812428b23", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e983a465c8183060e62db5aba64387b099d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e98832d1279f4afbd53ac6fb78cbb290c43"}], "guid": "bfdfe7dc352907fc980b868725387e98da2fd65747699fffe57e8125889c4071", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98dc3e1a90d1de56e111c7318f2c74dfa8", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e983a6127fa43f8fd04f27fe0edd6e2b272", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98251f2731a43a94d86e661d6fab67d04d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}