{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800615c8123318a5def1e36c0fc68cf77", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7a99df6e6a4873f55b3ad33955c73d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b18c0f5976a7db9e1507894dbeb78ee4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98961e7c9baafe5b4e26a5c59132394731", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b18c0f5976a7db9e1507894dbeb78ee4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5e2f4ec47caf1c35a822cf8398efdf7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980e0e1b108fea794cd952a7391b00e8b5", "guid": "bfdfe7dc352907fc980b868725387e983a7f56a8bda7f4a32b582f3e3543ec6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa54d6de76b3766ca716316a4bcfa958", "guid": "bfdfe7dc352907fc980b868725387e98eefa1fbb52d5820fb2f74287e89fa5be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dc17a52a9bde48abbdf6aa0fb456f34", "guid": "bfdfe7dc352907fc980b868725387e98731bf14971647468f98880f5ab2ecebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b190460224098c2c1e88e68820f9caf", "guid": "bfdfe7dc352907fc980b868725387e985188a1987f7cf2a34653743fc646f39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877ddf7b6313cd706029cb460556008b4", "guid": "bfdfe7dc352907fc980b868725387e988f117cd19bea713411f7d886e3ee57f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a568c35c39971429db9eb4d47e0a81e", "guid": "bfdfe7dc352907fc980b868725387e98f7013b8d08955f03cd9ad217b6575ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d24543ef467db9322f5e434060886d", "guid": "bfdfe7dc352907fc980b868725387e98c3717fce461d8c3c727f6ab1e1ef8dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876bf0680d3ef45d8d771c1f21757327a", "guid": "bfdfe7dc352907fc980b868725387e98971d704d92f697bb1c3431227aeca581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e4b91d8c45a573644ad0eb6208f9663", "guid": "bfdfe7dc352907fc980b868725387e987fb0a97b5514da31c86f2442dd9c5c3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bffa3ad165de1d8c6f1ea9f6e929d7f", "guid": "bfdfe7dc352907fc980b868725387e98557eecafb08f3d4c91d7a27a04cbc98f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870f00dd63d8e69bd36f47d5497fdf9fa", "guid": "bfdfe7dc352907fc980b868725387e9831a692eeee4b67a14159ea381967273a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d054cf40ca2ba98a261f364660bb8e5", "guid": "bfdfe7dc352907fc980b868725387e986a533507042bc1b488b8d411b4e467dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dc2da18bfec88bdf9a5ff779bfeb589", "guid": "bfdfe7dc352907fc980b868725387e98ae7871cec43b2a430799debd34abfca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5229544c5c45dfdf286798b6332ee85", "guid": "bfdfe7dc352907fc980b868725387e9843848d640b95d66ae7b0609d40822a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cde9edf8beb2e447c4b5a4b609a400d0", "guid": "bfdfe7dc352907fc980b868725387e985df37ff259fbebfcef0385a6ec8da199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865c1184c17db486fcf9bbc6febe09e13", "guid": "bfdfe7dc352907fc980b868725387e98f8b9a4332a9d35d417da9e60a68e1e5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b45abf1b053ae2c6b4eb525f3f39810f", "guid": "bfdfe7dc352907fc980b868725387e987016ef18ef8e69d7bc59d3c64c1062eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987168357ef794238ccb74cc50e2ab2463", "guid": "bfdfe7dc352907fc980b868725387e98638fdbf659b7db656daaabefeb0fa1e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d485e5e842dfcb4b8e12bc4427f5985", "guid": "bfdfe7dc352907fc980b868725387e982366610e765f0b5c67b62d5d700c6ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e571c1cafb617c7488f7c89c83aad155", "guid": "bfdfe7dc352907fc980b868725387e98201a1ca6306b21611dc8f609d62d8e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a6c40bc277b107e83e6f4919cbb6ccb", "guid": "bfdfe7dc352907fc980b868725387e9805d1768fe792537334f391c041c31f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827963fd00faebc675f318f31e14efe51", "guid": "bfdfe7dc352907fc980b868725387e981ca2893936b4e0ab83db00de99e3fe4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aff0e5345397732d0f6bd33b5d4f2f77", "guid": "bfdfe7dc352907fc980b868725387e98df05095938a6e6a95d183f2debb7eeca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984621b26a56bcc0c7b03c4488e575a47f", "guid": "bfdfe7dc352907fc980b868725387e988f3992c3226d3fffed029e44a32d785f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fecb56f3eb3a77458cc979057953e631", "guid": "bfdfe7dc352907fc980b868725387e98325b799f84feca7cce3ae0c158b3d6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b1b9793439a7de62d812c9ddd033a9a", "guid": "bfdfe7dc352907fc980b868725387e98d73be45a5c15dbc80c5e29415079a21a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826870282b53bbd75bb25f56e8db34ad6", "guid": "bfdfe7dc352907fc980b868725387e9895e853d89407c6a61308b8ed634f6c83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc242cb83c3ced7790dab55a74e11421", "guid": "bfdfe7dc352907fc980b868725387e983f07250a53c4dcc69dfbcf67ebdc325b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d85632ea9f1634b443aa31b6539c322a", "guid": "bfdfe7dc352907fc980b868725387e987481fc11365e35e19c58f07f6f4b732c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8c5e09cc5638112033a6b77d262dd84", "guid": "bfdfe7dc352907fc980b868725387e985b6c7b86e2e3ddab6f8772c5362a6fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c3d0ed99edddf108e18027faf417859", "guid": "bfdfe7dc352907fc980b868725387e988919c6af1499779a90e7598a5e84fabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc8e070dbabbc5c6bb30358659256b66", "guid": "bfdfe7dc352907fc980b868725387e98d6d0fe9c6b6c404e0d62b0cbcd05dfdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871b0f4eddc7fed7f83ac8cc3648eaa88", "guid": "bfdfe7dc352907fc980b868725387e9810d41d30cf3c158f9509077744b70505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d028d88ba499ae4f86cf3d133254f7e0", "guid": "bfdfe7dc352907fc980b868725387e9865d6cb4f29a80f2f2ce4e60e0090a0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc2b73ecf64724c451febd26501a10c", "guid": "bfdfe7dc352907fc980b868725387e98ea323f1a49908e40b0b598e61db59b71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff14c13548ee4f9d48ac8aa0b29a6e0a", "guid": "bfdfe7dc352907fc980b868725387e9866a81d67491e84a3ef0e7b3befdbdaba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec3eb9c718a98819059b16b4a931d9fc", "guid": "bfdfe7dc352907fc980b868725387e98f9a04fb7607f394362f9bff3a632b600", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ba85bba6830f9865340b08f473e9110", "guid": "bfdfe7dc352907fc980b868725387e98c1b9cbe39894801763b170085b67c5ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981012efba74b963b819169d75f1a30e63", "guid": "bfdfe7dc352907fc980b868725387e984e87e4b3e6b500cceaa46ede0ec13dfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f5c6c414c5faca330033752a8caa2a3", "guid": "bfdfe7dc352907fc980b868725387e9804e24ee381ecd94ecdaeb870ed0114ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613b273fe0cf9950f0b8a74cc86d15b7", "guid": "bfdfe7dc352907fc980b868725387e987e8729dc670ba4e8dc2e7ab782a73fb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b50d6ca11b358a2718813207ef881b21", "guid": "bfdfe7dc352907fc980b868725387e980673b86a47f0fe84859c2176137bfbd7"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3f6c017f50c3291e26862a62071ad6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9827348602d43e5f6625996052b45205fc", "guid": "bfdfe7dc352907fc980b868725387e981251f36c01e0bb324ca1cf7a8d9bddb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980735f3bf332d4a65c990e573aa0d7084", "guid": "bfdfe7dc352907fc980b868725387e98d8db050f1c7a5fd0ec0a4596c587f92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8f24bdfae16c5405d9c222775d5dd38", "guid": "bfdfe7dc352907fc980b868725387e98251d465f7239177f9eb05186be427f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6fbd6bdcd32e96d96d8a1d12c791bfa", "guid": "bfdfe7dc352907fc980b868725387e98b9bf91278debb41b3833f220f1b435bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810356de792fe6bb1fffc00c0c78635e4", "guid": "bfdfe7dc352907fc980b868725387e98af769f021239e440439d2f37c2a8b47a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e457ab1615e1122247062f4372026f4", "guid": "bfdfe7dc352907fc980b868725387e98f4410e2eefcc5e3073d1057d8a440acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800c29a4a9d62786ccf087802457c16c1", "guid": "bfdfe7dc352907fc980b868725387e981c659a32af5c88dd60c9e7e92e503c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff03eb30245cc818596cf3167e6dfd3e", "guid": "bfdfe7dc352907fc980b868725387e98385e9f712da19f3fdeab81bd33335d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c6f0e50183f72b5f01a1938b0afd82d", "guid": "bfdfe7dc352907fc980b868725387e98a44cf29d16204615eb6acc6a177ed90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982989d01309aeccaf0cae1b2f7b6df9fb", "guid": "bfdfe7dc352907fc980b868725387e981759ea69ef13d9431585b07246de10e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983718527e2a3ba4e0d75343d84adc1750", "guid": "bfdfe7dc352907fc980b868725387e98c7a014a0f2696eb65fdde5d95ff8a377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d9f63b7dbc7420d1fac3da35cfdad0", "guid": "bfdfe7dc352907fc980b868725387e987d3c88b047f31c79453e480da1cee745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e127ef74520c276c7ffa08782501ab1f", "guid": "bfdfe7dc352907fc980b868725387e98754dd466afb74dc55b83de8dbd18dd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810fc83ea3fb570cdd8df83093f420f33", "guid": "bfdfe7dc352907fc980b868725387e98d346057307b65a0c4cb63b7ce2cfbc65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98447dafed3b63c31784cdd232a0cac61a", "guid": "bfdfe7dc352907fc980b868725387e9831a5a7cf4456b84e8b2fb59d15681b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853d2262d5bdeba8e9d82d186da318b2f", "guid": "bfdfe7dc352907fc980b868725387e98630b01bae14ac405faf80a562b3c32b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985488e1a5133f82b13e3105c9a270a243", "guid": "bfdfe7dc352907fc980b868725387e98c9f34912719e77ba96475461238e0e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98038a5f3c05262c5d266bc824ee7a4577", "guid": "bfdfe7dc352907fc980b868725387e984867af3440057818079d08ea0c4d6195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eac1914af61c360ae7f9ea84c956f9d0", "guid": "bfdfe7dc352907fc980b868725387e9804dbc6bc4a995fc03805e33bbdc61583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98286e02af106c1f8c758e4d89b5e2174b", "guid": "bfdfe7dc352907fc980b868725387e98d92cafd93ccc407f0b67993abcefdce5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825ebfef12e242a6fe2ddb0db28f26a92", "guid": "bfdfe7dc352907fc980b868725387e9820143a2be1e220fac8de61a11087c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bff41c5c0052efe4dcaf453e4a8568a", "guid": "bfdfe7dc352907fc980b868725387e98f5c29ea9255f8220e21e5404dc5a3f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e5eceb0ae71355682e3fca8a670f146", "guid": "bfdfe7dc352907fc980b868725387e982693cc42ebb05a72e63217341d71bd9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9d26698aff11c9c38b6a84abdadf9de", "guid": "bfdfe7dc352907fc980b868725387e985d0530ef6188ebe958e1f0d3f3298d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ec4711766702df9bf08961a71a848f", "guid": "bfdfe7dc352907fc980b868725387e9829041b1e039eb006c470ff92591bf911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98584151d82012302d7e2acc9a7c50799f", "guid": "bfdfe7dc352907fc980b868725387e98b92b9cf860855796967f283ce7415d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b620f89d9eae05e36902ce713006ccdd", "guid": "bfdfe7dc352907fc980b868725387e98debe9957367259f62596ede328df84af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cd0e7822ad46a494f3a1770e9384fd3", "guid": "bfdfe7dc352907fc980b868725387e98ec8f6bff25068400792a0da62301d62a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984181da1b49c959041cd52518d42776bd", "guid": "bfdfe7dc352907fc980b868725387e981808cef69632857ddb48af8a1b5faf76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4381a5a198407dfca868b98c0a37aaa", "guid": "bfdfe7dc352907fc980b868725387e980f230ea024b72dbc683a1fabead8d6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc548005ac65debfb6ce0adce60356bb", "guid": "bfdfe7dc352907fc980b868725387e98deca3a41ff15842151e1be841cc3fff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75ff6fec5032d1c969826eb53006285", "guid": "bfdfe7dc352907fc980b868725387e98f21ff164321f2d57d89d63e9fa8d5d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98428ab303dadaaf334dfd39a32c709324", "guid": "bfdfe7dc352907fc980b868725387e981618ab1789cddc3783305bb624a1280e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda7df37368f9fc83ca9b0aab03f53fb", "guid": "bfdfe7dc352907fc980b868725387e98c367e72e8bce33e83d8a76f05c126e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c060700c61d9949b4950767374903421", "guid": "bfdfe7dc352907fc980b868725387e98c36efdc476e07ebbd7263ace396ae758"}], "guid": "bfdfe7dc352907fc980b868725387e98053600d677a0ee3a6e8294a5f6c29926", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98a7f5c11a3ddc6c6109c703c9daffc90f"}], "guid": "bfdfe7dc352907fc980b868725387e986ef21295949b1adf438aaa172cba7c15", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987799c6ff721a2f90b939c7d64edb78cc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985c8c1a45791dbc15ae7565c9ac08e62e", "name": "AppCheckCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}