{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9896add0c62aa03786ba355a31ebeed19c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1df362dc5e41384200cbd912f9ee69f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1df362dc5e41384200cbd912f9ee69f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b027dc8fc5e82de2699f80d3dad8740e", "guid": "bfdfe7dc352907fc980b868725387e98fbbe543b71214f4509d8c92248a60d08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abb2ab9b0e19c37175a2b69a6786e45c", "guid": "bfdfe7dc352907fc980b868725387e98471ff3f9f1b2efe128b7c2d23d239746", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e5ff174a878609f13faa3ef373a6919", "guid": "bfdfe7dc352907fc980b868725387e98b76c0130342994e0dd30f1d68cb8abfd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982764b1dcf9b32aec0e38e1b2d6e9d417", "guid": "bfdfe7dc352907fc980b868725387e98b026e65d04846ce716b6283013c9b796", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc2d68e96aeb5bd371946e4c406ec7ba", "guid": "bfdfe7dc352907fc980b868725387e985e0782c485d14f7513f47a5628a6b51b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805fe533845d1e95dc97f854ee06d4867", "guid": "bfdfe7dc352907fc980b868725387e980dc402cd31c13f836903d7124fb9cd77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893dd35d6893880c23e6b0754caee4f24", "guid": "bfdfe7dc352907fc980b868725387e989c0fc48a74ff41a0a9254c97ece8daaf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883ceb308e6de024f66c08059079c385b", "guid": "bfdfe7dc352907fc980b868725387e9861fa11d4d6cf54947eab5534b60f0d93", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983221db30ae1c0621daa5c592e856a5f1", "guid": "bfdfe7dc352907fc980b868725387e985abce06578652c4d8dc3d24c702bd3c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797b6eff9fbe7cd0fe3f9f6d5ac06cde", "guid": "bfdfe7dc352907fc980b868725387e98b8c7585471bcf10789da28bb2a96c405", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3b466148b56ef9a15068b1271d3a998", "guid": "bfdfe7dc352907fc980b868725387e98e293850950354b35a2786639c3e3939e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887644eb53c99513847ff113df4ed795d", "guid": "bfdfe7dc352907fc980b868725387e980ef10060e448131985daa2036af5ae3e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890fb1e690edb1fab8e35cd4ee9eb3fb1", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98556be8a881f3161bb4c6b49766267692", "guid": "bfdfe7dc352907fc980b868725387e98c1277031b6540d3602e8c79a5b369c19", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad75d6524935d1c27794e836f277dfba", "guid": "bfdfe7dc352907fc980b868725387e98b129ca47461bd3877af22b5d56ec22fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd3942b3f12c86c94558c4b778af30ee", "guid": "bfdfe7dc352907fc980b868725387e98808ab512e47ea5c54aad6b2807ee7bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1e37cbdd0b57f246a1e8aa8e27b951a", "guid": "bfdfe7dc352907fc980b868725387e98c191f1ff744a395685e3770c58c824f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98693d415812f21a82cc779e127971f7a9", "guid": "bfdfe7dc352907fc980b868725387e982bc1b5cee954a27e7861f672675db061", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98168c71e1b8a811259dfa638194a273d0", "guid": "bfdfe7dc352907fc980b868725387e984e7a5baa4ea128e81cc7b93ea895a0bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884cd0730c0d599218be85af01bb7ead4", "guid": "bfdfe7dc352907fc980b868725387e986f2e44c4deb448e91b7f9dcbd9877023", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fac26b0e6fd7b23940ca82c94a908405", "guid": "bfdfe7dc352907fc980b868725387e9878438d158bdc6c70e3cc6f1422a1fd5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7dae520e37d24f924d99574a5cbf90d", "guid": "bfdfe7dc352907fc980b868725387e9896c5db8f09f591e14135d056fe937d39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ae2d9ca0a98f455dc7f5cb23a95bbc1", "guid": "bfdfe7dc352907fc980b868725387e98ed733eeeb4cf442e79221b43ec6ab543", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982861b98d60737fa914af45d0641adfc4", "guid": "bfdfe7dc352907fc980b868725387e9863f74156f992cef9610ea0027b158797"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878c0efd877096b1000c3607d25d24c21", "guid": "bfdfe7dc352907fc980b868725387e9817dcb77d7f539f0ba7cf6bba23ab025a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a5093dc53f960d2efe966b88206fcd0", "guid": "bfdfe7dc352907fc980b868725387e98430e4d66334ef552e35896edf0800fd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ab25d1043deac39b09f276ecd083d55", "guid": "bfdfe7dc352907fc980b868725387e98ac1c4a6aecb934ba3f7ce5f4b7a5ce10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eb55c39807e2ea9b2008b0c837f77b3", "guid": "bfdfe7dc352907fc980b868725387e980d88881e8f1864ad44addeb4b546f957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cf87311a575d9d918ae5e12d78698ab", "guid": "bfdfe7dc352907fc980b868725387e981d2fe54f8f024aea0393135c6622c36f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98229270f805fc095dc236f2ee08087da3", "guid": "bfdfe7dc352907fc980b868725387e9875a7bbe49ed36b7b4958102bd487c186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acd1752ef782cc986687e276b8b3a2d5", "guid": "bfdfe7dc352907fc980b868725387e98c2fd66d30ed9c469a6f9e66511104826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98700e91e30ced3e54d6371aa0124f9b3a", "guid": "bfdfe7dc352907fc980b868725387e986898536a32f1ba314b25df11fafa9a37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98875d63e5145ef9776a909827216634cf", "guid": "bfdfe7dc352907fc980b868725387e989f1c87cce3ba04c5b99d6bc86b996ce0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98274fa51c7674ccc4666d5c149db091ad", "guid": "bfdfe7dc352907fc980b868725387e98892efd917ae976f6c02ccd36be495e89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d72c43d07dd965d76f1d1d7d6842d3a9", "guid": "bfdfe7dc352907fc980b868725387e988f6dd6984240ec7b3e112a512bcbd51f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a590c9badede4e76c4bef31761971789", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c93a6254f6ee2e16837cc02b7cd5467a", "guid": "bfdfe7dc352907fc980b868725387e98111a96713a87f9cb39e8152dd6dde744"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be4b78d82ba97ebf6d51d76536e6e8f0", "guid": "bfdfe7dc352907fc980b868725387e985b7f8093353d96741cf9ecdce14f0a8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899ac7f93d532d5bd8a62c0d44bd04c4b", "guid": "bfdfe7dc352907fc980b868725387e98a735fa885496d6e8c24d59f0cb2548ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c470b31da283d5a02d6ac7d60a86f161", "guid": "bfdfe7dc352907fc980b868725387e9837a1e7be82ce3385de98f18fd5990f89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e17c5f600857bd38381c0f5ac4d98eb", "guid": "bfdfe7dc352907fc980b868725387e9892af032a9b1b9cd1201f3d2ed313dfed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d791cdf252f5225e683246c4c3ffa38b", "guid": "bfdfe7dc352907fc980b868725387e9826eaae4dc2c7871e7fbd53b8b7eb1b96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987482fb8ded9de65cd553dcd8a34e5d9a", "guid": "bfdfe7dc352907fc980b868725387e98241a83cbc6157ce812894eb73152a9f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987491af3797b34757d18a6621dd57e09f", "guid": "bfdfe7dc352907fc980b868725387e98160b038fa4d62a21aeddf4fe7e39ba83"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}