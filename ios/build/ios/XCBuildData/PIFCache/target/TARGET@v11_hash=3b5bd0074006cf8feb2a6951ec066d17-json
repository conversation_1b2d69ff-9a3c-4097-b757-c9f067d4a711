{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98da4735e05b06f58ca87d5528f423e8c8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9810f2e6ed1de12faddabcc2f86c48d561", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9875baa3fc508f24a16855905f248ee526", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989336943122f11b5abc69a03ea2e0a77b", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9875baa3fc508f24a16855905f248ee526", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleToolboxForMac/GoogleToolboxForMac.modulemap", "PRODUCT_MODULE_NAME": "GoogleToolboxForMac", "PRODUCT_NAME": "GoogleToolboxForMac", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988331a90852808c6256e91f3ebaebd7d4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e7418fdff6e314ddfe28d5641c5332f7", "guid": "bfdfe7dc352907fc980b868725387e98a8fdd3c7e9e12daef83705afa6947bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98768302e0469f5df24e7b5730c7bae91d", "guid": "bfdfe7dc352907fc980b868725387e98cefc1c828773a9fea3bec017de376ead", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98329b66fefb642f0da6996a3a1389b8ed", "guid": "bfdfe7dc352907fc980b868725387e98af89a3e83c6a88b8b150647d2f526b3d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfcdce6792fa25274877ad6edb5e35fd", "guid": "bfdfe7dc352907fc980b868725387e983443f34dc793e28649f314cb74bd4d09", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f8dd3c3a654f3f9073732073ec3447fe", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9883f761147c6b9a92b99565161ac1db5f", "guid": "bfdfe7dc352907fc980b868725387e98dd71c75c12b36d50b25966c51f5df115"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9820f87cae90fa01564f33e67fb3dbe3af", "guid": "bfdfe7dc352907fc980b868725387e98037dcab307cda8268b43cb9fbe3a0a1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817b0a7c5b3c97af9491361691a227a07", "guid": "bfdfe7dc352907fc980b868725387e980101497900babc7e1a0283622194a128"}], "guid": "bfdfe7dc352907fc980b868725387e9863aaf6d0fa98da54bc756b10f9705444", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98fe58257bf7b4ea9900a460832f84def2"}], "guid": "bfdfe7dc352907fc980b868725387e98dc2a70af3ed5e0049661b9c6cccd95d3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c0fd762a586484acd39b6dba0e7611f4", "targetReference": "bfdfe7dc352907fc980b868725387e98e474ad9306e7b8df54bd6c4337ea1912"}, {"guid": "bfdfe7dc352907fc980b868725387e98b797f7a9dcd9f9975d0f92a6d1482143", "targetReference": "bfdfe7dc352907fc980b868725387e98a435583ab4c2282d404489aa813de99b"}], "guid": "bfdfe7dc352907fc980b868725387e9896bfb300a2f6d9ef507a183a5bf772eb", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e474ad9306e7b8df54bd6c4337ea1912", "name": "GoogleToolboxForMac-GoogleToolboxForMac_Logger_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a435583ab4c2282d404489aa813de99b", "name": "GoogleToolboxForMac-GoogleToolboxForMac_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9896cd7ae8c7639d8f9257b5465384bf6b", "name": "GoogleToolboxForMac", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98687f19ce59be21c066e59085f757b472", "name": "GoogleToolboxForMac.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}