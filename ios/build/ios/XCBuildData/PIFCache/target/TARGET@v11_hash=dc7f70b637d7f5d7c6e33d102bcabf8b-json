{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98988f621087387599fb808298a1d708c1", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a2ecdc397e122a3f97567ff16b507ce7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c636662362b0bcc8320b51d4849492f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98247ad21c5b9e4e190e1275f12f3287c7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988c636662362b0bcc8320b51d4849492f", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/quill_native_bridge_ios", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "quill_native_bridge_ios", "INFOPLIST_FILE": "Target Support Files/quill_native_bridge_ios/ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "PRODUCT_NAME": "quill_native_bridge_ios_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f685557278d72b3cbd63cb75cd1499ca", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e434b194e8ce7d9d0dc9a8ea9ba27f1f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985b4a829ddacea6e594fce18ac3ab2c69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d7eedd5c198ead0225c5b41cc3789bf5", "guid": "bfdfe7dc352907fc980b868725387e98edae0edc56647444b877be264063efb5"}], "guid": "bfdfe7dc352907fc980b868725387e982aa079f0d6c0b0288542d45b275fe9a0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9815c568b692670a257cffee120a27e3f5", "name": "quill_native_bridge_ios-quill_native_bridge_ios_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986b20fa06afd49bd8eb14532479b5f0ed", "name": "quill_native_bridge_ios_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}