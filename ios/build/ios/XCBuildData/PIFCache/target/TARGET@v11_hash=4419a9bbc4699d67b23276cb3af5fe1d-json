{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9875444efcbaa5e6a1510d39fe70529cf3", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e1524b9363655761513978ac14dd70a2", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980338b487b4f0af0807abaa34d4c52be1", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9866d21a20eb97554ae5b2a76f9c8fc737", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980338b487b4f0af0807abaa34d4c52be1", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b2b7eeb78fb6aa6cd72ccb6b01d0ee10", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8792cc82c427019ae5164d23c345cee", "guid": "bfdfe7dc352907fc980b868725387e9815cee8b775e5d418ae86403d764583e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c45604c1f87245900938f33806c72015", "guid": "bfdfe7dc352907fc980b868725387e981424fffdb69fd9495ecbb79f156e02d4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44e4bc867fa5f45e20f0fc7d40fa54e", "guid": "bfdfe7dc352907fc980b868725387e98bf2852402b0001fd46a30f60a8cb7ce9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f0f1e4fbcb03d3694a77253cc7967c2", "guid": "bfdfe7dc352907fc980b868725387e983c783ab489ce1a492935869c01a42d61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988856149bdb2808919e04892f1dc625d1", "guid": "bfdfe7dc352907fc980b868725387e98518feaa651e8f7b7e5c90f2737cdc950", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a55269dd6fbe7348d24a3351aa74476", "guid": "bfdfe7dc352907fc980b868725387e98b9e8099557ce0164bc8272d26d4f8a5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98313a2525541f9416b254499a80c2f1e6", "guid": "bfdfe7dc352907fc980b868725387e98e45cae66de38594cf9eb5519851d87ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833297940058873d3f9ffa1aa4798c1c4", "guid": "bfdfe7dc352907fc980b868725387e9873b05ee4caa37ed9c4586ff85ac360f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f55db718540a4937ccb7fd5c2e903550", "guid": "bfdfe7dc352907fc980b868725387e9879da1acaeb92ea8371b1d6964ce166c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e2a3b67a453f7191077dc957c1e8798", "guid": "bfdfe7dc352907fc980b868725387e989b2a9072cb0e0c063e6d4406c08ecae9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987463f81826446c43af543f82c97c6f7f", "guid": "bfdfe7dc352907fc980b868725387e986cedbd90f918f457e4d7ac8d4ea33367", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb38ce2c6f9be671053317bb9e5951c7", "guid": "bfdfe7dc352907fc980b868725387e987522288c1ea125f9372b91c1e83cb808", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb9b1edaeeb616251f161412bb727ab", "guid": "bfdfe7dc352907fc980b868725387e98830881ff0afd832a16f5dde3f8aabd3a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836811d2c030e8cd968923dcf9247e7b7", "guid": "bfdfe7dc352907fc980b868725387e98559c51e6bceeb3e122cd4e03df4ed345", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820daf7e1fb32f2c5ed7efcfcc2857f22", "guid": "bfdfe7dc352907fc980b868725387e9881f2e37332ebe8e9642e01e714f60cd0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b4856bdf39c2b971e0904b6a88a3bfa", "guid": "bfdfe7dc352907fc980b868725387e98e6d66997694569b4df9b0b97ea81e1b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a3adc50c77a4d9e29f0c0ea5ec2b811", "guid": "bfdfe7dc352907fc980b868725387e985d4c7edee92c0c0f925cae640690befc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f2f131d9c013f39433ebe192321a5c", "guid": "bfdfe7dc352907fc980b868725387e9839a24ea5e356cf88a8a95d04ec2d07e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c74d43c74a15e9cedd588d28c0ef9635", "guid": "bfdfe7dc352907fc980b868725387e981d937eb3b40aa5dfa98825952332bb53", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892f05562ea41a2e04782824bb0173a9b", "guid": "bfdfe7dc352907fc980b868725387e98ccb1897782eaacd76c03ecc676e8590a", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98971f05fffc3d983c299fa4e644cb6d92", "guid": "bfdfe7dc352907fc980b868725387e987e0088f732c4204dad15cef3619904d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1b5bd47a6fbd2e28e2c5559152f97fc", "guid": "bfdfe7dc352907fc980b868725387e98c5f1f469ab60ad87bd4afeb7d8b656a6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d9f862d03d9ba8b060ab46397bc5727c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9802837646a41eaba7b7874b2a2543f3f1", "guid": "bfdfe7dc352907fc980b868725387e9802ab2299f64e7c3837defd0d44834266"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4fd41dfaf342df4b51eefabfeab8058", "guid": "bfdfe7dc352907fc980b868725387e9806c9f20052906213ae3f40007a335f47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a651962627b413b7849b42000538664", "guid": "bfdfe7dc352907fc980b868725387e98eaa2014fcc682ab88b6068de1309529c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfaca905970837e6ed2b6749b7145982", "guid": "bfdfe7dc352907fc980b868725387e989e88f177030232e9a956e767be054f2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d2baf7120e48d9c08143875d8bbba0d", "guid": "bfdfe7dc352907fc980b868725387e98063200eb989cc596ff155daa6942758c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843126e1ad4de76b0f8b2339c03f71da9", "guid": "bfdfe7dc352907fc980b868725387e98dbd645458f12a3c6cf1378fe71861255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec59cfb5e4e3c52ae2c5913c3b3d741", "guid": "bfdfe7dc352907fc980b868725387e980dee08dc2b34655cdb409c9fb02ccfc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abcba063afa66cba016494fe2f698aba", "guid": "bfdfe7dc352907fc980b868725387e98838299e134f688bc07dfdd592d4252ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811634faf16a542c72fbc9385e4c5c346", "guid": "bfdfe7dc352907fc980b868725387e985f454e55c5491ef4139d590cc4e36f02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e8ad6ae636cc64eed6ad192de722c31", "guid": "bfdfe7dc352907fc980b868725387e98e00c770e7fcaf8de1f261b5dbb3bb1c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bca6d6e0f523075ceb02e7de313f7d8", "guid": "bfdfe7dc352907fc980b868725387e98350bcdd370c08f675f5f3a73c71f04b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896e9510983a8b9c38a986300bff2d033", "guid": "bfdfe7dc352907fc980b868725387e9837f39251832c456c47643c48f44f306b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98558785e3d4cfe836a000960860aaadee", "guid": "bfdfe7dc352907fc980b868725387e982f6a2d519cf363d86013b31561590530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc30cbfaa07d7319b1f848b739177ded", "guid": "bfdfe7dc352907fc980b868725387e9823f63fdd06ca9bc11e57c831beae3e2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874e7c386e62f4cea7c6fd10fe3c1991a", "guid": "bfdfe7dc352907fc980b868725387e98a31310d1aa56a2e7d94315e8645d334f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5edfa3b24db672885784aa48994ab9f", "guid": "bfdfe7dc352907fc980b868725387e985670de37568d936a4b650f026bea1dad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc40dd0a643dc110ebdb8d3762cbda07", "guid": "bfdfe7dc352907fc980b868725387e9898c0d859c21ef34677a6639786c2a027"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d900b62367d4f43d9df91d75d1cb12cc", "guid": "bfdfe7dc352907fc980b868725387e980283d998989a9eca97a7c442e7025e24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801f4aed02265aac031b2c6a66266de4a", "guid": "bfdfe7dc352907fc980b868725387e98d6e9be7053570ba95043f6171d820620"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adc3459eb9f223404193b4beb4386fc0", "guid": "bfdfe7dc352907fc980b868725387e9804dd4c54ce072b21a0578f74e1902c62"}], "guid": "bfdfe7dc352907fc980b868725387e981e28b676cc0a223074a8e7f46140e404", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e981749453ca8a188a92eab01cb307d0408"}], "guid": "bfdfe7dc352907fc980b868725387e981b1110a24024cb3e94e117edef9217a6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98099fe2c603d901c3a70223f8b8b5c9f0", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98c83dbfff546644eaaf3955f4c7f40bd5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}