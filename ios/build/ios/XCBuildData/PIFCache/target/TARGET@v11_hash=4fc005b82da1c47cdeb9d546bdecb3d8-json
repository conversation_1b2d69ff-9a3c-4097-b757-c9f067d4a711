{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ab0beee8f49449f8f2b17e4bb933ac7e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980fe6008bd0ea7d60a239f5cfb496f9fd", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc06e81495adb5e7ef973d6dacb12dd8", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984ac27f284fbf5fa4242b11e4c252631e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cc06e81495adb5e7ef973d6dacb12dd8", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_image_compress_common/flutter_image_compress_common.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_image_compress_common", "PRODUCT_NAME": "flutter_image_compress_common", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec29c327167a123c1352766ac15557e9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac4cebbacc73469fd7fff525ca9a948a", "guid": "bfdfe7dc352907fc980b868725387e9817011278146d850ad8c1800732eb1e38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892aa9f3bdac1931356fa0b853cec4a99", "guid": "bfdfe7dc352907fc980b868725387e980ac0e33a793cc19ff4abfb357eae768f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bcec637975a47fe917ab293fff8c9f5", "guid": "bfdfe7dc352907fc980b868725387e989d6f395bb26cbecbfd60c1405ceff351", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b20de8cb5f81060957bb3fda2dd62558", "guid": "bfdfe7dc352907fc980b868725387e98df995503faf86a7e536e8fd0813b9b8f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984957ce9c364c169bfcc6d74620f9e523", "guid": "bfdfe7dc352907fc980b868725387e984a42ac0fe50661fb4253f6986ea45bd3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c69a691d75b49019da5e2f1466bed3ab", "guid": "bfdfe7dc352907fc980b868725387e98de0db65b473b31d0081f4a5269702284", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810c44e7834ec6a0bf17c70f169208ac7", "guid": "bfdfe7dc352907fc980b868725387e9860e7129d76385a521f0b9fd136393b10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809f83877cfd9d47da03dc5607e500f2e", "guid": "bfdfe7dc352907fc980b868725387e98c102fd841861eee3b0b8bacef085b917", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b86dd6c2b9e7d333c8a3769c369f5f8a", "guid": "bfdfe7dc352907fc980b868725387e98f8633a22aa08547c812ac9bec8b70ea8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989da4879e47685f66bdabe6b8240fe9d3", "guid": "bfdfe7dc352907fc980b868725387e98daf47c85e530f3405587d1d40166baa5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be3e0f5afe8c3ac81a6f0b46a1a4da56", "guid": "bfdfe7dc352907fc980b868725387e98acadf1494a3e3cd73f3caf0731d75f80", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a0e000ffab48714111da7e794dc19e5", "guid": "bfdfe7dc352907fc980b868725387e98cdf6a5c0e94262f9f4ac605bffef3bfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804aea09826589e8a2b1f6e5fd4ffc037", "guid": "bfdfe7dc352907fc980b868725387e98749c0e50cc6ab0c5d2ae99062d78bd10", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a038f6200eb342b35bb229325f1e9edb", "guid": "bfdfe7dc352907fc980b868725387e98b60c9e9460d623e196c3542325e313b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d584dc77ddbf4eb98ffa7c118c3861", "guid": "bfdfe7dc352907fc980b868725387e984176d1afb1f259dfdc07efe17ea652cf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf3cfa6cd55608fd852538e897c3ea0b", "guid": "bfdfe7dc352907fc980b868725387e98bc8f2f12d75589e115d9449c5986f9fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98407e7b691c6832f8414e815f8eb4621f", "guid": "bfdfe7dc352907fc980b868725387e9818b5f752d3526a5d027411efb16100bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa93e9278dcecfb24b4c3c84887c0458", "guid": "bfdfe7dc352907fc980b868725387e989de49e9d16c76d6cdb2cd05defe828b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4e5bee87d6e212f6fbd703cf1dfa566", "guid": "bfdfe7dc352907fc980b868725387e980869c8041a3db4283bedf358c81f931e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892d6e0390b90947cb9c31d27caaabcec", "guid": "bfdfe7dc352907fc980b868725387e98c1f1e163902f92d3f444c8eef23fe9e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985470ce0b5c10d20d3f5eab1a1babf9ea", "guid": "bfdfe7dc352907fc980b868725387e98ae14d59b03d674cb5e1bad0f3d076dd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa16e49943f00a830f84ce2679dae2e", "guid": "bfdfe7dc352907fc980b868725387e98edc2a89fb1a6e200ab1412ec87f9c9aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983431a48b53ed9b82eadc5a8a4b5910db", "guid": "bfdfe7dc352907fc980b868725387e98492b5866dd16c57c285b5e79c6867b49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af881c7a6f89e66c14596b8e6a806c66", "guid": "bfdfe7dc352907fc980b868725387e98e484c4f5150fbf8fabdad69f35a52c75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b1f53415c03cb571a663f00e85042b", "guid": "bfdfe7dc352907fc980b868725387e98d15e2dd2c99fea1c18b18e69ffd1b703", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f51f5590980aa3a5004b19908004140b", "guid": "bfdfe7dc352907fc980b868725387e986fee5e9c3343f8d1008352e701bc900e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98088922fc337a6f23757d8aae454bd8e2", "guid": "bfdfe7dc352907fc980b868725387e98a3b33d70365c2c21c6e5ce5c403a6767", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d89cdf827b7ebe51e8aeaa738a897ee", "guid": "bfdfe7dc352907fc980b868725387e98bc874e43a4cb78d8f4d1a0ac40c942bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98934cef9bf6ee848bfe3ec38d62197835", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9819091840af7d18dbac9c8447edbac9d7", "guid": "bfdfe7dc352907fc980b868725387e9829d2b0b2b7d95a9ddf7e598d6ffc4db1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf24fc5737f2291a2732268e53c3d665", "guid": "bfdfe7dc352907fc980b868725387e988ad79f08695ea6c2cca3c5d7d8b8e33b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4b446b39012ad8a960ecd123d02e454", "guid": "bfdfe7dc352907fc980b868725387e98eb8f6a719f355b17f23d8eb2e9a5d44d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98199ef7fd5293af6c8b9c351f41a38060", "guid": "bfdfe7dc352907fc980b868725387e9816670788bc7f1e372bfade4c15b03b8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983de54dbebd51099a57e7d1e78dba3c5e", "guid": "bfdfe7dc352907fc980b868725387e98838df245c5366c7ccd43779dddceac8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a4dc69159fe9fd5ba036822d39b2b9", "guid": "bfdfe7dc352907fc980b868725387e980cc73214077d885d0ddd9bbb802b0c2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a41fd7a88d28827041d4ade160c0768a", "guid": "bfdfe7dc352907fc980b868725387e987699d61d47db16d864283d0af14a2a38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd65e40a4ef8d03f776d7b5271b5770", "guid": "bfdfe7dc352907fc980b868725387e98e97804a71a7eda4782f6d18bbb55dbdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa9c965be9d570c2cea165008ddb64ec", "guid": "bfdfe7dc352907fc980b868725387e98d47beba43c550f9d5c74eaccd5ff08b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98349873d5f1e9022b12138cf42b7d711f", "guid": "bfdfe7dc352907fc980b868725387e982130a66579435704115f7e00d09f53d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844fa631022e49eefce84c615472b15ca", "guid": "bfdfe7dc352907fc980b868725387e98163ea20656902ae2aebec0d2b7d2c959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c34b5990974397f84a33c7303172bab8", "guid": "bfdfe7dc352907fc980b868725387e98c065af2abb2799503e76158497948974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f79d6890a6140234b08248f0809a3fe", "guid": "bfdfe7dc352907fc980b868725387e986c074fcd043656e86edc370b80f2b6ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98065ff0bcdff30e437bc114bebd0700b2", "guid": "bfdfe7dc352907fc980b868725387e98897022fb7c83452617bb5421eee661b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ad1d9c0e3c75778494869db91ce86d4", "guid": "bfdfe7dc352907fc980b868725387e98cfe537ef6fd48aebe0e7b866f6b1142d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0b253f98ef5671ac31aae8eacd7032e", "guid": "bfdfe7dc352907fc980b868725387e988830eb10a635306371e79be09704e0ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850120356b5c80d14396f135b3e879ce6", "guid": "bfdfe7dc352907fc980b868725387e989ac43435114fcf14579f257bc3f9c364"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829edf26c70d9aa0630db22668ef4a44b", "guid": "bfdfe7dc352907fc980b868725387e985cf8026ab77fe57445a9e11bc4c4c7ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823a7f7bd1e4883202d403b18ad3bab18", "guid": "bfdfe7dc352907fc980b868725387e98ef071ab9cef1d242de5b1113119d33b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98056df8ef5c5a2e14103b21a4fa30dcaa", "guid": "bfdfe7dc352907fc980b868725387e9852ddaf4c3962f25802ecf6e4d216c596"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821bbd2b550794274f641618837bb6a78", "guid": "bfdfe7dc352907fc980b868725387e98abfa7315b06c10451197abd58e79d711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988979f8245ca97d389a032965783669f5", "guid": "bfdfe7dc352907fc980b868725387e987f00ce95bbef4bcae9c2b6d4ab613e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ebd28c20ccacc36039ef34ff819a14", "guid": "bfdfe7dc352907fc980b868725387e981243c2de66d0689b6cc84852675e392f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f746b24f823206f75745ded9621e0ca9", "guid": "bfdfe7dc352907fc980b868725387e98a7f2e7eb44ba9c0b257d1bf51c2035a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a6f984975f3b6cf727e7ee081fce1f4", "guid": "bfdfe7dc352907fc980b868725387e987e1026d7e32253d02e2a13c87719a604"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ed4bf4b7a3f0fd660a51efb8ce6fddb", "guid": "bfdfe7dc352907fc980b868725387e98fe57562c583a8a215f5783496b1ef801"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f35eee70e6700b52c6a120a184382c1", "guid": "bfdfe7dc352907fc980b868725387e98aa36efef915c33b7edff59f6a9a0bbfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a3f74f8c96375ef2056225bb9112ad3", "guid": "bfdfe7dc352907fc980b868725387e98fd0b1420ef9ecbffd05e08127fc813c3"}], "guid": "bfdfe7dc352907fc980b868725387e981f18c3bdd543f0383a05196ffefabc9e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e9808188f8afd5c22436d8f270ee53ec170"}], "guid": "bfdfe7dc352907fc980b868725387e98f59cae5e753b0a31876694ce85c47944", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ec476f90f1f6e616e836f5cbf5a8826f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e982742fb9f3ddc293e43c65bab714408c6", "name": "Mantle"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9837027570fe3a09bb9c7d0d2620332306", "name": "SDWebImageWebPCoder"}], "guid": "bfdfe7dc352907fc980b868725387e982ec175b6b4d6149d1cce89f5f0b3694a", "name": "flutter_image_compress_common", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b928db338a2b0bf59a36f34e391aa676", "name": "flutter_image_compress_common.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}