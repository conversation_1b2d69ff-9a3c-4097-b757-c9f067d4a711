{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b54854bd091d8dd188b0dbf7d3539280", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mobile_scanner/mobile_scanner-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mobile_scanner/mobile_scanner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/mobile_scanner/mobile_scanner.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mobile_scanner", "PRODUCT_NAME": "mobile_scanner", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986e5271238892b3282d4b59882c2dff00", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ce9795c2f6bb68b7aeb28827dcf1e31", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mobile_scanner/mobile_scanner-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mobile_scanner/mobile_scanner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/mobile_scanner/mobile_scanner.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mobile_scanner", "PRODUCT_NAME": "mobile_scanner", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989a13d9787dbc95be2543b5e4b4172995", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984ce9795c2f6bb68b7aeb28827dcf1e31", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter_sdk/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/mobile_scanner/mobile_scanner-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/mobile_scanner/mobile_scanner-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.5.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/mobile_scanner/mobile_scanner.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "mobile_scanner", "PRODUCT_NAME": "mobile_scanner", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98823ee80bb539993cfb981e0caf532738", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98218d065d8abfcbb159b5dfbdc640bcfa", "guid": "bfdfe7dc352907fc980b868725387e987a66fdae57affcbac014a1effaccb2d3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a33cda50bee1fa8f353bd354076a3878", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ccea11adbafb1bd0082f58bc5c398408", "guid": "bfdfe7dc352907fc980b868725387e98adacdbab6864bf52a043ed8f8e7ff945"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb4854f88022f2ee13d41b4acea8392a", "guid": "bfdfe7dc352907fc980b868725387e982125425c4e53fe51e59d0aa4c0bbf6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec6517cffaa648c7bdbe36ec4191d396", "guid": "bfdfe7dc352907fc980b868725387e984322a98edb8334603f5c29eecf4fe77d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898c00d603cd0f646bd4e8010320b3467", "guid": "bfdfe7dc352907fc980b868725387e980e6e0b0d69c3c7074045220e211767eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f95a86bdab15049838d7f8e7222adc96", "guid": "bfdfe7dc352907fc980b868725387e9893fd00919ba33640485796ee84733b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985393a52f1c6bb14f55c535e62a7eda1b", "guid": "bfdfe7dc352907fc980b868725387e98776a0845a2e433bd5ccba971941080f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1ace8feda0a20ccf7f9ab97f32e32ec", "guid": "bfdfe7dc352907fc980b868725387e9882e05e46d5904f1ee2bfd5fa276d77f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812e0657ea3ad77a81d331826e643322e", "guid": "bfdfe7dc352907fc980b868725387e980933e93e5ba88a309ee3953f848f79de"}], "guid": "bfdfe7dc352907fc980b868725387e98be66c5e6de334a83d0d9aad3f417069c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e985a7f7ca620286f8ac88ad70b5d18e842"}], "guid": "bfdfe7dc352907fc980b868725387e984aa18253eb3e60a42f6ad4ea2665188a", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e989cf704c50ea4e8e8358187fd3d616b63", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9832e568d1d64358cf46b257009674a4a8", "name": "GoogleMLKit"}, {"guid": "bfdfe7dc352907fc980b868725387e98e39aae0c91f0bdebfb6ac42304942a79", "name": "mobile_scanner-mobile_scanner_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98b54f2bdfc3ce691d3ad04972a364d2a5", "name": "mobile_scanner", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9819d278982b4910681a163531507644fe", "name": "mobile_scanner.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}