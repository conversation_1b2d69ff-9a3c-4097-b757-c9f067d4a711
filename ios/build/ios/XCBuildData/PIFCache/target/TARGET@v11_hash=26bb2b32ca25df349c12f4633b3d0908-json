{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b51540160eec37db678c8241c697017d", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3f5bc36dc86cd766190c1fa6f387026", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d0afb8f7a5682be724b1e56d9e87054", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e8b9429ecea162e915a3e5c82b1ed966", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986d0afb8f7a5682be724b1e56d9e87054", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d773885dbc8d33d1e0c562721f3bd771", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a837a5d76e0fa87ab9209e8e42e74664", "guid": "bfdfe7dc352907fc980b868725387e98d58671f7bede9a4561a060326940ef88", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98611a59535e46e4ffc1da0a7c12da6de1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a5e251a04a7e4c8be61604de3ce1f2b3", "guid": "bfdfe7dc352907fc980b868725387e980c765301a424996140c84f214b1947b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980592cca64964ba37bf578e9db0d396b7", "guid": "bfdfe7dc352907fc980b868725387e986dc3cdb441c51c3171ad39e5169e7f28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5c935d01d2a8df7b007964a300825dd", "guid": "bfdfe7dc352907fc980b868725387e9843b11e52f81e8dbcc02669f706676868"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981276c9fc245472f64f97a07c3190eda3", "guid": "bfdfe7dc352907fc980b868725387e987749866bf87c91798224f97bf071392e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984df89ed48395d8162e249d7911bdf2e5", "guid": "bfdfe7dc352907fc980b868725387e98dacc13aa8c05e154ebafa13baa0e3ae8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987524a411ff20ecb204479bdd0dc44eab", "guid": "bfdfe7dc352907fc980b868725387e98a10cea1e6a7445e3949bf4682dd90c17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981951e8016920001247efeabd7bac06fb", "guid": "bfdfe7dc352907fc980b868725387e986560be1f7f9655acd309c1cfff7d91fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989cbffc16cc3b37c648bdc31b4b71fe0f", "guid": "bfdfe7dc352907fc980b868725387e984da50f93913f66ddad1e35d389892b48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831c4aa2a5549233fc853685a4a5d7a6e", "guid": "bfdfe7dc352907fc980b868725387e9839f8146c6b59da4ed11ada02f2d87f88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879a5fad19fc78dc93058c7e35ca1b40a", "guid": "bfdfe7dc352907fc980b868725387e98a82024d9d7221981e73f80679ebc8a2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aa58b488ba178b32a311ed201463eb3", "guid": "bfdfe7dc352907fc980b868725387e9850b477589966ee18a47ace8c67fc61a9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca016c54b013173f57fa8e3e43acb7b8", "guid": "bfdfe7dc352907fc980b868725387e987bb8f96448c12154c4c18df2608c411d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1d530172aa7cb40004e000103494da7", "guid": "bfdfe7dc352907fc980b868725387e9811d8b730e9f93a12babbdfa48ab1e0c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c8c83f7f0033f174ee7068cbb03456", "guid": "bfdfe7dc352907fc980b868725387e98a6905fe3427bbcdde88dd8518f27ab05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb7bf6e50456941d3b5b13d1a18111e1", "guid": "bfdfe7dc352907fc980b868725387e98b8eb8d0777f1616be859b595b3e8d8c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6d43a19b8d3eaac498c5f025f987112", "guid": "bfdfe7dc352907fc980b868725387e98905239d0b06bf6425babbe4b96c89280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a39f427cb2f6d54082848663e5b7133", "guid": "bfdfe7dc352907fc980b868725387e989628fbe5806ff720f2838e6c47e0d6d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e632ed929de730cf4afd55106a79c26", "guid": "bfdfe7dc352907fc980b868725387e984e3d79b7ef91911c226137e74f3c0fa9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b2ae9f082d212c5166b5ddb24307fd", "guid": "bfdfe7dc352907fc980b868725387e98f98f040b85ae9ac14cb1fe99499a791c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879f4615e9b3fd01979cf1f510f38d1f5", "guid": "bfdfe7dc352907fc980b868725387e98ad0b973b394913bc048ca1f088831993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066dbbb470629e50ea2387718c8c2617", "guid": "bfdfe7dc352907fc980b868725387e9836de7a91895018d86e3c556bfb5dc594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8f72ff486686b0104df1e9c1a39298f", "guid": "bfdfe7dc352907fc980b868725387e981e0eccc6d67bac9bbe1194034a1b82e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880f3af3c8710088d9bb7ff4f7211e9d3", "guid": "bfdfe7dc352907fc980b868725387e986431f8afe53f49f65bc75a44c3a0dc27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec22bb4e958e067223fc6c6a7190a6e5", "guid": "bfdfe7dc352907fc980b868725387e98b83f6e0ba3e16aeb958a53a6a5cdf758"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d604b91d2da470bfbbacbf470f6efb7", "guid": "bfdfe7dc352907fc980b868725387e98ef07cbf542f52d316a9bfd2f7bb67eae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987844b80c58d2588dbd27669035fc35ca", "guid": "bfdfe7dc352907fc980b868725387e9880346555dbf076308cf9a9df724e40b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98648e4764281b987fa5a53858c95f1df5", "guid": "bfdfe7dc352907fc980b868725387e98936d0379896c8dd6afecb93b650a1116"}], "guid": "bfdfe7dc352907fc980b868725387e984c4168c6db492b578cae259f9e367f21", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98185b0a410d6773bbf97909ab82330835", "guid": "bfdfe7dc352907fc980b868725387e987f5a01b6f8eecd806f8940e8fa90397d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ecf1a81ce5cd8b78914d0bb073baf23", "guid": "bfdfe7dc352907fc980b868725387e980a734df298a32527391dc78b328062fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e989ca4d1bd54274cd86bbbb15130ac329d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fc4d1bcef8ce62596157e2d0cd5cf4f", "guid": "bfdfe7dc352907fc980b868725387e9853a0771c892be5e1d852c7b5ac73f2ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981925823dade64e954318992e61d3ff24", "guid": "bfdfe7dc352907fc980b868725387e98d2ece51ba14040ee30ff6364bfc59b48"}], "guid": "bfdfe7dc352907fc980b868725387e981483f7e0740ed2d8df7321ca11b72116", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98891c439d9f28afe994cf5ec184e7d619", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98ce9401ea8a299c7c114ba93208312e1e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}