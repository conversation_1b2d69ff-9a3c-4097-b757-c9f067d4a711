{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98794ab99d062b6a19c08aaabe9530ec55", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98da793d614446cb88e06df5ee22e17590", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98465cc28c8c9f53df5fdd0ca8b5a05ef3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c4e7978c3495d04f128d961dba9d704c", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98465cc28c8c9f53df5fdd0ca8b5a05ef3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a0d33cc2338313c3d2eb4399a5642893", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98eb5ed254baebc34f5d493ac99e4b5615", "guid": "bfdfe7dc352907fc980b868725387e9884f39fb16cadc0b1cc76894015c079f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865cb8235139f070d0471bc1998d92e2a", "guid": "bfdfe7dc352907fc980b868725387e987ea4e8f8a4905b5750b728df11cb13cd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ceb26ae48b7de50458e944885ceb9d4", "guid": "bfdfe7dc352907fc980b868725387e98a6c21db39877569cebfde50bbdc2d2e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989adc4353c963e49db072bbbd9a3c3ba1", "guid": "bfdfe7dc352907fc980b868725387e9898fbe034f6cf462c8acbd2ef96533ed4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ed69146cd7ba725253778dcf80ac169", "guid": "bfdfe7dc352907fc980b868725387e986d53e1f20d7b041b7729624b853aa7c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987a854066c0f432e146baa889d1d2b113", "guid": "bfdfe7dc352907fc980b868725387e98d6b95c0b5d290a8cf6ebe7822a107995", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984217e022de6bb1d7bb3a31f6fa523fa4", "guid": "bfdfe7dc352907fc980b868725387e98f81a89ccb5afb17f9bd447fccd1cff5b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802e999e67d78466b8925250e9ff66d23", "guid": "bfdfe7dc352907fc980b868725387e98fa08e25ed0de015617b2afea4a3e0b76", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c674e13e7848a87760a4cd84ce3d4d5", "guid": "bfdfe7dc352907fc980b868725387e98057aaa321b5e28ae03e80bd8534f5742"}], "guid": "bfdfe7dc352907fc980b868725387e98c7aa8482b8f7e04bc7c58063534f4a62", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c2602c257108e7ede5a80c0fa989881b", "guid": "bfdfe7dc352907fc980b868725387e98fe1aa4e5a9f86902c2ccbf5b687256ab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98773c68b403962fd2726463b7400de987", "guid": "bfdfe7dc352907fc980b868725387e98380927f4b2889796455899beff505858"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98babaee591e1535b764b3a9130ba56f2f", "guid": "bfdfe7dc352907fc980b868725387e983e8ecd52f368e4184473806c2b82e2d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b776714675328d5424c4e420b867c2f8", "guid": "bfdfe7dc352907fc980b868725387e983009892189ee9c351e81a3c2891dc616"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b31befb3f381a2029ef62106ea0414", "guid": "bfdfe7dc352907fc980b868725387e98130361f4c47d80537ecfb32628cb2e53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872a32a384ca584ba043aeac03bbe6037", "guid": "bfdfe7dc352907fc980b868725387e98b6162f0dc646405283082f5ce794da4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98309410edfb97b8f1165245cd27a6169e", "guid": "bfdfe7dc352907fc980b868725387e985a2f595a3f12c8e7cb60c0a6764eb0d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c30a0e6bd6da6b27695d9fafa2015a31", "guid": "bfdfe7dc352907fc980b868725387e987c18def4c87ce438febe368e45427233"}], "guid": "bfdfe7dc352907fc980b868725387e98983312c6f73b5ce625fc3b21589dff30", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "guid": "bfdfe7dc352907fc980b868725387e98c8fa069e5182344245b3c527819e3416"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "guid": "bfdfe7dc352907fc980b868725387e985cf123ce26870fca6e488a4c68fd3dab"}], "guid": "bfdfe7dc352907fc980b868725387e985ea99955cf8b793f70f6091087418edf", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98be4cea14ce76935c4227ae7ba4c2e192", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e9854e215567a4d784c55b1f342a3119c5e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}