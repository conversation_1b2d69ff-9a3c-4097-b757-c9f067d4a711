{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98f163dd7676e34d73118265310fd8bd77", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98991e523ec0ad6b3945cfe4387793f8a3", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "15.5", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98fbafb8a05ccd5bea303e1e67e163fed6", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9893c8d97432dd5f6c0f22699ed047cc86", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityPlusPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab7f558dfa412a154db7765da3c496ed", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/ConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df19cfb4c30f165759799231bdaa3a6e", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PathMonitorConnectivityProvider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e1727a09bcf4e6b37e6305c3e2ec27c1", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources/connectivity_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f700e08e72167d99a10feb2b4e568f5f", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcb602481879195261123bb0176793cf", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d913af3bed87a7692ef85f4ef66c2ef", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98576b45202df1f390869d335ce9f344b1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871df58e7e7216a06a894cc1d7cabba50", "name": "connectivity_plus", "path": "connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98487acc3bbaa5961226f9a9f4322e9f1b", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ce5ee051e26e593b23956efd6ef75730", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a9e20fae3e9b1e839182f49076ff207", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98926ed1c19fe078ee563f6c36ed45e806", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bd93eb15c3260a77eb36a3e68b12560", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c73422375d0327a5120fcc1edb241cf5", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b109b0caf8b1e5d201a4ad23a30a7885", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98224e3b6e299de8d06f1dff2a4c79ebcd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dfaa9eb558d6fb5cbbc64879fcf65f09", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e4cc7d289e244328b778f49a3f1432b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980737155c5729f3db1e24572407f43436", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d78e6759432502b18c8fb48cad3506b2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9836d2fc1ca1b3724484f0e1b6be00587d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894c49cc7792340f31354fb44c6b52000", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d8b45d163e80daab3c1b235a3afdb5c5", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/ios/connectivity_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e39e4ee0d7323d4ede115c456fd9f3bd", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9802ddc02ea68152a3372d7401f9b417cf", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9825592425cfaaa068cfb9b0ce6784e9af", "path": "connectivity_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98988fe848d7357475f5490c60a86353d7", "path": "connectivity_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986698a0147e9041bab086117ee9a429d7", "path": "connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98809829b6ae3a89fde3df34ee58b99e54", "path": "connectivity_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ef0f342edf673ecb76d441ffa3655cf", "path": "connectivity_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981052c056d7f5de3bb8720e276756048a", "path": "connectivity_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a4b61faadeafa3094bd4505c665a4250", "path": "connectivity_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983fb22ee9b054e675fde8be48f9e5968a", "path": "ResourceBundle-connectivity_plus_privacy-connectivity_plus-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bf12f4dfc581ee4cb082097df188a587", "name": "Support Files", "path": "../../../../Pods/Target Support Files/connectivity_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4ecac4b938431caed2ba4d49ca2fac9", "name": "connectivity_plus", "path": "../.symlinks/plugins/connectivity_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98159a6d448738c00ce85b000a95a3c781", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be06340baf8523cf4421f0d475976652", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/ios/Classes/FPPDeviceInfoPlusPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987132b7ffb8d22e8a18bf1feb47a81109", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98160d7644b715a0d6b6ed09b23a9578c3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9812c7dd74b616fe586c93ba2f05b81c06", "name": "device_info_plus", "path": "device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988c9c5cfb8c9f31bd9445584c4515f97f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e11dc86ec048dcb61edef8a6080fcf6", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883da087d89bfd952720fff01756262ad", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf237ce274ef4cd2961dd6836d353d3f", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e013b0266e78741a55d0951076af412", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2500730d3c1a507ed89782b4a2aaf16", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9303d7fad4744283afac92f9424b991", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c7d81dacea9592c3146b369469fc19d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816486b010554f3ee000222c0b4e4373e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986520fda96d8f2f88981614932cf774f7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895e7f7906596510920cff57c67e1843f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857d9d8a9ab91fde4fe3e7418abaf0a00", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982603fd737bd508821c8997df5b37074b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/ios/device_info_plus.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d69b66f83d1377b6d3e7f254ef02a443", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985314e3a2e2c36e6cf5e2ee0829fc480f", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9890f46d99202d0a007d01ba120668a91b", "path": "device_info_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981c1c8937d11950a8702f3afb942d68ad", "path": "device_info_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9838024a6a4b3c81f97e6b2fa677c9b4d7", "path": "device_info_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d05c329f3619c611cd18e537dd7ebd6c", "path": "device_info_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876d4c8b8155cd5388f1c36e680d5a6ad", "path": "device_info_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c799717c6be0aa56397c11cdc315f24b", "path": "device_info_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9826904caa560c26bdc04769b63064b194", "path": "device_info_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bce99d507ce1d04618fab24507745933", "name": "Support Files", "path": "../../../../Pods/Target Support Files/device_info_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855a7636364194209f98d3447575a0d79", "name": "device_info_plus", "path": "../.symlinks/plugins/device_info_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98df3602113abbd035e70c2f2eaeb5a33a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/FileInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825a683326338b3b3966cebff373314a6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/FilePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989c4df0741e8f544cd969b9216714c500", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/FileUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988bea372d7e6c2ccc8009884f8c23b085", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/ImageUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9813e4a8c8d763d59607f2bcf2466dcb5b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b557875b96f163444852ddaa15b7a706", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/include/file_picker-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982465ae8271a57c3e70e862d8dd4988b2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/include/file_picker/FileInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a5984b8af52f1bc41ee9aec490177281", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/include/file_picker/FilePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f527fea9717bc97e0fe289bbcf17b6e2", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/include/file_picker/FileUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee97fbbbe680a95dc892334978091ba9", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/include/file_picker/ImageUtils.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984c3c2a2278a23a8bb143afe28e7975df", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9883d6523499a2c9039c4a0d6b6531c1d1", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ba550b8f6a76b3b2f553f46327801be", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd9e55d0698e1a3495a06b113a901755", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868983088671df1e628857e96fcae9506", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb0d1f3a1ac049ffacfe3bc4840198c2", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880b2605ba72827bcce3b9d7c71a616b6", "name": "file_picker", "path": "file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98599b3ca6d7f3c177f2b57744e7c8e139", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dfa53af0ca599cd95501678abbc1107c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b88e05da85a603d21d1d71dc3869ee74", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a33418e2dbbc598707b9916aab3d18a", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806627a615711494465928716acaa8940", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9882b3f1ee43f4aa4da53c33850f18a67b", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9816185f9f8d94f1f5e3667fe35a796b4a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869964a5d01f2ec03e78ad765b195103b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981846124adff37e0224a753181d2bef5d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1b5d28e95bac9d9e42247f08a4b468c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b08a95f12f1acc87c9284b5428dd6ccb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9877d2e05bf5360e837479232a7a72c361", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d2cdbdad5e4e60037eabb96d74c4669", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819fa0765e3329065fa5795d5328f0b58", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981ed6f00169633f3a6ed409eb2d6f59e0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker/Sources/file_picker/include/file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9894bbbb9a3122d3ae5a6b0098e526b3e3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/ios/file_picker.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e983e1737ada151dc72582ea364f3608584", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/file_picker-10.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98771a1224535822ee9fcc3164af2fe7fb", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9823959b5078ae663c7e82a7678e49872b", "path": "file_picker.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9870bfce27c25273a9056113d97feb9edd", "path": "file_picker-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98945a2a8927545ba805e2ace5d97f3ca8", "path": "file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f33010f196596e48e9a6556bc2fc2c87", "path": "file_picker-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98820b7623544825fd24a5fda0b719f82c", "path": "file_picker.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987ffcf6136b17430e3c767fb1efb7e562", "path": "file_picker.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c6dca260d128dd54937d5f07b183bc06", "path": "ResourceBundle-file_picker_ios_privacy-file_picker-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ffe6b0d8555fe8d628c8490c23b490bf", "name": "Support Files", "path": "../../../../Pods/Target Support Files/file_picker", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba21325d00a59c231ff11e1e519dfb11", "name": "file_picker", "path": "../.symlinks/plugins/file_picker/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b89c63d7f3dd7ac7ed41b5dc5703ba20", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98001806556f7175db30c8889beb14700c", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838f9cb31bebe836ccff92e8ea592748f", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987870853ed00bb4703ea3f51d18937908", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f55895b4ec3aa27fdb48016e385ecb2a", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98152eb42b9b9a1b2fb5bd2b82fccbd79c", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "file", "guid": "bfdfe7dc352907fc980b868725387e98a07239dbdc6633d8ea155cc96a26a680", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/ios/Assets/download_tasks.sql", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980e259a1e2f43cd75ffc0b114589489bf", "name": "Assets", "path": "Assets", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d17d83c4b7151e6c3bd6181e7d0c732", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/ios/Classes/FlutterDownloaderDBManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980da3522224b9508e150efe65805320bc", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/ios/Classes/FlutterDownloaderDBManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9829060d4f4da0fbc864b5cc0ccbd72558", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/ios/Classes/FlutterDownloaderPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989fead15dd4bb1e9600d013c839f7cb94", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/ios/Classes/FlutterDownloaderPlugin.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98eacf0ff8a263f6ec0d07ce8679d94507", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983732a24aff125ef295d7d44e12c00de8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98190f7b24fa61563da1b519414c898595", "name": "flutter_downloader", "path": "flutter_downloader", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982285c2fce9718737be8a847ea51b92c3", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef3a17d21cc40eddca971d06c4520d2b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e29858ac319466e5546b5a5a6150739", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9857662f8acc2a74ec72903c4906f2b7a8", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806776c84513726eb9918835e5eb04423", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be9c6d58fb9cb7b1a1f2def5d88535e1", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9886d79673361ab91561c68cfcd6394612", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eec55012da2c44526c0b23849b749818", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4b523e731a5c6b1383b85aa88985331", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c80c5b07c1035444a609cf77d69c58a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982389ab207811b290e91222e11c42ce95", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d05bc32528ee106f7bc3ecaa3650b80f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98c6bdf1fa1d22300e76817648b9c962e1", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/ios/flutter_downloader.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ba9223e94f77507a4e3e5dcc30eb713b", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_downloader-1.12.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988bdca28b66c5bd2ed99a25b6ca2f3646", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98f6321689d8bcbc6dc910a1644f462262", "path": "flutter_downloader.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e9f6a419ebd077133815d0c656116674", "path": "flutter_downloader-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987404356e0ae678a20b1c96bac3e7a9ce", "path": "flutter_downloader-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802b7ffb671971d4ea333526ffef74848", "path": "flutter_downloader-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ef39ff3607b0f763f5e19a7a0eb2a47", "path": "flutter_downloader-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980a335f3278b8f012db115b002a0008e9", "path": "flutter_downloader.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9868cbba827c27c63459eef3f9eac53c0c", "path": "flutter_downloader.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989c38d062ca2238bd8ad7af3cbdaf190c", "path": "ResourceBundle-FlutterDownloaderDatabase-flutter_downloader-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d3d48a6d25f6694a3ea52c449b2726cd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_downloader", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d66fdf18921eae4207981a972d2dadd5", "name": "flutter_downloader", "path": "../.symlinks/plugins/flutter_downloader/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac4cebbacc73469fd7fff525ca9a948a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressFileHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9819091840af7d18dbac9c8447edbac9d7", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressFileHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892aa9f3bdac1931356fa0b853cec4a99", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cf24fc5737f2291a2732268e53c3d665", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bcec637975a47fe917ab293fff8c9f5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressListHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4b446b39012ad8a960ecd123d02e454", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/CompressListHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984957ce9c364c169bfcc6d74620f9e523", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/ImageCompressPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983de54dbebd51099a57e7d1e78dba3c5e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/ImageCompressPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980d89cdf827b7ebe51e8aeaa738a897ee", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/UIImage+scale.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a3f74f8c96375ef2056225bb9112ad3", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/UIImage+scale.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c69a691d75b49019da5e2f1466bed3ab", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/NSDictionary+SY.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2a4dc69159fe9fd5ba036822d39b2b9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/NSDictionary+SY.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9810c44e7834ec6a0bf17c70f169208ac7", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a41fd7a88d28827041d4ade160c0768a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9809f83877cfd9d47da03dc5607e500f2e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata8BIM.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988bd65e40a4ef8d03f776d7b5271b5770", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadata8BIM.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b86dd6c2b9e7d333c8a3769c369f5f8a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataBase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98aa9c965be9d570c2cea165008ddb64ec", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataBase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989da4879e47685f66bdabe6b8240fe9d3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataCIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98349873d5f1e9022b12138cf42b7d711f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataCIFF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be3e0f5afe8c3ac81a6f0b46a1a4da56", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataDNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844fa631022e49eefce84c615472b15ca", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataDNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a0e000ffab48714111da7e794dc19e5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c34b5990974397f84a33c7303172bab8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExif.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804aea09826589e8a2b1f6e5fd4ffc037", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExifAux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981f79d6890a6140234b08248f0809a3fe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataExifAux.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a038f6200eb342b35bb229325f1e9edb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98065ff0bcdff30e437bc114bebd0700b2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851d584dc77ddbf4eb98ffa7c118c3861", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGPS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980ad1d9c0e3c75778494869db91ce86d4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataGPS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf3cfa6cd55608fd852538e897c3ea0b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTC.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0b253f98ef5671ac31aae8eacd7032e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTC.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98407e7b691c6832f8414e815f8eb4621f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850120356b5c80d14396f135b3e879ce6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataIPTCContactInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa93e9278dcecfb24b4c3c84887c0458", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataJFIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9829edf26c70d9aa0630db22668ef4a44b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataJFIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4e5bee87d6e212f6fbd703cf1dfa566", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9823a7f7bd1e4883202d403b18ad3bab18", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerCanon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892d6e0390b90947cb9c31d27caaabcec", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98056df8ef5c5a2e14103b21a4fa30dcaa", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerFuji.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985470ce0b5c10d20d3f5eab1a1babf9ea", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821bbd2b550794274f641618837bb6a78", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerMinolta.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982aa16e49943f00a830f84ce2679dae2e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988979f8245ca97d389a032965783669f5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerNikon.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983431a48b53ed9b82eadc5a8a4b5910db", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f1ebd28c20ccacc36039ef34ff819a14", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerOlympus.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98af881c7a6f89e66c14596b8e6a806c66", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f746b24f823206f75745ded9621e0ca9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataMakerPentax.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9879b1f53415c03cb571a663f00e85042b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataPNG.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a6f984975f3b6cf727e7ee081fce1f4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataPNG.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f51f5590980aa3a5004b19908004140b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataRaw.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986ed4bf4b7a3f0fd660a51efb8ce6fddb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataRaw.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98088922fc337a6f23757d8aae454bd8e2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataTIFF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980f35eee70e6700b52c6a120a184382c1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/Classes/SYPictureMetadata/SYMetadataTIFF.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859b2066974aac30aaaf41c7b5204da77", "name": "SYPictureMetadata", "path": "SYPictureMetadata", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880766694a749edeba560b7b20c6552c7", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db45d7c95228e9b39bc9abebb1f3e4fa", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ab3ae8cc93b4a094c43eab5a244355d", "name": "flutter_image_compress_common", "path": "flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982420efe2276ec8e548e4d0d84bffbab1", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ece7e06b53a9142cf3e1ec0d27b467a3", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a50b586aee386b0670f3745f0969fe87", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d59ec6c517c9ad5909f2d3f0be1381cb", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988843fef744db8dc7f2bc743e4059a4a7", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9829ff5c72f731333c05e09aa6238dd855", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ad15e1a399f00293e7fc037deaf9d2bf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862c58a13537b43bd01dac391ac070bf6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7e2e3640a73b9a26aefe846d994ec0a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f55acdb0ce001b3ecbb256b07284f10b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98877a9fd3b3bf82643d0dd96e40221a17", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fdf720c03bf778f9748f0bc567eda1f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98dc4c8c480e73f4dc9f2006e727feebfc", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/ios/flutter_image_compress_common.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9827ffb27aba11adf8cda9a90f4d0d3841", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98810c8a11d2c4c44830b25fac0f6c3ff7", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9815ccc3c6eafa8ddbc6212b76933d8de9", "path": "flutter_image_compress_common.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98199ef7fd5293af6c8b9c351f41a38060", "path": "flutter_image_compress_common-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984a584c201b1801c3602b6e8e53a4b920", "path": "flutter_image_compress_common-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6f52c0989efff826435624931757874", "path": "flutter_image_compress_common-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b20de8cb5f81060957bb3fda2dd62558", "path": "flutter_image_compress_common-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ab0beee8f49449f8f2b17e4bb933ac7e", "path": "flutter_image_compress_common.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98cc06e81495adb5e7ef973d6dacb12dd8", "path": "flutter_image_compress_common.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985df1941893ec2796406dc66b7614bff8", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_image_compress_common", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d8251f461d8621bc4d49126515bc574", "name": "flutter_image_compress_common", "path": "../.symlinks/plugins/flutter_image_compress_common/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c33ff4e6d0cbee17a5d0cd045471ddaf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_temp_fork-0.1.5/ios/flutter_keyboard_visibility_temp_fork/Sources/flutter_keyboard_visibility_temp_fork/FlutterKeyboardVisibilityTempForkPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983be2de398b438546306793da9b2b3878", "name": "flutter_keyboard_visibility_temp_fork", "path": "flutter_keyboard_visibility_temp_fork", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9832574988ebe9417ad8ac955f36978289", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f4b457d50202427ec8cecfeef232500", "name": "flutter_keyboard_visibility_temp_fork", "path": "flutter_keyboard_visibility_temp_fork", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9856fc69507a59d6f2151269b71bfa2ed5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f34e166c93542a7af81a6839179f617f", "name": "flutter_keyboard_visibility_temp_fork", "path": "flutter_keyboard_visibility_temp_fork", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ead6e072ee7f194bddc9867989e503c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a573df6e316eac1924eb62e0a2b87d2c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846267e003e6dfacaef24c88214fef430", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a81c32ea53e9a905d40846d167c58ac5", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9827c9ddb2c5b1e3879001dcd1962c3c29", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984567afd3719206946210846c8387b89e", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98acb88822e6691845191fe15a843e877c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f49397f56fb97257c68b16d198e58850", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d33efee6ac98dc8171aaaa18d2d7ced", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98969eb98b4c292efe5874212d4bc501e4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ef1b37cfc12a4a5bbcb3575c0c73ab0f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e650a5596e9c69a6bbb689eb5f37d6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9887176a5f2880272e19b629487dff7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c70cbebd42e04790999abb4ddaea94d", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_temp_fork-0.1.5/ios/flutter_keyboard_visibility_temp_fork/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e981bc47583bf4565a53ee4348e7e060e25", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_temp_fork-0.1.5/ios/flutter_keyboard_visibility_temp_fork.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e984b2b867d03b75e58fb4654a741bad42d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_keyboard_visibility_temp_fork-0.1.5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9875fc6ed115e7547e50ab64721167be66", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98dc0f0881b0039123c111145fb39bb38c", "path": "flutter_keyboard_visibility_temp_fork.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d3d015d427c0090162a0884e176c575", "path": "flutter_keyboard_visibility_temp_fork-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9831209ee65d44fe140e36944dbf14de66", "path": "flutter_keyboard_visibility_temp_fork-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7d96039906d2520e79402c70f85f122", "path": "flutter_keyboard_visibility_temp_fork-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f35b51f79d20a2da985447b3d7cee796", "path": "flutter_keyboard_visibility_temp_fork-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986a25ad7121aa447d4f5f91d8a3128bea", "path": "flutter_keyboard_visibility_temp_fork.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980089efeead24cd129ff05b12eeb5c081", "path": "flutter_keyboard_visibility_temp_fork.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cba426d3387b6ea13e00fd9faae70dd4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_keyboard_visibility_temp_fork", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe67e24f77247216ec4f6a1cb2e3835c", "name": "flutter_keyboard_visibility_temp_fork", "path": "../.symlinks/plugins/flutter_keyboard_visibility_temp_fork/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980c8e1960417e471e702e97606d11832d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.5/ios/flutter_native_splash/Sources/flutter_native_splash/FlutterNativeSplashPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98bd69a83aee638148d0d8f2e1f9d2eabf", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.5/ios/flutter_native_splash/Sources/flutter_native_splash/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982dae15c7a9f4b6ec4d2ee1a07e3e2801", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.5/ios/flutter_native_splash/Sources/flutter_native_splash/include/flutter_native_splash/FlutterNativeSplashPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983458b724061751e9d322823f3906cc6d", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867b98f8ebd7fe27ec11efe99d57593a3", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d14331b81767d2aa9de1d40072805f5", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ca7e6287b795045a4635f89667f0c12", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8f1eb68ac1130b3b933bdc202128084", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981b0d831c4ae642f0f1220f432ba9cb42", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc3980e0af3c6e5f9142bd5acec0e5b7", "name": "flutter_native_splash", "path": "flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819aca38aca1a8660d8045bc0c2d50537", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f0aa1fb8acc4c88731540de510b4faa", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98470d676dbda94382be30e440e0bd571a", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986bb3866d9a1e9e8f319313e629a8ee3b", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d8f40554b77a4b0cf913dad27060530", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cba3aad93238bfd5cda00dbf011822ce", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e40a04d19f92e2dc0d280b15f7ab1cba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98414862ea4a93e05560b239407510d8d1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f40c74b1b72eaa981b94489bae872b77", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98de7350cef0b4d006772c689682b4c98c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ce6edbbbfbdb9b7e4af63d8e3a9dbfa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a42be728ae79eb1eddf8b5b2a12fab9b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98239fd9bde2cbd741865f20c575bec8b4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b3a1ad5a85e88771a6be62697fb3e92", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.5/ios/flutter_native_splash/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e989c5953ede240f614aeb711c1d9b8a709", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.5/ios/flutter_native_splash.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98dfdf049ef8a5d06fc8e6936e1db8722f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.5/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9878fbf6a745388ac0010401ad8cd5ac5d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988b12b19bdeb634ef8336e84d63a319c8", "path": "flutter_native_splash.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f97416e0e4495751dee4fc5d72aa92b", "path": "flutter_native_splash-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f32995a359b318e32584b9747a59b663", "path": "flutter_native_splash-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988309d166028afde680f714c57f13234b", "path": "flutter_native_splash-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db34f0217a92365be1fc361f2523bd4b", "path": "flutter_native_splash-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c189c49e9bfd59d35775d3db62dfbab1", "path": "flutter_native_splash.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980c612534475ed5895c9583ddc57641da", "path": "flutter_native_splash.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b64c7cfeb8c7f1290dc7aeb099831f7d", "path": "ResourceBundle-flutter_native_splash_privacy-flutter_native_splash-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e981f83bb948fc3be73954d907b69bc1db5", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_native_splash", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ec677f8f8b56db91fc10ead88bad3d1a", "name": "flutter_native_splash", "path": "../.symlinks/plugins/flutter_native_splash/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984ca2471aabf9f2b0ba007df976b06416", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9863cfe49d03650ce8facc3dd0c850bbb5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808da56e2c6c2fac68d1edeb0f37ec868", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806b27f9e1bbe843cc8f32cedc3eb8978", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b5274d265fcde8333f892206130962e", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f75111557b34503dd49af68e73dd0365", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987c06a0223670842c417a8201c5e03d1e", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983627f0c480597580e94bd457c0d9658c", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876f5b08b39728e8bb7e311051a5326be", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f7d0906681b3f649500d4230dac92ba", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980e1ea4893bdb291ddd8605b89f49f244", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98818c9a3ceda2d83b6a2b1acdd93fcf33", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982103449a851c0aa20cbdfd2228e6fb51", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980606696b21464a09594a5ce507e896e8", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2d4fdfcb0d4d86dac248d6f0ea0fa31", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/FLTGoogleSignInPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fa99a5741cad6e2004a07c0ab6e9f873", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981e95a20fcdcf934672a0db2d1934c38d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c34410a21355ca0085510914c8c48f1e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ccc55952e8d239aa72c61636a98a8e0", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/FLTGoogleSignInPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874e1fbd555f29c1095fb0e550c77646e", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/google_sign_in_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9852ed9597969c5849f7d51815df618dcb", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f3c30380455b0949e15c7dec9cc71c9b", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9869bf266e0d40e2c095934febe6897c42", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862727079b26a080a27cc8d0ed5142acb", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c3890289a9b1025566eadc4c57549d47", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c6a9617ff0c1fc394159028d3a1c15f", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afb5cc274cdb9eb210c0ea138ed35da6", "name": "google_sign_in_ios", "path": "google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f6409df1688db8817581ac3315533d2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e5cf0447090090e61adf560cc56d88c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e77d4ed3600d4cf613421a0ce67bb7f0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd1d26af5b3cc049d805409b057018b1", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989fe18fc76c74e17ad02aca16e264af40", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd7a9c0d1ecf54e062f68406c4a28bbe", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984bc53e0d0e421963b526ef950a665b7b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d563ebb2dbf599916823430e0cc0f5f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b25446795aca84358923e8cd32f665c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985bf7dff9e1b69e51aa82b89ca1e92691", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98967ccdc343a0a8b515450932abe6f1cd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b3ccb2e01819ffb190f8d8d321572fc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9873ef7f9f5af11be4192b36a1c03ee277", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986477a1e6c4e852515fcd9671b5a36147", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c520776816eaa4ab9064136518475f05", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios/Sources/google_sign_in_ios/include/FLTGoogleSignInPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983c143281260fb01a18ce1f7355f85635", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/darwin/google_sign_in_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d09f7d57ec312ba987cc44d0c2e14720", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/google_sign_in_ios-5.9.0/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9821a47949e15f91a8a97aa70fc076dbc8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9875928d8435c3e3197df453d7d0516f8a", "path": "google_sign_in_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9806bc61b2ae7ac15fb7aa7106072fc38d", "path": "google_sign_in_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984e147e615a437d88f55a2bc9911920e3", "path": "google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa9f8bfc827343358c5524f016e74d0b", "path": "google_sign_in_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9838aad89a3cfa68b0e2f042bd8bb92b60", "path": "google_sign_in_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98647276479f8b7d5dd8f3f0146e9064a3", "path": "google_sign_in_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9808ad69dc22f696e811bff46a99198bfe", "path": "ResourceBundle-google_sign_in_ios_privacy-google_sign_in_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986ac8c0abc90de5f6b30897e6e4ae4067", "name": "Support Files", "path": "../../../../Pods/Target Support Files/google_sign_in_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983173f0341cab997bb873773cadcd5698", "name": "google_sign_in_ios", "path": "../.symlinks/plugins/google_sign_in_ios/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e80c3989793c8694d5386c28c0bec4c8", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98187753e3f8c6bb109d5ec390bd98ad81", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9851c1215277a9a1a70f967d8e549429c3", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981518bc62c14b3c1f155215e6a73e928e", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848b25ed690ce0b230bdf45fb6d9e5215", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ae360b69f0e81e8499039deb2ffb7b66", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982572df77465314ce523576daf2f62684", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8c4b924338673e29e61700c20656119", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f997e740edbb810ca6bad064161e099", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d704ae1b696cc786326f55f2b417e05e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c657738a0e18070e2cda3690ca9f502", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4b428b745709c3c2296945e3d090a7f", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a3420bc52b14ed40f1486fb9aa8352a", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c44542381f5542196bea033813db2f39", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9860535617e29e38dcc80e65fadc609977", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerImageUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98af5fde2d48a21e570c8507a0df8d5f40", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerMetaDataUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98398ac1342d929befae700cb9ad696a45", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPhotoAssetUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b404e13eff4300bcd9c9fe19497abdd2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTImagePickerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984969c44357348006157fb763d20c4bc3", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/FLTPHPickerSaveImageToPathOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98516a37564896b62976c2b413dfaf4ad6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9808398ca91e8a921dd538905b40d36567", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f26aeb8d83c1d29b6d26fc9ad72c4fc", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerImageUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ea5f732757afba40c2883b15f0433d5", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerMetaDataUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a51b41acb2d85731687bc8b6abcea69", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPhotoAssetUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98137c16bc29ce45bb12b9260e99da3b2d", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e8edde379672d0794fb536f7ef8b61cf", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTImagePickerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985e9232db0fce8c82ed37d6033a258c45", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/FLTPHPickerSaveImageToPathOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee879ccd68784f357c523f67af6dad41", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/image_picker_ios/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983866b3fa6e2d5ba338ecbf10bb7c094a", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e71ccc916cd0e08e2e5f82f7a60fe3c0", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a658198b74b2aeb75d47f7043278574", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825d79061f04af4ea8055185869b64cf6", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98153a4ee1a78f443368d5424edc545ed4", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98125894ea8e58a6f86fba7933468187ae", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a7856fa4cd809f37546157ac31f5d332", "name": "image_picker_ios", "path": "image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f1fccdecb03ae0b17f9750685257b04", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98834e1e832f39f9f1b3387c072cdd58a8", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f834dfcf66f46321124a8028c72e626", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0a6507fd57ab31918cc886c4f7896ba", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881cfa421f5a7a6b72beaa7f2d0011328", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f9796414170cbb3e26456c19e03e6d1f", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980b08be29ffdcb59241aecf1a1046f2a7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989652eb0d5c7888d769b5f5fa0548af8b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbe720b4b0da2b456dc7fe21ef174810", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9899af3a32bb07dd7560ba32b4bf1a005b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6a95eab37fe038dbe4dd93f6f8db143", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98984441081c683da066ce1a2dd974dd70", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f51dafa5be1e92acaf37ed643ce9dae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988b5cec74c268887f105c3f5347572200", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98430968363faae4e00f4f9412ec8cba3c", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9885cc9928220df574c56b057e0e1c0664", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/ios/image_picker_ios/Sources/image_picker_ios/include/ImagePickerPlugin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f92a481d1242660ce5f10df08dd31187", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c586472edf8e55e306ffccef9caf2fe", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980069a48db3d91d6d9c92ddedfd1c8242", "path": "image_picker_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986b36828970adf247f70b512f56fc9ae2", "path": "image_picker_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984bac5fce29e8b4526f00c2ac3db79aa9", "path": "image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e7d1f44c0957173b1f97bc95bc7315a", "path": "image_picker_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a4e3002de1074daa225ad91acb69e449", "path": "image_picker_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9847913d00e6e2c5ef71e33027cb826a2d", "path": "image_picker_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bcc873a1635ef02bbdce2c092766ceb7", "path": "ResourceBundle-image_picker_ios_privacy-image_picker_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f4ada822b41f94f767c455499ac9188", "name": "Support Files", "path": "../../../../Pods/Target Support Files/image_picker_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2521271cbd7abf7b7b11d80f286df61", "name": "image_picker_ios", "path": "../.symlinks/plugins/image_picker_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ccea11adbafb1bd0082f58bc5c398408", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/BarcodeHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb4854f88022f2ee13d41b4acea8392a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/DetectionSpeed.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9898c00d603cd0f646bd4e8010320b3467", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScanner.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f95a86bdab15049838d7f8e7222adc96", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985393a52f1c6bb14f55c535e62a7eda1b", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerErrorCodes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f1ace8feda0a20ccf7f9ab97f32e32ec", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9812e0657ea3ad77a81d331826e643322e", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Classes/MobileScannerUtilities.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985dbc35622e9ab2e8b8ac355cf04f5277", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d1179c20ca4039387154ad311e8f809c", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98edf59c29cd820dda348d0cd84dca462f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98470490a097532a66e7101f23b87083e3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98331a35af331fe4645dabb5f328cb9c76", "name": "mobile_scanner", "path": "mobile_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98deb7104b75024a57d50f6649afec97f8", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989bd7388f9d61fed3defbd6a7066318ef", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac68cf174df8598a23ee1f66807ec3ee", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f5aaa4dedb16309d8a61ddc27e1dc51", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9839fcd7a1165d557ae23973029e42c37a", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ab401bc4deed5827f9ebe41eb32686f", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f837e8b27ca58158673cc4f975e407a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981cefedcf3853acb0e1fcf991dbe3a0bb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2b884b05bb6632cc3d77d2699b4bd3b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9867168106e364ab187f6a9e83d4b1bdba", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be04b3e3f91339c9d1385d5c7c9710a4", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987f0f68070e7617b8ce700f0b8301c533", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98f1e6ff12cb98cbadf7d15c3e9e6b2fa0", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982235f8008dc34ceb5f27efa36fc3b66f", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/mobile_scanner-6.0.10/ios/mobile_scanner.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982719cc4d6343951786355aa886768d41", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a2016026df91a63dde1b19fd9731a6ca", "path": "mobile_scanner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ec6517cffaa648c7bdbe36ec4191d396", "path": "mobile_scanner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98933f405c19f3dd62c57fabcbf31ed1dd", "path": "mobile_scanner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98580e4cae50f563aa131f0efc13312294", "path": "mobile_scanner-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98218d065d8abfcbb159b5dfbdc640bcfa", "path": "mobile_scanner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b54854bd091d8dd188b0dbf7d3539280", "path": "mobile_scanner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984ce9795c2f6bb68b7aeb28827dcf1e31", "path": "mobile_scanner.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98467c31dcd09eb6704fdd96ec2217c63c", "path": "ResourceBundle-mobile_scanner_privacy-mobile_scanner-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98875031b50c8a01b03231a0afc05ae9af", "name": "Support Files", "path": "../../../../Pods/Target Support Files/mobile_scanner", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98220d3c5c3e729925e15eeb391de211e8", "name": "mobile_scanner", "path": "../.symlinks/plugins/mobile_scanner/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981bab8cdbb84c7b0ce6381ac1a8d11438", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/nfc_manager-3.5.0/ios/Classes/NfcManagerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858a16938e3910b9624fee942942c6906", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/nfc_manager-3.5.0/ios/Classes/NfcManagerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e7c46850bed5189244a6d8c420ee1a5", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/nfc_manager-3.5.0/ios/Classes/SwiftNfcManagerPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9806d0f84fa9d2e7bccf1964869e7a3594", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/nfc_manager-3.5.0/ios/Classes/Translator.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9873f7ab52393f6abf9ebaac117084b341", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b9c6d3862b02d3c545f37de89f25b33", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98757e85fbae1c38e59cf393a25bac5c6c", "name": "nfc_manager", "path": "nfc_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ff302f8993ed6b7cc324aa17ab54960", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981fa6696bb7a5bc62a6a5e6a0c46f4565", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fbe08e4fdfe37aa5b32fa32c01a18139", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f7a722b62d364c27db6ba7ef582e096", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982300c6ce5cc602998c085674b7d292ac", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c5b166c5d2402e1cfeca94e743162465", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f74eaafcf55ef55ef274bdc1a9326d8f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d03a0b7b23a3a95df4d3dd4b142cca6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2eb18d2c713f7bc2ac03699f8cfb3c7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0b096e5366a78693adbc28763574307", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9881d2ff3012f4ff21c7423f0af6e691e9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98385f4dfa290065ac96495eeb8e052e11", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/nfc_manager-3.5.0/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988af35d833263bcf5c4fa8e8e91e6a028", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/nfc_manager-3.5.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e986ff269c023576abd53770923f89cda5e", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/nfc_manager-3.5.0/ios/nfc_manager.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ec21aa1f4fbb0bc2c6d143d0405b7a05", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98bb4d40560c11ad6b69c3d196df120bcb", "path": "nfc_manager.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891326ef073f2d9180e4d4baab5511df6", "path": "nfc_manager-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989b3afa5ba20726099c1b6d8e81464962", "path": "nfc_manager-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9d6a655837c09fb6a0a0c4c72b03451", "path": "nfc_manager-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e2e7c1f8cfe3bb4d8a76264a5f8e32d8", "path": "nfc_manager-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ef8a80ab68808d84d08aaba0ddfac18d", "path": "nfc_manager.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98850cdb678ccdd603ff93bff2fcdbdf74", "path": "nfc_manager.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9895f3b37368c32981deb0914d18222fff", "name": "Support Files", "path": "../../../../Pods/Target Support Files/nfc_manager", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9835bc0ee247a4ab4abf5a3daad915db1f", "name": "nfc_manager", "path": "../.symlinks/plugins/nfc_manager/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9803d911c8bb72d869d6d82f96069465c2", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98261d96a6e6614792d95078e8407b9a60", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989f88b560edfde5b4658b01a496943b88", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd6618896aed2c5eed80bf36834e010b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982f7ccf2945a7156bb4e3989a6a139515", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874bb0a23070be7ad0d36efb541fa2f72", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985fcc5a881c11a483a4f97cf15d5de7f8", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983848149832bd3fb14ab40a033453b221", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9821dac73fdadc3704c47dace3bc595344", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987282475d7106ee76db1ebe5820fc2c0d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98565dfc25a272723e106e728f21458edc", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a755a76be39fc2ff80956a9a6bee0276", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807868be39de1ff8c1ff460139e29b929", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ba89a2798c237c9d4399bb8a6bf690c7", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98617743f66119b0d0323b5114fa058d0c", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9813b7c07dce7b7a58119bc109e00cb73f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a0cfd636e0a829f568c8f3fac9e40824", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98afe4f2ba3e8c0d8a55aec9a92befa7c6", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d0728986f969c916cce4574576155d6", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987affef0e2594f1061c6142d2651841af", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2d71b2dd3cad0bd4854d2c3d25a98ca", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98716b0d0ab427bff3ed27fff64fe9cfdb", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ee5217e9ed0cd79f9f08d3404f98326", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989fef410206e330b7e25a97ce094acae0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7d08908db0906ab9d2aaaa7c28ecfcf", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98af303991e7e560ebc401844e60400f91", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb14d64bf1036a2fbf35121e345186b1", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98739974e914cfbbcaab1d7d0f9ada9ea1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989c959b04d6212834d69c90b6aa9ffec1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981eff5c2e30c469fed9c077790e53821f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcb57f8113d9197c3b72623140f58caa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6887f1012d0bacdf067e2f6f01bda26", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9838283da54f58907849d016757fa44362", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e2926bd480b936468bd6eb57161293b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a9e50e3edadd5809071765a04199079", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e988c571149422dddf62d4acba348073172", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988d9d92ce9e03395cab910819443ecf2d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a5674b55d1f6b8252eda3c3d05f53331", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985d9dbebc2bcdb3a9ceac797f936da9f5", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981b49ae905e0c53845b438b3af607f7ef", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c6bdee65b9b936263fbcb48a6652e292", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983761396c62eac88a0054f443218902cc", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a18e440edce14573a30c3236cedcc2b2", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9817707fbf8c9c2bf2cfae7f53700cb4cb", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9804bf545b50e74d57d8789b6f04a0800c", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982e01bda76d00424d78d7cc56b1ec9052", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d8368e4577084ed5431e669f027be402", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6c4758293e218c29e647f4bd7f40cc4", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98556be8a881f3161bb4c6b49766267692", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ad75d6524935d1c27794e836f277dfba", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c93a6254f6ee2e16837cc02b7cd5467a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd3942b3f12c86c94558c4b778af30ee", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98be4b78d82ba97ebf6d51d76536e6e8f0", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b027dc8fc5e82de2699f80d3dad8740e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982861b98d60737fa914af45d0641adfc4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98abb2ab9b0e19c37175a2b69a6786e45c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878c0efd877096b1000c3607d25d24c21", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e5ff174a878609f13faa3ef373a6919", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a5093dc53f960d2efe966b88206fcd0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982764b1dcf9b32aec0e38e1b2d6e9d417", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ab25d1043deac39b09f276ecd083d55", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc2d68e96aeb5bd371946e4c406ec7ba", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987eb55c39807e2ea9b2008b0c837f77b3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9893dd35d6893880c23e6b0754caee4f24", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98229270f805fc095dc236f2ee08087da3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883ceb308e6de024f66c08059079c385b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98acd1752ef782cc986687e276b8b3a2d5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983221db30ae1c0621daa5c592e856a5f1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98700e91e30ced3e54d6371aa0124f9b3a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98797b6eff9fbe7cd0fe3f9f6d5ac06cde", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98875d63e5145ef9776a909827216634cf", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3b466148b56ef9a15068b1271d3a998", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98274fa51c7674ccc4666d5c149db091ad", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887644eb53c99513847ff113df4ed795d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d72c43d07dd965d76f1d1d7d6842d3a9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1e37cbdd0b57f246a1e8aa8e27b951a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98693d415812f21a82cc779e127971f7a9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9899ac7f93d532d5bd8a62c0d44bd04c4b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98168c71e1b8a811259dfa638194a273d0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c470b31da283d5a02d6ac7d60a86f161", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884cd0730c0d599218be85af01bb7ead4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e17c5f600857bd38381c0f5ac4d98eb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fac26b0e6fd7b23940ca82c94a908405", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d791cdf252f5225e683246c4c3ffa38b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7dae520e37d24f924d99574a5cbf90d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987482fb8ded9de65cd553dcd8a34e5d9a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ae2d9ca0a98f455dc7f5cb23a95bbc1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987491af3797b34757d18a6621dd57e09f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985ca16c4e8e05a5ab5de52127e4527a20", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9805fe533845d1e95dc97f854ee06d4867", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986cf87311a575d9d918ae5e12d78698ab", "path": "../../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986851619475dbaaa9e520d0ed0edb7912", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b2d4073cf194240822b5fa89a9302c8", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e34c39584e12eb0bee119cc008dfd26a", "path": "../../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b4eece5b0e9364a6284953d9e6a79d16", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be860c5993247972528f723d8c652983", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f259407e016576d39369d2c2686180f", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983abc197e6d06172b3fb26ae2130762de", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b0f838032b5225e938138c1d8cc37f45", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bc8a421b88d3d9b27760e252aaa45e8", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9824e47ab8c09b1a725280d91ad4ca66e4", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e1efddf78d6a81234f6ad111d922aef", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98659c9fca34699fe4e1561eca04fa2e2c", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bdd538939d5ecb5052fe74bdd97494c6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989932141a4f2f7fda87263bb49a6c6562", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98648b175f584297fc4252748030a3834b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f639eeed77d7b294e2143e1df1ff3a4d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806d8a7058e85a617c62944566099ac98", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8a03fa1da522f6a9a93225071092f3", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98841ea7054baf17efb5979aff4387be05", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982af10ef83950d5ac5aab244859480974", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.6/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98066060bbe6edc9e64d0200d0b22b7704", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c01e4770f00b23f4783b69e2e94da6e2", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a590c9badede4e76c4bef31761971789", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989841aef49a8fbdfd531738bb198c4e71", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7b8bc1d7567b59a472cc61aefc6d2bc", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9890fb1e690edb1fab8e35cd4ee9eb3fb1", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9896add0c62aa03786ba355a31ebeed19c", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e1df362dc5e41384200cbd912f9ee69f", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d947eff73ae52dc6a47c856d74196dc2", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9848faf21fedd550bdf30db9351bc3aead", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f575245a0e0dd0e359a676e9c4888232", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d7eedd5c198ead0225c5b41cc3789bf5", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/quill_native_bridge_ios-0.0.1/ios/quill_native_bridge_ios/Sources/quill_native_bridge_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987aaf0e369100aa2e3d641e07bb6a754e", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d276923302160d2474a2a19dffccfb5", "name": "quill_native_bridge_ios", "path": "quill_native_bridge_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7b97f1c4936965208603deaaed9026d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988ccd74152eab464ba831c08da88fe8e7", "name": "quill_native_bridge_ios", "path": "quill_native_bridge_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a8276a298628f86fe67bb56c4d9179f5", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a1292e9f3a8a106c23a6e010057f566", "name": "quill_native_bridge_ios", "path": "quill_native_bridge_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98433226ddfac5513849d033b851d43311", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989171493a6b5fa9c9989018ee3817a7d2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9848b133b07160909517f0e757161fd38d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a22c71dd90a79a06c7274c939810c497", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f778a44b02b92b6b9921d85a4c5f7220", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c66537c6854cb6bd9bb5611e3adb2e96", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981a49f835f499238ff9caad7dd84e5c6e", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983b7d013de539319969edafceb6435836", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/quill_native_bridge_ios-0.0.1/ios/quill_native_bridge_ios/Sources/quill_native_bridge_ios/Messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5c6fdd9fae1baa7e2b97e20131be6b9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/quill_native_bridge_ios-0.0.1/ios/quill_native_bridge_ios/Sources/quill_native_bridge_ios/QuillNativeBridgeImpl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98976a51d1aa325d6cc17ce141fb821086", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/quill_native_bridge_ios-0.0.1/ios/quill_native_bridge_ios/Sources/quill_native_bridge_ios/QuillNativeBridgePlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e980f15759478955246b8c31afc57fc29cf", "name": "quill_native_bridge_ios", "path": "quill_native_bridge_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb5f6a7cb8e8c468a21be6ffd7dce269", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98733f0975b597be6625c9fdf39c87aa08", "name": "quill_native_bridge_ios", "path": "quill_native_bridge_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc3c71e4e4a8e2cc183051a6b92533b1", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e646da465921290a0864849e4a0d13f", "name": "quill_native_bridge_ios", "path": "quill_native_bridge_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e8da24239aa1f4fd027eb1bd0f934cb5", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9807726ee8fe39ba1e7d4718141c8f4a1e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984688a0df4618fa7cb5b2393d353aa083", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5ba493e5b43e415c934967d743d004e", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e91f44c913680908b4b56065399689a", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f786a2308540208827fd839035f1e2fd", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98199a6a02c7dda6f433e1616a7b047edb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf54b711bf5c4ac1c7aba98a5f78497e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b8bbc12638cd0c7b8f265b5a275bb554", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982162d1cf69ea425e3c221f2ffc3dcbc7", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984fb27939658eeb4a398398d91e454d46", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ce3c8faff7f0042a4252472b4d232b8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f077d7cc05afeb69522e4ee2826253aa", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98400d8322cc819a4575fb28b5a8384b34", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/quill_native_bridge_ios-0.0.1/ios/quill_native_bridge_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989fb01ac80f0d9ee44442c31963e6fe6d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/quill_native_bridge_ios-0.0.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9843458fda6bfccdca847fe4d2f9b6ac97", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/quill_native_bridge_ios-0.0.1/ios/quill_native_bridge_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bd12b81290c89075365d20bae1c2917d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984da590bfbdca49d32261ddb3a2c2883a", "path": "quill_native_bridge_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98100613c821dc78993e3430c0431c8df5", "path": "quill_native_bridge_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d8a9b504bf09bead3a0d22fa07a38546", "path": "quill_native_bridge_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e67078e20aa28b4618447d0dd32a1ff9", "path": "quill_native_bridge_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bdde9e3af8adceaa59335c636d8fc915", "path": "quill_native_bridge_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98988f621087387599fb808298a1d708c1", "path": "quill_native_bridge_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988c636662362b0bcc8320b51d4849492f", "path": "quill_native_bridge_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98c3b1c9fc4e2cdac962852487da11ac1c", "path": "ResourceBundle-quill_native_bridge_ios_privacy-quill_native_bridge_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9821e0d68d42135334669e215a31a1bcbb", "name": "Support Files", "path": "../../../../Pods/Target Support Files/quill_native_bridge_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c5c922bf2679aa4e72e826cc568f629", "name": "quill_native_bridge_ios", "path": "../.symlinks/plugins/quill_native_bridge_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98175f15ddb04cfd0d14b98b44f88a660d", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.4/ios/share_plus/Sources/share_plus/FPPSharePlusPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983cca1a3fa8a2424831bb25c6360f06e9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.4/ios/share_plus/Sources/share_plus/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bab7d733d8f8ce511ce86c7c39044080", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.4/ios/share_plus/Sources/share_plus/include/share_plus/FPPSharePlusPlugin.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983083391f8c0db7dba5afb784ea825da7", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ce6f671d6e95fde8bf766081e3dfa4b", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb3d678da589c8bca83fd451cc3c6e37", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f42b4a18a22abee9d7bb41eeab75c2e6", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a0e827cbbc6aa0be722cfc04a4d5b72b", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9834b729d53776931a794a7b7cf3327260", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9874ffa32848b3b66714179105adc09c6f", "name": "share_plus", "path": "share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802ae07b8823426fdd8af446ec6aadd58", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982abc236f3eabd55081e18d4ab68079be", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b405b5ab14c4f9bc569647c1b5861bf", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bcd3826e82f8344c436a60bb32552372", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986934005182cd024d1091882c0a89de6f", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985ea3ea326506de06ba981e347597b0b4", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac6e56938e49138211f3c2247211671f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b9cfcf62c640f89805985d5967fa7a3c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9826c96b97a2483f54a6fb318948040e33", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b5c1f91d7aad69bd2a5e13d2f254443f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e92b390a44de11aafba24e91882606f6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a19fd8f9e4f64aa71b4bfba5f3bd651a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985db4556eeac5d3a0eb964d05fff73f9f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98241a4f425f53253d189dfd9a59f6ad56", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.4/ios/share_plus/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98e64e63631cece279e5f5478ea72991a9", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e988f6ba1b67dd76edd403f3805181e2699", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/share_plus-10.1.4/ios/share_plus.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ae226493f8fbef114626001dcd684567", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f72e5f0b7b0bbce3fec40896d3e1118d", "path": "ResourceBundle-share_plus_privacy-share_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98907244a2f7dffe419d2f75c12b2cd3bc", "path": "share_plus.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5ba0a060dc65ab4e83d7c4a176baaf9", "path": "share_plus-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98aeb8563cfe362e2c294f28262b78a488", "path": "share_plus-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d3cd2604147ad53a395db9bcce4f68e", "path": "share_plus-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989aecfa664c9ec38bb9a7cbdc8e784693", "path": "share_plus-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98958745dda8db17f43d665cda5ae3af74", "path": "share_plus.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c0d417c0b42219e6fc785d7b61901682", "path": "share_plus.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b29b3f0b068fa7f317d3f3b054552848", "name": "Support Files", "path": "../../../../Pods/Target Support Files/share_plus", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e275ffc8898fe69337e35fdb447346d", "name": "share_plus", "path": "../.symlinks/plugins/share_plus/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984cc6db56da7687a1ae24bd2b22b33afc", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989e77d4b877164036ff08ea099dc314be", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987fcc20f5f4fd2ff6f58d4f6ff40cfb2c", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b782f971d1742be7b90202529c34e84", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98accbc283d23fd9183be7f6105be6015a", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98339192215912aab60f1b52a458cac15b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a1b14b2e24f665edd91d7689f293bd2", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9805a1512694059dd6f41ef199c1a441e9", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a91f7c423c36783a76ddeaee0d24c2c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98551a5f66f3a29330db4f4b23d87cc825", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e6422225346d2055519f19e8d78976fe", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc9bd89ee6c43667aae4a01b2a01f132", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e30171b87c62fd3fd259e794d76d91ce", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cf9d8131aa656548304459b98d0578d8", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9829ec2d458b41dd0ebd19bb3d083a2e43", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f899dc00eff95b5c5b707dd40aa60802", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853c6af21ed9c4f57603db16bcf95fd60", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c0e1fadaadee59952c8e69d1ef6a3714", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d90a17dd67c58ce4e6f53c470157cbe", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981e8cd609bafb96ed2a732520f195edf9", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d7f5cce2ff86c0a610ecab0366b3b9d", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98726fbc6e6e7b7fc19dd0ae11a7b96d78", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ab1dad027c40c73f823eaf1c39e1296b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8137c6a94abb33600ddc6a948a6161e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984114683a25512ef3c102cf16554f0357", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892429f9df1f7503eed7ba01f700b0941", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9890e8a8054c5d51f921b97c20d11053f3", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891e0f0b003b4e5c681537a70c6f6637c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf8ef8e7e8e44e1ddf61ccd522b5b3bb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9842ff904a701c4ed163b40cb27c713232", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9802fe9b79c60a996c6612934abb16e0cf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98583dc8a48706a6dea6f986070b3a2f6f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f191d55e6736c4d00c8f894f7600b5f3", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989e70547e99b5d65cd59dcdadc0323605", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9843e51aa9b4345a92f3b624e1a9544776", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b15490451bec6163afe588af767409b8", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e983ed5c7986e158d1b3e2d066dda5d1f5d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ec676ccf3a9977bb39bff6537722fa28", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e2b435ec9753203213b05e152aa0ef77", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98d4640dfc12e0222e77a2db2c799c4222", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885d2c05b25d7c5a83b416822b2c682ae", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985557aa51e1be957be8f68621589aed09", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a1e4ea67290ab9d9b780084efd281637", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985bf9370799ac89c3126b8e7d610730f5", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e983dc999aa0af12799c287ae48b86b96d2", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980862203791e06b66ab2c43fcc5531469", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98835d9491405b16ac5022e9518a2f99fd", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f4b5115b16d9cfe05e1d19c056185733", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e989d34b09dd000ebafa5f9822f73a1a8bb", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd3a8bfc65c4738455a7cc2eb28a962e", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b2309df648bfbd5b0a2ed1478620408d", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988f7877081ddc2e717654c10d92a4421a", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dea5b0d4655e14a58adec4a56f7bc5ca", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9801c230971b3f9711e6e9448282e8927b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855627441c23264826d3c64d6b963e9ca", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98654735adc1803cd9c4d19ee3f2bff643", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98be666f153358f078ebe6c382b82fb94b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866674f75d90586671f7016698d916ef6", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a3f4dd16e8b92cdfb9c3756c16e68a0", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9852b657b93b86782e1651eadd51b63f72", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98707efba4b4cda191e5c07016ac7f52d8", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b6a03a8bc637c68be5510331536eb2b", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98be2744c840bd68d4e287350e34ded431", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e6a25693d0f4ebd77cd48793477ae26", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b679c4a6596ce275a54b37465e227a7b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4f12125bdfd945c60f6421a299a8a57", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98386cdcb51161805e2ad81a7909e801f6", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815e3b4801651490647504ecbed4cbf58", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9869bc3c0f75709137edcf211d4f6e8e00", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98940538711e2421c9be69cf6da45ab6e9", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988039cdd7b4b72f009cb546ddc4ce8d20", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa56f057b4e29c9e953ea698ca54a468", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d9313da9bd2593992066e6a65a3657f", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843042654af5b51ae765674547c84e8a5", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98617c02d96896d18e11a56e2ae05f19c4", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9867769a05a467bf1762e93640e8ac5ccc", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985145c3563d417353becd7d3ef407069b", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a7c8b25f491465997e51ca003a46a61", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9833a55a0ecab6020c722db55b18ddd307", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988bc380c50adcf1f90c1264a71c714eee", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc918f2e05bc5226abb64ec60b7d3257", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9804bb9d1f327a9bbb07ce43741b7ef65c", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d56230b15c340be867d19cdfd855a7f8", "path": "../../../../../../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98872e9d4e416a389f2db278591faffb0e", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b4d3cde0a9291ffbd0698e37d3453ec2", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98281e0094143c8f10cc19df7bfa770481", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828560d196025747d559a9e91e2a71d4f", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c357bfb28164724473627cd0218bf2c", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b758caf5d3d557770fa042ebe76e675", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7930085cdd7b3c24f7ed3567e137540", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985d26670c4c2b70f524d1bd52c777bb98", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b5c2a02250daf2df4ab9b7bf6e1bdc2", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ec0d6b4d203e1171ae57459b0ea1000", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985f5d777755121266ec3bec1776b33cf3", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9825172f4f166341796ad631e802aecd81", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986f6772087b49ac62bf2a555a00334629", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985b543a363d4604716bb47a9500144219", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983f218d181905ade16be24869312fd497", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c4ec0c19e4a3cd7f0ac89e928bbc132", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bfdee6f65e9ff4954e490e25ff1a928f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9890a4d1dd5a3cc2b358fd0f917154f604", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d5cb64bfdbfc00e75cb14105616ed0a9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b60df53e4712f955b5b36515f3a7eed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b53d07aadf3aee472037974163ff74f", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e989103ba78ffb8005dde6de166208ce1a7", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e98fcf11d3299cd867d08f7e8969d6359ab", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e980cdcabaa36a304b640730fc28be27da3", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e2d1cf6996dff237d5494f3a995597f8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986042fa71a446c3bb067cf1b0b024216c", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cc01a7c10258cc3de3aa8c2d050c3aee", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba90be970874ef8ebdf35487b71bb251", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ef6aaa629c1cc70c66f5e961acca47bd", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e864e7efe315ca98f13bf3e87d4b6f18", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8b574c75eb14696156b4225bbd0f9b3", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b6f7107cc87a36e0fc80001a720e2ffb", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c7ddb80bd820a4f4230dc55c2878ee66", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c789cd16d509c8db418e2a090f1f8930", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987bb049a9844866d94ecf61f7a590dcfc", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98723863633c8318145360f61562dc9568", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fbdf3a9f1c4b475dba497c4ac3eaac30", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe6ffc1fc76377d292ecf9cb7c923d94", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98275352167b71e1cd73b9e1d842a681c8", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98649e87d2ea2f8b23daab65abe0bf09c8", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6c4fb9d58b35ead86cf6c1faa800312", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6e740c67063d9028b1172a64fe200b1", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bf8ff7494806794a213801a7c495440d", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f889c084028800459342780e2bd6da51", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a4cc6aa91b77539b694fcf79239ea497", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892f20c9fce03518bb774eaff7abd1f6c", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cb0afeecbd108e0774a5a2ecaca41dc", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e8a3d4f9c2e82c10344b67ca9ded195", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987e915f1081be4b65457ed847b5f2a397", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9849e3978c5303b71eb756db495213ccc2", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9889e155f00617f97cc2c39dbe75cfae29", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98499e68943c751c63c9a978594de84682", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bbbab0a0a91c3229914fd967c5bd4040", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9868dd3cbee4f1e3d3a52d23ac138ca0dc", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98274855827aa52e6bdc0948713b20cc94", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb7799ab13794dafa66bcbc64cb991ea", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98976f51dab0497c4174cac712f7e8193d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982bc1458e7fbbef0d5230f583c2d008ca", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dbcbb9e427d849a5fab11cebec67a8c6", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c8ec27cac0b2893a3ecd69d1873119ef", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e2bdc26f33acfd52019c2cfb8798c8b3", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9831c979603d6b76a14ff59dfac89bdfce", "name": "CFA-Infy-Vcard", "path": "CFA-Infy-Vcard", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9888906f9c4e22785384d9264027431fcc", "name": "flutter_project", "path": "flutter_project", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9876e8abc0d2be32527acd8d0c31391e1a", "name": "Documents", "path": "Documents", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2580fb0a91ba5608ccafcc891d935fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fe47ed8a9920647b23588892a083f54f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989b345de3d366c2121720e680511dfe45", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9855043e02a71c77c16ce0b9b10c9118ed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981761412e5b6f83ea9ce39dc0fb635302", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d638272d01bbff627bd627abf065c1a5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884afe33725fc2305c46ccae061022ee1", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988084eea5c7e6168175eb72b48c85a77c", "name": "..", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e981559bced2b2a6706b4ad1caef11a1bb4", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98b72a428ec5da347c81e100616795320d", "path": "../../../../../../../../.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98208391694fdd2c58e18e71becd480f95", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983d4ffd6168bd605659133dca693b2e3f", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e989c915e35fc93d593fc190ae47f1a0dd9", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b122b937cfb5711e299e5cd99cdc38c9", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98ddcf4db8bb80f88e83332995cf91c35f", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b97cf772057398fee2e39f2536c6ecb", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ea0371d170241790716ef293c634f46b", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988a4231adf863ab88a67c06c3ae93d869", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bcf6a6c165d0d2012dae80c4901a5181", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ee25d05fd291b9b99ff31c2de4d9cdb4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d51288625ec6303c169736d62f972d3", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988444afa57d60f78ccaf0b308e96387b6", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98185b0a410d6773bbf97909ab82330835", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVFoundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e988ecf1a81ce5cd8b78914d0bb073baf23", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/AVKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9888b6c3c65f5991bfcda51a9f62d2b166", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreGraphics.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9849f252c2f2e792712ecc4f85a9b438b8", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreTelephony.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e986e862e3eb208a928b09c5f864ccd790c", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/CoreText.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98a7eaa43c48fe5688c9ad540835f6fb5d", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98149e87db66169f6bf61a0207eeb352a3", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/ImageIO.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e986912a28286737fa1318c8c4b807ccb76", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/LocalAuthentication.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e987fc4d1bcef8ce62596157e2d0cd5cf4f", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Photos.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98b2450df649e0fef7ec0f8c75c335f43b", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SafariServices.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98147ddd183486f54d4497ffb62c36358a", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Security.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98c78e810d18182c6b4b5037c8fb836571", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/SystemConfiguration.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}, {"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e981925823dade64e954318992e61d3ff24", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/UIKit.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986bc18f5f60c2b1bd48acb9c2564c2a84", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d53f611f7a234a45f3aa4b9252c861d1", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834bb4102f793f1e4e3d04bbd56187655", "path": "Sources/AppAuthCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f5a6263ad8fb40332871ff8ed6a9f2a7", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d410ea9b4122ccd03b97bb032f9a7ac2", "path": "Sources/AppAuthCore/OIDAuthorizationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac8aaff38b32b81a38e8f24801b32996", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853058147fc6f7fc39b2ae8943f04b319", "path": "Sources/AppAuthCore/OIDAuthorizationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bac1ab61e84da97cad783238b3f13b4b", "path": "Sources/AppAuthCore/OIDAuthorizationService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f0ddfbf27eaef48298b0878697adb183", "path": "Sources/AppAuthCore/OIDAuthorizationService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865f1ba1826b8ece3fa9184bd3578a576", "path": "Sources/AppAuthCore/OIDAuthState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9890308ff383b00371ff3b387936c26caf", "path": "Sources/AppAuthCore/OIDAuthState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896b46694ece9fde721ebce62febd9b90", "path": "Sources/AppAuthCore/OIDAuthStateChangeDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fabf7f725b3811471e6076bcd05b5bf5", "path": "Sources/AppAuthCore/OIDAuthStateErrorDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98849b5a85aa298edf913d00763d09cf8d", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98669fb6285f1c9fca7569c051595e0334", "path": "Sources/AppAuthCore/OIDClientMetadataParameters.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c98978e9dbb27eac243bb6c63f5c20c", "path": "Sources/AppAuthCore/OIDDefines.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9875cea9d16d07ecad2c6a6d15b19531f1", "path": "Sources/AppAuthCore/OIDEndSessionRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989eb0264f49252f0b60c4648b8d7cda75", "path": "Sources/AppAuthCore/OIDEndSessionRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e92c5a121d3d4d8ff0cb6602c23c0b5e", "path": "Sources/AppAuthCore/OIDEndSessionResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce27f4aa61a6f5dd4004d499a8f5c4fe", "path": "Sources/AppAuthCore/OIDEndSessionResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f49fe2a0c7aac6a68cbc5981152d19b", "path": "Sources/AppAuthCore/OIDError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f91e47461e97992af939ec659eba845a", "path": "Sources/AppAuthCore/OIDError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab239bcf2b2ed1980254b8fd09ecd589", "path": "Sources/AppAuthCore/OIDErrorUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3a7ea25c16a5f4e91f21f673737c6a7", "path": "Sources/AppAuthCore/OIDErrorUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb74d3844c86be50a92992fd0c5a0b5f", "path": "Sources/AppAuthCore/OIDExternalUserAgent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98281af450986e8ffb6f7a6d0f87727e2d", "path": "Sources/AppAuthCore/OIDExternalUserAgentRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9837e7d166c07df08bb24cfbf3cdfb912a", "path": "Sources/AppAuthCore/OIDExternalUserAgentSession.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984005dff033c76cc9da20547f86485753", "path": "Sources/AppAuthCore/OIDFieldMapping.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9809e7e00b242e8080d97eac50271c38b8", "path": "Sources/AppAuthCore/OIDFieldMapping.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865eb70a2ca6130a6547fa5bb039fea88", "path": "Sources/AppAuthCore/OIDGrantTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98636483e3fdbbbc89a0e1618c2e6c8ca7", "path": "Sources/AppAuthCore/OIDGrantTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f370eefaa5ad2b0484a9bb0fbe61ad7e", "path": "Sources/AppAuthCore/OIDIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983bb65a3aabfedfd6eaeea9a993dd4c95", "path": "Sources/AppAuthCore/OIDIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9845b168b36e5fc16ffe011e7b56888f8f", "path": "Sources/AppAuthCore/OIDRegistrationRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c3308da60aaadf969bb366f75812b5f5", "path": "Sources/AppAuthCore/OIDRegistrationRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cabbbeb66c22cceecc097caa38ccb25e", "path": "Sources/AppAuthCore/OIDRegistrationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad3934926694b07258020129eb46dbc8", "path": "Sources/AppAuthCore/OIDRegistrationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1c93cd6e94321ad943ea98c554cbdde", "path": "Sources/AppAuthCore/OIDResponseTypes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b24b5d03ab48dbe16b5eea0fc8b6df6b", "path": "Sources/AppAuthCore/OIDResponseTypes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e22b5d954adc3b312d9e966333cdde6", "path": "Sources/AppAuthCore/OIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dcc0fb7b0b09d6b5dbe7ffe07eec785c", "path": "Sources/AppAuthCore/OIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9899252b053547e91fa89219329e7dea1b", "path": "Sources/AppAuthCore/OIDScopeUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cbed92f6598918a6118318c1c91e9a71", "path": "Sources/AppAuthCore/OIDScopeUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f549f80a3e8c2b939f57db0d1adcec6d", "path": "Sources/AppAuthCore/OIDServiceConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bda1c1d0f04c9bce980667728e90ca81", "path": "Sources/AppAuthCore/OIDServiceConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c1e4e1cc82ee0e6c2ed906b8ece7fd0", "path": "Sources/AppAuthCore/OIDServiceDiscovery.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98913252ded07f597a1dcb3f8c220bb050", "path": "Sources/AppAuthCore/OIDServiceDiscovery.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9cc62fc2312d5c6ccc54a6187335ea3", "path": "Sources/AppAuthCore/OIDTokenRequest.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885450b53290694b55bd21f44e81b2074", "path": "Sources/AppAuthCore/OIDTokenRequest.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98800994a6ce26f121609adc28ff39807f", "path": "Sources/AppAuthCore/OIDTokenResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981dd8f941ce9054b44578b0f75216f040", "path": "Sources/AppAuthCore/OIDTokenResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98198f73293915ff095a1d05aefe831841", "path": "Sources/AppAuthCore/OIDTokenUtilities.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98293e876554afabde44e656f5a0b59acd", "path": "Sources/AppAuthCore/OIDTokenUtilities.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d6ba6dd509385609555d16af1c153292", "path": "Sources/AppAuthCore/OIDURLQueryComponent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98684362ddc50a18a7a2f8df006f386f0b", "path": "Sources/AppAuthCore/OIDURLQueryComponent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983dc51b8b7dc22dc2742faca251588cea", "path": "Sources/AppAuthCore/OIDURLSessionProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984a99f9a35a7b48aff5e5f4290ecc00dd", "path": "Sources/AppAuthCore/OIDURLSessionProvider.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c9bfe10ad4cf18756c9a92c15ec0e807", "path": "Sources/AppAuthCore/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986ae98feac87d07cdd5b4a44c8cfaa899", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f4a956f1695217c2752e567a389fc09", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870e73229c5087dcd9d816194ecc01b64", "path": "Sources/AppAuth.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985c64b96be45648de992ab6d27e205277", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98303e47f4536b5270aec670ebffadde58", "path": "Sources/AppAuth/iOS/OIDAuthorizationService+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ac37ffa4f134c79f9e55e1203904ff3", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983de2b34ccb8441f12cb4a97dbbe67f0f", "path": "Sources/AppAuth/iOS/OIDAuthState+IOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98638ad968e4b2045e476af2d2c7595ea4", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e84173d2bf79f9459c85209fb231acd1", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentCatalyst.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986bb918e7e5bfa3ae44332e1401ff0a73", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983d2033260d01f1c2741a9a39e20af6f8", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOS.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f1ecbd48544fae7f7f9e57d3dbc5f68", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9881b71fc515430a83419a6a837e41272e", "path": "Sources/AppAuth/iOS/OIDExternalUserAgentIOSCustomBrowser.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ed9e5d9a85151dea89696545c379d484", "name": "ExternalUserAgent", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98402c3d28904bb1e4e500be756c32556e", "path": "AppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985b9ad0418ecb1b2778625e98be419f23", "path": "AppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e986fbdf94120537d576cc457382e91b201", "path": "AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98515db6769b1a25ab63fbc718ea06007e", "path": "AppAuth-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98373d06df1556dfc6d30fd948e4f9734c", "path": "AppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982d0b0aef96bc3bc6b8509b632b0b8664", "path": "AppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d11dce02a276cb42237c82ddae585b30", "path": "AppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982c857cf584c3f479a1225d63d24b504c", "path": "ResourceBundle-AppAuthCore_Privacy-AppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dc6a1be1edcd5a3887539dd351a24be5", "name": "Support Files", "path": "../Target Support Files/AppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c71f952c1410edb273e9e258755b7927", "name": "AppAuth", "path": "AppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e0e1b108fea794cd952a7391b00e8b5", "path": "AppCheckCore/Sources/Public/AppCheckCore/AppCheckCore.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988dc17a52a9bde48abbdf6aa0fb456f34", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980735f3bf332d4a65c990e573aa0d7084", "path": "AppCheckCore/Sources/AppAttestProvider/DCAppAttestService+GACAppAttestService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b190460224098c2c1e88e68820f9caf", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a8f24bdfae16c5405d9c222775d5dd38", "path": "AppCheckCore/Sources/DeviceCheckProvider/DCDevice+GACDeviceCheckTokenGenerator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877ddf7b6313cd706029cb460556008b4", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6fbd6bdcd32e96d96d8a1d12c791bfa", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985a568c35c39971429db9eb4d47e0a81e", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9810356de792fe6bb1fffc00c0c78635e4", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestArtifactStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826d24543ef467db9322f5e434060886d", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981e457ab1615e1122247062f4372026f4", "path": "AppCheckCore/Sources/AppAttestProvider/API/GACAppAttestAttestationResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9876bf0680d3ef45d8d771c1f21757327a", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800c29a4a9d62786ccf087802457c16c1", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestKeyIDStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e4b91d8c45a573644ad0eb6208f9663", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppAttestProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ff03eb30245cc818596cf3167e6dfd3e", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984bffa3ad165de1d8c6f1ea9f6e929d7f", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c6f0e50183f72b5f01a1938b0afd82d", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestProviderState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9870f00dd63d8e69bd36f47d5497fdf9fa", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982989d01309aeccaf0cae1b2f7b6df9fb", "path": "AppCheckCore/Sources/AppAttestProvider/Errors/GACAppAttestRejectionError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988d054cf40ca2ba98a261f364660bb8e5", "path": "AppCheckCore/Sources/AppAttestProvider/GACAppAttestService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983dc2da18bfec88bdf9a5ff779bfeb589", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983718527e2a3ba4e0d75343d84adc1750", "path": "AppCheckCore/Sources/AppAttestProvider/Storage/GACAppAttestStoredArtifact.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5229544c5c45dfdf286798b6332ee85", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9886d9f63b7dbc7420d1fac3da35cfdad0", "path": "AppCheckCore/Sources/Core/GACAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cde9edf8beb2e447c4b5a4b609a400d0", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e127ef74520c276c7ffa08782501ab1f", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865c1184c17db486fcf9bbc6febe09e13", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckAvailability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b45abf1b053ae2c6b4eb525f3f39810f", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9810fc83ea3fb570cdd8df83093f420f33", "path": "AppCheckCore/Sources/Core/Backoff/GACAppCheckBackoffWrapper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987168357ef794238ccb74cc50e2ab2463", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98447dafed3b63c31784cdd232a0cac61a", "path": "AppCheckCore/Sources/Core/Utils/GACAppCheckCryptoUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985d485e5e842dfcb4b8e12bc4427f5985", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckDebugProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853d2262d5bdeba8e9d82d186da318b2f", "path": "AppCheckCore/Sources/DebugProvider/GACAppCheckDebugProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e571c1cafb617c7488f7c89c83aad155", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985488e1a5133f82b13e3105c9a270a243", "path": "AppCheckCore/Sources/DebugProvider/API/GACAppCheckDebugProviderAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a6c40bc277b107e83e6f4919cbb6ccb", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckErrors.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98038a5f3c05262c5d266bc824ee7a4577", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrors.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827963fd00faebc675f318f31e14efe51", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eac1914af61c360ae7f9ea84c956f9d0", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckErrorUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aff0e5345397732d0f6bd33b5d4f2f77", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98286e02af106c1f8c758e4d89b5e2174b", "path": "AppCheckCore/Sources/Core/Errors/GACAppCheckHTTPError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984621b26a56bcc0c7b03c4488e575a47f", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825ebfef12e242a6fe2ddb0db28f26a92", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fecb56f3eb3a77458cc979057953e631", "path": "AppCheckCore/Sources/Core/GACAppCheckLogger+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b1b9793439a7de62d812c9ddd033a9a", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826870282b53bbd75bb25f56e8db34ad6", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckSettings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989bff41c5c0052efe4dcaf453e4a8568a", "path": "AppCheckCore/Sources/Core/GACAppCheckSettings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cc242cb83c3ced7790dab55a74e11421", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986e5eceb0ae71355682e3fca8a670f146", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d85632ea9f1634b443aa31b6539c322a", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b9d26698aff11c9c38b6a84abdadf9de", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8c5e09cc5638112033a6b77d262dd84", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9888ec4711766702df9bf08961a71a848f", "path": "AppCheckCore/Sources/Core/Storage/GACAppCheckStoredToken+GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c3d0ed99edddf108e18027faf417859", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98584151d82012302d7e2acc9a7c50799f", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTimer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dc8e070dbabbc5c6bb30358659256b66", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b620f89d9eae05e36902ce713006ccdd", "path": "AppCheckCore/Sources/Core/GACAppCheckToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871b0f4eddc7fed7f83ac8cc3648eaa88", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988cd0e7822ad46a494f3a1770e9384fd3", "path": "AppCheckCore/Sources/Core/APIService/GACAppCheckToken+APIResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d028d88ba499ae4f86cf3d133254f7e0", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenDelegate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989dc2b73ecf64724c451febd26501a10c", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984181da1b49c959041cd52518d42776bd", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefresher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff14c13548ee4f9d48ac8aa0b29a6e0a", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4381a5a198407dfca868b98c0a37aaa", "path": "AppCheckCore/Sources/Core/TokenRefresh/GACAppCheckTokenRefreshResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ec3eb9c718a98819059b16b4a931d9fc", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACAppCheckTokenResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc548005ac65debfb6ce0adce60356bb", "path": "AppCheckCore/Sources/Core/GACAppCheckTokenResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ba85bba6830f9865340b08f473e9110", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f75ff6fec5032d1c969826eb53006285", "path": "AppCheckCore/Sources/DeviceCheckProvider/API/GACDeviceCheckAPIService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981012efba74b963b819169d75f1a30e63", "path": "AppCheckCore/Sources/Public/AppCheckCore/GACDeviceCheckProvider.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98428ab303dadaaf334dfd39a32c709324", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckProvider.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f5c6c414c5faca330033752a8caa2a3", "path": "AppCheckCore/Sources/DeviceCheckProvider/GACDeviceCheckTokenGenerator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98613b273fe0cf9950f0b8a74cc86d15b7", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dda7df37368f9fc83ca9b0aab03f53fb", "path": "AppCheckCore/Sources/Core/APIService/GACURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b50d6ca11b358a2718813207ef881b21", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c060700c61d9949b4950767374903421", "path": "AppCheckCore/Sources/Core/APIService/NSURLSession+GACPromises.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cdbfff3307320c6d841e476e9a677ae6", "path": "AppCheckCore.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9827348602d43e5f6625996052b45205fc", "path": "A<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9812e0cf831b60ec71ceb8f4412ba4eb17", "path": "AppCheckCore-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa54d6de76b3766ca716316a4bcfa958", "path": "AppCheckCore-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9800615c8123318a5def1e36c0fc68cf77", "path": "AppCheckCore.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b18c0f5976a7db9e1507894dbeb78ee4", "path": "AppCheckCore.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d68ada46fbced9001d95a1a7126960e6", "name": "Support Files", "path": "../Target Support Files/AppCheckCore", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984577be965e6a02ac342577d9da89e678", "name": "AppCheckCore", "path": "AppCheckCore", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983fe1a3c7f57dd3c39e8c17c076894c8c", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupCellItemProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9813a1b2518e65510cbeb4686c89d5c133", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailBaseCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab24c91cb4220e291f1dc6862161f093", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailCameraCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982efdd495eeb2d095896b5686dac796bf", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailImageCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d9a19393d66242e37bbcaeaa4a901fcc", "path": "Sources/DKImagePickerController/View/DKAssetGroupDetailVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98871debed2b261c2dd99f63ac23f2d5c5", "path": "Sources/DKImagePickerController/View/Cell/DKAssetGroupDetailVideoCell.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eb407384d875b64c1b0c6c2a55a56aa0", "path": "Sources/DKImagePickerController/View/DKAssetGroupGridLayout.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c403e5953c804a84869cd05f47ec6ec9", "path": "Sources/DKImagePickerController/View/DKAssetGroupListVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989cb141a9f6f317ca5350ea018a30165a", "path": "Sources/DKImagePickerController/DKImageAssetExporter.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cc93920803898157af203c15f611cc8c", "path": "Sources/DKImagePickerController/DKImageExtensionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f1b8ec0214eae4afce5df9947715922b", "path": "Sources/DKImagePickerController/DKImagePickerController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987a9cd38634cb3558524c13fa89eec399", "path": "Sources/DKImagePickerController/DKImagePickerControllerBaseUIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b15842dc08cebc45ca4c770827bc7525", "path": "Sources/DKImagePickerController/View/DKPermissionView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9889aa1bf1778537d5ebff10753289b595", "path": "Sources/DKImagePickerController/DKPopoverViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983903beedb0e439fc267811b81dc9f10a", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b92e4697937c30ce3b75118bf9e36a93", "path": "Sources/DKImageDataManager/Model/DKAsset.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9807f94323e382e8a0c349f7e558192562", "path": "Sources/DKImageDataManager/Model/DKAsset+Export.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988f6ea9bd74d86122e68095e791a87233", "path": "Sources/DKImageDataManager/Model/DKAsset+Fetch.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987ce50f602253a67361813572a8bf2145", "path": "Sources/DKImageDataManager/Model/DKAssetGroup.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987bd36232a5450633635e9d6a75286772", "path": "Sources/DKImageDataManager/DKImageBaseManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981393d2a92a6d31a83013ebc060abfbb3", "path": "Sources/DKImageDataManager/DKImageDataManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9855691f66846b8528ff6f99e05c5b9463", "path": "Sources/DKImageDataManager/DKImageGroupDataManager.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98091a2282563226927ac396ad81f3d66b", "name": "ImageDataManager", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5d83698223d8e6e98c7fed9bf3382a9", "path": "Sources/Extensions/DKImageExtensionGallery.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9864f48b4d925ac818c77cc403f2b5dcc6", "name": "PhotoGallery", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98338dd27e381f97f9451ae0fe4ced6121", "path": "Sources/DKImagePickerController/Resource/DKImagePickerControllerResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e48f3c71be8b1d02a953f0cc090cd136", "path": "Sources/DKImagePickerController/Resource/Resources/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98c257c651453034c5ec279e0c4326c22b", "path": "Sources/DKImagePickerController/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9885ead75856294cb09cd27f18eb5984c8", "path": "Sources/DKImagePickerController/Resource/Resources/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9885e03a6504d194daf4dd64c7ad40b5ee", "path": "Sources/DKImagePickerController/Resource/Resources/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9862b9281b8b9b3c6d7b8b222fa04b0c72", "path": "Sources/DKImagePickerController/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98433e274dddd8ed7f83699564cf885422", "path": "Sources/DKImagePickerController/Resource/Resources/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98df876d9d6a21d789b806f5d205955b00", "path": "Sources/DKImagePickerController/Resource/Resources/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e34506ff34fa8c5a4a5f534d14b13a9b", "path": "Sources/DKImagePickerController/Resource/Resources/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e983879b627e14a4ff6ebddd319bdd46b3e", "path": "Sources/DKImagePickerController/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e985ae5e1899b950c8232bfb6d80fccc617", "path": "Sources/DKImagePickerController/Resource/Resources/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9890e6433b9d7a3e76e42e656908475d54", "path": "Sources/DKImagePickerController/Resource/Resources/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9832d4693f0327fb696ef41c4e9afb7f0d", "path": "Sources/DKImagePickerController/Resource/Resources/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9802db79bf216a4e275438dd6f482f26ec", "path": "Sources/DKImagePickerController/Resource/Resources/nb-NO.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989e9b5120fcfba3ceaa8eef15a34ef214", "path": "Sources/DKImagePickerController/Resource/Resources/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98404defb3cef64ced768a4c34297fe935", "path": "Sources/DKImagePickerController/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9854798798e2111185f894100035cab510", "path": "Sources/DKImagePickerController/Resource/Resources/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98fa095c8bcd5d202d5517c8be9e9a6a4f", "path": "Sources/DKImagePickerController/Resource/Resources/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986366eb0e74c31822e9ef532242d2a392", "path": "Sources/DKImagePickerController/Resource/Resources/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9881e4ebde090355eaec6b53dc3cea915d", "path": "Sources/DKImagePickerController/Resource/Resources/ur.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9833856868dfe94e3f64f736dbad0f0760", "path": "Sources/DKImagePickerController/Resource/Resources/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9835f4dc04382c497abdb800a59e64a35e", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e984c0481285bb6c28529ceffaa6d468a67", "path": "Sources/DKImagePickerController/Resource/Resources/zh-Hant.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e1b48eb77805551e1781b875e45c42d0", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986622295f30077e927d6ba04e08af8b41", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981a6bb25a1635bdcc4617c51fab08fd20", "path": "DKImagePickerController.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9869a2bde0e54d35d66817d59c2a4df403", "path": "DKImagePickerController-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9849c5d3adc57fd1600faf7a4fd01502ad", "path": "DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e31b8de1d3ef54538de39306d1747a8", "path": "DKImagePickerController-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c50a6d58d295b0e70ded53f31270bc7", "path": "DKImagePickerController-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9868c8bcf08c935db38f3159c8bab9a9ee", "path": "DKImagePickerController.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e987f432d7b687d6785e4624962f89873c3", "path": "DKImagePickerController.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98155d6e5502afe2de31f48481d416c4af", "path": "ResourceBundle-DKImagePickerController-DKImagePickerController-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98174ea3edd8786b9db13f0243954e1f45", "name": "Support Files", "path": "../Target Support Files/DKImagePickerController", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d2e7ecff74f49759609908ea9a2b1232", "name": "DKImagePickerController", "path": "DKImagePickerController", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984df89ed48395d8162e249d7911bdf2e5", "path": "DKPhotoGallery/DKPhotoGallery.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981951e8016920001247efeabd7bac06fb", "path": "DKPhotoGallery/DKPhotoGalleryContentVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989cbffc16cc3b37c648bdc31b4b71fe0f", "path": "DKPhotoGallery/Transition/DKPhotoGalleryInteractiveTransition.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987aa58b488ba178b32a311ed201463eb3", "path": "DKPhotoGallery/DKPhotoGalleryScrollView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ca016c54b013173f57fa8e3e43acb7b8", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b1d530172aa7cb40004e000103494da7", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionDismiss.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9827c8c83f7f0033f174ee7068cbb03456", "path": "DKPhotoGallery/Transition/DKPhotoGalleryTransitionPresent.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879b2ae9f082d212c5166b5ddb24307fd", "path": "DKPhotoGallery/DKPhotoIncrementalIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8f72ff486686b0104df1e9c1a39298f", "path": "DKPhotoGallery/DKPhotoPreviewFactory.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982f4142b97fab9e309b1923b853e55455", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9831c4aa2a5549233fc853685a4a5d7a6e", "path": "DKPhotoGallery/DKPhotoGalleryItem.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859b0fb932cc03b8846d64bc8bfe52d53", "name": "Model", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a5e251a04a7e4c8be61604de3ce1f2b3", "path": "DKPhotoGallery/Preview/PDFPreview/DKPDFView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980592cca64964ba37bf578e9db0d396b7", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoBaseImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b5c935d01d2a8df7b007964a300825dd", "path": "DKPhotoGallery/Preview/DKPhotoBasePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981276c9fc245472f64f97a07c3190eda3", "path": "DKPhotoGallery/Preview/DKPhotoContentAnimationView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bb7bf6e50456941d3b5b13d1a18111e1", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageDownloader.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b6d43a19b8d3eaac498c5f025f987112", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImagePreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989a39f427cb2f6d54082848663e5b7133", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageUtility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983e632ed929de730cf4afd55106a79c26", "path": "DKPhotoGallery/Preview/ImagePreview/DKPhotoImageView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879f4615e9b3fd01979cf1f510f38d1f5", "path": "DKPhotoGallery/Preview/PDFPreview/DKPhotoPDFPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98066dbbb470629e50ea2387718c8c2617", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPhotoPlayerPreviewVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9880f3af3c8710088d9bb7ff4f7211e9d3", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicator.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec22bb4e958e067223fc6c6a7190a6e5", "path": "DKPhotoGallery/Preview/DKPhotoProgressIndicatorProtocol.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980d604b91d2da470bfbbacbf470f6efb7", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoQRCodeResultVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987844b80c58d2588dbd27669035fc35ca", "path": "DKPhotoGallery/Preview/QRCode/DKPhotoWebVC.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98648e4764281b987fa5a53858c95f1df5", "path": "DKPhotoGallery/Preview/PlayerPreview/DKPlayerView.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987f96811426da61fa6b5432e62e4fd679", "name": "Preview", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879a5fad19fc78dc93058c7e35ca1b40a", "path": "DKPhotoGallery/Resource/DKPhotoGalleryResource.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9894c6fbca30120cd72873c9fe7ecd638f", "path": "DKPhotoGallery/Resource/Resources/Base.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98cc617b28c371a0869f887fe50399cf2e", "path": "DKPhotoGallery/Resource/Resources/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder.assetcatalog", "guid": "bfdfe7dc352907fc980b868725387e9896ec98ba73e4c6ff1d6e7401c65bcdd4", "path": "DKPhotoGallery/Resource/Resources/Images.xcassets", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98001d41c66b8ee0190fa395a71f733acf", "path": "DKPhotoGallery/Resource/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98e165b55841fc3f9468f9aadf3b6700df", "path": "DKPhotoGallery/Resource/Resources/zh-Hans.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985c506b5327bdcdb5e4bdf4ff6f41df61", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987b322c1847d3d3b0523c25877edbee61", "name": "Resource", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c7874c97785faa4572b36152bca06068", "path": "DKPhotoGallery.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987524a411ff20ecb204479bdd0dc44eab", "path": "DKPhotoGallery-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98574d7ee74f40ed3dbc32699dc8888b2f", "path": "DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9827895f817644cf63c242746ddc3c047b", "path": "DKPhotoGallery-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a837a5d76e0fa87ab9209e8e42e74664", "path": "DKPhotoGallery-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b51540160eec37db678c8241c697017d", "path": "DKPhotoGallery.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986d0afb8f7a5682be724b1e56d9e87054", "path": "DKPhotoGallery.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987a009fdd3a5cf9c64b688e1ae138302a", "path": "ResourceBundle-DKPhotoGallery-DKPhotoGallery-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989406b5aa716b6ce058c8b87a22fe8f6c", "name": "Support Files", "path": "../Target Support Files/DKPhotoGallery", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa06c7d8c3aada1c198baa9dd74368f0", "name": "DKPhotoGallery", "path": "DKPhotoGallery", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989159571587a83edd230fe8fe0f8e5588", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ffb89568df862be03ea38850be450e6a", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/cct.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e3afe1b822524f3413b4a6a5d06a5d67", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d9edfb428b817d1e80bb6c4ba88358e1", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/client_metrics.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fbb24e4e0eb1ec273850282ba0a86750", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cffcb4bb3aaecac9734e9db17dee3fb1", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/compliance.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9872f0bb1168ab40889bda4eddee0858d5", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f486da290d4293e6b66127913fa2dbb4", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_prequest_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98906333f166fab3368809a5222938c301", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a50930c3918e6757b6572e63f9fa3ba", "path": "GoogleDataTransport/GDTCCTLibrary/Protogen/nanopb/external_privacy_context.nanopb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989b0ee16e0501a2efb3017a6def5ced1e", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTCompressionHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987ced24e8f4d1166933a995b41f2644e8", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTCompressionHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98970839abdd6ba81b62ef2fdb462ec347", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTNanopbHelpers.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98931875cdb27a8a07f6543a21e368b3bc", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTNanopbHelpers.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858a848537bab599f095c913642d58891", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ddc08ab4082401ce92adb5753d26733", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ed4138310336fc4411baac729271911", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTUploadOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e575f80ab66f6401f74d1efaca5e5f8a", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTUploadOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98437cb270b17ca9f2b332ae804027d387", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCCTURLSessionDataResponse.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d900700560d1745204101e381e57e8a1", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCCTURLSessionDataResponse.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9636a0d3e60d45675b638a037e87116", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORAssert.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98421398ac069e95d617586daf5d261c5f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORAssert.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c3b65c6c03f3d800e689cf756511c2ed", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORClock.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f4729b9e0bf4abeed73b1a74632e7c61", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORClock.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d879abee3daa995e2b0580b533b5948", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORConsoleLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e0a9ce2e7ccf17e54dd4a81efcf383ba", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORConsoleLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee635d57e7c24c0ee451a407a56f873d", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORDirectorySizeTracker.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f21c96ae43ab035da91b23333884b53f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORDirectorySizeTracker.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6f177a53980fc24bb58af46925b0ca1", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREndpoints.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cdebdc83df43a66f39c3917d37958c4f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREndpoints.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdb77f11c61e00a0020918bad8db8eea", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREndpoints_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982d437cc8798a954768293863be4cfd05", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREvent.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883f673c30119e808bf02781151a25c12", "path": "GoogleDataTransport/GDTCORLibrary/GDTCOREvent.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982f811dfd35717079fd98d391834f4cbf", "path": "GoogleDataTransport/GDTCCTLibrary/Public/GDTCOREvent+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988eb1ebfee95394cd20c3383b3e79f165", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842d74e2f8f2c0bdba411d48d3b197fd6", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCOREvent+GDTMetricsSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c6722eca6b97751fec5a558f5167a20c", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCOREvent+GDTMetricsSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f53fd0f7b22c91cb2af0045eeb5728a0", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCOREvent_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986b3c0b60087bc8a6415772792186b8c7", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventDataObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbec6fa555ec0bddb42db5b52377e4e2", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCOREventDropReason.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f8fb871df35cdf01eacd260d2013dbe", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCOREventTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865204ce04183fbf47ca28f805bde8798", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9815def1273d794b4e45ed97385b22b4af", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982584dd60a1d254702c61fdf7c364e6d8", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORFlatFileStorage+Promises.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9831b44d48756c6bfb8e11e41d83046f39", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORFlatFileStorage+Promises.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9891ef1de13e956da3c8877a8253ed3621", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORLifecycle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987e9d51df9541c3c6026108ca39af2887", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLifecycle.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9814620e9a2b98d323382ec529b93b3021", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORLogSourceMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9871d1b67cd44b96e80d960938d53a7e2c", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORLogSourceMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980a238ced8b2597423a558c6fc21a0d0f", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetrics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b4f5cbdc09ec56ce088be957abce1e9c", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetrics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ac90b58617d5903f1cf8624d1afbf8e2", "path": "GoogleDataTransport/GDTCCTLibrary/Private/GDTCORMetrics+GDTCCTSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5e9c30862a2f97d7427f0f5c2d1a026", "path": "GoogleDataTransport/GDTCCTLibrary/GDTCORMetrics+GDTCCTSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bbc313c0d4a1530d5e121ac998482f7d", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986383deefe38415abf19c15e78a24798f", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983e0cc4d4d3a9bfde3150f6f4c3988b30", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORMetricsControllerProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98703428731d2aadabba721689be179a75", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORMetricsMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d002b405fe6a12a1f0f173fb80d1bcb8", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORMetricsMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989505e9e917a4d1bb24b5d721242ce2c1", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORPlatform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9875a4a16bb9b49ef2f0dd17614c5dc215", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORPlatform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9826992c94821e1cbd1953753645382064", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORProductData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cddcb36e6f8506b2dad4262183b88b8b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORProductData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985b323d9b7479f7b523a4f6b96e43811f", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORReachability.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98824ff0f770b775d35dd444fab760d26b", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORReachability.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9884fc10ab8361e63f71cdc0a82acd36a6", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORReachability_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9872cd5b63b025bb748c12ccb5f3c01f3d", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORRegistrar.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a5bd9c0c56ce00e479f68272f3ec12e1", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORRegistrar.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b98520daee59b8d85eb22ce215a16669", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORRegistrar_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9897e791310cb82c9830ca59cf0594a511", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageEventSelector.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983ab3b62a3fce1cf2e2a2b5f29f64fe67", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageEventSelector.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985719b218dfa81ce56d9a2c08cd28612c", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORStorageMetadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f504f43158f05ebc8e3a1d48eca47584", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORStorageMetadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871adcf9d0e58633bf1c230ed760e2b7a", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageProtocol.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae687e16e6e2d796b9fac45dbde7d003", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORStorageSizeBytes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983afa35a3e44f9afd043b8e590e41d4e1", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTargets.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9848eb69432e2a3a613a9108c3303b3601", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d3e7be4f3eb5ec49364c6aa0932a3924", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f951f92351d10534855d5641ee64450", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransformer_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98213bdaa3eb0204029613edc555c3e5d3", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GDTCORTransport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981a110ec182611b3bb408d4fe90e42940", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORTransport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ed7953781541fb2b206671d1956ddc8e", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORTransport_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98db327280c8161d1cbb2a8391847f67cd", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadBatch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98681d24959a41fed61bbc9fc1e8122f03", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadBatch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987d67a927eed1ab9c1b066161de1b40d8", "path": "GoogleDataTransport/GDTCORLibrary/Private/GDTCORUploadCoordinator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b3950ca8e0daf4637b58fee57570943", "path": "GoogleDataTransport/GDTCORLibrary/GDTCORUploadCoordinator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98135346e33cf3c19e3f20519f14c2722e", "path": "GoogleDataTransport/GDTCORLibrary/Internal/GDTCORUploader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c41c129b175861196db8e4fdb85a1008", "path": "GoogleDataTransport/GDTCORLibrary/Public/GoogleDataTransport/GoogleDataTransport.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98494827f81ee055f36590d402d538fd09", "path": "GoogleDataTransport/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988df88cd97240f397023549a12dd837c5", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98868b1dc443ad173d2b12c9b568ef26d8", "path": "GoogleDataTransport.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986dc6110fd7b1f38320da65802e938a39", "path": "GoogleDataTransport-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b6591f705bb0687932e4fea1e5405002", "path": "GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9896f2bfab841d9e9d941ff08974198733", "path": "GoogleDataTransport-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c448d645263873c8c8f0cf088799d9af", "path": "GoogleDataTransport.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9814c53dd6bfa021309193690ec5b47be8", "path": "GoogleDataTransport.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983f016842d6270c755202c601f1bdde7f", "path": "ResourceBundle-GoogleDataTransport_Privacy-GoogleDataTransport-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bbe9a89cf04ffdb02944fbb5d5f5dbbb", "name": "Support Files", "path": "../Target Support Files/GoogleDataTransport", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e387c4f689b0c757434dcd9b53cf94e6", "name": "GoogleDataTransport", "path": "GoogleDataTransport", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e012514cce7868d4a0f3802549ceb5b3", "path": "MLKitCore/Sources/MLKit.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9891886c512868d8b71352d8d315f9fc05", "name": "MLKitCore", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98683f608b42c2b7d35999f5b8aee938d6", "path": "GoogleMLKit.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e450605b7db1c4ed22a61cf1fc29e0eb", "path": "GoogleMLKit.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a5a43683930315b932ec80e4c94f94fd", "name": "Support Files", "path": "../Target Support Files/GoogleMLKit", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e88f884a42c850961853a0b52c787ab", "name": "GoogleMLKit", "path": "GoogleMLKit", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b3909cd1966aba0d91a2a9e76797063", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dc3aaff8fe71fa71903f4aee93d0f515", "path": "GoogleSignIn/Sources/GIDAppCheck/UI/GIDActivityIndicatorViewController.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98913b3bf3b63dfb637b5e5d4b3cea75eb", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989170c7e50c028f2056989b03cd47e5a9", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/GIDAppCheck.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865b82837a087af6e866902d32092501b", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDAppCheckError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830344d3c7ab7c787e08ab67ee9a589be", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981c37c627e29203c4157c47558e97c1d7", "path": "GoogleSignIn/Sources/GIDAppCheck/Implementations/Fake/GIDAppCheckProviderFake.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986ae284bcb9381744e769a0ba7cd21a3a", "path": "GoogleSignIn/Sources/GIDAuthentication.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac410bd35c05b45ffd60a65545a90a5a", "path": "GoogleSignIn/Sources/GIDAuthentication.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cf29ccb9661877af8f1d474cfe02eb99", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987a3bd95d58c7271ad0071bd3593d9f5a", "path": "GoogleSignIn/Sources/GIDAuthStateMigration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f858236ce3ce69fe9ecaa7d54a21af86", "path": "GoogleSignIn/Sources/GIDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98499c2f9d705c36ea9f76877449617fae", "path": "GoogleSignIn/Sources/GIDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e1d6d73815df241d12b1b6826bbf778", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDConfiguration.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985971f8ec544680f299f55200c923572c", "path": "GoogleSignIn/Sources/GIDConfiguration.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98045f19e701c302367cb8cfd520003b42", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988224a5f4ec87ca1b5c28358c1a59e657", "path": "GoogleSignIn/Sources/GIDEMMErrorHandler.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983487fa65ce957a2ce5df9e11b30985dd", "path": "GoogleSignIn/Sources/GIDEMMSupport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9828c903dc1ab63f3320e270e4c4208e25", "path": "GoogleSignIn/Sources/GIDEMMSupport.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e36d2a839593251e0a1254042e2c25f9", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDGoogleUser.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98caf9324ab048f1de998406a5ee3956ec", "path": "GoogleSignIn/Sources/GIDGoogleUser.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1c30b402c59b200fd49a99441c296e5", "path": "GoogleSignIn/Sources/GIDGoogleUser_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98431fda7ab49f33514e98cc84cffbc921", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986c1726b208c3bc5a4a0dd2134a27db2f", "path": "GoogleSignIn/Sources/GIDMDMPasscodeCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874723629cfda2653c605ef981140e2ab", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985a90158414d8de2c2c295f46f88cfc34", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98014e5afcaf9a451b8e295d317d55d499", "path": "GoogleSignIn/Sources/GIDMDMPasscodeState_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a433771f05bee4f36ba95c299d2e3b1", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDProfileData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98adfa30f1377ba82614e09349bceefdb8", "path": "GoogleSignIn/Sources/GIDProfileData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983591c7f5f569d6c8db8e87076852584f", "path": "GoogleSignIn/Sources/GIDProfileData_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9800faf006dd97807c4203b400b099f94e", "path": "GoogleSignIn/Sources/GIDScopes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ba17967c13aac8e864d799895964af36", "path": "GoogleSignIn/Sources/GIDScopes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9822d5bc1b0e5db638581bc18079141cbe", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ee30c1dd67c4a2dde9bde4880104ed6d", "path": "GoogleSignIn/Sources/GIDSignIn.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aa33587cbd786f9f8ba9069b86db0c11", "path": "GoogleSignIn/Sources/GIDSignIn_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f2a4baaeae3dc0bde9f2602c8ff9cb6a", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInButton.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ac8ec657530c0cb6e70e94b3efff14f2", "path": "GoogleSignIn/Sources/GIDSignInButton.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980206db5eda54dc08a9e5d0febf439b36", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986fc7bbff938993eac13e78c45cc37d19", "path": "GoogleSignIn/Sources/GIDSignInCallbackSchemes.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b025d321967de0654b83606fbd39b38", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980060efd1d6b20f22cc5c1e11f913c722", "path": "GoogleSignIn/Sources/GIDSignInInternalOptions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983777316eeddc509d239ad1c328ebbdb5", "path": "GoogleSignIn/Sources/GIDSignInPreferences.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e06e4d1bf9a203fb4b3837b9c6b9cff1", "path": "GoogleSignIn/Sources/GIDSignInPreferences.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987672f02dacb2f4f1b2bbf35a2c6d0e26", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDSignInResult.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bb3a44a40cf3ecf28f08806393664c9e", "path": "GoogleSignIn/Sources/GIDSignInResult.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9b8e575ade0d61efc08f492c5773ddb", "path": "GoogleSignIn/Sources/GIDSignInResult_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817a5bed03924d8e61a8d5be5f7ff084a", "path": "GoogleSignIn/Sources/GIDSignInStrings.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98afbf54909993088eba0188c7e8402128", "path": "GoogleSignIn/Sources/GIDSignInStrings.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c499f60750cee669d8f32d0d8eabdb8b", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983e41c8c95d527b578695397e2653c58a", "path": "GoogleSignIn/Sources/GIDTimedLoader/GIDTimedLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ca3ab237fa1b2fe585a95c84598629e0", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GIDToken.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98566fe2bc99214083e2b0ae9305cd5c62", "path": "GoogleSignIn/Sources/GIDToken.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f2149d966525d047d12f329719ffac1", "path": "GoogleSignIn/Sources/GIDToken_Private.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98917182555118410c2160f7a444c9a961", "path": "GoogleSignIn/Sources/Public/GoogleSignIn/GoogleSignIn.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4d7ca841deea27504005292fc709304", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98842fd86160a841ac79538db231fcc2f8", "path": "GoogleSignIn/Sources/NSBundle+GID3PAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98cd2d5bfb09992937cc5649de38a7a2ee", "path": "GoogleSignIn/Sources/Strings/ar.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ddf5f9ad9269203f811906ff6d631a3c", "path": "GoogleSignIn/Sources/Strings/ca.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9879b687af8acc5c2ae82e2ea39398b7f7", "path": "GoogleSignIn/Sources/Strings/cs.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d23ab132c73f1911826e4a5f8010f22d", "path": "GoogleSignIn/Sources/Strings/da.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986268d87b507545b5e515a9bc71003cc9", "path": "GoogleSignIn/Sources/Strings/de.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98c8369fcac221658c46d2444ca9d93a45", "path": "GoogleSignIn/Sources/Strings/el.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e983bb7fad073958ef9242201ba5a8742b3", "path": "GoogleSignIn/Sources/Strings/en.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98107ef54adcedee324a1c59ca29b70791", "path": "GoogleSignIn/Sources/Strings/en_GB.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98809be64de030a99e3bea64aa2d3464db", "path": "GoogleSignIn/Sources/Strings/es.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98da8101f9e538573afad7829546f96bce", "path": "GoogleSignIn/Sources/Strings/es_MX.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ca18bd709cc3abef9ee814289b59b65e", "path": "GoogleSignIn/Sources/Strings/fi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982cb9c23d03dfcc36afd352d4ffcafb93", "path": "GoogleSignIn/Sources/Strings/fr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982ccdba5c3898f0ce28dc6b1ef1b56fef", "path": "GoogleSignIn/Sources/Strings/fr_CA.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e98d54568e1f22be9593bea2b3ef5851413", "path": "GoogleSignIn/Sources/Resources/google.png", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e986b8469b94de5f6d68607f639136cd4f0", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "image.png", "guid": "bfdfe7dc352907fc980b868725387e9809076b350a006c8e6afd37f0dfe2bb10", "path": "GoogleSignIn/Sources/Resources/<EMAIL>", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98b6a5efba48f23135210c9e0fa06b3b4d", "path": "GoogleSignIn/Sources/Strings/he.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98525cbbc4c099c0cf6010ca797a7f922f", "path": "GoogleSignIn/Sources/Strings/hi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d85f176394a3caa506f2dcd892e9f0e6", "path": "GoogleSignIn/Sources/Strings/hr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986207e78b1f6523224e262e1a61b0f8c7", "path": "GoogleSignIn/Sources/Strings/hu.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9837606bcc68d3a7319bc001b4d5bca53f", "path": "GoogleSignIn/Sources/Strings/id.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980a4f9cf49a34d2ebd193eaa237027dd6", "path": "GoogleSignIn/Sources/Strings/it.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987af91da1fd46fdb407ff4e7d817f1953", "path": "GoogleSignIn/Sources/Strings/ja.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9804cfe0b6494b41c24311adc2575aea84", "path": "GoogleSignIn/Sources/Strings/ko.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98801157ac2fc03b21db50397a50248804", "path": "GoogleSignIn/Sources/Strings/ms.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9865c31d1f9b8af7d5eef17e2a1cceb816", "path": "GoogleSignIn/Sources/Strings/nb.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e986f090b4657bfa3678b44bacceb101369", "path": "GoogleSignIn/Sources/Strings/nl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e982e99a1469514eb1627ca86c822e340ea", "path": "GoogleSignIn/Sources/Strings/pl.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98d281e9ec2e8c73e55c3c9a57275ee4d7", "path": "GoogleSignIn/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98ab175cbf361395ca8aca3f2e730ad18e", "path": "GoogleSignIn/Sources/Strings/pt.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e980d5a847a8f7e26b0c19e7e89294642bf", "path": "GoogleSignIn/Sources/Strings/pt_BR.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9827a46468c32d39c31e9d91dad4e34d0b", "path": "GoogleSignIn/Sources/Strings/pt_PT.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98286d0b2bc0f4a82d4ac8f74f15151371", "path": "GoogleSignIn/Sources/Strings/ro.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "file", "guid": "bfdfe7dc352907fc980b868725387e98583b905dbd99c63c41791ac6e1588eaa", "path": "GoogleSignIn/Sources/Resources/Roboto-Bold.ttf", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e984ac8111ca76cb2b7fc2940e79af09b0c", "path": "GoogleSignIn/Sources/Strings/ru.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98dc67a217e28206cddabf58ec2550eda9", "path": "GoogleSignIn/Sources/Strings/sk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98d89c26b1b9c6dab8e3f92980398c5dd2", "path": "GoogleSignIn/Sources/Strings/sv.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e987789bfc41e634bda335329aa13c55730", "path": "GoogleSignIn/Sources/Strings/th.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e989556e267af9f752563931c9500827cbc", "path": "GoogleSignIn/Sources/Strings/tr.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98815c4d8b3ac7d8a6441640f256bf7361", "path": "GoogleSignIn/Sources/Strings/uk.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9865ba10336b34ac8b60acf884df569187", "path": "GoogleSignIn/Sources/Strings/vi.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e9882e969c8b00eee15977db88c35f92b31", "path": "GoogleSignIn/Sources/Strings/zh_CN.lproj", "sourceTree": "<group>", "type": "file"}, {"fileType": "folder", "guid": "bfdfe7dc352907fc980b868725387e98274e4eb94e29334fa66ed355a46f2de2", "path": "GoogleSignIn/Sources/Strings/zh_TW.lproj", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9829d249cde42c3ada4e115cd6ed42dec1", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e986be11c24eba9a698f9f72b08fe643a91", "path": "GoogleSignIn.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e99dc740803da44883c04865b9e6248d", "path": "GoogleSignIn-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9828c39b2bbc395ed51df2b34d792eeb4d", "path": "GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b437d26bdf655e6e170de09e930ecac9", "path": "GoogleSignIn-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9893fdb4334f18e38331af7f7481251c21", "path": "GoogleSignIn.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9841bce52c6c36cf98775abecf10dd1d10", "path": "GoogleSignIn.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984d297cd7066eea9469c4123164b4685f", "path": "ResourceBundle-GoogleSignIn-GoogleSignIn-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e5692661bbd2131d12b887d2e5bc835b", "name": "Support Files", "path": "../Target Support Files/GoogleSignIn", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9840712851a5d4128fcd179da460708e5a", "name": "GoogleSignIn", "path": "GoogleSignIn", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98768302e0469f5df24e7b5730c7bae91d", "path": "GTMDefines.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9873449ad1b9ebc0b470bdcff199a402a4", "path": "Resources/Base/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988614ba9127fd82fd6bcc2d540e106b7a", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cb5a146af85ca97a5c7db40306d9c7f7", "name": "Defines", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98329b66fefb642f0da6996a3a1389b8ed", "path": "Foundation/GTMLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820f87cae90fa01564f33e67fb3dbe3af", "path": "Foundation/GTMLogger.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98cfc6fd3225d83e66ab6c8a6fe98000e7", "path": "Resources/Logger/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fd0f6c16e1915853d61764c3e21288da", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eaee09a3aee8ab23062e4c36faac5423", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bfcdce6792fa25274877ad6edb5e35fd", "path": "Foundation/GTMNSData+zlib.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9817b0a7c5b3c97af9491361691a227a07", "path": "Foundation/GTMNSData+zlib.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f58089f8eeeefef3e106581e438f0f8e", "name": "NSData+zlib", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9858b4bfb52083ac8159aaf49e1b36bfe4", "path": "GoogleToolboxForMac.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9883f761147c6b9a92b99565161ac1db5f", "path": "GoogleToolboxForMac-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981d96e739b5728ba16db6127db43c5944", "path": "GoogleToolboxForMac-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987f7f09c2ec461b90b52571199d645172", "path": "GoogleToolboxForMac-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e7418fdff6e314ddfe28d5641c5332f7", "path": "GoogleToolboxForMac-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98da4735e05b06f58ca87d5528f423e8c8", "path": "GoogleToolboxForMac.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9875baa3fc508f24a16855905f248ee526", "path": "GoogleToolboxForMac.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e840c56e44b253610f41c156c2a4a8d", "path": "ResourceBundle-GoogleToolboxForMac_Logger_Privacy-GoogleToolboxForMac-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9882430cda83bff4e7360a74a59601467a", "path": "ResourceBundle-GoogleToolboxForMac_Privacy-GoogleToolboxForMac-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98326dac14069016568f269aa2195a6ee6", "name": "Support Files", "path": "../Target Support Files/GoogleToolboxForMac", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984591415e24bb1391f5b84307e99f8a5c", "name": "GoogleToolboxForMac", "path": "GoogleToolboxForMac", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9865cb8235139f070d0471bc1998d92e2a", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULAppEnvironmentUtil.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98773c68b403962fd2726463b7400de987", "path": "GoogleUtilities/Environment/GULAppEnvironmentUtil.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988ceb26ae48b7de50458e944885ceb9d4", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainStorage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98babaee591e1535b764b3a9130ba56f2f", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainStorage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989adc4353c963e49db072bbbd9a3c3ba1", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULKeychainUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b776714675328d5424c4e420b867c2f8", "path": "GoogleUtilities/Environment/SecureStorage/GULKeychainUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984217e022de6bb1d7bb3a31f6fa523fa4", "path": "GoogleUtilities/Environment/Public/GoogleUtilities/GULNetworkInfo.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9872a32a384ca584ba043aeac03bbe6037", "path": "GoogleUtilities/Environment/NetworkInfo/GULNetworkInfo.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c674e13e7848a87760a4cd84ce3d4d5", "path": "third_party/IsAppEncrypted/Public/IsAppEncrypted.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c30a0e6bd6da6b27695d9fafa2015a31", "path": "third_party/IsAppEncrypted/IsAppEncrypted.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9845d7aff747ac9faca300d345ea8e76d1", "name": "Environment", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983ed69146cd7ba725253778dcf80ac169", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLogger.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f1b31befb3f381a2029ef62106ea0414", "path": "GoogleUtilities/Logger/GULLogger.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a854066c0f432e146baa889d1d2b113", "path": "GoogleUtilities/Logger/Public/GoogleUtilities/GULLoggerLevel.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983f5f9ae5dd7b8326437d632c02df398b", "name": "<PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98fda31718a8050f03e54c7bd10850e49c", "path": "GoogleUtilities/Privacy/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ccf1e0bbfed4d8999e00cf8491e9bc4c", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6495d766d112f7812a90e38f042abd9", "name": "Privacy", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c3025ee94f85003a33eacb23ee82f7ae", "path": "GoogleUtilities.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c2602c257108e7ede5a80c0fa989881b", "path": "GoogleUtilities-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98304f74a018634a4577e23b495a83e5ea", "path": "GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb5ed254baebc34f5d493ac99e4b5615", "path": "GoogleUtilities-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98794ab99d062b6a19c08aaabe9530ec55", "path": "GoogleUtilities.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98465cc28c8c9f53df5fdd0ca8b5a05ef3", "path": "GoogleUtilities.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9834a7ca5a6f87b33be04399b1b437910d", "path": "ResourceBundle-GoogleUtilities_Privacy-GoogleUtilities-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a22d314abeb4ca6a77f6752ddb50b968", "name": "Support Files", "path": "../Target Support Files/GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9802e999e67d78466b8925250e9ff66d23", "path": "GoogleUtilities/UserDefaults/Public/GoogleUtilities/GULUserDefaults.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98309410edfb97b8f1165245cd27a6169e", "path": "GoogleUtilities/UserDefaults/GULUserDefaults.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98dbe171f05b15a05c4e92096b429c5f43", "name": "UserDefaults", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e178607cd3009787514af87ccfba6702", "name": "GoogleUtilities", "path": "GoogleUtilities", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9899521b42aa07cbee2b119fd5b1584843", "path": "GTMAppAuth/Sources/AuthSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989baf486fd1809b14cdbf4673044e1c7a", "path": "GTMAppAuth/Sources/AuthSessionDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d59ab3f55c22b116bb5a518e2f17516d", "path": "GTMAppAuth/Sources/AuthSessionStore.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9828348959d21f238dee50e8f6ad6f60a6", "path": "GTMAppAuth/Sources/KeychainStore/GTMOAuth2Compatibility.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98119a25121c534ecad37be21ced6f7493", "path": "GTMAppAuth/Sources/KeychainStore/KeychainAttribute.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa05d907bf05c40898c990cc9afd8988", "path": "GTMAppAuth/Sources/KeychainStore/KeychainHelper.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de7370690c060961a0d40e4d78121a84", "path": "GTMAppAuth/Sources/KeychainStore/KeychainStore.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9889f8e193cea5661f96edcd414170c02d", "path": "GTMAppAuth/Sources/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9896230fb3c56a38ce1d9cf41524145f03", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e987e6e022518354f62d82f9fd24c7f70c6", "path": "GTMAppAuth.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cfdc6b7fbbeca348935ff22c0b90dd96", "path": "GTMAppAuth-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e984f07e7c9448e8fd6e263a4bbdc187908", "path": "GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98aacc843ad1fa6136a1aefa0ecf2765b3", "path": "GTMAppAuth-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980c2268fb768c243dd6f27a4731ab3134", "path": "GTMAppAuth.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984b54bdafb94bf6ef00f4c34685cae44d", "path": "GTMAppAuth.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988456870447238eb1991145d4a33749f5", "path": "ResourceBundle-GTMAppAuth_Privacy-GTMAppAuth-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ec73b0343de13ebd435878e77d7e2a78", "name": "Support Files", "path": "../Target Support Files/GTMAppAuth", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e0e0305e2576ca34b817a9ff94f7855", "name": "GTMAppAuth", "path": "GTMAppAuth", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982caec58a397145191a5b563d24d6cfc4", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b845bb8c5de218ec211aa32a40f0e71", "path": "Sources/Core/GTMSessionFetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bb3fe98551dfb3a806c3338798645a51", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherLogging.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d71ad44eb95daf53b2b79576716ec33", "path": "Sources/Core/GTMSessionFetcherLogging.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e2c2a54029584046582c7c9f48fb7e1", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionFetcherService.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d0c0288a5eb1e0d8704c744aca024af", "path": "Sources/Core/GTMSessionFetcherService.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b601a033b10244a3386148e705d7ba35", "path": "Sources/Core/GTMSessionFetcherService+Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983484473d0f7fd54f7ad5b8e7bdcfad47", "path": "Sources/Core/Public/GTMSessionFetcher/GTMSessionUploadFetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98208b93b38b227c8c6cd5e082c15fa7a9", "path": "Sources/Core/GTMSessionUploadFetcher.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e4357d01a5337f6340227980cfe79727", "path": "Sources/Core/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c5a15ea121ea31ce66d578194b5c6501", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980c718d6e5d5cbdc20178f2bb68427b09", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e71dd0e3ec9327c1d0099fca4535ed4a", "path": "Sources/Full/Public/GTMSessionFetcher/GTMGatherInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c211a2a2b7c430a9104c236b922b98b5", "path": "Sources/Full/GTMGatherInputStream.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab338ceb5c16c395e7043b07346a4096", "path": "Sources/Full/Public/GTMSessionFetcher/GTMMIMEDocument.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb1017cb0c8c8720cd11050cd330a10d", "path": "Sources/Full/GTMMIMEDocument.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a29bc3dd9ff68403ac70f946fee065f8", "path": "Sources/Full/Public/GTMSessionFetcher/GTMReadMonitorInputStream.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a3303193462177181ce4dd4c3a1ea9b4", "path": "Sources/Full/GTMReadMonitorInputStream.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98a09466c653ea374a05b7912f990755d8", "path": "Sources/Full/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985b5c22bf7644304fd0c3b6ff3457a529", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dc53c5f9764b5979f436acbda8a9b4d4", "name": "Full", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a731798a2bb45ec5bc4a12a622dd8318", "path": "GTMSessionFetcher.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e2226947dad5045aacb32a30d37adbe5", "path": "GTMSessionFetcher-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9858e624058b663693bfba9f7b9eaf0653", "path": "GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb99d6ae36811c3e57c61a685673b85f", "path": "GTMSessionFetcher-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f1f2cdcd4327df4bbda973f816b9b85a", "path": "GTMSessionFetcher.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982a4c19e2f63db0275dfe488a14287353", "path": "GTMSessionFetcher.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988521b5274ecb4032e91b8e87e6152266", "path": "ResourceBundle-GTMSessionFetcher_Core_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b66bd0afaf11c76795bfc3aeade1322c", "path": "ResourceBundle-GTMSessionFetcher_Full_Privacy-GTMSessionFetcher-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98921cbca170bd9b5d6ab4720265bae3ea", "name": "Support Files", "path": "../Target Support Files/GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813e5b207250354e3ec624703018c91bf", "name": "GTMSessionFetcher", "path": "GTMSessionFetcher", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988b89f54d2fe1748d20188f2159f84713", "path": "src/demux/anim_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980435d9e2c43ce279104385a45963c074", "path": "src/demux/demux.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9817180dfee1405b8486def6397032ba64", "path": "src/webp/demux.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9810f5632a0abda19de4e3bce3d12c532f", "name": "demux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b674423b3822a153d629b0b838794ce0", "path": "src/mux/anim_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a350c8c863ca5fae506a0b62b3ef6b4", "path": "src/mux/animi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ece265b5d6bd965d59e487aadc4c8d9", "path": "src/webp/mux.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98663ff7155a2978295273901a6260d5c5", "path": "src/mux/muxedit.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98921f19a8f546b2e1cb264f40fb2fc483", "path": "src/mux/muxi.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987ad46126f15814e8e414a48534b38634", "path": "src/mux/muxinternal.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9888e09aea013a501a3814d551ec2f1a22", "path": "src/mux/muxread.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98425ddedf940f6e1b7f7d68358dcd3ddc", "name": "mux", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9878910d62f29772f0776a79111efecea2", "path": "sharpyuv/sharpyuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9816b720d062054e05fa2e44fd49567640", "path": "sharpyuv/sharpyuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bbc392acc23ed190864da021654d8e5f", "path": "sharpyuv/sharpyuv_cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983d7bf6f27922abde41d950c27daecb4d", "path": "sharpyuv/sharpyuv_cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9828fa0cf8a30524315eb0ab1fe4549070", "path": "sharpyuv/sharpyuv_csp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e69e9dedf6e96d3834ee387d743fdf80", "path": "sharpyuv/sharpyuv_csp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9876bdc8569f7148873722e3de681d5dcc", "path": "sharpyuv/sharpyuv_dsp.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98883f67b39152284fbff8cadea2ff0e52", "path": "sharpyuv/sharpyuv_dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f13d8a266bba227144b652c3b32891a1", "path": "sharpyuv/sharpyuv_gamma.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a53763d96705ab54af4c858288428d6c", "path": "sharpyuv/sharpyuv_gamma.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c04e1102f4586e6ac634270c93a7e112", "path": "sharpyuv/sharpyuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983c51fbdc71e6da745af12ad684c9ee44", "path": "sharpyuv/sharpyuv_sse2.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c3ad4486f560b84e1152f2b34319f661", "name": "<PERSON><PERSON><PERSON>", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98dd07be7245be516a523a46b8ef7d2231", "path": "libwebp.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988ab24d1290ef722150f24d45de145b61", "path": "libwebp-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9896fd2e433e977a16f9230689e8fd9cee", "path": "libwebp-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ce48714a6c8f6a5e26cb0f8206c6a1d", "path": "libwebp-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a6e29bfeaa5e40b819cfc8f6c212978e", "path": "libwebp-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9898abf586ee34d2f8708f79bd1d9a3205", "path": "libwebp.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981756fdd58a959ffd779ed02f199e20a5", "path": "libwebp.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e63f79c3bd22ff8c5f5fc590d2c6b4dd", "name": "Support Files", "path": "../Target Support Files/libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cf30d05a27fc23b490e431b290b3f4ce", "path": "src/dec/alpha_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98021fff86031d1e585568b2b598665d08", "path": "src/enc/alpha_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b55a1e394fa7036e8725d38eda4d78a0", "path": "src/dsp/alpha_processing.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984d171ccf7f078aab9b39e8fd8b5982e9", "path": "src/dsp/alpha_processing_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981c5d76c78c44c234f778b2540027cfbc", "path": "src/dsp/alpha_processing_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98913196477447932d46465c15deb0ad36", "path": "src/dsp/alpha_processing_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98feed936dc595f1480cbc2ce9b8b958f5", "path": "src/dsp/alpha_processing_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830d3d61b3677003ae2c52e112e61a2f5", "path": "src/dec/alphai_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e0d0e774c0d983dc4e72a088e304d1cf", "path": "src/enc/analysis_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b52adf1a46133fcc1a3e23a8479113dc", "path": "src/enc/backward_references_cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c29206035605a8c47bdda5b960eb7566", "path": "src/enc/backward_references_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989294a52cb8ccdcda99fdfbc3d2af3004", "path": "src/enc/backward_references_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9861bbf8ff636dc17e60d9ea0156071686", "path": "src/utils/bit_reader_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981ec5ad42e0afceed6cc7203bea6b2c9c", "path": "src/utils/bit_reader_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807501b7200e51edea5479e1ad4a83519", "path": "src/utils/bit_reader_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988289d340299c7b10211acbb53470f1f6", "path": "src/utils/bit_writer_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98812b4ea41eee0f81e15acf34dcdd286c", "path": "src/utils/bit_writer_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d076e5ed5b0a5b927820929027f6c8a9", "path": "src/dec/buffer_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9801e7263ef494bbfd264ce0cc72b064d5", "path": "src/utils/color_cache_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bdfeff02ddf663b61814379e045acd1", "path": "src/utils/color_cache_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7870289afc99ead8baa4b49a27041a1", "path": "src/dec/common_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982b03720b16cfcdb89608b8c40b57dc41", "path": "src/dsp/common_sse2.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98548da517c4948a9897393ca86736a6e1", "path": "src/dsp/common_sse41.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98695448039c06f7c72afa2787009ad323", "path": "src/enc/config_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fe22577a2b8b0eb1325e1b00d88fb320", "path": "src/dsp/cost.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bb5c698c04673dd6910ddf66f163fd02", "path": "src/enc/cost_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989ca70355c040b7f7683a4483ac1fc5cf", "path": "src/enc/cost_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c92cba54d08d0a6d14656a7c0995c039", "path": "src/dsp/cost_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f404a817366085ad281ee8e9cef12b87", "path": "src/dsp/cost_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986ad73471000a8410e06e323ed70946f0", "path": "src/dsp/cost_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98217a9b8b570dc06ec4a0b576c192721c", "path": "src/dsp/cost_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98880a9b9b5139164e663a4e8569363927", "path": "src/dsp/cpu.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ada1a4187a692f0432d429b06f6a7acf", "path": "src/dsp/cpu.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e989779e9a4b6f8564ae4fb8e56457b36f3", "path": "src/dsp/dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ae1ae69f1018e9562753a5726dd2dd46", "path": "src/dsp/dec_clip_tables.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98587b97686771e805d23e8df9aacc4e93", "path": "src/dsp/dec_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c0c8baa0b90f07317963144dfd8421ce", "path": "src/dsp/dec_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b7c6f66121211bcfa1e687051710b617", "path": "src/dsp/dec_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98828272371d59279c58033c8a68b21625", "path": "src/dsp/dec_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9836c9d476d65775efd218205172d2e162", "path": "src/dsp/dec_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98166243dd0cf56187f06abd83833df5c2", "path": "src/dsp/dec_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fea54dafb25c8e4041942f27f51c7329", "path": "src/webp/decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ea270a73944864a2c4df0688614b8c2", "path": "src/dsp/dsp.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981499346a008d55c2d11e9354121a853a", "path": "src/dsp/enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985cd2a6d235b1214411b5acfb64703f3f", "path": "src/dsp/enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c971218b2ed33785237b894570545e6f", "path": "src/dsp/enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bc85f2cd52bc233bd3d404fe87d72f11", "path": "src/dsp/enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9866f6eb9f89b920289504b2fb6e8b414f", "path": "src/dsp/enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e984278a89f93097bb6e36e521cb85b6fab", "path": "src/dsp/enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9825b451b31dad05b4efbc7e183a446dcf", "path": "src/dsp/enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98701f13e40a93bf76eac664262fc33044", "path": "src/webp/encode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986c153a72cfae385d568350eda3a75d6a", "path": "src/utils/endian_inl_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f79e22863025d6df838cb8c44a66a69f", "path": "src/enc/filter_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9831035d9c10b5e9d4802fb03069373728", "path": "src/dsp/filters.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e5c4d02e64efa95468c397da05720ee6", "path": "src/dsp/filters_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988c8aadbd7545ab41f6d5fbe654d824d0", "path": "src/dsp/filters_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988b6577b4ed7213dd60a71f21ff24efa9", "path": "src/dsp/filters_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98acb0de44640f3aa171d7650fbec9251e", "path": "src/dsp/filters_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987939b2e9629dbe9f8ecb298928e8889a", "path": "src/utils/filters_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f6bbe7b425da4e54373df2abd521cb61", "path": "src/utils/filters_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cd457471ead918c70903494c09552650", "path": "src/webp/format_constants.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f15d063a406083676a5996750351d078", "path": "src/dec/frame_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9861878c2e95a3e3981372401632807bf8", "path": "src/enc/frame_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eafbcc30cbb40c2c41abfd6f0e570882", "path": "src/enc/histogram_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98620d4d3c61f870216bce66c2789512cc", "path": "src/enc/histogram_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98049529b73623ba7017934bd071ec0752", "path": "src/utils/huffman_encode_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9c907b8d4ce56339e0a3f218f4794e9", "path": "src/utils/huffman_encode_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b53023153fa825aad9f5cd7af8908182", "path": "src/utils/huffman_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987373dccca6bd726ca21990cb0f043aa7", "path": "src/utils/huffman_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983501e9096d53d3ea82712d5cbec102e1", "path": "src/dec/idec_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983ada76f59847f767b652c4c049c45759", "path": "src/dec/io_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bd1d9fc8971aec0b1ed0b26c47a2ff69", "path": "src/enc/iterator_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9840716ccbd3c52d20748edaaf626d03dd", "path": "src/dsp/lossless.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1e5a05d33a711e042b3cca0fc9abdb3", "path": "src/dsp/lossless.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bae39a982a04befd54eada57084ecb0f", "path": "src/dsp/lossless_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e985d3fc10fea1dc31cf83c819f7ff17d38", "path": "src/dsp/lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9835019f0af5f25262b4ee316d4073f602", "path": "src/dsp/lossless_enc_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9891fd28757e54f2e3727bdc7938ec1182", "path": "src/dsp/lossless_enc_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982568078b79d1910770ae9de34592ff91", "path": "src/dsp/lossless_enc_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b6eda639bf7d550df4f9641737ce6ce2", "path": "src/dsp/lossless_enc_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98e04d96c6f1ea81dc2c0fcd8da14d9bac", "path": "src/dsp/lossless_enc_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cc4f471cbaad68ea14b27b2f8d49e4f4", "path": "src/dsp/lossless_enc_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98dc118c8f9453e1e0c8e5e37072cae356", "path": "src/dsp/lossless_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98446ecb82a74ae0933733d8c98454b7b0", "path": "src/dsp/lossless_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981d61e7e77a5649563d0d44ca5a5053c2", "path": "src/dsp/lossless_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ea9b2817f9b0c52c44e7bf9aa3b0a3c9", "path": "src/dsp/lossless_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fba6c9d094e50f5bea2d3817130d4a70", "path": "src/dsp/lossless_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98793f307869b6baeaccc8b71e023b3129", "path": "src/dsp/mips_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d12415091279d9d472fa8c237276555e", "path": "src/dsp/msa_macro.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dec6433f47667a4badc56c33072ac53b", "path": "src/webp/mux_types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9893854ae6b89a650e908fb245618928bd", "path": "src/enc/near_lossless_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981710c3ed58dab63cc9cbc4bd6c9cef31", "path": "src/dsp/neon.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983f879f02b072db13625cdfd403426cb9", "path": "src/utils/palette.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98772e6b51213dc7e9f51553431976800f", "path": "src/utils/palette.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fc4e7c9ca353e1e2b09b735f30375a14", "path": "src/enc/picture_csp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98eeb7cecc4b2de7305b0bf45857fa4de2", "path": "src/enc/picture_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98139d8c43e640ca6725aa5cf625a5a6a1", "path": "src/enc/picture_psnr_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9895acddfe68432df367e9b1f5fc340c92", "path": "src/enc/picture_rescale_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9858c80dc3149691ce8a33ebfb3f1e7700", "path": "src/enc/picture_tools_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98226d3d32468ea7a6ce7ef8d8527751d1", "path": "src/enc/predictor_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825663b75a53bbb0462dde17b8cc4abd3", "path": "src/dsp/quant.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986e437d1853cb863589284f00d67f2a8d", "path": "src/dec/quant_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98ffe4391db483ad41a7e60a79c14e3ec7", "path": "src/enc/quant_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d0c3dfc7a18d657bc307afc40e5e8a20", "path": "src/utils/quant_levels_dec_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988a586ebee8118c0f8f69b2c0f612328e", "path": "src/utils/quant_levels_dec_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cbedf8119255a34fb1685b1ba23e9810", "path": "src/utils/quant_levels_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eeafe12f8aba047a3afd530575fadf23", "path": "src/utils/quant_levels_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9804f1baad1365ec29ab3fb24dcc34bf34", "path": "src/utils/random_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9c0bfc57553b936a38698de116b4221", "path": "src/utils/random_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98366f4eac09c6f36efd3c84275217b945", "path": "src/dsp/rescaler.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98aca26d3050b2461993f512202da54960", "path": "src/dsp/rescaler_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98afed7882eb7b062718935c4b72d03988", "path": "src/dsp/rescaler_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98bfaafd67380d68f8cb98e44030b7b060", "path": "src/dsp/rescaler_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98fce7d0751e84044faac05e0a78e25510", "path": "src/dsp/rescaler_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b497d345b9caeadf10532f4aa458ecc8", "path": "src/dsp/rescaler_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98aadfc1abfd222055d236c8cb5b24f1cb", "path": "src/utils/rescaler_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987ed2f47b3515005162aaacbb2afc91e9", "path": "src/utils/rescaler_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986c12c9440524f3249c689904498a45d6", "path": "src/dsp/ssim.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9808db55a3ff9d105d348eba931ccd2dbc", "path": "src/dsp/ssim_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e983bbed08a93a044bb0f52c1d016c23ca5", "path": "src/enc/syntax_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9874fd273bd02d552b35b10b446e257dcf", "path": "src/utils/thread_utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981de486c90c5f989f69cb6d20202606cc", "path": "src/utils/thread_utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e980c1c3fd30d3694b7835322123ba5ca1a", "path": "src/enc/token_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e981484999620bc3ddf4578636aa106ee97", "path": "src/dec/tree_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98a7034efe3ab293dad42d4c681acd2321", "path": "src/enc/tree_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cde40db7a3c685fffd30ce719f170873", "path": "src/webp/types.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f72c0e6c425228ba135d243a90b5f66c", "path": "src/dsp/upsampling.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cabc528a48d06604ebcdf76e6b996c0d", "path": "src/dsp/upsampling_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c39c7ec3fe33f2b46261f819a4b9096a", "path": "src/dsp/upsampling_msa.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9852df4df15abfae14e7fe6571892ef6fc", "path": "src/dsp/upsampling_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c17d80d802b7068f3292fad1bb7384d5", "path": "src/dsp/upsampling_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98b031fe1180f301916ee29c28eb851ab2", "path": "src/dsp/upsampling_sse41.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98523b987cfd351c052d6da08cfccac49f", "path": "src/utils/utils.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9830a0c8908fad12a8fc1b77df7e02d647", "path": "src/utils/utils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98c5275711f249e5f887ae69b2770c9f8b", "path": "src/dec/vp8_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98656d2eb4bfd7517a767b0339edb0c39b", "path": "src/dec/vp8_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98048991c75d850a6c250f56538c1dd1cd", "path": "src/dec/vp8i_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c5db74c824f4514917c7f7cefa21bfce", "path": "src/enc/vp8i_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98cac8d0b72ee298dd27fc34803d1dc087", "path": "src/dec/vp8l_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982fb9b8bd2e6f0c27b952622763bf7a87", "path": "src/enc/vp8l_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a9ff0fa4d0033d62f05127200aef5e0e", "path": "src/dec/vp8li_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988aa7c881ef7ee24fe9f961eeed6c4a5a", "path": "src/enc/vp8li_enc.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98d47eb2e5f81e53aa97241c85bd41f97e", "path": "src/dec/webp_dec.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9884ade4594aa325223c3440145b3580dd", "path": "src/enc/webp_enc.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9855cd0b7ba0ecbe4bdb95738481268e92", "path": "src/dec/webpi_dec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e986dc35e8016b08ffa4880a21e14cce94e", "path": "src/dsp/yuv.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838f664c02c746bd32e278feb28363ecb", "path": "src/dsp/yuv.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98160a690d3ccf59746cfaa5e95ff8c9fd", "path": "src/dsp/yuv_mips32.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e9801b48e8a0f88f6ec4fcb305337e6946c", "path": "src/dsp/yuv_mips_dsp_r2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98f35969e474f203d486d2e500ed103221", "path": "src/dsp/yuv_neon.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98edaac65fe8ce1328d3888ca118db43f3", "path": "src/dsp/yuv_sse2.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e988f9114f93086a8a24929a1c97f25589c", "path": "src/dsp/yuv_sse41.c", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983eaade3400776d0192934efd5db057cf", "name": "webp", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98839d34bc36a635de88e037408f5ee6e5", "name": "libwebp", "path": "libwebp", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9842fc59e6bda7c18a27e0b0ebee58564e", "path": "Mantle/include/Mantle.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984291474950301d9c64083fe2dc1bfaa7", "path": "Mantle/include/MTLJSONAdapter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982e37a083316b2f67c92f5a2c6b52e1b4", "path": "Mantle/MTLJSONAdapter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e702109387f29d5bfb2dc4e334e72faf", "path": "Mantle/include/MTLModel.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986edb0837ab7c031ffd82e523372b57b1", "path": "Mantle/MTLModel.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae52dc618b83195b5d478e33f1fde628", "path": "Mantle/include/MTLModel+NSCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b97ae66d376af98639debbc0c28c3f19", "path": "Mantle/MTLModel+NSCoding.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986e1ceb1bb17b44144d6d6b4b7574c223", "path": "Mantle/MTLReflection.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f0882f4661671098031edd9727a3f8c", "path": "Mantle/MTLReflection.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981a1698a6ca0bcfe6cf29b4f7b5f1d7e8", "path": "Mantle/include/MTLTransformerErrorHandling.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9879d2f5f43b4e7b0663d2eb9a9f01314b", "path": "Mantle/MTLTransformerErrorHandling.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d18a82c975b01acf328a9c81d4917b3", "path": "Mantle/include/MTLValueTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c4d0eee735490dab1be3ea65bb3fd9c4", "path": "Mantle/MTLValueTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9825e51202d5fae26828810e0455d9c41b", "path": "Mantle/include/NSArray+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f58eebe3899f2402c7a0604409960aa0", "path": "Mantle/NSArray+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ae46bcf0823beecd5846bea8a03c248e", "path": "Mantle/NSDictionary+MTLJSONKeyPath.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bddcf38e908f755f27e0705433a8e86d", "path": "Mantle/NSDictionary+MTLJSONKeyPath.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9831aa6d0b15a71b6111c4c2b01708eef2", "path": "Mantle/include/NSDictionary+MTLManipulationAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9844a65924f94c6f657acd6c046f451220", "path": "Mantle/NSDictionary+MTLManipulationAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c667600c0468f25e75a27fc98a75e0c", "path": "Mantle/include/NSDictionary+MTLMappingAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f0ea48ba12d7361f2395f6f9ad0e945", "path": "Mantle/NSDictionary+MTLMappingAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980e5ed5e6a946e634aa3a5adaa07a6b92", "path": "Mantle/NSError+MTLModelException.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98318c252886d70124f9578d7f712d12c0", "path": "Mantle/NSError+MTLModelException.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987438e2aaa0316ef6a0168a85da8e3123", "path": "Mantle/include/NSObject+MTLComparisonAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984c9888f7e9badc44cbf961a0b18c8998", "path": "Mantle/NSObject+MTLComparisonAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874fbb6f3e35fde5ac9e1991174cef769", "path": "Mantle/include/NSValueTransformer+MTLInversionAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98530b25a7a592edfbe99b2be4ddbe7c84", "path": "Mantle/NSValueTransformer+MTLInversionAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb2047ad37f81ea79d2a1ff2f59514f4", "path": "Mantle/include/NSValueTransformer+MTLPredefinedTransformerAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b155facdf793d50cce917a6cbc9288dc", "path": "Mantle/NSValueTransformer+MTLPredefinedTransformerAdditions.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a32810daa1ee87f67f3b00d1de1ebe6", "path": "Mantle/extobjc/include/MTLEXTKeyPathCoding.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b9363302d310ceb3afbf45998a551e5d", "path": "Mantle/extobjc/include/MTLEXTRuntimeExtensions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98497cfbf98e0f85c5e2d0b93e02cd2bd1", "path": "Mantle/extobjc/MTLEXTRuntimeExtensions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98650508ffd758bc206ddd29c9c951c785", "path": "Mantle/extobjc/include/MTLEXTScope.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9871c3fda5bbc9e8e2f0f7acc60f476049", "path": "Mantle/extobjc/MTLEXTScope.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98952fe7441adc0497849f3aa90530e700", "path": "Mantle/extobjc/include/MTLMetamacros.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d7dc1772cd7eb8adb18d55333564c235", "name": "extobjc", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98657e2dbe5c8db84375b60b57ba1c35ad", "path": "Mantle.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9841912f7f8200968db131076196fb1798", "path": "Mantle-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fa260d55da4ac215e7e29b12305f7880", "path": "Mantle-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a4939a74501366507d5f7a5b42c9160f", "path": "Mantle-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d96879472e90ce050721419f601f263", "path": "Mantle-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980d4afc8b737681e0f4bd353b665d3f34", "path": "Mantle.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f16599dde3ac9f606d5054e2d6694787", "path": "Mantle.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983146e4bad4d37e454b601c38adbf00b7", "name": "Support Files", "path": "../Target Support Files/Mantle", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f97883768ced3bfa2ef5a5d3879d59f0", "name": "Mantle", "path": "Mantle", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98933fa248b76be2ffca3c3e94a14c61ed", "path": "Frameworks/MLImage.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985011fc553348739be65935a57546f64b", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98798857c4d816c79be3e28dd6bb5ccc30", "path": "MLImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a22beb6fd5cf7c6d1c65425445853cac", "path": "MLImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985a5c40b91dfa28e3d311b9481cbe92da", "name": "Support Files", "path": "../Target Support Files/MLImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98df781c42fdc1e528e9e9163912ce7a0d", "name": "MLImage", "path": "MLImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e988f624bc6dafb63dd9964258ab98236e6", "path": "Frameworks/MLKitBarcodeScanning.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b1582a0a048976d6e6538cc817f0e9a8", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f280577ec075f8251cc0ab78b302bb57", "path": "MLKitBarcodeScanning.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98c6c892f3e6fe6ac4313b6f80025e06a6", "path": "MLKitBarcodeScanning.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987468ab44dc58fbee9ef1320555196a90", "name": "Support Files", "path": "../Target Support Files/MLKitBarcodeScanning", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880758792666f778485b29d271a06c40e", "name": "MLKitBarcodeScanning", "path": "MLKitBarcodeScanning", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e986fa743b54d9637c2574aeb846da9bfb3", "path": "Frameworks/MLKitCommon.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98566026ba01e33c646f8d0a18a82647fc", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981a1285521d6dab8a01c5204544db714f", "path": "MLKitCommon.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dafe0ffd4a84dec3cdb4ad9a0dbe13f2", "path": "MLKitCommon.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fe689040d8faa75b0cf4e24c0031a586", "name": "Support Files", "path": "../Target Support Files/MLKitCommon", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7be37509606b212850f8d5ab1e53bdf", "name": "MLKitCommon", "path": "MLKitCommon", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e98aebf08f4cd85885fcb14797a9aede3c3", "path": "Frameworks/MLKitVision.framework", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989827fbbb028d4a2214f48a6e92d05470", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98abaa29be23b0431a4d2aae2f27eb6d22", "path": "MLKitVision.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988f7691cf17a1be96da18a15688408d8b", "path": "MLKitVision.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b122f3a12aaa2046ad97da56187f00f4", "name": "Support Files", "path": "../Target Support Files/MLKitVision", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a06ed4eb577629477ed31c504e83aff3", "name": "MLKitVision", "path": "MLKitVision", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f8a92fcd17bc6f0b7964e6ad80f170ae", "path": "pb.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e987bbbfd9090d0a27301aab0a1649a09f4", "path": "pb_common.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984a23733a5a863425c40cda654cb39b25", "path": "pb_common.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e982f46e2ce026e4ac61d9d0ae2ef4f1294", "path": "pb_decode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c2d8ba4a27921a850a8e85edaedc99a0", "path": "pb_decode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.c", "guid": "bfdfe7dc352907fc980b868725387e98aa4bcc30f8e98d3472bc9dd1610bc8ea", "path": "pb_encode.c", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9843228d96958ae541d9f5fa4f20473194", "path": "pb_encode.h", "sourceTree": "<group>", "type": "file"}, {"guid": "bfdfe7dc352907fc980b868725387e98278ec477e68a8aa3cf37486bbc85e649", "name": "decode", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98d53497cf65de9ab0351de05894dddf46", "name": "encode", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e987b1762b0fd692e98c4e8e824954ad7c3", "path": "spm_resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a3929b1e43820667e2d73583ddc8b10b", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9801d77c663761675092af6c2aab1b369d", "path": "nanopb.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f7ad8e4331ea03bcd9d06f4c714b0aff", "path": "nanopb-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9857ae5a62ea71214380506d0de731b730", "path": "nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba59e6d4633e5136dbdbc632e5b3f245", "path": "nanopb-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c176f96e5fd3d8e49da6620752d5b8a", "path": "nanopb-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986134154d8b59c668658043dfd7b1e022", "path": "nanopb.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98a73b8d2e65918cc18902e504c6a82edf", "path": "nanopb.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98308e72894a78e05ab78c059442232778", "path": "ResourceBundle-nanopb_Privacy-nanopb-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9886b842fa01e3e480351dd721dc0d0292", "name": "Support Files", "path": "../Target Support Files/nanopb", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989a95fd536ef8c3dd72300d5a1be97677", "name": "nanopb", "path": "nanopb", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d8792cc82c427019ae5164d23c345cee", "path": "Sources/FBLPromises/include/FBLPromise.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9802837646a41eaba7b7874b2a2543f3f1", "path": "Sources/FBLPromises/FBLPromise.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c45604c1f87245900938f33806c72015", "path": "Sources/FBLPromises/include/FBLPromise+All.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a4fd41dfaf342df4b51eefabfeab8058", "path": "Sources/FBLPromises/FBLPromise+All.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a44e4bc867fa5f45e20f0fc7d40fa54e", "path": "Sources/FBLPromises/include/FBLPromise+Always.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980a651962627b413b7849b42000538664", "path": "Sources/FBLPromises/FBLPromise+Always.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f0f1e4fbcb03d3694a77253cc7967c2", "path": "Sources/FBLPromises/include/FBLPromise+Any.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98dfaca905970837e6ed2b6749b7145982", "path": "Sources/FBLPromises/FBLPromise+Any.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988856149bdb2808919e04892f1dc625d1", "path": "Sources/FBLPromises/include/FBLPromise+Async.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d2baf7120e48d9c08143875d8bbba0d", "path": "Sources/FBLPromises/FBLPromise+Async.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985a55269dd6fbe7348d24a3351aa74476", "path": "Sources/FBLPromises/include/FBLPromise+Await.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9843126e1ad4de76b0f8b2339c03f71da9", "path": "Sources/FBLPromises/FBLPromise+Await.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98313a2525541f9416b254499a80c2f1e6", "path": "Sources/FBLPromises/include/FBLPromise+Catch.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cec59cfb5e4e3c52ae2c5913c3b3d741", "path": "Sources/FBLPromises/FBLPromise+Catch.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9833297940058873d3f9ffa1aa4798c1c4", "path": "Sources/FBLPromises/include/FBLPromise+Delay.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abcba063afa66cba016494fe2f698aba", "path": "Sources/FBLPromises/FBLPromise+Delay.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f55db718540a4937ccb7fd5c2e903550", "path": "Sources/FBLPromises/include/FBLPromise+Do.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9811634faf16a542c72fbc9385e4c5c346", "path": "Sources/FBLPromises/FBLPromise+Do.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988e2a3b67a453f7191077dc957c1e8798", "path": "Sources/FBLPromises/include/FBLPromise+Race.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981e8ad6ae636cc64eed6ad192de722c31", "path": "Sources/FBLPromises/FBLPromise+Race.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987463f81826446c43af543f82c97c6f7f", "path": "Sources/FBLPromises/include/FBLPromise+Recover.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981bca6d6e0f523075ceb02e7de313f7d8", "path": "Sources/FBLPromises/FBLPromise+Recover.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fb38ce2c6f9be671053317bb9e5951c7", "path": "Sources/FBLPromises/include/FBLPromise+Reduce.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9896e9510983a8b9c38a986300bff2d033", "path": "Sources/FBLPromises/FBLPromise+Reduce.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984eb9b1edaeeb616251f161412bb727ab", "path": "Sources/FBLPromises/include/FBLPromise+Retry.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98558785e3d4cfe836a000960860aaadee", "path": "Sources/FBLPromises/FBLPromise+Retry.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9836811d2c030e8cd968923dcf9247e7b7", "path": "Sources/FBLPromises/include/FBLPromise+Testing.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98bc30cbfaa07d7319b1f848b739177ded", "path": "Sources/FBLPromises/FBLPromise+Testing.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9820daf7e1fb32f2c5ed7efcfcc2857f22", "path": "Sources/FBLPromises/include/FBLPromise+Then.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9874e7c386e62f4cea7c6fd10fe3c1991a", "path": "Sources/FBLPromises/FBLPromise+Then.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988b4856bdf39c2b971e0904b6a88a3bfa", "path": "Sources/FBLPromises/include/FBLPromise+Timeout.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f5edfa3b24db672885784aa48994ab9f", "path": "Sources/FBLPromises/FBLPromise+Timeout.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a3adc50c77a4d9e29f0c0ea5ec2b811", "path": "Sources/FBLPromises/include/FBLPromise+Validate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc40dd0a643dc110ebdb8d3762cbda07", "path": "Sources/FBLPromises/FBLPromise+Validate.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858f2f131d9c013f39433ebe192321a5c", "path": "Sources/FBLPromises/include/FBLPromise+Wrap.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d900b62367d4f43d9df91d75d1cb12cc", "path": "Sources/FBLPromises/FBLPromise+Wrap.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c74d43c74a15e9cedd588d28c0ef9635", "path": "Sources/FBLPromises/include/FBLPromiseError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9801f4aed02265aac031b2c6a66266de4a", "path": "Sources/FBLPromises/FBLPromiseError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9892f05562ea41a2e04782824bb0173a9b", "path": "Sources/FBLPromises/include/FBLPromisePrivate.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98971f05fffc3d983c299fa4e644cb6d92", "path": "Sources/FBLPromises/include/FBLPromises.h", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9835c9a4026f670d5ee055e7db9ffdef5f", "path": "Sources/FBLPromises/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98738e3c290575bcdc48724aa02ccd36a2", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e981cc29ae80bf18aebcdb6802babf3bd6f", "path": "PromisesObjC.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98adc3459eb9f223404193b4beb4386fc0", "path": "PromisesObjC-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988453f3fc66f75e0b26eaa77d1fca415b", "path": "PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1b5bd47a6fbd2e28e2c5559152f97fc", "path": "PromisesObjC-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9875444efcbaa5e6a1510d39fe70529cf3", "path": "PromisesObjC.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e980338b487b4f0af0807abaa34d4c52be1", "path": "PromisesObjC.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9819204046891a51ca46672a25f681951d", "path": "ResourceBundle-FBLPromises_Privacy-PromisesObjC-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9849d04c485634a03d9028fba00440f8c3", "name": "Support Files", "path": "../Target Support Files/PromisesObjC", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9896504b9355f3a74f61945558a20f1415", "name": "PromisesObjC", "path": "PromisesObjC", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b332cbd9505c52f9f9722062f52d89e3", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d7ba36c4a73118a768f6b8255812f1d2", "path": "SDWebImage/Private/NSBezierPath+SDRoundedCorners.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989993fb02bb1afa6f43a2390b4fcbb6b7", "path": "SDWebImage/Core/NSButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9821f8c8a28bc9472dcc5b67a0b970b52c", "path": "SDWebImage/Core/NSButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98759df685460f0e1655d9d7d6cfe6e8b8", "path": "SDWebImage/Core/NSData+ImageContentType.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983b6e95e4e0d268e0c69382a71822580d", "path": "SDWebImage/Core/NSData+ImageContentType.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ff84b44cfa100dd637dcb3f11eefec58", "path": "SDWebImage/Core/NSImage+Compatibility.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98918f4bedeb68e14956522afdf74cf7a1", "path": "SDWebImage/Core/NSImage+Compatibility.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ba03de3a3a709e9b479ec7b6f94e3825", "path": "SDWebImage/Core/SDAnimatedImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9878e05ee64a34fdd69edf6cbf3855673e", "path": "SDWebImage/Core/SDAnimatedImage.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c15873a7819fe78de446c68a4ccb2485", "path": "SDWebImage/Core/SDAnimatedImagePlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983979e05b77ea2d6b8d3a7b933d9acd07", "path": "SDWebImage/Core/SDAnimatedImagePlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9838989d85a58dd1e2a6d86ffc0913e5dc", "path": "SDWebImage/Core/SDAnimatedImageRep.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983033df4246a920b39e8b245fbf5eb20d", "path": "SDWebImage/Core/SDAnimatedImageRep.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fbe16a5f37597624b6331de38c6d068b", "path": "SDWebImage/Core/SDAnimatedImageView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982f36ef875ac313a891a6776bc798e549", "path": "SDWebImage/Core/SDAnimatedImageView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98acd33b3ea4fffc1e8a63d243b13db1a8", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9816580c78fadd4adb3ce05781a3e42ad7", "path": "SDWebImage/Core/SDAnimatedImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b746fe0ddb4d76094f704b71ca71aed1", "path": "SDWebImage/Private/SDAssociatedObject.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f25706eae9b36d51a41674f4e88d2841", "path": "SDWebImage/Private/SDAssociatedObject.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983c89240cc7080eebcb8f58baa954d97e", "path": "SDWebImage/Private/SDAsyncBlockOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9850097ea22d5ccc4ca336865f741815fe", "path": "SDWebImage/Private/SDAsyncBlockOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98675957ef8e539a3e47ec3e07c757479a", "path": "SDWebImage/Core/SDCallbackQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984112f6fcd1d4b76459c3192b7bb9cc07", "path": "SDWebImage/Core/SDCallbackQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989c0a98028c9cec6d18e3ad6451edf832", "path": "SDWebImage/Private/SDDeviceHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984062904c24dd3599c889b113496a3f37", "path": "SDWebImage/Private/SDDeviceHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982bd03eefdc234bb1fbdb0f85b6334196", "path": "SDWebImage/Core/SDDiskCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f3b5840be5e6c1d44611022793657e81", "path": "SDWebImage/Core/SDDiskCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981b61c56c8678bd70c48be24b3cc5dabd", "path": "SDWebImage/Private/SDDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a5cbd47bab3d8521cfe3cb134a80a580", "path": "SDWebImage/Private/SDDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c46794eb227748d2aef8264e0cc96067", "path": "SDWebImage/Private/SDFileAttributeHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98de63f301f36d3a11eb2ee6d7da9b897c", "path": "SDWebImage/Private/SDFileAttributeHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987b49a0627cf43ec70220b8e53bb6b0f2", "path": "SDWebImage/Core/SDGraphicsImageRenderer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986a54d3c6ff085f3cba67bb2781a3f158", "path": "SDWebImage/Core/SDGraphicsImageRenderer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9834d726384c1c6f26e83c9a984ebaf3c2", "path": "SDWebImage/Core/SDImageAPNGCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9885fc9a952d4f27d9c7f418af1aacfc5b", "path": "SDWebImage/Core/SDImageAPNGCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9803d01c9e3cccc78fd661f68864b6afb3", "path": "SDWebImage/Private/SDImageAssetManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884d26d64773df15dda68bfa6e05caaec", "path": "SDWebImage/Private/SDImageAssetManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9874baab5c9ee35ce32a3f07d0fe7e3ebd", "path": "SDWebImage/Core/SDImageAWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9842db603a469b5a4b7608ecb746407416", "path": "SDWebImage/Core/SDImageAWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9883b585e3a8eee90e2b141c3d4ed93b73", "path": "SDWebImage/Core/SDImageCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f135019e630609093cffe449194b5b1b", "path": "SDWebImage/Core/SDImageCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98812a54e4122a9ef64d1769c4ac3d14d3", "path": "SDWebImage/Core/SDImageCacheConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e84e31f13eb3a160e106f2b07df55900", "path": "SDWebImage/Core/SDImageCacheConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98240af2daadfb613b900d6d565973d940", "path": "SDWebImage/Core/SDImageCacheDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98efac53062076a627953150192359ded8", "path": "SDWebImage/Core/SDImageCacheDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9807e13f6489a85b46f680d259a97a4c66", "path": "SDWebImage/Core/SDImageCachesManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a37ca570ab36f4e478bb5da1a4d64482", "path": "SDWebImage/Core/SDImageCachesManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985188ce21d2548fb5878b327ec2c84b80", "path": "SDWebImage/Private/SDImageCachesManagerOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98abf1de6288c5eb21b334ff92c4d0d669", "path": "SDWebImage/Private/SDImageCachesManagerOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98753515e96c8e8e1b5c86f4837ed2f23a", "path": "SDWebImage/Core/SDImageCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983c0ff25d24f44ee112e618ef200858d0", "path": "SDWebImage/Core/SDImageCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab817b8be09e7debc21140b521a645e8", "path": "SDWebImage/Core/SDImageCoderHelper.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985ebfc174c222bcf0ecaf8b13574235f4", "path": "SDWebImage/Core/SDImageCoderHelper.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dcd58b1097c1cd858faf5028ed837668", "path": "SDWebImage/Core/SDImageCodersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9d8669c1e367dc657e81934099e5c32", "path": "SDWebImage/Core/SDImageCodersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c7f40414e392d7d236cba93de2149799", "path": "SDWebImage/Core/SDImageFrame.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981e9a372016a2af3def72f12486bccab0", "path": "SDWebImage/Core/SDImageFrame.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98246bd38a11172f0ee8ae68ae6775956a", "path": "SDWebImage/Private/SDImageFramePool.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f75be7814e7e64a31d21d12ca3dc6b8c", "path": "SDWebImage/Private/SDImageFramePool.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98571c2d9e4514956dcf661019708bb2df", "path": "SDWebImage/Core/SDImageGIFCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9891314ba307eeb77edd67c52bc95b6b4e", "path": "SDWebImage/Core/SDImageGIFCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987a5b297df172810de3b4887178050c80", "path": "SDWebImage/Core/SDImageGraphics.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ce8ae8c2b2882a427dde10b444323d25", "path": "SDWebImage/Core/SDImageGraphics.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9847b89386e6c545acd79ceb1abdf6d5c1", "path": "SDWebImage/Core/SDImageHEICCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a0824e8f9a2740606f78ab277d756e57", "path": "SDWebImage/Core/SDImageHEICCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987c2ab496a2e6f692dc040eec86bcf389", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982c5046071864103b23c745c0a9d5eed8", "path": "SDWebImage/Core/SDImageIOAnimatedCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986601ab4b3ea1d2bed6eabc1ccbe44773", "path": "SDWebImage/Private/SDImageIOAnimatedCoderInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c80b9ef09f38b4b0bcc5165db4e2c6e3", "path": "SDWebImage/Core/SDImageIOCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e46ea7124b81e2374f4c8793513225bd", "path": "SDWebImage/Core/SDImageIOCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987bfece4b6fb7487dcdfae9c3e5fa8db4", "path": "SDWebImage/Core/SDImageLoader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c59714db64f343d48b32ba020c97a5fc", "path": "SDWebImage/Core/SDImageLoader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c582dda3cc19bfcfe20f3fb512e9b903", "path": "SDWebImage/Core/SDImageLoadersManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98600578aff78b9a720360fbe25408e210", "path": "SDWebImage/Core/SDImageLoadersManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988495cf6114bb2ea9ac6e30e948342719", "path": "SDWebImage/Core/SDImageTransformer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cc92b63278f576409fdbd1ba00772825", "path": "SDWebImage/Core/SDImageTransformer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9877a538347ae2aa96dcd86ef1f7404227", "path": "SDWebImage/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98affb17342fcaffa24e1181ff6b612212", "path": "SDWebImage/Private/SDInternalMacros.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9860b79c017eeca8ba9760176982ff0242", "path": "SDWebImage/Core/SDMemoryCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e984394940bd7a029292516bb585aa3cd95", "path": "SDWebImage/Core/SDMemoryCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9871ef3b07c892626d814b0db91b4b4d5f", "path": "SDWebImage/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d62a7c74dda4b7fb3e8e8ba8581b7ece", "path": "SDWebImage/Private/SDWeakProxy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d9772c57abe51ca4871b6b62bbf4d1cd", "path": "SDWebImage/Private/SDWeakProxy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c4780b1a1c2b32ecf3ba9c03d027e36", "path": "WebImage/SDWebImage.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984d566e66ebfc31ecf03849943899e043", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c19056d46a9d2f091d84e7a2db67ba47", "path": "SDWebImage/Core/SDWebImageCacheKeyFilter.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98656c535bdef27af08719d22ed5f25fc6", "path": "SDWebImage/Core/SDWebImageCacheSerializer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98263c3b64bd78421d782837168a2a1e9f", "path": "SDWebImage/Core/SDWebImageCacheSerializer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9898ce8bea7ca677b41c19d1e12567cb40", "path": "SDWebImage/Core/SDWebImageCompat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982a6f8894b2b7e044b925be85a2cad69a", "path": "SDWebImage/Core/SDWebImageCompat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a95a0f2fc19b16e630e4bba5041ca489", "path": "SDWebImage/Core/SDWebImageDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985f8fac5f4555adfabb9b2faf18bf0891", "path": "SDWebImage/Core/SDWebImageDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981f821f5aff86e57740ef82488a7bb991", "path": "SDWebImage/Core/SDWebImageDownloader.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c92f0ba1b0bc99eb488e6692a9b82de2", "path": "SDWebImage/Core/SDWebImageDownloader.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e6bf8ad3270c47b5b286311cc39526b1", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884a13ea267080d564df0d04898f038d8", "path": "SDWebImage/Core/SDWebImageDownloaderConfig.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0fe5b0083fa54d9646d4d09a6677722", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9854eb07390ab48a37c327656316362f60", "path": "SDWebImage/Core/SDWebImageDownloaderDecryptor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989d533c5bec0b47a60ec33e232012e5bd", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9820f0acdca9e11a2bb013286225a1f656", "path": "SDWebImage/Core/SDWebImageDownloaderOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9895eab4b1d70cecf0c9c0d62b84b0ecff", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989a0fdef32050042f01b018cb8effc5bd", "path": "SDWebImage/Core/SDWebImageDownloaderRequestModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9886f1efbd5d156d8d199677c3ddb04c91", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cd4c712c5fa539f73b3563599fdb45ac", "path": "SDWebImage/Core/SDWebImageDownloaderResponseModifier.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981025f6d2088a3fef5dca606f7ad95de3", "path": "SDWebImage/Core/SDWebImageError.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9893536d6cbf8a3d3035b8486b9c1f86c8", "path": "SDWebImage/Core/SDWebImageError.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98752100a297e4be76a2149b448161373d", "path": "SDWebImage/Core/SDWebImageIndicator.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98e25324d70037bf23e5347a56d239e8c4", "path": "SDWebImage/Core/SDWebImageIndicator.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f9ce42b39eb1765a921ac915c0e20eaf", "path": "SDWebImage/Core/SDWebImageManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981e78765ab0d946a4d798c9a05a2607d3", "path": "SDWebImage/Core/SDWebImageManager.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984ffa519d719874b2489c129a148bd6e9", "path": "SDWebImage/Core/SDWebImageOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f25f7e1301e25262aa875987160eff99", "path": "SDWebImage/Core/SDWebImageOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981c4d94ca61255ad906489386c36fbb86", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1c2f1004602070c56feaee59b6193d9", "path": "SDWebImage/Core/SDWebImageOptionsProcessor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9635b72224bf864300edb017ca2ed53", "path": "SDWebImage/Core/SDWebImagePrefetcher.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eb049fa8d8a163f90329fb2b1cfcc894", "path": "SDWebImage/Core/SDWebImagePrefetcher.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9ceca46f9a50f07eb022ffaa3a6e719", "path": "SDWebImage/Core/SDWebImageTransition.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c335f2257d2987ea283316bc81129f85", "path": "SDWebImage/Core/SDWebImageTransition.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982a0a12d3d92d82066f9be428c69c5b4a", "path": "SDWebImage/Private/SDWebImageTransitionInternal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9882d0f95e7c2f292169c18b3df0875988", "path": "SDWebImage/Core/UIButton+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d37ac35413451fc553da34d00fd810d3", "path": "SDWebImage/Core/UIButton+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986cb30d69ba8b8fa82604be397fb99961", "path": "SDWebImage/Private/UIColor+SDHexString.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98159f2d7e2d27b7163da5765025ae7811", "path": "SDWebImage/Private/UIColor+SDHexString.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d32a2891b7bb4b3262e71a82fd273543", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981aca7482f371c29b6f549093bfef65eb", "path": "SDWebImage/Core/UIImage+ExtendedCacheData.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98a24c9ed9d4ddedbffd2ef77d5edc5a01", "path": "SDWebImage/Core/UIImage+ForceDecode.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98975edfaebbf33dbe9fa69d47da9a8315", "path": "SDWebImage/Core/UIImage+ForceDecode.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987bf2ab7ba7108261f747349e23c36029", "path": "SDWebImage/Core/UIImage+GIF.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9849e70cbea71dc8061bf61503b7aeb8c9", "path": "SDWebImage/Core/UIImage+GIF.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986d9432b8e8b916ee3e50619bbecd97bd", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988aa4871f6598f0295166ad39f054406a", "path": "SDWebImage/Core/UIImage+MemoryCacheCost.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985515a94561b38bc9d59bb768eb281a21", "path": "SDWebImage/Core/UIImage+Metadata.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9818758948a1cc2646113f75839a64799e", "path": "SDWebImage/Core/UIImage+Metadata.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985534f29618bbd89623f30640bcdfd0aa", "path": "SDWebImage/Core/UIImage+MultiFormat.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a9deb62365078b09706af82d9c809ab9", "path": "SDWebImage/Core/UIImage+MultiFormat.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a03557308b429ab031d47e4c9ac758d", "path": "SDWebImage/Core/UIImage+Transform.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e986f6743687fb5fcdf843d986154c3c671", "path": "SDWebImage/Core/UIImage+Transform.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983cff95ac5e10ad9825189ba2364b549d", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e989d830f1f11dde21594c310b5850f1d9d", "path": "SDWebImage/Core/UIImageView+HighlightedWebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980cae5a066a23e64462e31a20a86d0c19", "path": "SDWebImage/Core/UIImageView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985501e55b0a4af87c8971495d86b784ee", "path": "SDWebImage/Core/UIImageView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b7151832e679f984b9111df41f37e534", "path": "SDWebImage/Core/UIView+WebCache.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9855bae466f4ce3aff1eef74b3099fcc3c", "path": "SDWebImage/Core/UIView+WebCache.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985da32bd7e5a76d37fbe1c08c2ccd697c", "path": "SDWebImage/Core/UIView+WebCacheOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98949c597220c170dc62a2e0c70ca68dcc", "path": "SDWebImage/Core/UIView+WebCacheOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98370fb5623fa24b09d189d6fac65ad062", "path": "SDWebImage/Core/UIView+WebCacheState.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6979b4b582974b64fc20fbfe635dfd6", "path": "SDWebImage/Core/UIView+WebCacheState.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e983205d52814188692f777b629c26313c7", "path": "WebImage/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9873a66389658666937a94552dd1eefa8a", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98699ec6efc1dce012d3a719c7e704d9ee", "name": "Core", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982534da8fa079ec0814637b9fe329d4f1", "path": "ResourceBundle-SDWebImage-SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9885b256f1ab8b8ff01fa6e51e27662e9d", "path": "SDWebImage.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9851d09bafce9a184c5994453a5eb61a55", "path": "SDWebImage-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e981b1b94951fc7eafadea7c5486f8a95f4", "path": "SDWebImage-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98218ae70f16eed5c6cc5bc4836324b276", "path": "SDWebImage-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986648aac2c267537f898ee12abcff671d", "path": "SDWebImage-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e984bee2981770a98e21f8d80c7f2fd9acd", "path": "SDWebImage.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9893c15ff6ed75b9e22cf392a4d0f0df11", "path": "SDWebImage.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9881bf42a9d86039da0eea530b86be0aa3", "name": "Support Files", "path": "../Target Support Files/SDWebImage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee705ad64d1ddfabcbe3ff67a1d870c9", "name": "SDWebImage", "path": "SDWebImage", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98601b1b7af1e37bbb52f4ab52a2575851", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9892f477e58ecf3c2852182080ebdd95fd", "path": "SDWebImageWebPCoder/Classes/SDImageWebPCoder.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98eb7b07f54fa7c7eb5225c05ba1f983d9", "path": "SDWebImageWebPCoder/Private/SDInternalMacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ee7bccf776fd4e28a68e302bea113d04", "path": "SDWebImageWebPCoder/Private/SDmetamacros.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988c6aa306d618f0270662642d15fd9989", "path": "SDWebImageWebPCoder/Module/SDWebImageWebPCoder.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ef9b41f80dd1984fa64dbea99081b576", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985960a331e4453dcaf865e35568ab13ff", "path": "SDWebImageWebPCoder/Classes/SDWebImageWebPCoderDefine.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98998db4b34f28dfa07a44623299170f70", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9825dc6411fa74fbca1a37ff865cdc5818", "path": "SDWebImageWebPCoder/Classes/UIImage+WebP.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e983728832c2046082c5abc01c8bca05b18", "path": "SDWebImageWebPCoder.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9858ed5402cdb24ed1ecdcad87558880bf", "path": "SDWebImageWebPCoder-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9865dadfc8b772a88be82aad4e7ee30249", "path": "SDWebImageWebPCoder-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988f5396f268c1b7137db7f33e70edfd55", "path": "SDWebImageWebPCoder-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98085ac97b3250c07b48bec8160698eed0", "path": "SDWebImageWebPCoder.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98d497bbacfe60bfb1c4e431c47613bd38", "path": "SDWebImageWebPCoder.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988f1b3735c753c19c1c83179f671af33b", "name": "Support Files", "path": "../Target Support Files/SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d63e96cfba918266ab2d0caca6bf5c4b", "name": "SDWebImageWebPCoder", "path": "SDWebImageWebPCoder", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989723dd6a62b813e63663ab077e855240", "path": "SwiftyGif/NSImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dabdd945169d1d17b86f1bb08acf3ae3", "path": "SwiftyGif/NSImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989590bbdf84ca4f899b3f8b423e2b9d74", "path": "SwiftyGif/ObjcAssociatedWeakObject.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cff828a1d33a6ab11c45150986b71c16", "path": "SwiftyGif/SwiftyGif.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989592e4219c416032e2fcc32d8eca74b4", "path": "SwiftyGif/SwiftyGifManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9825d59652f2fc47451e081d3be23b548c", "path": "SwiftyGif/UIImage+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98965afda4f5c42f05dcaf6ffafee2d4cd", "path": "SwiftyGif/UIImageView+SwiftyGif.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e984d5083492a9bc0c010a1aa195bdbeb36", "path": "SwiftyGif/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c916f7b6bf21eca5ea31ff9cd24aba75", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98d4464d26a668c2ff8da5cf3f24bf0b68", "path": "ResourceBundle-SwiftyGif-SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98eefafc13a6cec5208c2bda6af438a683", "path": "SwiftyGif.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98537a523921711c0392dada8c411e265d", "path": "SwiftyGif-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985750850d27aacb4fdfce61f3880e020d", "path": "SwiftyGif-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e988dda648b32fd4954d4152b11c2e46d38", "path": "SwiftyGif-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c70b3d8a097685e91ccc0faad9a477fa", "path": "SwiftyGif-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9859368a37f84bc3406eee0e374d97eb85", "path": "SwiftyGif.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850e3d65dfb5b297ec606733335abe639", "path": "SwiftyGif.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9893ae1378b9a28899014b9c316f9a3930", "name": "Support Files", "path": "../Target Support Files/SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea34eb146ead53b1718a06a713528659", "name": "SwiftyGif", "path": "SwiftyGif", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981f44909d90b49c14ac28d576b81c0fc2", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98753ff13da987122262249987e5ef2877", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/Documents/flutter_project/CFA-Infy-Vcard/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/Documents/flutter_project/CFA-Infy-Vcard/ios/Pods", "targets": ["TARGET@v11_hash=8271563e2fc8738a44241c14b9ed3151", "TARGET@v11_hash=6d88d76514812a36ad9b46edd6f4b9b7", "TARGET@v11_hash=3f00fd00ee704b7621cea9d698a3e4e2", "TARGET@v11_hash=7401cd68ca7822a0a93ebbfa4f081482", "TARGET@v11_hash=07923eea5bee37bf22d06b5375453268", "TARGET@v11_hash=6e5228ff8ae92876ded11e5132ddadc6", "TARGET@v11_hash=c4a3cded5e04b0d45070a5f51f365b6f", "TARGET@v11_hash=4b120a18f672b8ccdcccd932d74ec24c", "TARGET@v11_hash=26bb2b32ca25df349c12f4633b3d0908", "TARGET@v11_hash=ce8d7b5d58c5af87c75c044d6fe6a624", "TARGET@v11_hash=4d84ac13a75547eabdf3adc1cc6d20b2", "TARGET@v11_hash=76e399f261992bf0b1f117e14e4f2aa9", "TARGET@v11_hash=96edb483af1f48f164ec641fcecf4632", "TARGET@v11_hash=90140de30495fb5f5d837acf393cab4b", "TARGET@v11_hash=1b091917cc80c4748b768d7eddaf138a", "TARGET@v11_hash=4fc005b82da1c47cdeb9d546bdecb3d8", "TARGET@v11_hash=efe3e7d21e1ea9c648ff9ecbba811eae", "TARGET@v11_hash=88260b75f812ded83724779cc42c012a", "TARGET@v11_hash=386136e0c4fc94cef639c325145a8adb", "TARGET@v11_hash=1688bdb8951ad963fec04a66be6b75aa", "TARGET@v11_hash=44b270d5076149f998026c9dd3767009", "TARGET@v11_hash=4115cbe693ffa300f68913b426dee7b1", "TARGET@v11_hash=27c98f8a25b16ba25a24ec3b39039315", "TARGET@v11_hash=ee86bda8e23f1a035ab963d0b87d7d86", "TARGET@v11_hash=325a279911cd9d679df4601961b10384", "TARGET@v11_hash=a92153b0a196812ab284baf029423a0a", "TARGET@v11_hash=3b5bd0074006cf8feb2a6951ec066d17", "TARGET@v11_hash=3e4d9678b4943e77c7aafafd5c12a850", "TARGET@v11_hash=40662245c7418bfce3e67b404de8a6d0", "TARGET@v11_hash=15effe8a5b184cbc0aa4d5e3a4ef79b8", "TARGET@v11_hash=69db352f0b59412b065e3f1d8663801a", "TARGET@v11_hash=5a6bc3a0d333992d5a1018ab22891234", "TARGET@v11_hash=74da56a372ff83c39eac9d2fea2a7750", "TARGET@v11_hash=ae54291ca87fc0c038cdf7713862b694", "TARGET@v11_hash=44c73477ef971527e97b71fa7bf881de", "TARGET@v11_hash=1db0bf2287f40125f9d5f94f08674b9e", "TARGET@v11_hash=bac38311a0aab791067468e3d4fe9046", "TARGET@v11_hash=e8b5808fe02bcebe16288f1827f23db2", "TARGET@v11_hash=36fe9d0d9a911c0829ea3ac0f949cf06", "TARGET@v11_hash=77d6b770dc1789dcaeb2ddc09a82fc9d", "TARGET@v11_hash=21f63119d3daaedf3215d0fa69937bb9", "TARGET@v11_hash=63cb763520b5eb9fc7f15a39889c3869", "TARGET@v11_hash=2dcd697428439b3a1cd33f355d44d46b", "TARGET@v11_hash=ed4b71832296ff25711a55376170c883", "TARGET@v11_hash=0a2b0c43e815b2c169ec010ebbba8723", "TARGET@v11_hash=7740ae354cd32263525d9cace8c7a8e5", "TARGET@v11_hash=c8eeae2a81a250cc2d3c63d3823d5091", "TARGET@v11_hash=280303657486e91707f3944f707f3c3d", "TARGET@v11_hash=f17cd3d6125f79aec3de6b441377aa22", "TARGET@v11_hash=b3975c1f62a902646b7ec167c0c8d4da", "TARGET@v11_hash=0539713964ebdf56efc93575417c0d88", "TARGET@v11_hash=3881db9620e2388d6f943cb51f0eb07a", "TARGET@v11_hash=36f5cfac2e7bc69a84c6ea91cf8128e9", "TARGET@v11_hash=e6616a010ba6a40fb8ef82be3813c7b2", "TARGET@v11_hash=f9ba29a39ef38a8e49f293a56f6334c5", "TARGET@v11_hash=4419a9bbc4699d67b23276cb3af5fe1d", "TARGET@v11_hash=dc6449b38843fdf9591edf1481082fba", "TARGET@v11_hash=00a33b239cb5cc6fcb56e45521c4502b", "TARGET@v11_hash=dc7f70b637d7f5d7c6e33d102bcabf8b", "TARGET@v11_hash=314fbbb9d75f80253fd630ae71139e98", "TARGET@v11_hash=e812ee55632ae0e2fbc8bc96b3036eae", "TARGET@v11_hash=ac3ad1c3a11ff689365550cdb65f09fb", "TARGET@v11_hash=acc1ddbdad5c5b8e3f38fd481f5158a4", "TARGET@v11_hash=bef8c56b876d6b387cf6c7ed74f52073", "TARGET@v11_hash=649037c8fd2f11c448edec3e5118bfbe", "TARGET@v11_hash=680e63d4ec8c71d8f807ade4cc8b4b30", "TARGET@v11_hash=566b655a277e8971a57b2e8c3f8469fa", "TARGET@v11_hash=03f4dc3bc2abf342af69e53139919360", "TARGET@v11_hash=9ac2a5a4daf1ef810d41597c3efb57ff", "TARGET@v11_hash=28c5c3803af1dddaa2c11ad2d0844777", "TARGET@v11_hash=a2726b63d67c4ecbb78dffd87d434768", "TARGET@v11_hash=579fbf289634d2a2f018d21b751f8610"]}